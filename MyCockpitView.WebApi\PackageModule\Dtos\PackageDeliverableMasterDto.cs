﻿
using AutoMapper;
using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.PackageModule.Entities;

namespace MyCockpitView.WebApi.PackageModule.Dtos;

public class PackageDeliverableMasterDto : BaseEntityDto
{
    public string?  Title { get; set; }
}

public class PackageDeliverableMasterDtoMapperProfile : Profile
{
    public PackageDeliverableMasterDtoMapperProfile()
    {
        CreateMap<PackageDeliverableMaster, PackageDeliverableMasterDto>()
        .ReverseMap();


    }
}