﻿using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using MyCockpitView.CoreModule;

namespace MyCockpitView.WebApi.DesignScriptModule.Entities;

public class DesignScriptDataCard : BaseEntity
{
    [StringLength(255)]
    public string? Title { get; set; }

    [StringLength(255)]
    public string? Subtitle { get; set; }

    public int CodeFlag { get; set; } = 0;

    [StringLength(255)]
    public string? Code { get; set; }

    public string? GFCTag { get; set; }

    [StringLength(255)]
    public string? LibraryCode { get; set; }

    [StringLength(255)]
    public string? Filename { get; set; }
    public string? BlobPath { get; set; }
    public int Size { get; set; } = 0;

    [StringLength(255)]
    public string? ContentType { get; set; }
    public string? Url { get; set; }
    public string? ThumbFilename { get; set; }
    public string? ThumbUrl { get; set; }

    public string? Category { get; set; }
    public bool IsHidden { get; set; }
    public Guid? LibraryEntityID { get; set; }

    [StringLength(10)]
    public string? ProjectCode { get; set; }
    public int? ProjectID { get; set; }
    public Guid? ProjectUID { get; set; }

    public string? History { get; set; }

    [Column("Links")]
    public string? _links { get; set; }

    [NotMapped]
    public ICollection<Guid> Links
    {
        get
        {
            return _links != null && _links != string.Empty ?
                _links.Split(',').Select(x => Guid.Parse(x)).ToList() :
                new List<Guid>() { };
        }
        set
        {
            _links = string.Join(",", value);
        }
    }
    public virtual ICollection<DesignScriptDataCardAttachment> Attachments { get; set; } = new List<DesignScriptDataCardAttachment>();

    public virtual ICollection<DesignScriptDataCardAttribute> Attributes { get; set; } = new List<DesignScriptDataCardAttribute>();

    public virtual ICollection<DesignScriptDataCardEntityMap> Maps { get; set; } = new List<DesignScriptDataCardEntityMap>();

    public bool IsReadOnly { get; set; }
}

public class DesignScriptDataCardConfiguration : BaseEntityConfiguration<DesignScriptDataCard>, IEntityTypeConfiguration<DesignScriptDataCard>
{
    public void Configure(EntityTypeBuilder<DesignScriptDataCard> builder)
    {
        base.Configure(builder);

        builder.HasIndex(e => e.CodeFlag);
        builder.HasIndex(e => e.Filename);
        builder.HasIndex(e => e.IsHidden);
        builder.HasIndex(e => e.LibraryCode);
        builder.HasIndex(e => e.Category);
        builder.HasIndex(e => e.Title);
        builder.HasIndex(e => e.Subtitle);
        builder.HasIndex(e => e.Code);
        builder.HasIndex(e => e.ProjectID);
        builder.HasIndex(e => e.ProjectUID);
    }
}