﻿using MyCockpitView.WebApi.Services;
using MyCockpitView.WebApi.TodoModule.Entities;

namespace MyCockpitView.WebApi.TodoModule.Services;

public interface ITodoService : IBaseEntityService<Todo>
{
    Task<int> Create(Todo Entity, ICollection<TodoAgenda>? Agendas = null);
    Task<IEnumerable<int>> GetAssigneeContactIDs(int entityID, string stageCode, string propertyName);
    Task<dynamic> GetTaskByStage(string Entity, int EntityID, string StageCode, string StageTitle, decimal StageDuration, DateTime? FollowUpDate = null);
    Task TaskAction(int EntityID, string StageCode, int WFTaskID, string taskComment = null);
}