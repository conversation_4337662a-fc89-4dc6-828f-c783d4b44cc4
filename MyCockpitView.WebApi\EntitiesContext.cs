﻿using Microsoft.EntityFrameworkCore.ChangeTracking;
using Microsoft.EntityFrameworkCore.Storage;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.CoreModule;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;

using MyCockpitView.WebApi.ContactModule.Entities;
using MyCockpitView.WebApi.AuthModule.Entities;
using MyCockpitView.WebApi.AppSettingMasterModule;
using MyCockpitView.WebApi.ActivityModule;
using MyCockpitView.WebApi.StatusMasterModule;
using MyCockpitView.WebApi.TypeMasterModule;
using MyCockpitView.WebApi.TodoModule.Entities;
using MyCockpitView.WebApi.WFTaskModule.Entities;

using MyCockpitView.WebApi.MeetingModule.Entities;
using MyCockpitView.WebApi.RequestTicketModule.Entities;
using Microsoft.AspNetCore.Identity;
using MyCockpitView.WebApi.WebPushSubscriptionModule;
using MyCockpitView.WebApi.ProjectModule.Entities;
using MyCockpitView.WebApi.PackageModule.Entities;
using MyCockpitView.WebApi.CompanyModule.Entities;
using MyCockpitView.WebApi.LibraryModule.Entities;
using MyCockpitView.WebApi.ExpenseModule.Entities;
using MyCockpitView.WebApi.HabitModule.Entities;
using MyCockpitView.WebApi.DesignScriptModule.Entities;
using MyCockpitView.WebApi.InspectionModule.Entities;
using MyCockpitView.WebApi.LeaveModule.Entities;
using MyCockpitView.WebApi.PayrollModule.Entities;
using MyCockpitView.WebApi.ProcessLibraryModule.Entities;
using MyCockpitView.WebApi.WFStageModule.Entities;
using MyCockpitView.WebApi.Services;

namespace MyCockpitView.WebApi;


public class EntitiesContext : IdentityDbContext<User, Role, Guid,IdentityUserClaim<Guid>,IdentityUserRole<Guid>,IdentityUserLogin<Guid>, IdentityRoleClaim<Guid>, IdentityUserToken<Guid>>
{
    private readonly IServiceProvider? _serviceProvider;
    public EntitiesContext(DbContextOptions<EntitiesContext> opt) : base(opt)
    { }

    public EntitiesContext(DbContextOptions<EntitiesContext> opt, IServiceProvider serviceProvider)
        : base(opt)
    {
        _serviceProvider = serviceProvider;
    }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
       
        //Custom functions for Querying
        modelBuilder.AddCustomFunctions();

       
        modelBuilder.ApplyConfiguration(new RefreshTokenConfiguration());
        modelBuilder.ApplyConfiguration(new AppSettingMasterConfiguration());
        modelBuilder.ApplyConfiguration(new CompanyConfiguration());
        modelBuilder.ApplyConfiguration(new StatusMasterConfiguration());
        modelBuilder.ApplyConfiguration(new TypeMasterConfiguration());
        modelBuilder.ApplyConfiguration(new ActivityConfiguration());

        //Auth
        modelBuilder.ApplyConfiguration(new LoginSessionConfiguration());

        //Company
        modelBuilder.ApplyConfiguration(new CompanyConfiguration());

        //Contacts
        modelBuilder.ApplyConfiguration(new ContactConfiguration());
        modelBuilder.ApplyConfiguration(new ContactAssociationConfiguration());
        modelBuilder.ApplyConfiguration(new ContactAttachmentConfiguration());
        modelBuilder.ApplyConfiguration(new ContactAppointmentConfiguration());
        modelBuilder.ApplyConfiguration(new ContactAppointmentAttachmentConfiguration());
        modelBuilder.ApplyConfiguration(new ContactGroupConfiguration());
        modelBuilder.ApplyConfiguration(new ContactGroupMemberConfiguration());
        modelBuilder.ApplyConfiguration(new ContactAppointmentBurnOutLogConfiguration());

        //Projects
        modelBuilder.ApplyConfiguration(new ProjectConfiguration());
        modelBuilder.ApplyConfiguration(new ProjectAreaConfiguration());
        modelBuilder.ApplyConfiguration(new ProjectAttachmentConfiguration());
        modelBuilder.ApplyConfiguration(new ProjectAssociationConfiguration());
        modelBuilder.ApplyConfiguration(new ProjectInwardConfiguration());
        modelBuilder.ApplyConfiguration(new ProjectInwardAttachmentConfiguration());
        modelBuilder.ApplyConfiguration(new ProjectNoteConfiguration());
        modelBuilder.ApplyConfiguration(new ProjectAreaConfiguration());
        modelBuilder.ApplyConfiguration(new ProjectConsultantConfiguration());
        modelBuilder.ApplyConfiguration(new ProjectOutwardConfiguration());
        modelBuilder.ApplyConfiguration(new ProjectOutwardAttachmentConfiguration());
        modelBuilder.ApplyConfiguration(new ProjectBillConfiguration());
        modelBuilder.ApplyConfiguration(new ProjectBillPaymentConfiguration());
        modelBuilder.ApplyConfiguration(new ProjectBillPaymentAttachmentConfiguration());
        modelBuilder.ApplyConfiguration(new ProjectScopeConfiguration());
        modelBuilder.ApplyConfiguration(new ProjectScopeServiceConfiguration());
        modelBuilder.ApplyConfiguration(new ProjectScopeServiceMastertConfiguration());
        modelBuilder.ApplyConfiguration(new ProjectScopeVersionConfiguration());
        modelBuilder.ApplyConfiguration(new ProjectClientReportLogConfiguration());
        modelBuilder.ApplyConfiguration(new ProjectGigPointConfiguration());
        modelBuilder.ApplyConfiguration(new ProjectHistoryConfiguration());

        //Todo
        modelBuilder.ApplyConfiguration(new TodoConfiguration());
        modelBuilder.ApplyConfiguration(new TodoAgendaConfiguration());
        modelBuilder.ApplyConfiguration(new TodoAttachmentConfiguration());

        //WFTask
        modelBuilder.ApplyConfiguration(new WFTaskConfiguration());
        modelBuilder.ApplyConfiguration(new WFTaskAttachmentConfiguration());
        modelBuilder.ApplyConfiguration(new TimeEntryConfiguration());
        modelBuilder.ApplyConfiguration(new AssessmentConfiguration());
        modelBuilder.ApplyConfiguration(new AssessmentMasterConfiguration());
        modelBuilder.ApplyConfiguration(new WFStageConfiguration());
        modelBuilder.ApplyConfiguration(new WFStageActionConfiguration());
        modelBuilder.ApplyConfiguration(new TaskRequestConfiguration());


        //Meeting
        modelBuilder.ApplyConfiguration(new MeetingConfiguration());
        modelBuilder.ApplyConfiguration(new MeetingAttendeeConfiguration());
        modelBuilder.ApplyConfiguration(new MeetingAgendaConfiguration());
        modelBuilder.ApplyConfiguration(new MeetingAgendaAttachmentConfiguration());

        //RequestTicket
        modelBuilder.ApplyConfiguration(new RequestTicketConfiguration());
        modelBuilder.ApplyConfiguration(new RequestTicketAssigneeConfiguration());
        modelBuilder.ApplyConfiguration(new RequestTicketAttachmentConfiguration());

        //WebPushSubscription
        modelBuilder.ApplyConfiguration(new WebPushSubscriptionConfiguration());

        //Package
        modelBuilder.ApplyConfiguration(new PackageConfiguration());
        modelBuilder.ApplyConfiguration(new PackageAssociationConfiguration());
        modelBuilder.ApplyConfiguration(new PackageAttachmentConfiguration());
        modelBuilder.ApplyConfiguration(new PackageDeliverableConfiguration());
        modelBuilder.ApplyConfiguration(new PackageDeliverableMasterConfiguration());
        modelBuilder.ApplyConfiguration(new PackageDeliverableTaskMapConfiguration());
        modelBuilder.ApplyConfiguration(new PackageDesignIntentConfiguration());
        modelBuilder.ApplyConfiguration(new PackageFeedbackConfiguration());
        modelBuilder.ApplyConfiguration(new PackageFeedbackAttachmentConfiguration());

        //LIBRARY ENTITY
        modelBuilder.ApplyConfiguration(new LibraryEntityConfiguration());
        modelBuilder.ApplyConfiguration(new LibraryEntityAttachmentConfiguration());
        modelBuilder.ApplyConfiguration(new LibraryEntityAttributeConfiguration());
        modelBuilder.ApplyConfiguration(new LibraryEntityVendorConfiguration());
        modelBuilder.ApplyConfiguration(new LibraryTitleMasterConfiguration());
        modelBuilder.ApplyConfiguration(new LibraryAttributeMasterConfiguration());

        //Expense
        modelBuilder.ApplyConfiguration(new ExpenseConfiguration());
        modelBuilder.ApplyConfiguration(new ExpenseAttachmentConfiguration());

        //Habit
        modelBuilder.ApplyConfiguration(new HabitConfiguration());
        modelBuilder.ApplyConfiguration(new HabitAttachmentConfiguration());
        modelBuilder.ApplyConfiguration(new HabitResponseConfiguration());

        //DESIGNSCRIPT
        modelBuilder.ApplyConfiguration(new DesignScriptEntityConfiguration());
        modelBuilder.ApplyConfiguration(new DesignScriptDataCardConfiguration());
        modelBuilder.ApplyConfiguration(new DesignScriptDataCardAttachmentConfiguration());
        modelBuilder.ApplyConfiguration(new DesignScriptDataCardAttributeConfiguration());
        modelBuilder.ApplyConfiguration(new DesignScriptDataCardEntityMapConfiguration());
        modelBuilder.ApplyConfiguration(new DesignScriptEntityItemMapConfiguration());
        modelBuilder.ApplyConfiguration(new DesignScriptItemConfiguration());
        modelBuilder.ApplyConfiguration(new DesignScriptItemMasterConfiguration());
        modelBuilder.ApplyConfiguration(new DesignScriptMeasurementConfiguration());
        modelBuilder.ApplyConfiguration(new DesignScriptMeasurementGroupConfiguration());

        //Inspection
        modelBuilder.ApplyConfiguration(new InspectionConfiguration());
        modelBuilder.ApplyConfiguration(new InspectionRecipientConfiguration());
        modelBuilder.ApplyConfiguration(new InspectionItemConfiguration());
        modelBuilder.ApplyConfiguration(new InspectionItemAttachmentConfiguration());


        //Leave
        modelBuilder.ApplyConfiguration(new LeaveConfiguration());
        modelBuilder.ApplyConfiguration(new HolidayMasterConfiguration());
        modelBuilder.ApplyConfiguration(new LeaveAttachmentConfiguration());

        //Payroll
        modelBuilder.ApplyConfiguration(new PayrollConfiguration());
        modelBuilder.ApplyConfiguration(new LoanConfiguration());

        //ProcessLibrary
        modelBuilder.ApplyConfiguration(new ProcessLibraryEntityConfiguration());
        modelBuilder.ApplyConfiguration(new ProcessLibraryEntityAttachmentConfiguration());

        // Apply precision to all decimal properties
        foreach (var entityType in modelBuilder.Model.GetEntityTypes())
        {
            var decimalProperties = entityType.GetProperties()
                .Where(p => p.ClrType == typeof(decimal) || p.ClrType == typeof(decimal?));

            foreach (var property in decimalProperties)
            {
                // Apply precision and scale directly to the model
                modelBuilder.Entity(entityType.ClrType)
                    .Property(property.Name)
                    .HasPrecision(18, 2); // Precision and scale
            }
        }

        base.OnModelCreating(modelBuilder);

    }

    public DbSet<User> Users { get; set; }
    public DbSet<Role> Roles { get; set; }
    public DbSet<IdentityUserRole<Guid>> UserRoles { get; set; }
    public DbSet<RefreshToken> RefreshTokens { get; set; }
    public DbSet<Activity> Activities { get; set; }
    public DbSet<AppSettingMaster> AppSettingMasters { get; set; }
    public DbSet<StatusMaster> StatusMasters { get; set; }
    public DbSet<TypeMaster> TypeMasters { get; set; }
    public DbSet<Company> Companies { get; set; }

    //Auth
    public DbSet<LoginSession> LoginSessions { get; set; }

    //Contacts
    public DbSet<Contact> Contacts { get; set; }
    public DbSet<ContactAssociation> ContactAssociations { get; set; }
    public DbSet<ContactAttachment> ContactAttachments { get; set; }
    public DbSet<ContactAppointment> ContactAppointments { get; set; }
    public DbSet<ContactAppointmentAttachment> ContactAppointmentAttachments { get; set; }
    public DbSet<ContactGroup> ContactGroups { get; set; }
    public DbSet<ContactGroupMember> ContactGroupMembers { get; set; }
    public DbSet<ContactAppointmentBurnOutLog> ContactAppointmentBurnOutLogs { get; set; }

    //Projects
    public DbSet<Project> Projects { get; set; }
    public DbSet<ProjectArea> ProjectAreas { get; set; }
    public DbSet<ProjectAssociation> ProjectAssociations { get; set; }
    public DbSet<ProjectConsultant> ProjectConsultants { get; set; }
    public DbSet<ProjectAttachment> ProjectAttachments { get; set; }
    public DbSet<ProjectBill> ProjectBills { get; set; }
    public DbSet<ProjectBillPayment> ProjectBillPayments { get; set; }
    public DbSet<ProjectBillPaymentAttachment> ProjectBillPaymentAttachments { get; set; }
    public DbSet<ProjectInward> ProjectInwards { get; set; }
    public DbSet<ProjectInwardAttachment> ProjectInwardAttachments { get; set; }
    public DbSet<ProjectNote> ProjectNotes { get; set; }
    public DbSet<ProjectOutward> ProjectOutwards { get; set; }
    public DbSet<ProjectOutwardAttachment> ProjectOutwardAttachments { get; set; }
    public DbSet<ProjectScope> ProjectScopes { get; set; }
    public DbSet<ProjectScopeService> ProjectScopeServices { get; set; }
    public DbSet<ProjectScopeServiceMaster> ProjectScopeServiceMasters { get; set; }
    public DbSet<ProjectScopeVersion> ProjectScopeVersions { get; set; }
    public DbSet<ProjectClientReportLog> ProjectClientReportLogs { get; set; }
    public DbSet<ProjectGigPoint> ProjectGigPoints { get; set; }
    public DbSet<ProjectHistory> ProjectHistories { get; set; }

    //Todo
    public DbSet<Todo> Todos { get; set; }
    public DbSet<TodoAttachment> TodoAttachments { get; set; }
    public DbSet<TodoAgenda> TodoAgendas { get; set; }

    //WFTask
    public DbSet<WFTaskAttachment> WFTaskAttachments { get; set; }
    public DbSet<WFStage> WFStages { get; set; }
    public DbSet<WFStageAction> WFStageActions { get; set; }
    public DbSet<WFTask> WFTasks { get; set; }
    public DbSet<AssessmentMaster> AssessmentMasters { get; set; }
    public DbSet<Assessment> Assessments { get; set; }
    public DbSet<TimeEntry> TimeEntries { get; set; }
    public DbSet<TaskRequest> TaskRequests { get; set; }

    //Meeting
    public DbSet<Meeting> Meetings { get; set; }
    public DbSet<MeetingAttendee> MeetingAttendees { get; set; }
    public DbSet<MeetingAgenda> MeetingAgendas { get; set; }
    public DbSet<MeetingAgendaAttachment> MeetingAgendaAttachments { get; set; }

    //RequestTicket
    public DbSet<RequestTicket> RequestTickets { get; set; }
    public DbSet<RequestTicketAssignee> RequestTicketAssignees { get; set; }
    public DbSet<RequestTicketAttachment> RequestTicketAttachments { get; set; }

    //WebPushSubscription
    public DbSet<WebPushSubscription> WebPushSubscriptions { get; set; }

    //Package
    public DbSet<Package> Packages { get; set; }
    public DbSet<PackageAttachment> PackageAttachments { get; set; }
    public DbSet<PackageAssociation> PackageAssociations { get; set; }
    public DbSet<PackageDeliverable> PackageDeliverables { get; set; }
    public DbSet<PackageDeliverableMaster> PackageDeliverableMasters { get; set; }
    public DbSet<PackageDeliverableTaskMap> PackageDeliverableTaskMaps { get; set; }
    public DbSet<PackageDesignIntent> PackageDesignIntents { get; set; }
    public DbSet<PackageFeedback> PackageFeedbacks { get; set; }
    public DbSet<PackageFeedbackAttachment> PackageFeedbackAttachments { get; set; }

    //LIBRARY ENTITY
    public DbSet<LibraryEntity> LibraryEntities { get; set; }
    public DbSet<LibraryEntityAttachment> LibraryEntityAttachments { get; set; }
    public DbSet<LibraryEntityAttribute> LibraryEntityAttributes { get; set; }
    public DbSet<LibraryEntityVendor> LibraryEntityVendors { get; set; }
    public DbSet<LibraryTitleMaster> LibraryTitleMasters { get; set; }
    public DbSet<LibraryAttributeMaster> LibraryAttributeMasters { get; set; }

    //Expense
    public DbSet<Expense> Expenses { get; set; }
    public DbSet<ExpenseAttachment> ExpenseAttachments { get; set; }

    //Habit
    public DbSet<Habit> Habits { get; set; }
    public DbSet<HabitAttachment> HabitAttachments { get; set; }
    public DbSet<HabitResponse> HabitResponses { get; set; }

    //DESIGNSCRIPT
    public DbSet<DesignScriptDataCard> DesignScriptDataCards { get; set; }
    public DbSet<DesignScriptDataCardAttachment> DesignScriptDataCardAttachments { get; set; }
    public DbSet<DesignScriptDataCardAttribute> DesignScriptDataCardAttributes { get; set; }
    public DbSet<DesignScriptDataCardEntityMap> DesignScriptDataCardEntityMaps { get; set; }
    public DbSet<DesignScriptEntity> DesignScriptEntities { get; set; }
    public DbSet<DesignScriptEntityItemMap> DesignScriptEntityItemMaps { get; set; }
    public DbSet<DesignScriptItem> DesignScriptItems { get; set; }
    public DbSet<DesignScriptItemMaster> DesignScriptItemMasters { get; set; }
    public DbSet<DesignScriptMeasurement> DesignScriptMeasurements { get; set; }
    public DbSet<DesignScriptMeasurementGroup> DesignScriptMeasurementGroups { get; set; }

    //Inspection
    public DbSet<Inspection> Inspections { get; set; }
    public DbSet<InspectionRecipient> InspectionRecipients { get; set; }
    public DbSet<InspectionItem> InspectionItems { get; set; }
    public DbSet<InspectionItemAttachment> InspectionItemAttachments { get; set; }

    //Leave
    public DbSet<Leave> Leaves { get; set; }
    public DbSet<HolidayMaster> HolidayMasters { get; set; }
    public DbSet<LeaveAttachment> LeaveAttachments { get; set; }

    //Payroll
    public DbSet<Payroll> Payrolls { get; set; }
    public DbSet<Loan> Loans { get; set; }  

    //ProcessLibrary
    public DbSet<ProcessLibraryEntity> ProcessLibraryEntities { get; set; }
    public DbSet<ProcessLibraryEntityAttachment> ProcessLibraryEntityAttachments { get; set; }

    // END

    private IDbContextTransaction _transaction;

    public async Task BeginTransactionAsync()
    {
        _transaction = await Database.BeginTransactionAsync();
    }

    public async Task CommitAsync()
    {
        try
        {
            await SaveChangesAsync();
            await _transaction.CommitAsync();
        }
        finally
        {
            await _transaction.DisposeAsync();
        }
    }

    public async Task RollbackAsync()
    {
        await _transaction.RollbackAsync();
        await _transaction.DisposeAsync();
    }

    public async override Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        if (ChangeTracker != null
              && ChangeTracker.Entries() != null
              && ChangeTracker.Entries().Where(x => x.Entity is BaseEntity).Any())
        {
            string? currentUsername = null;

            // Get the current user from the CurrentUserService
            if (_serviceProvider != null)
            {
                using var scope = _serviceProvider.CreateScope();
                var currentUserService = scope.ServiceProvider.GetService<ICurrentUserService>();
                if (currentUserService != null)
                {
                    currentUsername = currentUserService.GetCurrentUsername();
                }
            }

            if (currentUsername != null)
            {
                var contact = Contacts.SingleOrDefault(x => x.Username == currentUsername);
                string contactName = contact?.Name ?? "System";
                int? contactId = contact?.ID;

                var entities = ChangeTracker.Entries().Where(x => x.Entity is BaseEntity
                                                       && (x.State == EntityState.Added || x.State == EntityState.Modified));

                foreach (var entity in entities)
                {
                    if (entity.State == EntityState.Added)
                    {
                        ((BaseEntity)entity.Entity).Created = DateTime.UtcNow;
                        ((BaseEntity)entity.Entity).CreatedBy = contactName;
                        ((BaseEntity)entity.Entity).CreatedByContactID = contactId;
                    }

                    ((BaseEntity)entity.Entity).Modified = DateTime.UtcNow;
                    ((BaseEntity)entity.Entity).ModifiedBy = contactName;
                    ((BaseEntity)entity.Entity).ModifiedByContactID = contactId;
                }

                var markedAsDeleted = ChangeTracker.Entries().Where(x => x.Entity is BaseEntity
                                                            && x.State == EntityState.Deleted);

                foreach (var entity in markedAsDeleted)
                {
                    // Set the entity to unchanged (if we mark the whole entity as Modified, every field gets sent to Db as an update)
                    entity.State = EntityState.Unchanged;
                    // Only update the IsDeleted flag - only this will get sent to the Db
                    ((BaseEntity)entity.Entity).IsDeleted = true;
                    ((BaseEntity)entity.Entity).Modified = DateTime.UtcNow;
                    ((BaseEntity)entity.Entity).ModifiedBy = contactName;
                    ((BaseEntity)entity.Entity).ModifiedByContactID = contactId;

                }
            }
        }

        return await base.SaveChangesAsync(cancellationToken);
    }



}
