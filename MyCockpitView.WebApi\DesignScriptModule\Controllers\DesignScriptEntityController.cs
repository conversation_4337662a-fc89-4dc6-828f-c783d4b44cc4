﻿
using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.WebApi.Responses;
using MyCockpitView.Utility.Common;
using MyCockpitView.WebApi.ActivityModule;
using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.DesignScriptModule.Services;
using MyCockpitView.WebApi.ContactModule.Services;
using MyCockpitView.WebApi.DesignScriptModule.Dtos;
using MyCockpitView.WebApi.DesignScriptModule.Entities;
using MyCockpitView.Utility.RDLCClient;
using System.Net.Mime;

namespace MyCockpitView.WebApi.DesignScriptModule.Controllers;

[Authorize]
[ApiController]
[Route("[controller]")]
public class DesignScriptEntityController : ControllerBase
{
    private readonly IDesignScriptEntityService service;
    private readonly EntitiesContext db;
    private readonly IActivityService activityService;
    private readonly IMapper mapper;
    private readonly IContactService contactService;

    public DesignScriptEntityController(EntitiesContext db, IDesignScriptEntityService service, IMapper mapper, IActivityService activityService, IContactService contactService   )
    {
        this.db = db;
        this.service = service;
        this.activityService = activityService;
        this.mapper = mapper;
        this.contactService = contactService;
    }

    [HttpGet]
    public async Task<ActionResult<IEnumerable<DesignScriptEntityDto>>> Get(
        [FromQuery] string? filters = null,
        [FromQuery] string? search = null,
        [FromQuery] string? sort = null)
    {
        var query = service.Get(filters != null ? DataTools.GetObjectFromJsonString<APIFilter>(filters).Filters : null, search, sort)  .Include(x => x.DataCardMaps)
                    .ThenInclude(d => d.DataCard)
                    .ThenInclude(a=>a.Attachments)
                  .Include(x => x.DataCardMaps)
                    .ThenInclude(d => d.DataCard)
                 .ThenInclude(a => a.Attributes)
            ;

        var results = mapper.Map<IEnumerable<DesignScriptEntityDto>>(await query.ToListAsync());

        //var typeMasters = await db.TypeMasters
        //    .AsNoTracking()
        //    .Where(x => x.Entity == nameof(DesignScriptEntity))
        //    .ToListAsync();

        //foreach (var obj in results)
        //{
        //    obj.TypeValue = typeMasters.FirstOrDefault(x => x.Value == obj.TypeFlag)?.Title ?? "";
        //}

        //var statusMasters = await db.StatusMasters
        //  .AsNoTracking()
        //  .Where(x => x.Entity == nameof(DesignScriptEntity))
        //  .ToListAsync();

        //foreach (var obj in results)
        //{
        //    obj.StatusValue = statusMasters.FirstOrDefault(x => x.Value == obj.StatusFlag)?.Title ?? "";
        //}

        return Ok(results);
    }

    [HttpGet("Pages")]
    public async Task<ActionResult<PagedResponse<DesignScriptEntityDto>>> GetPages(
        [FromQuery] int page = 0,
        [FromQuery] int pageSize = 50,
        [FromQuery] string? filters = null,
        [FromQuery] string? search = null,
        [FromQuery] string? sort = null)
    {
        var query = service.Get(filters != null ? DataTools.GetObjectFromJsonString<APIFilter>(filters).Filters : null, search, sort);

        var totalCount = await query.CountAsync();
        var totalPages = (int)Math.Ceiling((decimal)totalCount / pageSize);
        var results = mapper.Map<IEnumerable<DesignScriptEntityDto>>(await query
            .Skip(pageSize * page)
            .Take(pageSize)
            .ToListAsync());

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(DesignScriptEntity))
            .ToListAsync();

        foreach (var obj in results)
        {
            obj.Type = typeMasters.FirstOrDefault(x => x.Value == obj.TypeFlag)?.Title ?? "";
        }

        var statusMasters = await db.StatusMasters
           .AsNoTracking()
           .Where(x => x.Entity == nameof(DesignScriptEntity))
           .ToListAsync();

        foreach (var obj in results)
        {
            obj.Status = statusMasters.FirstOrDefault(x => x.Value == obj.StatusFlag)?.Title ?? "";
        }

        return Ok(new PagedResponse<DesignScriptEntityDto>(results, totalCount, totalPages));
    }

    [HttpGet("{id:int}")]
    public async Task<ActionResult<DesignScriptEntityDto>> GetByID(int id)
    {
        var responseDto = mapper.Map<DesignScriptEntityDto>(await service.GetById(id));
        if (responseDto == null)
        {
            return NotFound();
        }

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(DesignScriptEntity))
            .ToListAsync();

        responseDto.Type = typeMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        var statusMasters = await db.StatusMasters
          .AsNoTracking()
          .Where(x => x.Entity == nameof(DesignScriptEntity))
          .ToListAsync();

        responseDto.Status = statusMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        return Ok(responseDto);
    }

    [HttpPost]
    public async Task<ActionResult<DesignScriptEntityDto>> Post(DesignScriptEntityDto dto)
    {
        var id = await service.Create(mapper.Map<DesignScriptEntity>(dto));
        var responseDto = mapper.Map<DesignScriptEntityDto>(await service.GetById(id));

        if (responseDto == null)
            return BadRequest("Not Created");

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(DesignScriptEntity))
            .ToListAsync();

        responseDto.Type = typeMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        var statusMasters = await db.StatusMasters
  .AsNoTracking()
  .Where(x => x.Entity == nameof(DesignScriptEntity))
  .ToListAsync();

        responseDto.Status = statusMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        //if (!string.IsNullOrEmpty(User.Identity?.Name))
        //{
        //    var currentContact = await contactService.Get().FirstOrDefaultAsync(x => x.Username == User.Identity.Name);
        //    if (currentContact != null)
        //    {
        //        var parent = await contactService.Get().SingleOrDefaultAsync(x => x.ID == responseDto.ContactID);
        //        if (parent != null)
        //        {
        //            await activityService.LogUserActivity(currentContact, nameof(parent), parent.ID, $"{parent.Name}", $"{nameof(DesignScriptEntity).Replace(nameof(parent),"")} | {responseDto.Company.Title} | {responseDto.Designation} | {responseDto.ManValue}mV", "Created");
        //        }
        //    }
        //}

        return CreatedAtAction(nameof(GetByID), new { id = responseDto.ID }, responseDto);
    }

    [HttpPut("{id:int}")]
    public async Task<ActionResult<DesignScriptEntityDto>> Put(int id, DesignScriptEntityDto dto)
    {
        if (id != dto.ID)
        {
            return BadRequest();
        }

        await service.Update(mapper.Map<DesignScriptEntity>(dto));
        var responseDto = mapper.Map<DesignScriptEntityDto>(await service.GetById(id));

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(DesignScriptEntity))
            .ToListAsync();

        responseDto.Type = typeMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        var statusMasters = await db.StatusMasters
  .AsNoTracking()
  .Where(x => x.Entity == nameof(DesignScriptEntity))
  .ToListAsync();

        responseDto.Status = statusMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        //if (!string.IsNullOrEmpty(User.Identity?.Name))
        //{
        //    var currentContact = await contactService.Get().FirstOrDefaultAsync(x => x.Username == User.Identity.Name);
        //    if (currentContact != null)
        //    {
        //        var parent = await contactService.Get().SingleOrDefaultAsync(x => x.ID == responseDto.ContactID);
        //        if (parent != null)
        //        {
        //            await activityService.LogUserActivity(currentContact, nameof(parent), parent.ID, $"{parent.Name}", $"{nameof(DesignScriptEntity).Replace(nameof(parent), "")} | {responseDto.Company.Title} | {responseDto.Designation} | {responseDto.ManValue}mV", "Updated");
        //        }
        //    }
        //}

        return Ok(responseDto);
    }

    [HttpDelete("{id:int}")]
    public async Task<IActionResult> Delete(int id)
    {
        var responseDto = mapper.Map<DesignScriptEntityDto>(await service.GetById(id));
        if (responseDto == null) return BadRequest($"{nameof(DesignScriptEntity)} not found!");

        await service.Delete(id);

        //if (!string.IsNullOrEmpty(User.Identity?.Name))
        //{
        //    var currentContact = await contactService.Get().FirstOrDefaultAsync(x => x.Username == User.Identity.Name);
        //    if (currentContact != null)
        //    {
        //        var parent = await contactService.Get().SingleOrDefaultAsync(x => x.ID == responseDto.ContactID);
        //        if (parent != null)
        //        {
        //            await activityService.LogUserActivity(currentContact, nameof(parent), parent.ID, $"{parent.Name}", $"{nameof(DesignScriptEntity).Replace(nameof(parent), "")} | {responseDto.Company.Title} | {responseDto.Designation} | {responseDto.ManValue}mV", "Deleted");
        //        }
        //    }
        //}

        return NoContent();
    }


    [HttpGet("uid/{id:guid}")]
    [AllowAnonymous]
    public async Task<ActionResult<DesignScriptEntityDto>> GetByGUID(Guid id)
    {
        var responseDto = mapper.Map<DesignScriptEntityDto>(await service.GetById(id));
        if (responseDto == null)
        {
            return NotFound();
        }

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(DesignScriptEntity))
            .ToListAsync();

        responseDto.Type = typeMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        var statusMasters = await db.StatusMasters
                        .AsNoTracking()
                        .Where(x => x.Entity == nameof(DesignScriptEntity))
                        .ToListAsync();

        responseDto.Status = statusMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        return Ok(responseDto);
    }


    [HttpGet("SearchTagOptions")]
    public async Task<IActionResult> GetSearchTagOptions()
    {
        var results = await service.GetSearchTagOptions();
        return Ok(results);
    }


    [HttpGet("FieldOptions")]
    public async Task<IActionResult> GetFieldOptions(string field)
    {
        var response = await service.GetFieldOptions(field);
        return Ok(response);
    }

    [HttpGet("NextCode/{typeFlag}/{projectId}")]
    public async Task<IActionResult> GetNextCode(int typeFlag, int projectId, int? parentId = null)
    {
        var response = new { Code = await service.GetNextCode(typeFlag, projectId, parentId) };
            return Ok(response);
    }

    [HttpGet("SearchTagOptions/{projectId}")]
    public async Task<IActionResult> GetSearchTagOptions(int projectId)
    {
        var response = await service.GetSearchTagOptions(projectId);
        return Ok(response);
    }

    [HttpGet("Report/{reportName}/{id:guid}")]
    [AllowAnonymous]
    public async Task<IActionResult> Report(string reportName, Guid id, string? size,
        string? filters = null, string? parameters = null, string output = "PDF", bool inline = false)
    {
        var _filters = filters != null ? DataTools.GetObjectFromJsonString<APIFilter>(filters).Filters : null;

        var _parameters = parameters != null ? DataTools.GetObjectFromJsonString<APIFilter>(parameters).Filters : null;

        ReportDefinition? reportDef = reportName.ToLower() switch
        {
            "design-script" => await service.GetDesignScriptPDF(id, size, _filters),
            "design-canvas" => await service.GetDesignCanvasPDF(id, size, _filters),
            "project-material-list" => await service.GetMaterialListPDF(id, size, _filters),
            "project-palette" => await service.GetPalettePDF(id, size, _filters),
            "element-estimate" => await service.GetElementEstimate(id, size, _filters, output),
            "space-estimate" => await service.GetSpaceEstimate(id, size, _filters, output),
            "project-item-list" => await service.GetItemList(id, size, _filters, output),
            "project-item-list-dwg" => await service.GetItemList(id, size, _filters, output, showDWGSpec: true),
            "measurement-sheet" => await service.GetElementMeasurementSheet(id, size, _filters, output),
            "element-measurement-sheet" => await service.GetElementMeasurementSheet(id, size, _filters, output),
            "zone-measurement-sheet" => await service.GetZoneMeasurementSheet(id, size, _filters, output),
            "package-set" => await service.GetPackageSetPDF(id, size, _filters),
            "element-boq" => await service.GetElementBOQ(id, size, _filters, output),
            "zone-boq" => await service.GetZoneBOQ(id, size, _filters, output),
            "element-boq-summary" => await service.GetElementBOQSummary(id, size, _filters, output),
            "zone-boq-summary" => await service.GetZoneBOQSummary(id, size, _filters, output),
            "element-estimated-boq" => await service.GetElementEstimatedBOQ(id, size, _filters, output),
            "zone-estimated-boq" => await service.GetZoneEstimatedBOQ(id, size, _filters, output),
            "project-schedule" => await service.GetSchedule(id, size, _filters, reportType: output),
            _ => null
        };

        if (reportDef?.FileContent == null)
        {
            return BadRequest("Report not generated!");
        }

        return File(
            fileContents: reportDef.FileContent,
            contentType: reportDef.FileContentType,
            fileDownloadName: $"{reportDef.Filename}{reportDef.FileExtension}",
            enableRangeProcessing: true
        );
    }

    public record ReportRequest(
        string ReportName,
        Guid Id,
        string? Size,
        string? Filters,
        string? Parameters,
        string Output = "PDF",
        bool Inline = false
    );

    [HttpGet("Excel/{reportName}/{id:guid}")]
    public async Task<IActionResult> GetExcel(string reportName, Guid id, string? filters = null, bool inline = false)
    {
        var _filters = filters != null ? DataTools.GetObjectFromJsonString<APIFilter>(filters).Filters : null;

        byte[]? report = reportName.ToLower() switch
        {
            "entity-list" => await service.GetEntityListExcel(id, _filters),
            "space-estimate" => await service.GetSpaceEstimateExcel(id, _filters),
            "element-estimate" => await service.GetElementEstimateExcel(id, _filters),
            "item-list" => await service.GetItemListExcel(id, _filters),
            "project-schedule" => await service.GetScheduleExcel(id, _filters),
            _ => null
        };

        if (report == null)
        {
            return BadRequest("Excel cannot be generated");
        }

        var filename = $"{reportName.ToUpper()}-{DateTimeOffset.Now.ToString("dd-MMM-yyyy")}.xlsx";

        return File(
            fileContents: report,
            contentType: MediaTypeNames.Application.Octet,
            fileDownloadName: filename,
            enableRangeProcessing: true
        );
    }
}