﻿
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using MyCockpitView.CoreModule;

namespace MyCockpitView.WebApi.DesignScriptModule.Entities;

public class DesignScriptItemMaster : BaseEntity
{
    [StringLength(256)]
    public string?  DSR { get; set; }

    [StringLength(256)]
    public string?  DSRNumber { get; set; }

    [StringLength(256)]
    public string?  Title { get; set; }

    [StringLength(256)]
    public string?  ItemGroup { get; set; }

    [StringLength(256)]
    public string?  Category { get; set; }
    public string?  Specification { get; set; }

    [StringLength(256)]
    public string?  Units { get; set; }
    [Precision(14, 2)]
    public decimal Rate { get; set; } = 0;

    public int CodeFlag { get; set; }

    [StringLength(256)]
    public string?  Code { get; set; }

    [StringLength(256)]
    public string?  ReferenceCode { get; set; }

  
    public string? DrawingSpecification { get; set; }

}

public class DesignScriptItemMasterConfiguration : BaseEntityConfiguration<DesignScriptItemMaster>, IEntityTypeConfiguration<DesignScriptItemMaster>
{
    public void Configure(EntityTypeBuilder<DesignScriptItemMaster> builder)
    {
       base.Configure(builder);

        builder.HasIndex(e => e.DSRNumber);
        builder.HasIndex(e => e.DSR);
        builder.HasIndex(e => e.Code);
        builder.HasIndex(e => e.Category);
        builder.HasIndex(e => e.ItemGroup);
        builder.HasIndex(e => e.Title);
        builder.HasIndex(e => e.CodeFlag);
        builder.HasIndex(e => e.ReferenceCode);
    }
}