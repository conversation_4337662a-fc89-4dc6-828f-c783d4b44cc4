﻿using AutoMapper;
using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.ProjectModule.Entities;
using System.ComponentModel.DataAnnotations;

namespace MyCockpitView.WebApi.ProjectModule.Dtos;

public class ProjectBillPaymentDto : BaseEntityDto
{
    public int ProjectBillID { get; set; }

    public string? Mode { get; set; }

    public decimal Amount { get; set; }

    public decimal TDS { get; set; }

    public decimal TDSRate { get; set; }


    public string? Comment { get; set; }

    public string? TransactionNo { get; set; }

    public DateTime TransactionDate { get; set; }

    public string? BankDetail { get; set; }

    public string? RefUrl { get; set; }

    public string? RefGuid { get; set; }

    public decimal BillAmountReceived { get; set; }
    public decimal GstAmountReceived { get; set; }
}

public class ProjectBillPaymentDtoMapperProfile : Profile
{
    public ProjectBillPaymentDtoMapperProfile()
    {
        CreateMap<ProjectBillPayment, ProjectBillPaymentDto>()
                   .ReverseMap();

    }
}