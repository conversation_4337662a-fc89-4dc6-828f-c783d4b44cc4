﻿using System.Data;
using System.Globalization;
using System.Text.RegularExpressions;
using MyCockpitView.WebApi.Exceptions;
using System.Text;
using MyCockpitView.WebApi.DesignScriptModule.Entities;
using MyCockpitView.WebApi.DesignScriptModule.Services;
using MyCockpitView.WebApi.MeetingModule.Entities;
using MyCockpitView.WebApi.MeetingModule.Services;
using MyCockpitView.WebApi.Services;
using MyCockpitView.WebApi.PackageModule.Entities;
using MyCockpitView.WebApi.PackageModule.Dtos;
using MyCockpitView.WebApi.ProjectModule.Entities;
using MyCockpitView.WebApi.ProjectModule.Services;
using MyCockpitView.WebApi.WFTaskModule.Services;
using MyCockpitView.WebApi.WFTaskModule.Entities;
using MyCockpitView.CoreModule;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.Utility.Common;
using MyCockpitView.Utility.RDLCClient;
using MyCockpitView.WebApi.TypeMasterModule;
using MyCockpitView.Utility.Excel;
using MyCockpitView.WebApi.AzureBlobsModule;
using MyCockpitView.Utility.PDFSharp;

namespace MyCockpitView.WebApi.PackageModule.Services;

public class PackageService : BaseEntityService<Package>, IPackageService
{
    public PackageService(EntitiesContext db) : base(db) { }

    public IQueryable<Package> Get(IEnumerable<QueryFilter>? Filters = null, string? Search = null, string? Sort = null)
    {

        IQueryable<Package> _query = base.Get(Filters);

        //Apply filters
        if (Filters != null)
        {


            if (Filters.Where(x => x.Key.Equals("isAnnexure", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var _item = Convert.ToBoolean(Filters.First(x => x.Key.Equals("isAnnexure", StringComparison.OrdinalIgnoreCase)).Value);

                var predicate = PredicateBuilder.False<Package>();

                if (_item)
                {
                    predicate = predicate.Or(x => x.IsAnnexure);
                }
                else
                {
                    predicate = predicate.Or(x => !x.IsAnnexure);
                }

                _query = _query.Where(predicate);
            }


            if (Filters.Where(x => x.Key.Equals("isDelayed", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var _item = Convert.ToBoolean(Filters.First(x => x.Key.Equals("isDelayed", StringComparison.OrdinalIgnoreCase)).Value);

                var predicate = PredicateBuilder.False<Package>();

                if (_item)
                {
                    predicate = predicate.Or(x => (x.StatusFlag != 1 && x.FinalDate < DateTime.UtcNow)
                        || (x.StatusFlag == 1 && x.FinalDate < x.SubmissionDate));
                }
                else
                {
                    predicate = predicate.Or(x => (x.StatusFlag != 1 && x.FinalDate >= DateTime.UtcNow)
                        || (x.StatusFlag == 1 && x.FinalDate >= x.SubmissionDate));
                }

                _query = _query.Where(predicate);
            }

            if (Filters.Where(x => x.Key.Equals("searchtag", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<Package>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("searchtag", StringComparison.OrdinalIgnoreCase)))
                {
                    predicate = predicate.Or(x => x._searchTags.Contains(_item.Value));
                }
                _query = _query.Where(predicate);
            }

            if (Filters.Where(x => x.Key.Equals("phaseCode", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<Package>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("phaseCode", StringComparison.OrdinalIgnoreCase)))
                {
                    predicate = predicate.Or(x => x.PhaseCode == _item.Value);
                }
                _query = _query.Where(predicate);
            }

            if (Filters.Where(x => x.Key.Equals("designScriptEntityID", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<Package>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("designScriptEntityID", StringComparison.OrdinalIgnoreCase)))
                {
                    var isNumeric = Convert.ToInt32(_item.Value);

                    predicate = predicate.Or(x => x.DesignScriptEntityID == isNumeric);
                }
                _query = _query.Where(predicate);
            }

            if (Filters.Where(x => x.Key.Equals("stage", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<Package>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("stage", StringComparison.OrdinalIgnoreCase)))
                {
                    predicate = predicate.Or(x => x.Stage == _item.Value);
                }
                _query = _query.Where(predicate);
            }

            if (Filters.Where(x => x.Key.Equals("projectid", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<Package>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("projectid", StringComparison.OrdinalIgnoreCase)))
                {
                    var isNumeric = Convert.ToInt32(_item.Value);

                    predicate = predicate.Or(x => x.ProjectID == isNumeric);
                }
                _query = _query.Where(predicate);
            }

            if (Filters.Where(x => x.Key.Equals("PartnerContactID", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<Package>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("PartnerContactID", StringComparison.OrdinalIgnoreCase)))
                {
                    var isNumeric = Convert.ToInt32(_item.Value);

                    predicate = predicate.Or(x => x.Associations.Where(a => a.TypeFlag == 0 && a.ContactID == isNumeric).Any());
                }
                _query = _query.Include(x => x.Associations).Where(predicate);
            }
            if (Filters.Where(x => x.Key.Equals("AssociateContactID", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<Package>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("AssociateContactID", StringComparison.OrdinalIgnoreCase)))
                {
                    var isNumeric = Convert.ToInt32(_item.Value);

                    predicate = predicate.Or(x => x.Associations.Where(a => a.TypeFlag == 1 && a.ContactID == isNumeric).Any());
                }
                _query = _query.Include(x => x.Associations).Where(predicate);
            }
            if (Filters.Where(x => x.Key.Equals("PartnerOrAssociateContactID", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<Package>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("PartnerOrAssociateContactID", StringComparison.OrdinalIgnoreCase)))
                {
                    var isNumeric = Convert.ToInt32(_item.Value);

                    predicate = predicate.Or(x => x.Associations.Where(a => (a.TypeFlag == 1 || a.TypeFlag == 0) && a.ContactID == isNumeric).Any());
                }
                _query = _query.Include(x => x.Associations).Where(predicate);
            }

            if (Filters.Where(x => x.Key.Equals("rangestart", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var _item = Filters.First(x => x.Key.Equals("rangestart", StringComparison.OrdinalIgnoreCase));

                DateTime result = Convert.ToDateTime(_item.Value);

                _query = _query.Where(x => (x.StatusFlag == 1
                && x.SubmissionDate != null
                && x.SubmissionDate.Value >= result)
                || (x.StatusFlag == 0 && (x.StartDate >= result || x.FinalDate >= result)));

            }

            if (Filters.Where(x => x.Key.Equals("rangeend", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var _item = Filters.First(x => x.Key.Equals("rangeend", StringComparison.OrdinalIgnoreCase));

                DateTime result = Convert.ToDateTime(_item.Value);
                var end = result.AddDays(1);

                _query = _query.Where(x => (x.StatusFlag == 1
                && x.SubmissionDate != null
                && x.SubmissionDate.Value < end)
                || (x.StatusFlag == 0 && (x.StartDate < end || x.FinalDate < end)));

            }

            if (Filters.Where(x => x.Key.Equals("lastPerStageService", StringComparison.OrdinalIgnoreCase)).Any())
            {
                //_query = _query
                //    .Where(x => x.ProjectID != null && x.DesignScriptEntityID != null && x.Stage != null)
                //    .GroupBy(x => new
                //    {
                //        x.ProjectID,
                //        x.DesignScriptEntityID,
                //        x.Stage
                //    })
                //    .Select(g => new
                //    {
                //        ProjectID = g.Key.ProjectID,
                //        DesignScriptEntityID = g.Key.DesignScriptEntityID,
                //        Stage = g.Key.Stage,
                //        Package = g.OrderByDescending(p => p.Created).FirstOrDefault()
                //    })
                //    .Select(x => x.Package);

                // Step 1: Extract the IDs of the packages that meet the grouping criteria
                var packageIds = _query
                    .Where(x => x.ProjectID != null && x.DesignScriptEntityID != null && x.Stage != null)
                    .GroupBy(x => new
                    {
                        x.ProjectID,
                        x.DesignScriptEntityID,
                        x.Stage
                    })
                    .Select(g => g.OrderByDescending(p => p.Created).FirstOrDefault().ID) // Extract only the IDs
                    .ToList();

                // Step 2: Filter the main query using the extracted IDs
                _query = _query.Where(x => packageIds.Contains(x.ID));

            }

            if (Filters.Where(x => x.Key.Equals("projectstatusflag", StringComparison.OrdinalIgnoreCase)).Any())
            {

                var predicate = PredicateBuilder.False<Package>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("projectstatusflag", StringComparison.OrdinalIgnoreCase)))
                {
                    var isNumeric = Convert.ToInt32(_item.Value);

                    predicate = predicate.Or(x => x.Project.StatusFlag == isNumeric);
                }
                _query = _query.Include(x => x.Project).Where(predicate);
            }


            if (Filters.Where(x => x.Key.Equals("ProjectPartnerContactID", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<Package>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("ProjectPartnerContactID", StringComparison.OrdinalIgnoreCase)))
                {
                    var isNumeric = Convert.ToInt32(_item.Value);

                    predicate = predicate.Or(x => x.Project.Associations.Where(a => a.TypeFlag == 0 && a.ContactID == isNumeric).Any());
                }
                _query = _query.Include(x => x.Project.Associations).Where(predicate);
            }
            if (Filters.Where(x => x.Key.Equals("ProjectAssociateContactID", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<Package>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("ProjectAssociateContactID", StringComparison.OrdinalIgnoreCase)))
                {
                    var isNumeric = Convert.ToInt32(_item.Value);

                    predicate = predicate.Or(x => x.Project.Associations.Where(a => a.TypeFlag == 1 && a.ContactID == isNumeric).Any());
                }
                _query = _query.Include(x => x.Project.Associations).Where(predicate);
            }
            if (Filters.Where(x => x.Key.Equals("ProjectPartnerOrAssociateContactID", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<Package>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("ProjectPartnerOrAssociateContactID", StringComparison.OrdinalIgnoreCase)))
                {
                    var isNumeric = Convert.ToInt32(_item.Value);

                    predicate = predicate.Or(x => x.Project.Associations.Where(a => (a.TypeFlag == 1 || a.TypeFlag == 0) && a.ContactID == isNumeric).Any());
                }
                _query = _query.Include(x => x.Project.Associations).Where(predicate);
            }

            if (Filters.Where(x => x.Key.Equals("companyID", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<Package>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("companyID", StringComparison.OrdinalIgnoreCase)))
                {
                    var isNumeric = Convert.ToInt32(_item.Value);

                    predicate = predicate.Or(x => x.CompanyID == isNumeric);
                }
                _query = _query.Where(predicate);
            }

        }

        if (Search != null && Search != String.Empty)
        {
            _query = _query.Include(x => x.Project)
                .Include(x => x.Associations).ThenInclude(c => c.Contact)
                  .Where(x => x.Title.ToLower().Contains(Search.ToLower())
                                    || x.Project.Title.ToLower().Contains(Search.ToLower())
                                     || x.Stage.ToLower().Contains(Search.ToLower())
                                     || x.ActiveStage.ToLower().Contains(Search.ToLower())
                                     || x.Associations.Any(a => (a.Contact.FirstName + " " + a.Contact.LastName).ToLower().Contains(Search.ToLower()))
                                     || x._searchTags.ToLower().Contains(Search.ToLower())
                                    );

        }

        if (Sort != null && Sort != String.Empty)
        {
            var _orderedQuery = _query.OrderBy(l => 0);
            var keywords = Sort.Replace("asc", "").Split(',');

            foreach (var key in keywords)
            {
                if (key.Trim().Equals("created", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenBy(x => x.Created);
                else if (key.Trim().Equals("created desc", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenByDescending(x => x.Created);
                else if (key.Trim().Equals("modified", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenBy(x => x.Modified);
                else if (key.Trim().Equals("modified desc", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenByDescending(x => x.Modified);
                else if (key.Trim().Equals("startdate", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenBy(x => x.StartDate);
                else if (key.Trim().Equals("startdate desc", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenByDescending(x => x.StartDate);
                else if (key.Trim().Equals("finaldate", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenBy(x => x.FinalDate);
                else if (key.Trim().Equals("finaldate desc", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenByDescending(x => x.FinalDate);
                else if (key.Trim().Equals("submissionDate", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenBy(x => x.SubmissionDate != null ? x.SubmissionDate : x.FinalDate);
                else if (key.Trim().Equals("submissionDate desc", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenByDescending(x => x.SubmissionDate != null ? x.SubmissionDate : x.FinalDate);
                else if (key.Trim().Equals("code", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenBy(x => x.Code);
            }

            return _orderedQuery;
        }

        return _query.OrderBy(x => x.FinalDate);

    }

    public async Task<Package?> GetById(int Id)
    {

        return await db.Packages.AsNoTracking()
            .Include(x => x.Deliverables).ThenInclude(c => c.PackageDeliverableTaskMaps)
            .Include(x => x.Associations).ThenInclude(c => c.Contact)
            .Include(x => x.Project)
            .Include(x => x.Attachments)
            .Include(x => x.Feedbacks).ThenInclude(x => x.Attachments)
            .SingleOrDefaultAsync(x => x.ID == Id);

    }
    public async Task<Package?> GetById(Guid Id)
    {

        return await db.Packages.AsNoTracking()
            .Include(x => x.Deliverables).ThenInclude(c => c.PackageDeliverableTaskMaps)
            .Include(x => x.Associations).ThenInclude(c => c.Contact)
            .Include(x => x.Project)
            .Include(x => x.Attachments)
            .Include(x => x.Feedbacks).ThenInclude(x => x.Attachments)
            .SingleOrDefaultAsync(x => x.UID == Id);

    }

    public async Task<int> Create(Package Entity)
    {

        var _project = await db.Projects
            .AsNoTracking()
            .SingleOrDefaultAsync(x => x.ID == Entity.ProjectID);

        if (_project == null) throw new EntityServiceException("Project not found!");

        Entity.CompanyID = _project.CompanyID;

        if (Entity.TypeFlag == McvConstant.PACKAGE_TYPEFLAG_ACTIVE)
        {
            //Status filtering
            var _statusMasters = await db.StatusMasters.AsNoTracking().Where(x => x.Entity == nameof(Project)).ToListAsync();
            if (_project == null || (_project.StatusFlag != 1
                && _project.StatusFlag != 2)) throw new EntityServiceException($"Project Should be {(_statusMasters.Any() ? _statusMasters.FirstOrDefault(x => x.Value == McvConstant.PROJECT_STATUSFLAG_PREPOPOSAL).Title : "Pre-Proposal")} or {(_statusMasters.Any() ? _statusMasters.FirstOrDefault(x => x.Value == McvConstant.PROJECT_STATUSFLAG_INPROGRESS).Title : "In-Progress")}"

                    );

            var proposed = await Get()
          .Where(x => x.ProjectID == Entity.ProjectID
                    && x.Stage == Entity.Stage
                    && x.TypeFlag == McvConstant.PACKAGE_TYPEFLAG_PROPOSED
                    && x.DesignScriptEntityID == Entity.DesignScriptEntityID).ToListAsync();

            foreach (var pkg in proposed)
            {
                pkg.IsDeleted = true;
                await base.Update(pkg);
            }

        }
        else
        {
            var query = Get()
           .Where(x => x.ProjectID == Entity.ProjectID
                     && x.Stage == Entity.Stage
                     && x.TypeFlag == Entity.TypeFlag
                     && x.DesignScriptEntityID == Entity.DesignScriptEntityID);

            if (await query.AnyAsync())
            {
                throw new EntityServiceException("Package already exists!");
            }
        }

        Entity = await GenerateCode(Entity, _project);

        if (await Get()
           .Where(x => x.Title == Entity.Title)
            .AnyAsync()) throw new EntityServiceException("Package " + Entity.Title + " already Exists! Cannot create duplicate Package!");

        if (await Get()
           .Where(x => x.StatusFlag == McvConstant.PACKAGE_STATUSFLAG_ACTIVE && x.TypeFlag == McvConstant.PACKAGE_TYPEFLAG_ACTIVE) //active
          .Where(x => x.Stage == Entity.Stage)
          .Where(x => x.ProjectID == Entity.ProjectID && x.DesignScriptEntityID == Entity.DesignScriptEntityID)
          .Where(x => (x.Revision < Entity.Revision) || (x.Revision == Entity.Revision && x.AnnexureIndex <= Entity.AnnexureIndex))
           .AnyAsync()) throw new EntityServiceException("Package with Previous version is already Active! Please complete it before creating New Package!");
        var sharedService = new SharedService(db); ;
        var _workStart = Convert.ToInt32((await sharedService.GetPresetValue(McvConstant.OFFICE_START_MINUTES_UTC)));
        var _workEnd = Convert.ToInt32((await sharedService.GetPresetValue(McvConstant.PACKAGE_END_MINUTES_UTC)));

        if (Entity.StartDate < DateTime.UtcNow) Entity.StartDate = DateTime.UtcNow;

        if (Entity.FinalDate < Entity.StartDate) Entity.FinalDate = Entity.StartDate;

        Entity.StartDate = (Entity.StartDate.Date).AddMinutes(_workStart);
        Entity.FinalDate = (Entity.FinalDate.Date).AddMinutes(_workEnd);
        try
        {
            Entity.VHrRate = Convert.ToDecimal(await sharedService.GetPresetValue(McvConstant.COMPANY_VHR_COST));
        }
        catch
        {
            Entity.VHrRate = 0;
        }

        Entity.VHrAssignedCost = Entity.VHrAssigned * Entity.VHrRate;
        Entity.ProposedVHrAssignedCost = Entity.ProposedVHrAssigned * Entity.VHrRate;


        if (Entity.TypeFlag == McvConstant.PACKAGE_TYPEFLAG_ACTIVE)
        {
            var _query = await Get()
           .Where(x => x.TypeFlag == Entity.TypeFlag && x.StatusFlag == McvConstant.PACKAGE_STATUSFLAG_SENT)
         .Where(x => x.ProjectID == Entity.ProjectID)
         //&& x.Stage == _submission.Stage
         //&& x.DesignScriptEntityID == _submission.DesignScriptEntityID)
         .ToListAsync();

            var _previousSubmissions = _query.Select(x => new
            {
                Revision = x.Revision + (x.AnnexureIndex < 10 ? x.AnnexureIndex / 10.0 : x.AnnexureIndex / 100.0),
                DelayHours = x.SubmissionDate > x.FinalDate ? (x.SubmissionDate.Value - x.FinalDate).TotalHours : 0,
            }).ToList();


            Entity.DValue = Convert.ToDecimal(
                (_previousSubmissions.Sum(x => x.DelayHours)) / (_previousSubmissions.Count + 1));

            var _groups = _query
                 .GroupBy(x => new { x.DesignScriptEntityID, x.Stage })
                .Select(x => new
                {
                    x.Key.DesignScriptEntityID,
                    x.Key.Stage,
                    Revision = x.Max(r => r.Revision)
                });
            Entity.RValue = Convert.ToDecimal(_groups.Sum(x => x.Revision) / (_groups.Count() != 0 ? _groups.Count() : 1));
        }

        await base.Create(Entity);


        return Entity.ID;

    }

    private async Task<List<PackageDesignIntent>> GetDesignIntents(Package Entity)
    {
        var _filterIDs = new List<int>();

        var _entityIDs = new List<int>() { Entity.DesignScriptEntityID.Value };

        _filterIDs = _entityIDs.ToList();
        while (_entityIDs.Count > 0)
        {
            _entityIDs = await GetRecursiveChildrenIDs(_entityIDs);
            _filterIDs = _filterIDs.Concat(_entityIDs).ToList();
        }

        var designIntents = await db.DesignScriptEntities
                                           .AsNoTracking()
                                           
                                   .Where(x => x.ProjectID == Entity.ProjectID)
                               .Where(x => _filterIDs.Any(c => c == x.ID))
                               .GroupJoin(
           db.TypeMasters.Where(t => t.Entity == nameof(DesignScriptEntity)),
           e => e.TypeFlag,
           t => t.Value,
           (e, t) => new { e, TypeValue = t.DefaultIfEmpty() }
                               )
                               .Select(x => new
                               {
                                   ID = x.e.ID,
                                   Code = x.e.Code,
                                   Title = x.e.Title,
                                   Description = x.e.Description,
                                   OrderFlag = x.e.OrderFlag,
                                   TypeFlag = x.e.TypeFlag,
                                   ParentID = x.e.ParentID != null ? x.e.ParentID : 0,
                                   TypeValue = x.TypeValue.Any() ? x.TypeValue.FirstOrDefault().Title : "UNDEFINED",
                               })
                               .ToListAsync();
        return designIntents.Select(x => new PackageDesignIntent
        {
            DesignScriptEntityID = x.ID,
            Code = x.Code,
            Title = x.Title,
            Description = x.Description,
            OrderFlag = x.OrderFlag,
            TypeFlag = x.TypeFlag,
            ParentID = x.ParentID,
            TypeValue = x.TypeValue,
        }).ToList();
    }


    public async Task StartFlow(int packageID)
    {
        var package = await db.Packages.AsNoTracking()
                           
               .SingleOrDefaultAsync(x => x.ID == packageID);

        if (package == null) throw new EntityServiceException("Package not found!");

        if (package.TypeFlag == McvConstant.PACKAGE_TYPEFLAG_ACTIVE)
        {
            package.ActiveStage = await AssignPackageTask(package.ID, 1, true);

            db.Entry(package).State = EntityState.Modified;
            await db.SaveChangesAsync();
        }
    }

    private async Task<Package> GenerateCode(Package Entity, Project _project)
    {
        var _phaseDetail = await db.DesignScriptEntities.AsNoTracking()
               .SingleOrDefaultAsync(x => x.ID == Entity.DesignScriptEntityID);

        if (Entity.TypeFlag == McvConstant.PACKAGE_TYPEFLAG_ACTIVE)
        {

            var query = db.Packages.AsNoTracking()
                .Where(x => x.ProjectID == Entity.ProjectID
                          && x.Stage == Entity.Stage
                          && x.TypeFlag == Entity.TypeFlag
                          && x.DesignScriptEntityID == Entity.DesignScriptEntityID);

            if (await query.AnyAsync())
            {
                var revision = await query
                       .MaxAsync(x => x.Revision);

                var annexureIndex = await query
                     .Where(x => x.Revision == revision)
                       .MaxAsync(x => x.AnnexureIndex);

                if (Entity.IsAnnexure)
                {
                    annexureIndex++;
                    string _code = String.Empty;
                    int mod;

                    var _decimal = annexureIndex;
                    while (_decimal > 0)
                    {

                        mod = (_decimal - 1) % 26;
                        _code = Convert.ToChar(97 + mod).ToString() + _code;
                        _decimal = (int)((_decimal - mod) / 26);
                    }
                    Entity.Revision = revision;
                    Entity.AnnexureIndex = annexureIndex;
                    Entity.Annexure = _code;
                }
                else
                {
                    revision++;
                    Entity.Revision = revision;
                    Entity.AnnexureIndex = 0;
                    Entity.Annexure = string.Empty;
                }

            }
            else
            {
                Entity.Revision = 0;
                Entity.AnnexureIndex = 0;
                Entity.Annexure = string.Empty;
            }


            Entity.Title = $"{_project.Code}-{Entity.Stage}-{_phaseDetail.Code}-R{(Entity.Revision + Entity.Annexure).Trim()}-{_phaseDetail.Title}";

            Entity.Code = $"{Entity.Stage}-{_phaseDetail.Code}-R{(Entity.Revision + Entity.Annexure).Trim()}";

        }
        else
        {
            Entity.Revision = 0;
            Entity.AnnexureIndex = 0;
            Entity.Annexure = string.Empty;

            Entity.Title = $"{_project.Code}-{Entity.Stage}-{_phaseDetail.Code}-PROPOSED-{_phaseDetail.Title}";

            Entity.Code = $"{Entity.Stage}-{_phaseDetail.Code}-PROPOSED";
        }

        return Entity;
    }

    public async Task<string> GetNextRevision(Package Entity)
    {

        var query = db.Packages.AsNoTracking()
          .Where(x => x.ProjectID == Entity.ProjectID
                    && x.Stage == Entity.Stage
                    && x.TypeFlag == Entity.TypeFlag
                    && x.DesignScriptEntityID == Entity.DesignScriptEntityID);

        if (await query.AnyAsync())
        {
            var revision = (await query.OrderByDescending(x => x.Created)
                   .FirstOrDefaultAsync()).Revision;

            var annexureIndex = await query
                 .Where(x => x.Revision == revision)
                   .MaxAsync(x => x.AnnexureIndex);

            if (Entity.IsAnnexure)
            {
                annexureIndex++;
                string _code = String.Empty;
                int mod;

                var _decimal = annexureIndex;
                while (_decimal > 0)
                {

                    mod = (_decimal - 1) % 26;
                    _code = Convert.ToChar(97 + mod).ToString() + _code;
                    _decimal = (int)((_decimal - mod) / 26);
                }
                Entity.Revision = revision;
                Entity.AnnexureIndex = annexureIndex;
                Entity.Annexure = _code;
            }
            else
            {
                revision++;
                Entity.Revision = revision;
                Entity.AnnexureIndex = 0;
                Entity.Annexure = string.Empty;
            }

        }
        else
        {
            Entity.Revision = 0;
            Entity.AnnexureIndex = 0;
            Entity.Annexure = string.Empty;

        }

        return ($"R{Entity.Revision}{Entity.Annexure}").Trim();

    }

    public async Task Update(Package UpdatedEntity)
    {

        var originalEntity = await db.Packages.AsNoTracking()
                        
                        .Include(x => x.DesignIntents)
            .SingleOrDefaultAsync(x => x.ID == UpdatedEntity.ID);

        if (originalEntity == null) throw new EntityServiceException("Package not found!");

        //var sharedService = new SharedService(db);
        //var isEvenSaturdayOff = await sharedService.IsEvenSaturdayOff();
        //var isOddSaturdayOff = await sharedService.IsOddSaturdayOff();
        //var holidays = await sharedService.GetHolidays();
        //UpdatedEntity.StartDate = ClockTools.GetNextValidWorkingDate((DateTime)UpdatedEntity.StartDate,
        //        isEvenSaturdayOff,
        //        isOddSaturdayOff,
        //        holidays);

        //UpdatedEntity.StartDate = ClockTools.GetUTC(ClockTools.GetIST((DateTime)UpdatedEntity.StartDate).Date
        //       .AddMinutes(await sharedService.GetBusinessStartMinutesIST()));

        //UpdatedEntity.FinalDate = ClockTools.GetNextValidWorkingDate((DateTime)UpdatedEntity.FinalDate,
        //        isEvenSaturdayOff,
        //        isOddSaturdayOff,
        //        holidays);

        //UpdatedEntity.FinalDate = UpdatedEntity.FinalDate.Date
        //      .AddMinutes(Convert.ToInt32(await sharedService.GetPresetValue(McvConstant.PACKAGE_END_MINUTES_UTC)));

        UpdatedEntity.VHrAssignedCost = UpdatedEntity.VHrAssigned * UpdatedEntity.VHrRate;
        UpdatedEntity.ProposedVHrAssignedCost = UpdatedEntity.ProposedVHrAssigned * UpdatedEntity.VHrRate;

        if (originalEntity.TypeFlag != UpdatedEntity.TypeFlag && UpdatedEntity.TypeFlag == McvConstant.PACKAGE_TYPEFLAG_ACTIVE) // ON TypeFlag change
        {
            var _project = await db.Projects
           .AsNoTracking()
           //.Include(x=>x.Associates)
           .SingleOrDefaultAsync(x => x.ID == UpdatedEntity.ProjectID);
            //Status filtering
            var _statusMasters = await db.StatusMasters.AsNoTracking().Where(x => x.Entity == nameof(Project)).ToListAsync();
            if (_project == null || (_project.StatusFlag != 1
                && _project.StatusFlag != 2)) throw new EntityServiceException($"Project Should be {(_statusMasters.Any() ? _statusMasters.FirstOrDefault(x => x.Value == McvConstant.PROJECT_STATUSFLAG_PREPOPOSAL).Title : "Pre-Proposal")} or {(_statusMasters.Any() ? _statusMasters.FirstOrDefault(x => x.Value == McvConstant.PROJECT_STATUSFLAG_INPROGRESS).Title : "In-Progress")}"

                    );
            UpdatedEntity = await GenerateCode(UpdatedEntity, _project);

            var _query = await db.Packages.AsNoTracking()
         
         .Where(x => x.TypeFlag == UpdatedEntity.TypeFlag && x.StatusFlag == McvConstant.PACKAGE_STATUSFLAG_SENT)
       .Where(x => x.ProjectID == UpdatedEntity.ProjectID)
       //&& x.Stage == _submission.Stage
       //&& x.DesignScriptEntityID == _submission.DesignScriptEntityID)
       .ToListAsync();

            var _previousSubmissions = _query.Select(x => new
            {
                Revision = x.Revision + (x.AnnexureIndex < 10 ? x.AnnexureIndex / 10.0 : x.AnnexureIndex / 100.0),
                DelayHours = x.SubmissionDate > x.FinalDate ? (x.SubmissionDate.Value - x.FinalDate).TotalHours : 0,
            }).ToList();


            UpdatedEntity.DValue = Convert.ToDecimal(
                (_previousSubmissions.Sum(x => x.DelayHours)) / (_previousSubmissions.Count + 1));

            var _groups = _query
                 .GroupBy(x => new { x.DesignScriptEntityID, x.Stage })
                .Select(x => new
                {
                    x.Key.DesignScriptEntityID,
                    x.Key.Stage,
                    Revision = x.Max(r => r.Revision)
                });
            UpdatedEntity.RValue = Convert.ToDecimal(_groups.Sum(x => x.Revision) / (_groups.Count() != 0 ? _groups.Count() : 1));
        }



        db.Entry(UpdatedEntity).State = EntityState.Modified;
        await db.SaveChangesAsync();

        if (originalEntity.TypeFlag != UpdatedEntity.TypeFlag && UpdatedEntity.TypeFlag == McvConstant.PACKAGE_TYPEFLAG_ACTIVE)
        {
            UpdatedEntity.ActiveStage = await AssignPackageTask(UpdatedEntity.ID, 1, true);
        }
        else
        {
            await UpdatePackageTaskDue(UpdatedEntity.ID, UpdatedEntity.StartDate, UpdatedEntity.FinalDate);
        }



    }


   
    public async Task<decimal> GetDuration(int ID)
    {

        var _package = await Get()
                        .SingleOrDefaultAsync(x => x.ID == ID);

        if (_package == null) return 0;

        return Convert.ToDecimal(ClockTools.GetDifference(_package.StartDate, _package.FinalDate).TotalHours);

    }

    public async Task<string> GetSubmissionEmailPreview(int Id)
    {

        var _tos = new List<string>();
        var _ccs = new List<string>();

        var _submission = await Get()
            .Include(x => x.Project)
            .Include(x => x.Associations).ThenInclude(c => c.Contact)
            .SingleOrDefaultAsync(x => x.ID == Id);

        if (_submission == null) throw new EntityServiceException("Package not found!");

        Regex myRegex = new Regex(McvConstant.EMAIL_REGEX, RegexOptions.None);

        if (_submission.EmailID != null)
        {
            foreach (Match myMatch in myRegex.Matches(_submission.EmailID))
            {
                if (myMatch.Success && !_tos.Any(x => x.ToLower() == myMatch.Value.Trim().ToLower()))
                {
                    _tos.Add(myMatch.Value.Trim());
                }
            }
        }

        if (_submission.CC != null)
        {
            foreach (Match myMatch in myRegex.Matches(_submission.CC))
            {
                if (myMatch.Success && !_ccs.Any(x => x.ToLower() == myMatch.Value.Trim().ToLower()))
                {
                    _ccs.Add(myMatch.Value.Trim());
                }
            }
        }
        var sharedService = new SharedService(db); ;
        //CC TO STUDIO
        var _defaultCCList = await sharedService.GetPresetValue(McvConstant.PACKAGE_SUBMISSION_EMAIL_CC);
        if (_defaultCCList != null)
        {
            foreach (Match myMatch in myRegex.Matches(_defaultCCList))
            {
                if (myMatch.Success && !_ccs.Any(x => x.ToLower() == myMatch.Value.Trim().ToLower()))
                {
                    _ccs.Add(myMatch.Value.Trim());
                }
            }
        }

        var _firstContact = "";
        var _secondContact = "";
        var _firstContactLeaves = "";
        var _secondContactLeaves = "";

        if (_submission.Associations.Where(x => x.TypeFlag == 0).Any())
        {
            var _firstAssociate = _submission.Associations.Where(x => x.TypeFlag == 0).FirstOrDefault().Contact;

            if (_firstAssociate.Email1 != null
                && _firstAssociate.Email1 != string.Empty)
            {
                _firstContact = _firstAssociate.Email1.Trim() + " (" + _firstAssociate.FullName + ")";

                var _leaves = await db.Leaves.AsNoTracking()
                       .Where(x => x.ContactID == _firstAssociate.ID)
                       .Where(x => x.StatusFlag == McvConstant.LEAVE_STATUSFLAG_APPROVED)
                       .Where(x => x.TypeFlag == McvConstant.LEAVE_TYPEFLAG_APPROVED)
                       .Where(x => x.End > DateTime.UtcNow)
                       .OrderBy(x => x.Start)
                       .ToListAsync();

                if (_leaves.Any())
                {
                    _firstContactLeaves += $"On leave(";



                    foreach (var leave in _leaves.OrderBy(x => x.Start))
                    {
                        _firstContactLeaves += $"{(leave.Start > DateTime.UtcNow ? ClockTools.GetIST(leave.Start).ToString("dd MMM yyyy") : ClockTools.GetISTNow().ToString("dd MMM yyyy"))}-{ClockTools.GetIST(leave.End).ToString("dd MMM yyyy")} | ";
                    }

                    _firstContactLeaves += $")";
                }
                if (!_ccs.Any(x => x.Equals(_firstAssociate.Email1.Trim(), StringComparison.OrdinalIgnoreCase)))
                    _ccs.Add(_firstAssociate.Email1.Trim());
            }
        }

        if (_submission.Associations.Where(x => x.TypeFlag == 1).Any())
        {
            var _firstPartner = _submission.Associations.Where(x => x.TypeFlag == 1).FirstOrDefault().Contact;

            if (_firstPartner.Email1 != null
                && _firstPartner.Email1 != string.Empty)
            {
                _secondContact = _firstPartner.Email1.Trim() + " (" + _firstPartner.FullName + ")";

                var _leaves = await db.Leaves.AsNoTracking()
                       .Where(x => x.ContactID == _firstPartner.ID)
                       .Where(x => x.StatusFlag == McvConstant.LEAVE_STATUSFLAG_APPROVED)
                       .Where(x => x.TypeFlag == McvConstant.LEAVE_TYPEFLAG_APPROVED)
                       .Where(x => x.End > DateTime.UtcNow)
                       .OrderBy(x => x.Start)
                       .ToListAsync();

                if (_leaves.Any())
                {
                    _secondContactLeaves += $"On leave(";



                    foreach (var leave in _leaves.OrderBy(x => x.Start))
                    {
                        _secondContactLeaves += $"{(leave.Start > DateTime.UtcNow ? ClockTools.GetIST(leave.Start).ToString("dd MMM yyyy") : ClockTools.GetISTNow().ToString("dd MMM yyyy"))}-{ClockTools.GetIST(leave.End).ToString("dd MMM yyyy")} | ";
                    }

                    _secondContactLeaves += $")";
                }

                if (!_ccs.Any(x => x.Equals(_firstPartner.Email1.Trim(), StringComparison.OrdinalIgnoreCase)))
                    _ccs.Add(_firstPartner.Email1.Trim());
            }
        }

        var _sheets = await db.PackageAttachments.AsNoTracking()
            .Where(x => x.PackageID == Id)
            .Where(x => x.Filename.ToLower().Contains(".pdf"))
            .OrderBy(x => x.OrderFlag)
            .ToListAsync();

        var _code = _submission.Project.Code.ToString();

        var _meetingAgendas = await db.MeetingAgendas.AsNoTracking()
            .Include(x => x.Meeting)
            .Where(x => !x.IsVersion)
            .Where(x => x.PackageID != null
            && x.PackageID == _submission.ID).ToListAsync();

        var _supportFiles = await db.PackageAttachments.AsNoTracking()
                                  .Where(x => x.PackageID == Id)
                                  .Where(x => x.TypeFlag == 0)
                                       .Where(x => !x.Filename.ToLower().Contains(".pdf") && !x.Filename.ToLower().Contains(".mp4"))
                                  .OrderBy(x => x.OrderFlag)
                                  .ToListAsync();

        var packageSubmissionUrl = await sharedService.GetPresetValue(McvConstant.PACKAGE_SUBMISSION_URL_ROOT);
        var _submissionContent = GetSubmissionMailBody(
              ("SUBMISSION | " + _submission.Project.Title + " | " + _submission.Title).ToUpper(),
                _submission.Project.Title,
                _code,
                _firstContact,
                _firstContactLeaves,
                _secondContact,
                _secondContactLeaves,
                _tos,
                _ccs,
            ClockTools.GetIST(DateTime.UtcNow).ToString("dd MMM yyyy"),
            _submission.Title,
            _submission.SubmissionMessage,
           packageSubmissionUrl + _submission.UID.ToString(),

            _sheets,
            _supportFiles,
            _meetingAgendas
            );

        return _submissionContent;

    }



    public async Task UploadSubmissionsToCloud()
    {
        var limitDate = new DateTime(2023, 1, 1);
        var _submissions = await db.Packages.AsNoTracking()
            .Where(x => x.TypeFlag == McvConstant.PACKAGE_TYPEFLAG_ACTIVE)
            .Where(x => x.StatusFlag == McvConstant.PACKAGE_STATUSFLAG_SENT)
            .Where(x => !x.IsSubmissionSetProcessed)
            //.Where(x=>x.SubmissionDate > limitDate)
            .OrderByDescending(x => x.SubmissionDate)
            .ToListAsync();

        var count = 0;
        var _failed = 0;
        var _uploaded = 0;
        var _exist = 0;
        foreach (var _obj in _submissions)
        {
            Console.WriteLine("Package :" + _obj.Title);
            try
            {

                var blobUrl = await GetSubmissionSet(_obj.ID);
                if (blobUrl != null)
                {
                    _obj.CloudFile = blobUrl;
                    _obj.IsSubmissionSetProcessed = true;

                    Console.WriteLine("Uploaded :" + _obj.CloudFile);
                    _uploaded++;

                }
                else
                {
                    _obj.IsSubmissionSetProcessed = true;
                    Console.WriteLine("Submission set not generated");
                    _failed++;
                }

                db.Entry(_obj).State = EntityState.Modified;
                await db.SaveChangesAsync();

                count++;
            }
            catch (Exception e)
            {
            }
            Console.WriteLine(_exist + " of " + _submissions.Count + " exist");
            Console.WriteLine(_uploaded + " of " + _submissions.Count + " uploaded");
            Console.WriteLine(_failed + " of " + _submissions.Count + " failed");
            Console.WriteLine(count + " of " + _submissions.Count + " processed");
            Console.WriteLine("========================");
        }

    }

    public async Task<decimal> GetPackageStageServicePercentage(int scopeID, int phaseID, string stage)
    {

        var stageService = await db.ProjectScopeServices.AsNoTracking()
          .Include(x => x.ProjectScope)
          .Select(x => new
          {
              x.ProjectID,
              x.ProjectScopeID,
              x.Abbreviation,
              ScopePercentage = x.ProjectScope.SharePercentage,
              ServicePercentage = x.SharePercentage
          }).SingleOrDefaultAsync(x => x.ProjectScopeID == scopeID && x.Abbreviation == stage);

        var phase = await db.DesignScriptEntities.AsNoTracking()
            .Select(x => new
            {
                x.ID,
                x.SharePercentage
            }).SingleOrDefaultAsync(x => x.ID == phaseID);

        var scopePercentage = stageService != null ? stageService.ScopePercentage : 0;
        var phasePercentage = phase != null ? phase.SharePercentage : 0;
        var servicePercentage = stageService != null ? stageService.ServicePercentage : 0;

        return scopePercentage * (phasePercentage / 100m) * (servicePercentage / 100m);

    }

    #region Reports

    public async Task<ReportDefinition> GetSubmissionDetailIndex(int PackageID, DataTable DataTable)
    {

        var _package = await Get()
            .Include(x => x.Project)
         .SingleOrDefaultAsync(x => x.ID == PackageID);

        if (_package == null) throw new EntityServiceException("Package not found");

        TextInfo textInfo = new CultureInfo("en-IN", false).TextInfo;
        var _projectName = _package.Project.Code + "-" + textInfo.ToTitleCase(_package.Project.Title.ToLower());
        var _reportProperties = new List<ReportProperties>();
        _reportProperties.Add(new ReportProperties() { PropertyName = "Project", PropertyValue = _projectName });
        _reportProperties.Add(new ReportProperties() { PropertyName = "Submission", PropertyValue = _package.Title });

        var _reportDef = new ReportDefinition()
        {
            ReportName = "SubmissionDirectory",
            ReportPath = @"https://nhub.blob.core.windows.net/rdlc/SubmissionElementIndex.rdlc",
            ReportDataSet = DataTable,
            ReportProperties = _reportProperties,
        };
        var sharedService = new SharedService(db);

        var reportServiceApi = await sharedService.GetPresetValue(McvConstant.RDLC_PROCESSOR_API);
        return await ReportClient.GenerateReport(_reportDef, reportServiceApi);

    }

    #endregion Reports


    public async Task<IEnumerable<PackageAnalysis>> GetAnalysisData(IEnumerable<QueryFilter>? Filters = null, string? Search = null, string? Sort = null)
   {

        var _packages =await Get(Filters)
           .Include(x => x.Associations).ThenInclude(c => c.Contact)
           .Include(x => x.Project)
           .Where(x => x.DesignScriptEntityID != null)
           .Select(x=> new
           {
               x.ID,
               x.ProjectID,
               x.DesignScriptEntityID,
               x.Title,
               x.Code,
               x.TypeFlag,
               x.StatusFlag,
               x.Revision,
               x.AnnexureIndex,
               x.Annexure,
               x.StartDate,
               x.FinalDate,
               x.SubmissionDate,
               x.Modified,
               x.Stage,
               x.ProjectTitle,
               x.PhaseTitle,
               x.VHrRate,
               x.VHrAssigned,
               x.VHrAssignedCost,
               x.ProposedVHrAssigned,
               x.ProposedVHrAssignedCost,
               x.VHrConsumed,
               x.VHrConsumedCost,
               x.Associations,
               x.ActiveStage,
               x.ProposedFinalDate,
               x.ProposedStartDate,
               x.ProposedProbablity,
               x.ProposedPriority,
               x.DValue,
               x.RValue
           })
           .ToListAsync();

        var _filteredPackageIDs = _packages.Select(x => x.ID).ToList();

        var _filteredProjectIDs = _packages.Select(x => x.ProjectID).Distinct().ToList();

        var _filteredDSEntityIDs = _packages.Select(x => x.DesignScriptEntityID).Distinct().ToList();

        var packageTypeMasters =await db.TypeMasters.AsNoTracking()
            .Where(s => s.Entity != null && s.Entity == nameof(Package))
            .Select(x=> new
            {
                x.Title,
                x.Value
            })
            .ToListAsync();

        var packageStatusMasters =await db.StatusMasters.AsNoTracking()
             .Where(s => s.Entity != null && s.Entity == nameof(Package))
             .Select(x => new
             {
                 x.Title,
                 x.Value
             })
            .ToListAsync();

        var projectStatusMasters = await db.StatusMasters.AsNoTracking()
            .Where(s => s.Entity != null && s.Entity == nameof(Project))
            .Select(x => new
            {
                x.Title,
                x.Value
            })
            .ToListAsync();

        var designScriptEnitities =await db.DesignScriptEntities.AsNoTracking()
            .Where(x => _filteredDSEntityIDs.Any(c => c == x.ID))
            .Select(x => new
            {
                x.ID,
                x.Title,
                x.Code,
                x.TypeFlag,
                x.ParentID,
                x.SharePercentage,
                x.ProjectScopeID
            })
            .ToListAsync();

        var projects = await db.Projects.AsNoTracking()
            .Where(x => _filteredProjectIDs.Any(c => c == x.ID))
            .Select(x => new
            {
                x.ID,
                x.Title,
                x.Code,
                x.TypeFlag,
                x.StatusFlag,
                x.CompanyFee,
            })
            .ToListAsync();

        var projectScopes =await db.ProjectScopes.AsNoTracking()
            .Include(x => x.Services)
            .Where(x=> _filteredProjectIDs.Contains(x.ProjectID))
            .Select(x => new
            {
                x.ID,
                x.Title,
                x.TypeFlag,
                x.ProjectID,
                x.SharePercentage,
                x.Services
            })
            .ToListAsync();

        var tasks =await db.WFTasks.AsNoTracking()
            .Where(s => s.Entity != null && s.Entity == nameof(Package) && s.StatusFlag==McvConstant.WFTASK_STATUSFLAG_COMPLETED)
            .Where(x=> _filteredPackageIDs.Contains(x.EntityID.Value))
            .Select(x=> new
            {
                x.ID,
                x.EntityID,
                x.Title,
                x.StatusFlag,
                x.ContactID,
                x.VHrAssigned,
                x.VHrAssignedCost,
                x.IsAssessmentRequired,
                x.OutcomeFlag
            })
            .ToListAsync();

       

        var groupedData = _packages
            .GroupJoin(projects,
                    a=> a.ProjectID,
                    b => b.ID,
                    (a, b) => new
                    {
                        package = a,
                        projects = b.DefaultIfEmpty()
                    }).SelectMany(join => join.projects.DefaultIfEmpty(),
                                    (t1, t2) => new
                                    {
                                        t1.package.ID,
                                        t1.package.ProjectID,
                                        t1.package.DesignScriptEntityID,
                                        t1.package.Title,
                                        t1.package.Code,
                                        t1.package.TypeFlag,
                                        t1.package.StatusFlag,
                                        t1.package.Revision,
                                        t1.package.AnnexureIndex,
                                        t1.package.Annexure,
                                        t1.package.StartDate,
                                        t1.package.FinalDate,
                                        t1.package.SubmissionDate,
                                        t1.package.Stage,
                                        t1.package.ProjectTitle,
                                        t1.package.PhaseTitle,
                                        t1.package.VHrRate,
                                        t1.package.VHrAssigned,
                                        t1.package.VHrAssignedCost,
                                        t1.package.ProposedVHrAssigned,
                                        t1.package.ProposedVHrAssignedCost,
                                        t1.package.VHrConsumed,
                                        t1.package.VHrConsumedCost,
                                        t1.package.Associations,
                                        t1.package.Modified,
                                        t1.package.ActiveStage,
                                        t1.package.ProposedFinalDate,
                                        t1.package.ProposedStartDate,
                                        t1.package.ProposedProbablity,
                                        t1.package.ProposedPriority,
                                        t1.package.DValue,
                                        t1.package.RValue,
                                        Project = t2
                                    }
            )
            .GroupJoin(packageTypeMasters,
                        a => a.TypeFlag,
                        b => b.Value,
                        (a, b) => new
                        {
                            package = a,
                            types = b.DefaultIfEmpty()
                        }).SelectMany(join => join.types.DefaultIfEmpty(),
                                        (t1, t2) => new
                                        {
                                            package = t1.package,
                                            type = t2.Title
                                        })
            .GroupJoin(packageStatusMasters,
                        a => a.package.StatusFlag,
                        b => b.Value,
                        (a, b) => new
                        {
                            package = a.package,
                            type = a.type,
                            statuses = b.DefaultIfEmpty()
                        }).SelectMany(join => join.statuses.DefaultIfEmpty(),
                                        (t1, t2) => new
                                        {
                                            package = t1.package,
                                            type = t1.type,
                                            status = t2.Title
                                        })
             .GroupJoin(projectStatusMasters,
                        a => a.package.Project.StatusFlag,
                        b => b.Value,
                        (a, b) => new
                        {
                            package = a.package,
                            type = a.type,
                            status = a.status,
                            projectStatuses = b.DefaultIfEmpty()
                        }).SelectMany(join => join.projectStatuses.DefaultIfEmpty(),
                                        (t1, t2) => new
                                        {
                                            package = t1.package,
                                            type = t1.type,
                                            status = t1.status,
                                            projectStatus = t2.Title
                                        })

            //PHASES
            .GroupJoin(designScriptEnitities,
                     a => a.package.DesignScriptEntityID,
                     b => b.ID,
                    (a, b) => new
                    {
                        package = a.package,
                        type = a.type,
                        status = a.status,
                        projectStatus = a.projectStatus,
                        phases = b.DefaultIfEmpty()
                    }).SelectMany(join => join.phases.DefaultIfEmpty(),
                                    (t1, t2) => new
                                    {
                                        package = t1.package,
                                        type = t1.type,
                                        status = t1.status,
                                        projectStatus = t1.projectStatus,
                                        phase = t2
                                    })

          //Scopes
          .GroupJoin(projectScopes,
                    x => x.phase.ProjectScopeID,
                    y => y.ID,
                    (a, b) => new
                    {
                        package = a.package,
                        type = a.type,
                        status = a.status,
                        projectStatus = a.projectStatus,
                        a.phase,
                        scopes = b.DefaultIfEmpty()
                    })

                .SelectMany(join => join.scopes.DefaultIfEmpty(), (t1, t2) => new
                {
                    package = t1.package,
                    type = t1.type,
                    status = t1.status,
                    projectStatus = t1.projectStatus,
                    phase = t1.phase,
                    scope = t2
                })

          //Tasks
          .GroupJoin(tasks,
                    x => x.package.ID,
                    y => y.EntityID,
                    (a, b) => new
                    {
                        package = a.package,
                        type = a.type,
                        status = a.status,
                        projectStatus = a.projectStatus,
                        a.phase,
                        a.scope,
                        tasks = b.DefaultIfEmpty()
                    })
                  .Select(x => new
                  {
                      x.package.ProjectID,
                      x.package.ProjectTitle,
                      PackageID = x.package.ID,
                      Package = x.package.Title,
                      x.package.Stage,
                      x.package.PhaseTitle,
                      Scope = x.scope != null ? x.scope.Title : "NA",
                      x.package.StartDate,
                      x.package.FinalDate,
                      SubmissionDate = x.package.SubmissionDate != null ? x.package.SubmissionDate.Value : x.package.FinalDate,
                      x.package.VHrRate,
                      Delay = x.package.StatusFlag == 0 ?
                                    (x.package.FinalDate- DateTime.UtcNow).TotalMinutes / 60.0 :
                                    (x.package.SubmissionDate != null ?
                                   (x.package.FinalDate- x.package.SubmissionDate.Value).TotalMinutes / 60.0 :
                                   (x.package.FinalDate- x.package.Modified).TotalMinutes / 60.0)
                                    ,
                      Partner = x.package.Associations.Where(a => a.TypeFlag == 0).Any() ?
                                x.package.Associations.Where(a => a.TypeFlag == 0).FirstOrDefault().Contact.FullName : "N/A",
                      PartnerContactID = x.package.Associations.Where(a => a.TypeFlag == 0).Any() ?
                                x.package.Associations.Where(a => a.TypeFlag == 0).FirstOrDefault().ContactID : (int?)null,
                      Associate = x.package.Associations.Where(a => a.TypeFlag == 1).Any() ?
                                x.package.Associations.Where(a => a.TypeFlag == 1).FirstOrDefault().Contact.FullName : "N/A",
                      AssociateContactID = x.package.Associations.Where(a => a.TypeFlag == 1).Any() ?
                                x.package.Associations.Where(a => a.TypeFlag == 1).FirstOrDefault().ContactID : (int?)null,
                      x.package.Revision,
                      x.package.Annexure,
                      x.package.VHrAssigned,
                      VHrAssignedCost = x.package.VHrAssigned * x.package.VHrRate,
                      x.package.VHrConsumed,
                      x.package.VHrConsumedCost,
                      x.package.StatusFlag,
                      Status = x.status,
                      ActiveStage = x.package.StatusFlag == 0 ? x.package.ActiveStage : "",
                      TypeValue = x.type,
                      x.package.TypeFlag,


                      x.package.ProposedFinalDate,
                      x.package.ProposedPriority,
                      x.package.ProposedProbablity,
                      x.package.ProposedStartDate,
                      x.package.ProposedVHrAssigned,
                      x.package.ProposedVHrAssignedCost,
                      ProjectFee = x.package.Project.CompanyFee,
                      ProjectStatusFlag = x.package.Project.StatusFlag,
                      ProjectStatus = x.projectStatus,

                      PhasePercentage = x.phase != null ? x.phase.SharePercentage : 0,

                      ScopePercentage = x.scope != null ? x.scope.SharePercentage : 0,

                      ServicePercentage = x.scope != null && x.scope.Services.Any(s => s.Abbreviation == x.package.Stage) ? x.scope.Services.FirstOrDefault(s => s.Abbreviation == x.package.Stage).SharePercentage : 0,

                      x.package.DValue,
                      x.package.RValue,

                      tasks = x.tasks.Where(t => t != null)
                  });

        if (Search != null && Search != String.Empty)
        {
            Search = Search.ToLower();


            groupedData = groupedData
                  .Where(x => x.ProjectTitle.ToLower().Contains(Search.ToLower())
                                    || x.Package.ToLower().Contains(Search.ToLower())
                                     || x.Associate.ToLower().Contains(Search.ToLower())
                                      || x.Partner.ToLower().Contains(Search.ToLower())
                                    ).OrderByDescending(x => x.ProjectTitle);

        }


        var _result = groupedData.Select(x => new PackageAnalysis
        {
            ProjectID = x.ProjectID,
            Project = x.ProjectTitle,
            ProjectStatus = x.ProjectStatus,
            ProjectStatusFlag = x.ProjectStatusFlag,
            PackageID = x.PackageID,
            Package = x.Package,
            Phase = x.PhaseTitle,
            Stage = x.Stage,
            Scope = x.Scope,

            StartDate = x.StartDate,
            FinalDate = x.FinalDate,
            SubmissionDate = x.SubmissionDate,
            Delay = Convert.ToDecimal(x.Delay),
            Partner = x.Partner,
            PartnerContactID = x.PartnerContactID,
            Associate = x.Associate,
            AssociateContactID = x.AssociateContactID,
            Revision = ($"R{x.Revision}{x.Annexure}").Trim(),
            VHrRate = x.VHrRate,

            VHrAssigned = x.VHrAssigned,
            VHrAssignedCost = x.VHrAssignedCost,
            VHrConsumed = x.StatusFlag == McvConstant.PACKAGE_STATUSFLAG_ACTIVE ?

            x.tasks.Where(t => t.ContactID != x.PartnerContactID && t.ContactID != x.AssociateContactID).Any() ? x.tasks.Where(t => t.ContactID != x.PartnerContactID && t.ContactID != x.AssociateContactID).Sum(t => t.VHrAssigned) : 0

            : x.VHrConsumed,
            VHrConsumedCost = x.StatusFlag == McvConstant.PACKAGE_STATUSFLAG_ACTIVE ?

             x.tasks.Where(t => t.ContactID != x.PartnerContactID && t.ContactID != x.AssociateContactID).Any() ? x.tasks.Where(t => t.ContactID != x.PartnerContactID && t.ContactID != x.AssociateContactID).Sum(t => t.VHrAssignedCost) : 0

            : x.VHrConsumedCost,

            VHrBalance = x.VHrAssigned - (x.StatusFlag == McvConstant.PACKAGE_STATUSFLAG_ACTIVE ?

            x.tasks.Where(t => t.ContactID != x.PartnerContactID && t.ContactID != x.AssociateContactID).Any() ? x.tasks.Where(t => t.ContactID != x.PartnerContactID && t.ContactID != x.AssociateContactID).Sum(t => t.VHrAssigned) : 0

            : x.VHrConsumed),

            VHrBalanceCost = x.VHrAssignedCost - (x.StatusFlag == McvConstant.PACKAGE_STATUSFLAG_ACTIVE ?

            x.tasks.Where(t => t.ContactID != x.PartnerContactID && t.ContactID != x.AssociateContactID).Any() ? x.tasks.Where(t => t.ContactID != x.PartnerContactID && t.ContactID != x.AssociateContactID).Sum(t => t.VHrAssignedCost) : 0

            : x.VHrConsumedCost),

            AssignedTaskCount = x.tasks.Where(t => t.ContactID != x.PartnerContactID && t.ContactID != x.AssociateContactID).Where(t => t.StatusFlag != McvConstant.WFTASK_STATUSFLAG_COMPLETED).Any() ? x.tasks.Where(t => t.ContactID != x.PartnerContactID && t.ContactID != x.AssociateContactID).Count() : 0,

            AssignedTaskVHr = x.tasks.Where(t => t.ContactID != x.PartnerContactID && t.ContactID != x.AssociateContactID).Where(t => t.StatusFlag != McvConstant.WFTASK_STATUSFLAG_COMPLETED).Any() ? x.tasks.Where(t => t.ContactID != x.PartnerContactID && t.ContactID != x.AssociateContactID).Sum(t => t.VHrAssigned) : 0,

            CompletedTaskCount = x.tasks.Where(t => t.ContactID != x.PartnerContactID && t.ContactID != x.AssociateContactID).Where(t => t.StatusFlag == McvConstant.WFTASK_STATUSFLAG_COMPLETED).Any() ? x.tasks.Where(t => t.ContactID != x.PartnerContactID && t.ContactID != x.AssociateContactID).Where(t => t.StatusFlag == McvConstant.WFTASK_STATUSFLAG_COMPLETED).Count() : 0,

            CompletedTaskVHr = x.tasks.Where(t => t.ContactID != x.PartnerContactID && t.ContactID != x.AssociateContactID).Where(t => t.StatusFlag == McvConstant.WFTASK_STATUSFLAG_COMPLETED).Any() ? x.tasks.Where(t => t.ContactID != x.PartnerContactID && t.ContactID != x.AssociateContactID).Where(t => t.StatusFlag == McvConstant.WFTASK_STATUSFLAG_COMPLETED).Sum(t => t.VHrAssigned) : 0,

            AssessedTaskCount = x.tasks.Where(t => t.ContactID != x.PartnerContactID && t.ContactID != x.AssociateContactID).Where(t => t.StatusFlag == McvConstant.WFTASK_STATUSFLAG_COMPLETED).Where(t => t.OutcomeFlag == 1 && t.IsAssessmentRequired).Any() ? x.tasks.Where(t => t.ContactID != x.PartnerContactID && t.ContactID != x.AssociateContactID).Where(t => t.StatusFlag == McvConstant.WFTASK_STATUSFLAG_COMPLETED).Where(t => t.OutcomeFlag == 1 && t.IsAssessmentRequired).Count() : 0,

            AssessedTaskVHr = x.tasks.Where(t => t.ContactID != x.PartnerContactID && t.ContactID != x.AssociateContactID).Where(t => t.StatusFlag == McvConstant.WFTASK_STATUSFLAG_COMPLETED).Where(t => t.OutcomeFlag == 1 && t.IsAssessmentRequired).Any() ? x.tasks.Where(t => t.ContactID != x.PartnerContactID && t.ContactID != x.AssociateContactID).Where(t => t.StatusFlag == McvConstant.WFTASK_STATUSFLAG_COMPLETED).Where(t => t.OutcomeFlag == 1 && t.IsAssessmentRequired).Sum(t => t.VHrAssigned) : 0,



            StatusFlag = x.StatusFlag,
            Status = x.Status,
            TypeFlag = x.TypeFlag,
            TypeValue = x.TypeValue,
            ActiveStage = x.ActiveStage,
            ProposedFinalDate = x.ProposedFinalDate,
            ProposedPriority = x.ProposedPriority,
            ProposedProbablity = x.ProposedProbablity,
            ProposedStartDate = x.ProposedStartDate,
            ProposedVHrAssigned = x.ProposedVHrAssigned,
            ProposedVHrAssignedCost = x.ProposedVHrAssignedCost,
            StageServicePercentage = (x.ScopePercentage / 100m) * (x.PhasePercentage / 100m) * (x.ServicePercentage),
            StageServiceAmount = x.ProjectFee * ((x.ScopePercentage / 100m) * (x.PhasePercentage / 100m) * (x.ServicePercentage)) / 100m,

            DValue = x.DValue,
            RValue = x.RValue

        });

        if (Sort != null && Sort != String.Empty)
        {
            var _orderedQuery = _result.OrderBy(l => 0);
            var keywords = Sort.Replace("asc", "").Split(',');

            foreach (var key in keywords)
            {
                if (key.Trim().Equals("project", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenBy(x => x.Project);
                else if (key.Trim().Equals("project desc", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenByDescending(x => x.Project);
                else if (key.Trim().Equals("partner", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenBy(x => x.Partner);
                else if (key.Trim().Equals("partner desc", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenByDescending(x => x.Partner);
                else if (key.Trim().Equals("associate", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenBy(x => x.Associate);
                else if (key.Trim().Equals("associate desc", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenByDescending(x => x.Associate);
                else if (key.Trim().Equals("package", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenBy(x => x.Package);
                else if (key.Trim().Equals("package desc", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenByDescending(x => x.Package);
                else if (key.Trim().Equals("startdate", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenBy(x => x.StartDate);
                else if (key.Trim().Equals("startdate desc", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenByDescending(x => x.StartDate);
                else if (key.Trim().Equals("finaldate", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenBy(x => x.FinalDate);
                else if (key.Trim().Equals("finaldate desc", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenByDescending(x => x.FinalDate);
                else if (key.Trim().Equals("submissionDate", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenBy(x => x.SubmissionDate != null ? x.SubmissionDate : x.FinalDate);
                else if (key.Trim().Equals("submissionDate desc", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenByDescending(x => x.SubmissionDate != null ? x.SubmissionDate : x.FinalDate);

                else if (key.Trim().Equals("proposedFinalDate", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenBy(x => x.ProposedFinalDate != null ? x.ProposedFinalDate : x.FinalDate);
                else if (key.Trim().Equals("proposedFinalDate desc", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenByDescending(x => x.ProposedFinalDate != null ? x.ProposedFinalDate : x.FinalDate);
            }

            return _orderedQuery;
        }

        return _result
            .OrderByDescending(x => x.SubmissionDate != null ? x.SubmissionDate : x.FinalDate)
            ;


    }

    public async Task<byte[]> GetAnalysisExcel(IEnumerable<QueryFilter>? Filters = null, string? Search = null, string? Sort = null)
    {

        var _dataSet = new DataSet();
        var query = await GetAnalysisData(Filters, Search, Sort);

        return ExcelUtility.ExportExcel<PackageAnalysis>(query);

    }

    public async Task UpdatePackageConsumption()
    {

        var _packages = await db.Packages.AsNoTracking()
             .Where(x => x.TypeFlag == McvConstant.PACKAGE_TYPEFLAG_ACTIVE && x.StatusFlag == McvConstant.PACKAGE_STATUSFLAG_SENT)
             .OrderByDescending(x => x.SubmissionDate)
             //.Select(x=>x.ID)
             .ToListAsync();

        var count = 0;
        foreach (var obj in _packages)
        {

            var _consumedData = await GetPackageVHrConsumption(obj.ID);
            if (_consumedData != null)
            {
                if (obj.VHrConsumed != _consumedData.VHrConsumed)
                {

                    obj.VHrConsumed = _consumedData.VHrConsumed;
                    obj.VHrConsumedInclusive = _consumedData.VHrConsumedInclusive;
                    obj.VHrConsumedCost = _consumedData.VHrConsumedCost;
                    obj.VHrConsumedInclusiveCost = _consumedData.VHrConsumedInclusiveCost;

                    db.Entry(obj).State = EntityState.Modified;
                    await db.SaveChangesAsync();
                    count++;
                }
            }

        }

    }

    public async Task<ReportDefinition> GetDesignIntentReport(int PackageID, string RenderType = "PDF")
    {
        var designScriptEntityService = new DesignScriptEntityService(db);

        var package = await db.Packages.AsNoTracking()
            .Include(x => x.Project)
            .Include(x => x.DesignIntents)
            .SingleOrDefaultAsync(x => x.ID == PackageID);

        if (!package.DesignIntents.Any())
        {

            var phase = await db.DesignScriptEntities.AsNoTracking()
                .SingleOrDefaultAsync(x => x.ID == package.DesignScriptEntityID);

            var designScriptEntities = db.DesignScriptEntities
                                               .AsNoTracking()
                                               
                                       .Where(x => x.ProjectID == package.ProjectID);

            if (!phase.isMasterPhase)
            {
                var _filterIDs = new List<int>();

                var _entityIDs = new List<int>() { package.DesignScriptEntityID.Value };

                _filterIDs = _entityIDs.ToList();
                while (_entityIDs.Count > 0)
                {
                    _entityIDs = await GetRecursiveChildrenIDs(_entityIDs);
                    _filterIDs = _filterIDs.Concat(_entityIDs).ToList();
                }

                designScriptEntities = designScriptEntities.Where(x => _filterIDs.Any(c => c == x.ID));
            }

            var designIntents = await designScriptEntities
                                   .GroupJoin(
               db.TypeMasters.Where(t => t.Entity == nameof(DesignScriptEntity)),
               e => e.TypeFlag,
               t => t.Value,
               (e, t) => new { e, TypeValue = t.DefaultIfEmpty() }
                                   )
                                   .Select(x => new
                                   {
                                       DesignScriptEntityID = x.e.ID,
                                       Code = x.e.Code,
                                       Title = x.e.Title,
                                       Description = x.e.Description,
                                       OrderFlag = x.e.OrderFlag,
                                       TypeFlag = x.e.TypeFlag,
                                       ParentID = x.e.ParentID != null ? x.e.ParentID : 0,
                                       TypeValue = x.TypeValue.Any() ? x.TypeValue.FirstOrDefault().Title : "UNDEFINED",
                                   })
                                   .ToListAsync();
            ;
            foreach (var item in designIntents.Select(x => new PackageDesignIntent
            {
                DesignScriptEntityID = x.DesignScriptEntityID,
                Code = x.Code,
                Title = x.Title,
                Description = x.Description,
                OrderFlag = x.OrderFlag,
                TypeFlag = x.TypeFlag,
                ParentID = x.ParentID,
                TypeValue = x.TypeValue,
            }).ToList())
            {
                package.DesignIntents.Add(item);
            }
        }

        var designIntentReportData = package.DesignIntents
                                .Select(x => new DesignIntentReportData
                                {
                                    ID = x.DesignScriptEntityID,
                                    Code = x.Code,
                                    Title = x.Title,
                                    Description = x.Description,
                                    OrderFlag = x.OrderFlag,
                                    TypeFlag = x.TypeFlag,
                                    ParentID = x.ParentID,
                                    TypeValue = x.TypeValue,
                                })
                                .ToList();


        var designIntentPDF = await designScriptEntityService.GetDesignIntentPDF("a4", package.Project, true, designIntentReportData);
        return designIntentPDF;
    }


    public async Task<PackageDto> GetPackageVHrConsumption(int PackageID)
    {

        var _packageAssociates = db.PackageAssociations
            .Where(x => x.PackageID == PackageID);

        var _exclusiveTaskData = await db.WFTasks.AsNoTracking()
                         .Where(x => x.StatusFlag == McvConstant.WFTASK_STATUSFLAG_COMPLETED)
                         .Where(x => x.Entity != null && x.Entity == nameof(Package))
                         .Where(x => x.EntityID != null && x.EntityID == PackageID)
                           .Where(x => !_packageAssociates.Where(s => s.TypeFlag == McvConstant.PACKAGE_ASSOCIATION_TYPEFLAG_PARTNER || s.TypeFlag == McvConstant.PACKAGE_ASSOCIATION_TYPEFLAG_ASSOCIATE)
                                                  .Select(s => s.ContactID)
                                                      .Any(s => s == x.ContactID))
                         .Select(x => new
                         {
                             WFTaskID = x.ID,
                             EntityID = x.EntityID,
                             ContactID = x.ContactID,
                             VHrAssessed = x.VHrAssessed,
                             VHrAssessedCost = x.VHrAssessedCost,
                         })

             .ToListAsync();

        var _inclusiveTaskData = await db.WFTasks.AsNoTracking()
                        .Where(x => x.StatusFlag == 1)
                        .Where(x => x.Entity != null && x.Entity==nameof(Package))
                        .Where(x => x.EntityID != null && x.EntityID == PackageID)
                        .Select(x => new
                        {
                            WFTaskID = x.ID,
                            EntityID = x.EntityID,
                            ContactID = x.ContactID,
                            VHrAssessed = x.VHrAssessed,
                            VHrAssessedCost = x.VHrAssessedCost,
                        })

            .ToListAsync();

        if (_exclusiveTaskData.Any())
        {
            var _package = new PackageDto();
            _package.VHrConsumed = _exclusiveTaskData.Sum(a => a.VHrAssessed);
            _package.VHrConsumedCost = _exclusiveTaskData.Sum(a => a.VHrAssessedCost);
            _package.VHrConsumedInclusive = _inclusiveTaskData.Sum(a => a.VHrAssessed);
            _package.VHrConsumedInclusiveCost = _inclusiveTaskData.Sum(a => a.VHrAssessedCost);
            return _package;
        }

        return null;

    }

    public async Task<string> GetSubmissionSet(int PackageID)
    {

        var package = await Get()
            .Include(x => x.Project)
            .Include(x => x.Attachments)
            .Include(x => x.DesignIntents)
            .SingleOrDefaultAsync(x => x.ID == PackageID);

        if (package == null)
        {
            throw new EntityServiceException("Package not found!");
        }

        var _attachments = package.Attachments
            .Where(x => x.TypeFlag == McvConstant.PACKAGE_ATTACHMENT_TYPEFLAG_EXTERNAL)
            .Where(x => x.Filename.ToLower().Contains(".pdf"))
            .OrderBy(x => x.OrderFlag)
            .ToList();



        var _transmittalDef = await GetTransmittalReport(PackageID);

        if (_transmittalDef == null || _transmittalDef.FileContent == null || _transmittalDef.FileContent.Length == 0)
            throw new EntityServiceException("Transmittal Not generated!");


        var sharedService = new SharedService(db);

        //upload transmittal to Blobs
        var transmittalFilename = $"{nameof(Package)}/{package.UID}/{Guid.NewGuid()}/{package.ProjectCode}-{package.ProjectTitle}-{package.Title}-Transmittal.pdf";

        var azureStorageKey = await sharedService.GetPresetValue(McvConstant.AZURE_STORAGE_KEY);

        var azureBlobService = new AzureBlobService();
        //var transmittalUrl = await azureBlobService.UploadAsync(azureStorageKey, await sharedService.GetPresetValue(McvConstant.BLOB_CONTAINER_ATTACHMENTS), transmittalFilename, new MemoryStream(_transmittalDef.FileContent));

        //List<string> _layouts = new List<string>();
        //_layouts.Add(transmittalUrl);

        var files = new List<byte[]>();
        files.Add(_transmittalDef.FileContent);

        var designScriptEntityService = new DesignScriptEntityService(db);

        if (!package.DesignIntents.Any())
        {

            var phase = await designScriptEntityService.Get()
                 .SingleOrDefaultAsync(x => x.ID == package.DesignScriptEntityID);

            var designScriptEntities = designScriptEntityService.Get()
                                       .Where(x => x.ProjectID == package.ProjectID);

            if (!phase.isMasterPhase)
            {
                var _filterIDs = new List<int>();

                var _entityIDs = new List<int>() { package.DesignScriptEntityID.Value };

                _filterIDs = _entityIDs.ToList();
                while (_entityIDs.Count > 0)
                {
                    _entityIDs = await GetRecursiveChildrenIDs(_entityIDs);
                    _filterIDs = _filterIDs.Concat(_entityIDs).ToList();
                }

                designScriptEntities = designScriptEntities
                    .Where(x => _filterIDs.Any(c => c == x.ID));
            }
            var typeMasterService = new TypeMasterService(db);
            var typeMasters = await typeMasterService.Get().Where(x => x.Entity == nameof(Package))
                .Select(x => new
                {
                    x.Title,
                    x.Value
                }).ToListAsync();

            var designIntents = await designScriptEntities
                                   .Select(x => new
                                   {
                                       DesignScriptEntityID = x.ID,
                                       Code = x.Code,
                                       Title = x.Title,
                                       Description = x.Description,
                                       OrderFlag = x.OrderFlag,
                                       TypeFlag = x.TypeFlag,
                                       ParentID = x.ParentID != null ? x.ParentID : 0,
                                   })
                                   .ToListAsync();

            ;
            foreach (var item in designIntents.Select(x => new PackageDesignIntent
            {
                DesignScriptEntityID = x.DesignScriptEntityID,
                Code = x.Code,
                Title = x.Title,
                Description = x.Description,
                OrderFlag = x.OrderFlag,
                TypeFlag = x.TypeFlag,
                ParentID = x.ParentID,
                TypeValue = typeMasters.Any(a => a.Value == x.TypeFlag) ? typeMasters.FirstOrDefault(a => a.Value == x.TypeFlag).Title : "UNDEFINED",
                PackageID = package.ID,

            }))
            {
                package.DesignIntents.Add(item);
            }
        }

        var designIntentReportData = package.DesignIntents
                              .Select(x => new DesignIntentReportData
                              {
                                  ID = x.DesignScriptEntityID,
                                  Code = x.Code,
                                  Title = x.Title,
                                  Description = x.Description,
                                  OrderFlag = x.OrderFlag,
                                  TypeFlag = x.TypeFlag,
                                  ParentID = x.ParentID,
                                  TypeValue = x.TypeValue,
                              })
                              .ToList();

        var designIntentPDF = await designScriptEntityService.GetDesignIntentPDF("a4", package.Project, true, designIntentReportData);

        if (designIntentPDF.FileContent != null)
        {
            var designIntentFilename = $"{nameof(Package)}/{package.UID}/{Guid.NewGuid()}/{package.ProjectCode}-{package.ProjectTitle}-{package.Title}-DesignIntent.pdf";

            //var designIntentUrl = await azureBlobService.UploadAsync(azureStorageKey, await sharedService.GetPresetValue(McvConstant.BLOB_CONTAINER_ATTACHMENTS), designIntentFilename, new MemoryStream(designIntentPDF.FileContent));

            //_layouts.Add(designIntentUrl);

            files.Add(designIntentPDF.FileContent);
        }

        foreach (var item in _attachments)
        {
            if (item.Filename.ToLower().EndsWith(".pdf"))
            {
                //_layouts.Add(item.Url);
                using ( var stream = new MemoryStream())
                {
                    await azureBlobService.DownloadByUrlAsync(item.Url, stream);
                    files.Add(stream.ToArray());
                }
            }
        }

        //if (_layouts.Count == 0) return null;

        if(files.Count == 0) return null;

        var _filename = $"{nameof(Package)}/Submission/{package.UID}/{Guid.NewGuid()}/{package.ProjectCode}-{package.ProjectTitle}-{package.Title}.pdf";
        try
        {
            //var apiUrl = await sharedService.GetPresetValue(McvConstant.PDF_COMBINE_BLOB_API);

            //var url = await PDFCombineClient.CombinePDFBlobs(apiUrl, await sharedService.GetPresetValue(McvConstant.BLOB_CONTAINER_ATTACHMENTS), _filename, _layouts);

            var combinedPdf = PdfUtility.CombinePDFs(files);

            var url = await azureBlobService.UploadAsync(azureStorageKey, await sharedService.GetPresetValue(McvConstant.BLOB_CONTAINER_ATTACHMENTS), _filename, new MemoryStream(combinedPdf));

            var originalHostName = await sharedService.GetPresetValue(McvConstant.AZURE_STORAGE_BLOB_ORIGINAL_HOSTNAME);
            var cdnHostName = await sharedService.GetPresetValue(McvConstant.AZURE_STORAGE_BLOB_CDN_HOSTNAME);


            url = url.Replace(originalHostName, cdnHostName);

            return url;
        }
        catch (Exception e)
        {
            throw new EntityServiceException($"Submission Set could not be genrated!.\n{e.Message}");
        }


    }

    public async Task UpdatePackageTaskDue(int ID, DateTime StartDate, DateTime FinalDate)
    {

        var _tasks = await db.WFTasks
          .Where(x => x.Entity == nameof(Package) && x.EntityID == ID && x.StatusFlag != 1 && x.StatusFlag != -1 && x.StageIndex != 3)
          .ToListAsync();

        foreach (var _task in _tasks)
        {
            _task.DueDate = await GetPackageTaskDueDate(StartDate, FinalDate, _task.StageIndex.Value);


        }
        await db.SaveChangesAsync();


    }


    public async Task<DateTime> GetPackageTaskDueDate(DateTime StartDate, DateTime EndDate, int StageIndex = 1)
    {

        var _dueDate = EndDate;
        //if (_dueDate < DateTime.UtcNow) _dueDate = DateTime.UtcNow;

        var _difference = EndDate > StartDate ? (EndDate - StartDate).Days : 0;
        if (StageIndex == 1) //Brainstorming
        {
            if (_difference == 0)
            {
                _dueDate = EndDate.Date;
            }
            else if (_difference > 0 && _difference <= 3)
            {
                _dueDate = StartDate.Date;
            }
            else
            {
                _dueDate = StartDate.AddDays(1);
            }
        }
        else if (StageIndex == 5) //Package
        {
            if (_difference == 0)
            {
                _dueDate = EndDate;
            }
            else if (_difference > 0 && _difference <= 3)
            {
                _dueDate = EndDate;
            }
            else
            {
                _dueDate = EndDate.AddDays(-1);
            }
        }
        else if (StageIndex == 6) //Review
        {
            if (_difference == 0)
            {
                _dueDate = EndDate;
            }
            else if (_difference > 0 && _difference <= 3)
            {
                _dueDate = EndDate;
            }
            else
            {
                _dueDate = EndDate;
            }
        }
        else //Submission
        {
            _dueDate = EndDate;
        }
        var sharedService = new SharedService(db); ;
        return ClockTools.GetUTC(ClockTools.GetIST(_dueDate).Date
                    .AddMinutes(await sharedService.GetBusinessEndMinutesIST()));

    }



    public async Task<ReportDefinition> GetSubmissionDirectoryReport(int ProjectID, string RenderType = "PDF")
    {

        var _project = await db.Projects.AsNoTracking()
           .SingleOrDefaultAsync(x => x.ID == ProjectID);

        if (_project == null) throw new EntityServiceException("Project not found!");

        var textInfo = new CultureInfo("en-IN", false).TextInfo;
        var _projectName = _project.Code + "-" + textInfo.ToTitleCase(_project.Title.ToLower());
        var _reportProperties = new List<ReportProperties>
        {
            new ReportProperties() { PropertyName = "ReportTitle", PropertyValue = "Submission Directory" },
            new ReportProperties() { PropertyName = "Title", PropertyValue = _projectName }
        };
        var sharedService = new SharedService(db); ;
        var _reportContainerUrl = await sharedService.GetPresetValue(McvConstant.RDLC_REPORT_CONTAINER_URL);
        var DEVMODE = Convert.ToBoolean((await sharedService.GetPresetValue(McvConstant.DEVMODE)));

        var _reportPath = $"SubmissionDirectory.rdlc";

        var _reportDef = new ReportDefinition()
        {
            ReportName = "SubmissionDirectory",
            ReportPath = DEVMODE ? $"{_reportContainerUrl}DEV/{_reportPath}" : $"{_reportContainerUrl}{_reportPath}",
            ReportDataSet = DataTools.ToDataTable(await GetSubmissionDirectoryData(ProjectID)),
            ReportProperties = _reportProperties,
            Filename = $"{_projectName}-SubmissionDirectory-{ClockTools.GetISTNow().ToString("yyMMddHHmm")}",
            RenderType = RenderType
        };

        var reportServiceApi = await sharedService.GetPresetValue(McvConstant.RDLC_PROCESSOR_API);
        return await ReportClient.GenerateReport(_reportDef, reportServiceApi);

    }

    public async Task<ReportDefinition> GetSubmissionHistoryBriefReport(int ProjectID, string RenderType = "PDF")
    {

        var _project = await db.Projects.AsNoTracking()
           .SingleOrDefaultAsync(x => x.ID == ProjectID);

        if (_project == null) throw new EntityServiceException("Project not found!");

        var projectPackages = await Get()
          .Where(x => x.ProjectID == ProjectID && x.TypeFlag == McvConstant.PACKAGE_TYPEFLAG_ACTIVE && x.DesignScriptEntityID != null && x.StatusFlag== McvConstant.PACKAGE_STATUSFLAG_SENT)
          .ToListAsync();

        var designScriptService = new DesignScriptEntityService(db);

        var zones = await designScriptService.Get()
            .Where(x => x.ProjectID == ProjectID && x.TypeFlag == McvConstant.DESIGN_SCRIPT_ENTITY_TYPEFLAG_ZONE)
            .Select(x => new
            {
                x.ID,
                x.Code,
                x.Title,
                x.OrderFlag
            })
            .ToListAsync();

        var result = new List<SubmissionDirectory>();

        var sharedService = new SharedService(db);
        var rootApi = await sharedService.GetPresetValue(McvConstant.ROOT_API);
        foreach (var zone in zones)
        {
            var zonePackages = projectPackages
            .Where(x => x.DesignScriptEntityID == zone.ID)
            .OrderBy(x => x.SubmissionDate)
            .ToList();



            if (zonePackages.Any())
            {


                foreach (var sub in zonePackages)
                {
                    string htmlUrl = $"{rootApi}/package/submission/{sub.UID}";
                    string _qrCode = null;
                    try
                    {
                        _qrCode = await sharedService.GenerateQR(new QRRequest
                        {
                            Url = htmlUrl
                        });
                    }
                    catch (Exception)
                    {

                    }

                    result.Add(new SubmissionDirectory
                    {
                        Phase = zone.Code + "-" + zone.Title,
                        PhaseOrder = Convert.ToInt32(zone.OrderFlag),
                        Code = sub.Code,
                        Title = sub.Title,
                        SubmissionDate = ClockTools.GetIST(sub.SubmissionDate != null ? sub.SubmissionDate.Value : DateTime.UtcNow).ToString("dd MMM yyyy HH:mm"),
                        //IsLatest =  false,
                        QRCode = _qrCode,
                        Url = htmlUrl
                    });


                }
            }
            else
            {

                result.Add(new SubmissionDirectory
                {
                    Phase = zone.Code + "-" + zone.Title,
                    PhaseOrder = Convert.ToInt32(zone.OrderFlag),
                });
            }
        }

        var textInfo = new CultureInfo("en-IN", false).TextInfo;
        var _projectName = _project.Code + "-" + textInfo.ToTitleCase(_project.Title.ToLower());
        var _reportProperties = new List<ReportProperties>
        {
            new ReportProperties() { PropertyName = "ReportTitle", PropertyValue = "Submission History" },
            new ReportProperties() { PropertyName = "Title", PropertyValue = _projectName }
        };
        var _reportContainerUrl = await sharedService.GetPresetValue(McvConstant.RDLC_REPORT_CONTAINER_URL);
        var DEVMODE = Convert.ToBoolean((await sharedService.GetPresetValue(McvConstant.DEVMODE)));

        var _reportPath = $"SubmissionHistoryBrief.rdlc";

        var _reportDef = new ReportDefinition()
        {
            ReportName = "SubmissionHistory",
            ReportPath = DEVMODE ? $"{_reportContainerUrl}DEV/{_reportPath}" : $"{_reportContainerUrl}{_reportPath}",
            ReportDataSet = DataTools.ToDataTable(result),
            ReportProperties = _reportProperties,
            Filename = $"{_projectName}-SubmissionHistory-{ClockTools.GetISTNow().ToString("yyMMddHHmm")}",
            RenderType = RenderType
        };

        var reportServiceApi = await sharedService.GetPresetValue(McvConstant.RDLC_PROCESSOR_API);
        return await ReportClient.GenerateReport(_reportDef, reportServiceApi);

    }

    public async Task<ReportDefinition> GetSubmissionHistoryDetailReport(int ProjectID, string RenderType = "PDF")
    {

        var _project = await db.Projects.AsNoTracking()
           .SingleOrDefaultAsync(x => x.ID == ProjectID);

        if (_project == null) throw new EntityServiceException("Project not found!");

        var projectPackages = await Get()
          .Where(x => x.ProjectID == ProjectID && x.TypeFlag == McvConstant.PACKAGE_TYPEFLAG_ACTIVE && x.DesignScriptEntityID != null && x.StatusFlag==McvConstant.PACKAGE_STATUSFLAG_SENT)
          .Include(x => x.Attachments)
          .ToListAsync();

        var designScriptService = new DesignScriptEntityService(db);

        var zones = await designScriptService.Get()
            .Where(x => x.ProjectID == ProjectID && x.TypeFlag == McvConstant.DESIGN_SCRIPT_ENTITY_TYPEFLAG_ZONE)
            .Select(x => new
            {
                x.ID,
                x.Code,
                x.Title,
                x.OrderFlag
            })
            .ToListAsync();

        var result = new List<SubmissionDirectory>();
        var result2 = new List<SubmissionDirectorySheet>();

        var sharedService = new SharedService(db);
        var rootApi = await sharedService.GetPresetValue(McvConstant.ROOT_API);
        foreach (var zone in zones)
        {
            var zonePackages = projectPackages
            .Where(x => x.DesignScriptEntityID == zone.ID)
            .OrderBy(x => x.SubmissionDate)
            .ToList();



            if (zonePackages.Any())
            {


                foreach (var sub in zonePackages)
                {
                    string htmlUrl = $"{rootApi}/package/submission/{sub.UID}";
                    string _qrCode = null;
                    try
                    {
                        _qrCode = await sharedService.GenerateQR(new QRRequest
                        {
                            Url = htmlUrl
                        });
                    }
                    catch (Exception)
                    {

                    }

                    result.Add(new SubmissionDirectory
                    {
                        Phase = zone.Code + "-" + zone.Title,
                        PhaseOrder = Convert.ToInt32(zone.OrderFlag),
                        Code = sub.Code,
                        Title = sub.Title,
                        SubmissionDate = ClockTools.GetIST(sub.SubmissionDate != null ? sub.SubmissionDate.Value : DateTime.UtcNow).ToString("dd MMM yyyy HH:mm"),
                        //IsLatest =  false,
                        QRCode = _qrCode,
                        Url = htmlUrl
                    });

                    foreach (var file in sub.Attachments)
                    {
                        if (file.Attributes.Any())
                        {
                            foreach (var sheet in file.Attributes)
                            {
                                result2.Add(new SubmissionDirectorySheet
                                {

                                    Code = sub.Code,
                                    Title = sheet.Title,
                                    SheetNo = sheet.SheetNo,
                                    Description = sheet.Description
                                }
                                                );
                            }
                        }


                    }

                }
            }
            else
            {

                result.Add(new SubmissionDirectory
                {
                    Phase = zone.Code + "-" + zone.Title,
                    PhaseOrder = Convert.ToInt32(zone.OrderFlag),
                });
            }
        }

        var textInfo = new CultureInfo("en-IN", false).TextInfo;
        var _projectName = _project.Code + "-" + textInfo.ToTitleCase(_project.Title.ToLower());
        var _reportProperties = new List<ReportProperties>
        {
            new ReportProperties() { PropertyName = "ReportTitle", PropertyValue = "Submission History" },
            new ReportProperties() { PropertyName = "Title", PropertyValue = _projectName }
        };
        var _reportContainerUrl = await sharedService.GetPresetValue(McvConstant.RDLC_REPORT_CONTAINER_URL);
        var DEVMODE = Convert.ToBoolean((await sharedService.GetPresetValue(McvConstant.DEVMODE)));

        var _reportPath = $"SubmissionHistory.rdlc";

        var _reportDef = new ReportDefinition()
        {
            ReportName = "SubmissionHistory",
            ReportPath = DEVMODE ? $"{_reportContainerUrl}DEV/{_reportPath}" : $"{_reportContainerUrl}{_reportPath}",
            ReportDataSet = DataTools.ToDataTable(result),
            ReportProperties = _reportProperties,
            Filename = $"{_projectName}-SubmissionHistory-{ClockTools.GetISTNow().ToString("yyMMddHHmm")}",
            RenderType = RenderType
        };

        _reportDef.SubReports.Add(new ReportDefinition()
        {
            ReportName = "SubmissionHistory_Sub",
            ReportPath = DEVMODE ? $"{_reportContainerUrl}DEV/SubmissionHistory_Sub.rdlc" : $"{_reportContainerUrl}SubmissionHistory_Sub.rdlc",
            ReportDataSet = DataTools.ToDataTable(result2),
        });

        var reportServiceApi = await sharedService.GetPresetValue(McvConstant.RDLC_PROCESSOR_API);
        return await ReportClient.GenerateReport(_reportDef, reportServiceApi);

    }

    public async Task<ReportDefinition> GetTransmittalReport(int PackageID, string RenderType = "PDF")
    {

        var _package = await db.Packages.AsNoTracking()
            .Include(x => x.Project)
         .SingleOrDefaultAsync(x => x.ID == PackageID);

        if (_package == null) throw new EntityServiceException("Package not found");

        TextInfo textInfo = new CultureInfo("en-IN", false).TextInfo;
        var _projectName = _package.Project.Code + "-" + textInfo.ToTitleCase(_package.Project.Title.ToLower());

        var _count = await db.Packages.AsNoTracking().Where(x => x.ProjectID == _package.ProjectID && x.StatusFlag >= 1).CountAsync();
        var _submissionDate = _package.SubmissionDate != null ? ClockTools.GetIST(_package.SubmissionDate.Value).ToString("dd MMM yyyy HH:mm") : ClockTools.GetISTNow().ToString("dd MMM yyyy HH:mm");

        var _reportProperties = new List<ReportProperties>
            {
                new ReportProperties() { PropertyName = "Project", PropertyValue = _projectName },
                new ReportProperties() { PropertyName = "Submission", PropertyValue = _package.Title },
                new ReportProperties() { PropertyName = "Comment", PropertyValue = _package.SubmissionMessage == null ? "" : _package.SubmissionMessage },
                new ReportProperties() { PropertyName = "Count", PropertyValue = _count.ToString("00") },
                new ReportProperties() { PropertyName = "SubmissionDate", PropertyValue = _submissionDate },
                new ReportProperties() { PropertyName = "To", PropertyValue = _package.EmailID != null ? _package.EmailID.Replace(",", "; ") : "" },
                new ReportProperties() { PropertyName = "CC", PropertyValue = _package.CC != null ? _package.CC.Replace(",", "; ") : "" },
                 new ReportProperties() { PropertyName = "DValue", PropertyValue = _package.DValue.ToString() },
                  new ReportProperties() { PropertyName = "RValue", PropertyValue = _package.RValue.ToString() }
            };

        var _reportDef = new ReportDefinition()
        {
            ReportName = "Transmittal",
            ReportPath = @"https://nhub.blob.core.windows.net/rdlc/Transmittal.rdlc",
            ReportDataSet = await GetTransmittalData(_package.ID),
            ReportProperties = _reportProperties,
            Filename = _projectName + "-" + "Transmittal",
            RenderType = RenderType
        };

        _reportDef.SubReports.Add(new ReportDefinition()
        {
            ReportName = "SubmissionDirectory",
            ReportPath = @"https://nhub.blob.core.windows.net/rdlc/TransmittalDirectory.rdlc",
            ReportDataSet = DataTools.ToDataTable(await GetSubmissionDirectoryData(_package.ProjectID, PackageID)),
        });
        var sharedService = new SharedService(db);

        var reportServiceApi = await sharedService.GetPresetValue(McvConstant.RDLC_PROCESSOR_API);
        return await ReportClient.GenerateReport(_reportDef, reportServiceApi);

    }
    public async Task<DataTable> GetTransmittalData(int PackageID)
    {

        DataTable table = new DataTable();
        table.Columns.Add("Order", typeof(int));
        table.Columns.Add("Filename", typeof(string));
        table.Columns.Add("SheetNo", typeof(string));
        table.Columns.Add("Title", typeof(string));
        table.Columns.Add("Description", typeof(string));
        table.Columns.Add("FileType", typeof(string));

        var _attachments = await db.PackageAttachments.AsNoTracking()
            .Where(x => x.TypeFlag == 0)
            .Where(x => x.PackageID == PackageID)
            .OrderBy(x => x.OrderFlag)
            .ToListAsync();


        foreach (var file in _attachments)
        {
            var _fileType = Path.GetExtension(file.Filename).ToLower() == ".pdf" ? "PDF" : "SUPPORT";

            if (_fileType == "PDF")
            {
                foreach (var item in file.Attributes)
                {
                    table.Rows.Add(file.OrderFlag,
                        file.Filename,
                        item.SheetNo,
                        item.Title,
                        item.Description,
                        _fileType);
                }
            }
            else if (_fileType == "MP4")
            {

            }
            else
            {
                table.Rows.Add(file.OrderFlag, file.Filename, null, file.Filename, null, _fileType);
            }
        }
        return table;

    }

    public async Task<DataTable> GetSubmissionHistoryData(int Id)
    {

        DataTable table = new DataTable();
        table.Columns.Add("Phase", typeof(string));
        table.Columns.Add("PhaseOrder", typeof(Int32));
        table.Columns.Add("Code", typeof(string));
        table.Columns.Add("Title", typeof(string));
        table.Columns.Add("SubmissionDate", typeof(string));
        table.Columns.Add("IsLatest", typeof(Boolean));

        var _phases = await db.DesignScriptEntities.AsNoTracking()
            .Where(x => x.ProjectID == Id && x.TypeFlag == 0).ToListAsync();

        foreach (var _phase in _phases)
        {
            var _submissions = await db.Packages.AsNoTracking().Where(x => x.ProjectID == Id
            && x.DesignScriptEntityID != null && x.StatusFlag != 0

            && x.DesignScriptEntityID == _phase.ID)
            .OrderByDescending(x => x.SubmissionDate)
            .ToListAsync();

            if (_submissions.Any())
            {
                foreach (var sub in _submissions)
                {

                    var _latestSub = _submissions
                        .Where(x => x.ProjectID == sub.ProjectID && x.DesignScriptEntityID == sub.DesignScriptEntityID)
                        .OrderByDescending(x => x.SubmissionDate)
                        .FirstOrDefault();

                    table.Rows.Add(_phase.Code + "-" + _phase.Title,
                        _phase.OrderFlag,
                        sub.Code,
                        sub.Title,
                        ClockTools.GetIST(sub.SubmissionDate != null ? sub.SubmissionDate.Value : DateTime.UtcNow).ToString("dd MMM yyyy HH:mm"),
                        (sub.Stage == _latestSub.Stage && sub.Revision == _latestSub.Revision && sub.AnnexureIndex == _latestSub.AnnexureIndex));
                }
            }
            else
            {
                table.Rows.Add(_phase.Code + "-" + _phase.Title,
                        _phase.OrderFlag,
                            " ",
                            " ",
                            null,
                            false);
            }
        }

        return table;

    }
    public async Task<DataTable> GetSubmissionHistoryData(DesignScriptEntity _phase, IEnumerable<Package> Submissions)
    {

        DataTable table = new DataTable();
        table.Columns.Add("Phase", typeof(string));
        table.Columns.Add("PhaseOrder", typeof(Int32));
        table.Columns.Add("Code", typeof(string));
        table.Columns.Add("Title", typeof(string));
        table.Columns.Add("SubmissionDate", typeof(string));
        table.Columns.Add("IsLatest", typeof(Boolean));


        //var _submissions = await Get().Where(x => x.ProjectID == Phase.ProjectID
        //&& x.PhaseCode != null && x.StatusFlag != 0

        //&& x.PhaseCode == Phase.PhaseCode)
        ////.OrderByDescending(x => x.SubmissionDate)
        //.ToListAsync();

        if (Submissions.Any())
        {
            foreach (var sub in Submissions)
            {

                var _latestSub = Submissions
                        .Where(x => x.ProjectID == sub.ProjectID && x.DesignScriptEntityID == sub.DesignScriptEntityID)
                        .OrderByDescending(x => x.SubmissionDate)
                        .FirstOrDefault();

                table.Rows.Add(_phase.Code + "-" + _phase.Title,
                    _phase.OrderFlag,
                    sub.Code,
                    sub.Title,
                    ClockTools.GetIST(sub.SubmissionDate != null ? sub.SubmissionDate.Value : DateTime.UtcNow).ToString("dd MMM yyyy HH:mm"),
                    (sub.Stage == _latestSub.Stage && sub.Revision == _latestSub.Revision && sub.AnnexureIndex == _latestSub.AnnexureIndex));
            }
        }
        else
        {
            table.Rows.Add(_phase.Code + "-" + _phase.Title,
                    _phase.OrderFlag,
                        " ",
                        " ",
                        null,
                        false);
        }


        return table;

    }

    public async Task<DataTable> GetSubmissionHistorySubData(int ProjectID)
    {

        DataTable table = new DataTable();
        table.Columns.Add("PhaseCode", typeof(string));
        table.Columns.Add("PLegend", typeof(string));
        table.Columns.Add("Code", typeof(string));
        table.Columns.Add("SubmissionTitle", typeof(string));
        table.Columns.Add("Version", typeof(float));
        table.Columns.Add("SubmissionDate", typeof(string));
        table.Columns.Add("Title", typeof(string));
        table.Columns.Add("SheetNo", typeof(string));
        table.Columns.Add("Description", typeof(string));
        table.Columns.Add("Order", typeof(int));

        var _phases = await db.DesignScriptEntities.AsNoTracking()
            .Where(x => x.ProjectID == ProjectID && x.TypeFlag == 0).ToListAsync();

        foreach (var _phase in _phases)
        {
            var _submissions = await db.Packages.AsNoTracking().Where(x => x.ProjectID == ProjectID
            && x.DesignScriptEntityID != null && x.StatusFlag != 0

            && x.DesignScriptEntityID == _phase.ID)
               .Include(x => x.Attachments)
            .OrderByDescending(x => x.SubmissionDate)
            .ToListAsync();

            if (_submissions.Any())
            {
                foreach (var sub in _submissions)
                {

                    var _latestSub = _submissions
                        .Where(x => x.ProjectID == sub.ProjectID && x.DesignScriptEntityID == sub.DesignScriptEntityID)
                        .OrderByDescending(x => x.SubmissionDate)
                        .FirstOrDefault();


                    foreach (var file in sub.Attachments)
                    {
                        if (file.Attributes.Any())
                        {
                            foreach (var sheet in file.Attributes)
                            {
                                table.Rows.Add(sub.PhaseCode,
                                                _phase.Title,
                                                sub.Code,
                                                sub.Title,
                                                sub.Revision,
                                                ClockTools.GetIST(sub.SubmissionDate.Value).ToString("dd MMM yyyy HH:mm"),
                                                sheet.Title,
                                                sheet.SheetNo,
                                                sheet.Description,
                                                file.ID
                                                );
                            }
                        }
                        else
                        {
                            table.Rows.Add(sub.PhaseCode,
                                                _phase.Title,
                                                sub.Code,
                                                sub.Title,
                                                sub.Revision,
                                                ClockTools.GetIST(sub.SubmissionDate.Value).ToString("dd MMM yyyy HH:mm"),
                                                file.Filename,
                                                "",
                                                "",
                                                file.ID
                                                );
                        }

                    }
                }
            }
        }

        return table;

    }



    public async Task<IEnumerable<SubmissionDirectory>> GetSubmissionDirectoryData(int ProjectID, int? PackageID = null)
    {



        var projectPackages = await Get()
           .Where(x => x.ProjectID == ProjectID && x.TypeFlag == McvConstant.PACKAGE_TYPEFLAG_ACTIVE && x.DesignScriptEntityID != null && x.StatusFlag == McvConstant.PACKAGE_STATUSFLAG_SENT)
           .ToListAsync();

        var designScriptService = new DesignScriptEntityService(db);

        var zones = await designScriptService.Get()
            .Where(x => x.ProjectID == ProjectID && x.TypeFlag == McvConstant.DESIGN_SCRIPT_ENTITY_TYPEFLAG_ZONE)
            .Select(x => new
            {
                x.ID,
                x.Code,
                x.Title,
                x.OrderFlag
            })
            .ToListAsync();

        var lastPackages = projectPackages.Where(x => x.StatusFlag == McvConstant.PACKAGE_STATUSFLAG_SENT).ToList();

        Package _currentPackage = null;
        if (PackageID != null)
        {
            _currentPackage = projectPackages
                .SingleOrDefault(x => x.ID == PackageID);
            if (_currentPackage != null)
            {
                _currentPackage.SubmissionDate = DateTime.UtcNow;
                _currentPackage.StatusFlag = 1;
                if (_currentPackage.SubmissionDate != null)
                    lastPackages = lastPackages.Where(x => x.SubmissionDate <= _currentPackage.SubmissionDate).ToList();
            }
        }

        var result = new List<SubmissionDirectory>();

        var sharedService = new SharedService(db);
        var rootApi = await sharedService.GetPresetValue(McvConstant.ROOT_API);
        foreach (var zone in zones)
        {
            var zonePackages = lastPackages
            .Where(x => x.DesignScriptEntityID == zone.ID)
            .ToList();

            if (_currentPackage != null
                && _currentPackage.DesignScriptEntityID == zone.ID
                )
            {
                if (!zonePackages.Any(x => x.ID == _currentPackage.ID))
                    zonePackages.Add(_currentPackage);
            }

            if (zonePackages.Any())
            {
                var _latestSub = zonePackages
                        .OrderByDescending(x => x.SubmissionDate)
                        .FirstOrDefault();

                foreach (var sub in zonePackages.Where(x => x.Stage == _latestSub.Stage && x.Revision == _latestSub.Revision).OrderBy(x => x.AnnexureIndex))
                {
                    string _qrCode = null;
                    try
                    {
                        _qrCode = await sharedService.GenerateQR(new QRRequest
                        {
                            Url = $"{rootApi}/package/submission/{sub.UID}"
                        });
                    }
                    catch (Exception)
                    {

                    }


                    result.Add(new SubmissionDirectory
                    {
                        Phase = zone.Code + "-" + zone.Title,
                        PhaseOrder = Convert.ToInt32(zone.OrderFlag),
                        Code = sub.Code,
                        Title = sub.Title,
                        SubmissionDate = ClockTools.GetIST(sub.SubmissionDate != null ? sub.SubmissionDate.Value : DateTime.UtcNow).ToString("dd MMM yyyy HH:mm"),
                        IsLatest = _currentPackage != null && _currentPackage.ID == sub.ID ? true : false,
                        QRCode = _qrCode
                    });


                }
            }
            else
            {

                result.Add(new SubmissionDirectory
                {
                    Phase = zone.Code + "-" + zone.Title,
                    PhaseOrder = Convert.ToInt32(zone.OrderFlag),
                    Code = "",
                    Title = "",
                    SubmissionDate = null,
                    IsLatest = false,
                    QRCode = null
                });
            }
        }

        return result;

    }


    public async Task CompletePackageTasks(int WFTaskID, string taskStatus, string Username)
    {

        var sharedService = new SharedService(db);
        var task = await db.WFTasks.AsNoTracking()
            .Include(x => x.Contact)
                .Where(x => x.ID == WFTaskID)
                .SingleOrDefaultAsync();

        if (task == null) throw new EntityServiceException("Task not found");

        if (task.StageIndex == 1) //Initial Review
        {
            var taskService = new WFTaskService(db);
            await taskService.PauseOtherActiveTasks(task.ID, task.ContactID);

            if (task.StatusFlag == 2) //STARTED
            {
                var timeEntryService = new TimeEntryService(db);
                await timeEntryService.StartTimeLog(new TimeEntry
                {
                    ContactID = task.ContactID,
                    WFTaskID = task.ID,
                    Entity = task.Entity,
                    EntityID = task.EntityID,
                    EntityTitle = task.Subtitle,
                    TaskTitle = task.Title,
                });
            }
            else if (task.StatusFlag == 3) //PAUSED
            {
                var timeEntryService = new TimeEntryService(db);
                await timeEntryService.EndTimeLog(task.ID, true);
            }
            else
            {
                var timeEntryService = new TimeEntryService(db);
                await timeEntryService.EndTimeLog(task.ID);
            }

            if (task.StatusFlag == 1)
            {
                //check for ALL-WIN
                var isAllDone = await taskService.IsAllTasksCompleted(task);
                //var isAllApproved = await taskService.IsAllApproved(_task);

                //next stage
                if (isAllDone)
                {
                    //await taskService.HandleParallelTasks(_task.ID);

                    await db.SaveChangesAsync();

                    await PurgeInvitees(task.EntityID.Value);

                    await AssignPackageTask(task.EntityID.Value, 5);
                }
            }
        }
        else if (task.StageIndex == 3) //Studio
        {
            //check for any other tasks inprogress n pause them
            var taskService = new WFTaskService(db);
            await taskService.PauseOtherActiveTasks(task.ID, task.ContactID);

            if (task.StatusFlag == 2) //STARTED
            {
                var timeEntryService = new TimeEntryService(db);
                await timeEntryService.StartTimeLog(new TimeEntry
                {
                    ContactID = task.ContactID,
                    WFTaskID = task.ID,
                    Entity = task.Entity,
                    EntityID = task.EntityID,
                    EntityTitle = task.Subtitle,
                    TaskTitle = task.Title,
                });
            }
            else if (task.StatusFlag == 3) //PAUSED
            {
                var timeEntryService = new TimeEntryService(db);
                await timeEntryService.EndTimeLog(task.ID, true);
            }
            else
            {
                var timeEntryService = new TimeEntryService(db);
                await timeEntryService.EndTimeLog(task.ID);

                //Notify Assigner
                if (task.AssignerContactID != null)
                {

                    var contact = await db.Contacts.AsNoTracking().FirstOrDefaultAsync(x => x.ID == task.AssignerContactID);
                    if (contact != null && contact.Username != null)
                    {
                        await sharedService.PushNotification(contact.Username, $"Studio Work Completed by {task.Contact.Name}", $"{task.Title} | {task.Subtitle} | {ClockTools.GetIST(task.DueDate).ToString("dd MMM yyyy HH:mm")}", nameof(WFTask), task.ID.ToString());
                    }


                }
            }
        }
        else if (task.StageIndex == 5)//Package
        {
            var _studioWorks = db.WFTasks.AsNoTracking()
           .Include(x => x.Assessments)
           .Where(x => x.StageIndex == 3 && x.OutcomeFlag != -1
           && x.Entity==task.Entity
           && x.EntityID == task.EntityID);

            var _pendingStudioWorks = _studioWorks
                .Where(x => x.StatusFlag != McvConstant.WFTASK_STATUSFLAG_COMPLETED);

            var _unAssessedStudioWorks = _studioWorks
               .Where(x => x.StatusFlag == McvConstant.WFTASK_STATUSFLAG_COMPLETED
               && !x.Assessments.Any()
               && x.IsAssessmentRequired);

            var _assignerPendinStudioWorks = _pendingStudioWorks;
            //.Where(x => x.AssignerContactID == _task.ContactID);

            var _assignerUnAssessedStudioWorks = _unAssessedStudioWorks;
            //.Where(x => x.AssignerContactID == _task.ContactID);

            if (await _assignerPendinStudioWorks.AnyAsync())
                throw new EntityServiceException("Studio Work tasks are still in progress!");

            if (await _assignerUnAssessedStudioWorks.AnyAsync())
                throw new EntityServiceException("Studio Work tasks assessment not done!");


            if (!await _pendingStudioWorks.AnyAsync() && !await _unAssessedStudioWorks.AnyAsync())
            {
                var taskService = new WFTaskService(db);
                await taskService.HandleParallelTasks(task.ID);
                await AssignPackageTask(task.EntityID.Value, 6);

                var _otherTaskContactIDs = await db.WFTasks.AsNoTracking()
                    .Where(x => x.Entity==task.Entity && x.EntityID == task.EntityID)
                    .Where(x => x.StageIndex == task.StageIndex
                   && x.StageRevision == task.StageRevision).Select(x => x.ContactID).Distinct().ToListAsync();


            }
        }
        else if (task.StageIndex == 6)//Final Review Meet
        {
            if (task.OutcomeFlag == 0)
                throw new EntityServiceException("Task outcome not updated");

            if (task.OutcomeFlag == -1) //rejected
            {
                var _nextRevision = task.StageRevision + 1;
                var taskService = new WFTaskService(db);
                await taskService.HandleParallelTasks(task.ID);
                await AssignPackageTask(task.EntityID.Value, 5);
            }
            else
            {
                var taskService = new WFTaskService(db);
                //check for ALL-WIN
                var isAllDone = await taskService.IsAllTasksCompleted(task);
                var isAllApproved = await taskService.IsAllApproved(task);

                //next stage
                if (isAllDone && isAllApproved)
                {
                    await PurgeInvitees(task.EntityID.Value);
                    await AssignPackageTask(task.EntityID.Value, 7);
                }
            }
        }
        else if (task.StageIndex == 7)//submission
        {
            await SendSubmission(task.EntityID.Value);
            var taskService = new WFTaskService(db);
            await taskService.HandleParallelTasks(task.ID);

            await PurgeInvitees(task.EntityID.Value);
        }


        //await sharedService.LogUserActivity(Username, task.Entity,
        //        task.EntityID.Value,
        //        "",
        //         task.Title + " | " + "R" + task.StageRevision.ToString() + " | " + task.Subtitle,
        //        taskStatus,
        //        task.Comment,
        //        task.ID);

    }

    public async Task PurgeInvitees(int PackageID)
    {

        var _invitees = await db.PackageAssociations.AsNoTracking()
            .Where(x => x.PackageID == PackageID)
            .Where(x => x.TypeFlag == 2)
            .ToListAsync();

        foreach (var obj in _invitees)
        {
            obj.StatusFlag = 1;
            db.Entry(obj).State = EntityState.Modified;

        }
        await db.SaveChangesAsync();

    }

    public async Task SendSubmission(int Id, bool skipAgendaUpdate = false, bool skipEmail = false)
    {



        var package = await db.Packages
                        //.AsNoTracking()

                        .Include(x => x.DesignIntents)
            .SingleOrDefaultAsync(x => x.ID == Id);


        if (package == null)
        {
            throw new EntityServiceException("Package Not found");
        }

        if (package.EmailID == null)
        {
            throw new EntityServiceException("Email id Not found");
        }

        if (package.StatusFlag == McvConstant.PACKAGE_STATUSFLAG_SENT) return;

            //GET PACKAGE ASSOCIATIONS
            var packageAssociations = await db.PackageAssociations.AsNoTracking()
            .Include(c => c.Contact)
       .Where(x => x.PackageID == package.ID)
       .ToListAsync();

        var _firstContact = "";
        var _secondContact = "";
        var _firstContactLeaves = "";
        var _secondContactLeaves = "";

        var toRecipients = package.ToRecipients;
        var ccRecipients = package.CCRecipients;

        if (packageAssociations.Where(x => x.TypeFlag == McvConstant.PACKAGE_ASSOCIATION_TYPEFLAG_ASSOCIATE).Any())
        {
            var _firstAssociate = packageAssociations.Where(x => x.TypeFlag == McvConstant.PACKAGE_ASSOCIATION_TYPEFLAG_ASSOCIATE).FirstOrDefault().Contact;

            if (_firstAssociate.Email1 != null
                && _firstAssociate.Email1 != string.Empty)
            {
                _firstContact = _firstAssociate.Email1.Trim() + " (" + _firstAssociate.FullName + ")";

                var _leaves = await db.Leaves.AsNoTracking()
                       .Where(x => x.ContactID == _firstAssociate.ID)
                       .Where(x => x.StatusFlag == McvConstant.LEAVE_STATUSFLAG_APPROVED)
                       .Where(x => x.TypeFlag == McvConstant.LEAVE_TYPEFLAG_APPROVED)
                       .Where(x => x.End > DateTime.UtcNow)
                       .ToListAsync();

                if (_leaves.Any())
                {
                    _firstContactLeaves += $"On leave(";



                    foreach (var leave in _leaves.OrderBy(x => x.Start))
                    {
                        _firstContactLeaves += $"{(leave.Start > DateTime.UtcNow ? ClockTools.GetIST(leave.Start).ToString("dd MMM yyyy") : ClockTools.GetISTNow().ToString("dd MMM yyyy"))}-{ClockTools.GetIST(leave.End).ToString("dd MMM yyyy")} | ";
                    }

                    _firstContactLeaves += $")";
                }
                if (!ccRecipients.Any(x => x.Equals(_firstAssociate.Email1.Trim(), StringComparison.OrdinalIgnoreCase)) &&
                    !toRecipients.Any(x => x.Equals(_firstAssociate.Email1.Trim(), StringComparison.OrdinalIgnoreCase)))
                    ccRecipients.Add(_firstAssociate.Email1.Trim());

            }
        }

        if (packageAssociations.Where(x => x.TypeFlag == McvConstant.PACKAGE_ASSOCIATION_TYPEFLAG_PARTNER).Any())
        {
            var _firstPartner = packageAssociations.Where(x => x.TypeFlag == McvConstant.PACKAGE_ASSOCIATION_TYPEFLAG_PARTNER).FirstOrDefault().Contact;

            if (_firstPartner.Email1 != null
                && _firstPartner.Email1 != string.Empty)
            {
                _secondContact = _firstPartner.Email1.Trim() + " (" + _firstPartner.FullName + ")";

                var _leaves = await db.Leaves.AsNoTracking()
                       .Where(x => x.ContactID == _firstPartner.ID)
                       .Where(x => x.StatusFlag == McvConstant.LEAVE_STATUSFLAG_APPROVED)
                       .Where(x => x.TypeFlag == McvConstant.LEAVE_TYPEFLAG_APPROVED)
                       .Where(x => x.End > DateTime.UtcNow)
                       .ToListAsync();

                if (_leaves.Any())
                {
                    _secondContactLeaves += $"On leave(";



                    foreach (var leave in _leaves.OrderBy(x => x.Start))
                    {
                        _secondContactLeaves += $"{(leave.Start > DateTime.UtcNow ? ClockTools.GetIST(leave.Start).ToString("dd MMM yyyy") : ClockTools.GetISTNow().ToString("dd MMM yyyy"))}-{ClockTools.GetIST(leave.End).ToString("dd MMM yyyy")} | ";
                    }

                    _secondContactLeaves += $")";
                }

                if (!ccRecipients.Any(x => x.Equals(_firstPartner.Email1.Trim(), StringComparison.OrdinalIgnoreCase)) &&
                    !toRecipients.Any(x => x.Equals(_firstPartner.Email1.Trim(), StringComparison.OrdinalIgnoreCase)))
                    ccRecipients.Add(_firstPartner.Email1.Trim());
            }
        }
        //package.ToRecipients = toRecipients;
        //package.CCRecipients = ccRecipients;
        package.EmailID = string.Join(";", toRecipients);
        package.CC = string.Join(";", ccRecipients);

        var _packageConsumedData = await GetPackageVHrConsumption(package.ID);
        if (_packageConsumedData != null)
        {
            package.VHrConsumed = _packageConsumedData.VHrConsumed;
            package.VHrConsumedInclusive = _packageConsumedData.VHrConsumedInclusive;
            package.VHrConsumedCost = _packageConsumedData.VHrConsumedCost;
            package.VHrConsumedInclusiveCost = _packageConsumedData.VHrConsumedInclusiveCost;
        }

        var _query = await db.Packages.AsNoTracking()

           .Where(x => x.TypeFlag == McvConstant.PACKAGE_TYPEFLAG_ACTIVE && x.StatusFlag == McvConstant.PACKAGE_STATUSFLAG_SENT)
         .Where(x => x.ProjectID == package.ProjectID)
         .ToListAsync();

        var _previousSubmissions = _query
             .Where(x => x.ID != package.ID)
            .Select(x => new
            {
                Revision = x.Revision + (x.AnnexureIndex < 10 ? x.AnnexureIndex / 10.0 : x.AnnexureIndex / 100.0),
                DelayHours = x.SubmissionDate > x.FinalDate ? (x.SubmissionDate.Value - x.FinalDate).TotalHours : 0,
            }).ToList();

        var _currentDelay = DateTime.UtcNow > package.FinalDate ? (DateTime.UtcNow - package.FinalDate).TotalHours : 0;

        package.DValue = Convert.ToDecimal(
            (_previousSubmissions.Sum(x => x.DelayHours) + _currentDelay) / (_previousSubmissions.Count + 1));

        _query.Add(package);
        var _groups = _query
             .GroupBy(x => new { x.DesignScriptEntityID, x.Stage })
            .Select(x => new
            {
                x.Key.DesignScriptEntityID,
                x.Key.Stage,
                Revision = x.Max(r => r.Revision)
            });
        package.RValue = Convert.ToDecimal(_groups.Sum(x => x.Revision) / _groups.Count());

        //foreach (var item in _submission.DesignIntents.ToList())
        //{
        //    _db.Entry(item).State = EntityState.Deleted;
        //}
        foreach (var intent in package.DesignIntents.ToList())
        {
            db.Entry(intent).State = EntityState.Deleted;
        }
        await db.SaveChangesAsync();

        var phase = await db.DesignScriptEntities.AsNoTracking()
                .SingleOrDefaultAsync(x => x.ID == package.DesignScriptEntityID);

        var designScriptEntities = db.DesignScriptEntities.AsNoTracking()

        .Where(x => x.ProjectID == package.ProjectID);

        if (!phase.isMasterPhase)
        {
            var _filterIDs = new List<int>();
            var _entityIDs = new List<int>() { package.DesignScriptEntityID.Value };

            _filterIDs = _entityIDs.ToList();
            while (_entityIDs.Count > 0)
            {
                _entityIDs = await GetRecursiveChildrenIDs(_entityIDs);
                _filterIDs = _filterIDs.Concat(_entityIDs).ToList();
            }

            designScriptEntities = designScriptEntities.Where(x => _filterIDs.Any(c => c == x.ID));
        }

        var typeMasterService = new TypeMasterService(db);
        var typeMasters = await typeMasterService.Get().Where(x => x.Entity == nameof(Package))
            .Select(x => new
            {
                x.Title,
                x.Value
            }).ToListAsync();

        var designIntents = await designScriptEntities
                               .Select(x => new
                               {
                                   DesignScriptEntityID = x.ID,
                                   Code = x.Code,
                                   Title = x.Title,
                                   Description = x.Description,
                                   OrderFlag = x.OrderFlag,
                                   TypeFlag = x.TypeFlag,
                                   ParentID = x.ParentID != null ? x.ParentID : 0,
                               })
                               .ToListAsync();

        ;
        foreach (var item in designIntents.Select(x => new PackageDesignIntent
        {
            DesignScriptEntityID = x.DesignScriptEntityID,
            Code = x.Code,
            Title = x.Title,
            Description = x.Description,
            OrderFlag = x.OrderFlag,
            TypeFlag = x.TypeFlag,
            ParentID = x.ParentID,
            TypeValue = typeMasters.Any(a => a.Value == x.TypeFlag) ? typeMasters.FirstOrDefault(a => a.Value == x.TypeFlag).Title : "UNDEFINED",
            PackageID = package.ID,

        }))
        {
            package.DesignIntents.Add(item);
        }

        await db.SaveChangesAsync();


        var sharedService = new SharedService(db);
        var _attachments = await db.PackageAttachments.AsNoTracking()
         .Where(x => x.PackageID == package.ID)
         .Where(x => x.TypeFlag == 0)
         .Where(x => x.Filename.ToLower().Contains(".pdf"))
         .OrderBy(x => x.ID)
         .AnyAsync();

        if (_attachments)
        {

            var blobUrl = await GetSubmissionSet(package.ID);
            if (blobUrl != null)
            {


                package.CloudFile = blobUrl;
                package.IsSubmissionSetProcessed = true;

            }
            else
            {
                throw new EntityServiceException("Submision set processing failed!");
            }
        }

        var _meetingAgendas = await db.MeetingAgendas.AsNoTracking()
         .Include(x => x.Meeting)
         .Where(x => !x.IsVersion)
         .Where(x => x.PackageID != null
         && x.PackageID == package.ID).ToListAsync();



        var _tos = new List<string>();
        var _ccs = new List<string>();

        
            Regex myRegex = new Regex(McvConstant.EMAIL_REGEX, RegexOptions.None);

            if (package.EmailID != null)
            {
                foreach (Match myMatch in myRegex.Matches(package.EmailID))
                {
                    if (myMatch.Success && !_tos.Any(x => x.ToLower() == myMatch.Value.Trim().ToLower()))
                    {
                        _tos.Add(myMatch.Value.Trim());
                    }
                }
            }
            if (package.CC != null)
            {
                foreach (Match myMatch in myRegex.Matches(package.CC))
                {
                    if (myMatch.Success && !_tos.Any(x => x.ToLower() == myMatch.Value.Trim().ToLower()) && !_ccs.Any(x => x.ToLower() == myMatch.Value.Trim().ToLower()))
                    {
                        _ccs.Add(myMatch.Value.Trim());
                    }
                }
            }

            //CC TO STUDIO
            var _defaultCCList = await sharedService.GetPresetValue(McvConstant.PACKAGE_SUBMISSION_EMAIL_CC);
            if (_defaultCCList != null)
            {
                foreach (Match myMatch in myRegex.Matches(_defaultCCList))
                {
                    if (myMatch.Success && !_tos.Any(x => x.ToLower() == myMatch.Value.Trim().ToLower()) && !_ccs.Any(x => x.ToLower() == myMatch.Value.Trim().ToLower()))
                    {
                        _ccs.Add(myMatch.Value.Trim());
                    }
                }
            }

            var _project = await db.Projects.AsNoTracking()
              .SingleOrDefaultAsync(x => x.ID == package.ProjectID);

            var _code = _project.Code.ToString();
            TextInfo textInfo = new CultureInfo("en-IN", false).TextInfo;
            var _projectName = textInfo.ToTitleCase(_project.Title.ToLower());

            var _sheets = await db.PackageAttachments.AsNoTracking()
                .Where(x => x.PackageID == Id)
                .Where(x => x.Filename.ToLower().Contains(".pdf"))
                .OrderBy(x => x.OrderFlag)
                .ToListAsync();

            var _supportFiles = await db.PackageAttachments.AsNoTracking()
                                .Where(x => x.PackageID == Id)
                                .Where(x => x.TypeFlag == 0)
                                     .Where(x => !x.Filename.ToLower().Contains(".pdf") && !x.Filename.ToLower().Contains(".mp4"))
                                .OrderBy(x => x.OrderFlag)
                                .ToListAsync();

            var packageSubmissionURL = await sharedService.GetPresetValue(McvConstant.PACKAGE_SUBMISSION_URL_ROOT);
            var _submissionContent = GetSubmissionMailBody(
          ("SUBMISSION | " + _project.Title + " | " + package.Title).ToUpper(),
              _project.Title,
              _code,
              _firstContact,
              _firstContactLeaves,
              _secondContact,
              _secondContactLeaves,
              _tos,
              _ccs,
          ClockTools.GetIST(DateTime.UtcNow).ToString("dd MMM yyyy"),
          package.Title,
          package.SubmissionMessage,
          packageSubmissionURL + package.UID.ToString(),

          _sheets,
          _supportFiles,
          _meetingAgendas
          );



            package.SubmissionDate = DateTime.UtcNow;
            package.StatusFlag = McvConstant.PACKAGE_STATUSFLAG_SENT;

            if (_project.StatusFlag == McvConstant.PROJECT_STATUSFLAG_LOCKED)
            {
                _project.StatusFlag = McvConstant.PROJECT_STATUSFLAG_INPROGRESS;
                db.Entry(_project).State = EntityState.Modified;

                await db.SaveChangesAsync();
            }

        

        if (!skipAgendaUpdate)
        {
            await UpdateAgendaAfterSubmission(package, packageAssociations);

        }

        var projectService = new ProjectService(db);
        await projectService.RecordHistory(package.ProjectID);


        if (!skipEmail)
        {
            var _senderName = await sharedService.GetPresetValue(McvConstant.PACKAGE_SUBMISSION_EMAIL_SENDER_NAME);
            var _senderEmail = await sharedService.GetPresetValue(McvConstant.PACKAGE_SUBMISSION_EMAIL_SENDER_ID);


            var emailTo = new List<(string name, string email)>();
            foreach (var obj in _tos)
                emailTo.Add(("", obj));

            var emailCC = new List<(string name, string email)>();
            foreach (var obj in _ccs)
                emailCC.Add(("", obj));


            await sharedService.SendMail(("SUBMISSION | " + _project.Title + " | " + package.Title).ToUpper(),
              _senderName,
              _senderEmail,
              _submissionContent,
             emailTo, emailCC, null, _senderEmail, _senderName);
        }
    

    }

    public async Task UpdateAgendaAfterSubmission(Package package, List<PackageAssociation> packageAssociations)
    {
        var _agendas = await db.MeetingAgendas
            .Where(x => !x.IsVersion)
            .Where(x => !x.IsForwarded)
            .Where(x => x.StatusFlag == McvConstant.MEETING_AGENDA_STATUSFLAG_PENDING)
            .Where(x => x.PackageID != null
 && x.PackageID == package.ID).ToListAsync();
        var meetingService = new MeetingService(db);
        foreach (var agenda in _agendas)
        {

            if (agenda.Title == null || agenda.Title == String.Empty)
                agenda.Title = "NO TITLE";


            agenda.PreviousHistory = await meetingService.GetMeetingAgendaHistoryString(agenda);

            agenda.Comment = "SUBMISSION | " + package.Title + "\n" + package.SubmissionMessage;
            agenda.DueDate = package.SubmissionDate.Value.AddDays(3);

            //var _email = package.EmailID.Contains(',') ? package.EmailID.Substring(0, package.EmailID.IndexOf(',')) : package.EmailID.Trim();
            //var _contact = await db.Contacts.AsNoTracking()
            //    .Where(x => x.Email1.Equals(_email) || x.Email2.Equals(_email))
            //    .FirstOrDefaultAsync();

            //if (_contact != null)
            //{
            //    agenda.ActionBy = _contact.FullName;
            //    agenda.ActionByContactID = _contact.ID;
            //}
            //else
            //{
            //    agenda.ActionBy = packageAssociations.Where(x => x.TypeFlag == 0).FirstOrDefault().Contact.FullName;
            //    agenda.ActionByContactID = packageAssociations.Where(x => x.TypeFlag == 0).FirstOrDefault().Contact.ID;
            //}

            var agendaActionBy = packageAssociations.Where(x => x.TypeFlag == McvConstant.PACKAGE_ASSOCIATION_TYPEFLAG_AGENDA_ACTION_BY).FirstOrDefault();

            if (agendaActionBy != null && agendaActionBy.Contact != null)
            {
                agenda.ActionBy = agendaActionBy.Contact.FullName;
                agenda.ActionByContactID = agendaActionBy.Contact.ID;
            }
            else
            {
                var _email = package.ToRecipients.First();
                var _contact = await db.Contacts.AsNoTracking()
                    .Where(x => x.Email1==_email || x.Email2==_email)
                    .FirstOrDefaultAsync();

                if (_contact != null)
                {
                    agenda.ActionBy = _contact.FullName;
                    agenda.ActionByContactID = _contact.ID;
                }
                else
                {
                    agenda.ActionBy = packageAssociations.Where(x => x.TypeFlag == McvConstant.PACKAGE_ASSOCIATION_TYPEFLAG_PARTNER).FirstOrDefault().Contact.FullName;
                    agenda.ActionByContactID = packageAssociations.Where(x => x.TypeFlag == McvConstant.PACKAGE_ASSOCIATION_TYPEFLAG_PARTNER).FirstOrDefault().Contact.ID;
                }
            }

            if (agenda.DueDate != null)
            {
                var sharedService = new SharedService(db); ;
                agenda.DueDate = ClockTools.GetUTC(ClockTools.GetIST(agenda.DueDate.Value).Date.AddMinutes(await sharedService.GetBusinessEndMinutesIST()));
            }

            if (agenda.ActionByContactID == null) agenda.ActionBy = null;

            agenda.ReminderCount = 0;
            agenda.UpdateFrom = "SUBMISSION";


        }
        await db.SaveChangesAsync();
    }

    public async Task<List<int>> GetRecursiveChildrenIDs(List<int> _entityIDs)
    {
        return await db.DesignScriptEntities.AsNoTracking()
                                
                                .Where(x => _entityIDs.Any(i => i == x.ParentID))
                                .Select(x => x.ID)
                                .ToListAsync();
    }
    public async Task<string> AssignPackageTask(int PackageID, int StageIndex = 1, bool IsAttached = false)
    {

        var _package = await db.Packages.AsNoTracking()

            .Where(x => x.ID == PackageID).SingleOrDefaultAsync();

        if (_package == null) throw new EntityServiceException("Package not found");

        var _packageAssociations = await db.PackageAssociations.AsNoTracking()
            .Where(x => x.PackageID == _package.ID).ToListAsync();

        var _project = await db.Projects
            .AsNoTracking()
            .Include(x => x.Associations)
            .SingleOrDefaultAsync(x => x.ID == _package.ProjectID);

        if (_project == null) throw new EntityServiceException("Project not found!");

        if (!_project.Associations.Where(x => x.TypeFlag == 0).Any())
            throw new EntityServiceException("Project partner not found!");



        var _title = String.Empty;

        switch (StageIndex)
        {
            case 1:
                _title = $"{McvConstant.PACKAGE_TASK_STAGE_1}";
                break;

            case 3:
                _title = $"{McvConstant.PACKAGE_TASK_STAGE_3}";
                break;

            case 5:
                _title = $"{McvConstant.PACKAGE_TASK_STAGE_5}";
                break;

            case 6:
                _title = $"{McvConstant.PACKAGE_TASK_STAGE_6}";
                break;

            case 7:
                _title = $"{McvConstant.PACKAGE_TASK_STAGE_7}";
                break;
        }

        _package.ActiveStage = _title;

        var _assigneeContactIDs = new List<int>();

        if (StageIndex == 1)
        {

            if (_packageAssociations.Any())
            {
                foreach (var obj in _packageAssociations)
                {
                    var _lastTasks = await db.WFTasks.AsNoTracking()
                             .Where(x => x.Entity==nameof(Package)
                          && x.EntityID == _package.ID
                          && x.StageIndex == StageIndex)
                             .Where(x => x.ContactID == obj.ContactID).AnyAsync();

                    if (!_lastTasks && !_assigneeContactIDs.Any(x => x == obj.ContactID))
                        _assigneeContactIDs.Add(obj.ContactID);
                }
            }

        }
        else if (StageIndex == 5 || StageIndex == 7)
        {
            if (_packageAssociations.Where(x => x.TypeFlag == 0 || x.TypeFlag == 1).Any())
            {
                foreach (var obj in _packageAssociations.Where(x => x.TypeFlag == 0 || x.TypeFlag == 1))
                {
                    if (!_assigneeContactIDs.Any(x => x == obj.ContactID))
                        _assigneeContactIDs.Add(obj.ContactID);
                }
            }
        }
        else if (StageIndex == 6)
        {
            if (_packageAssociations.Where(x => x.TypeFlag == 0).Any())
            {
                foreach (var obj in _packageAssociations.Where(x => x.TypeFlag == 0))
                {
                    if (!_assigneeContactIDs.Any(x => x == obj.ContactID))
                        _assigneeContactIDs.Add(obj.ContactID);
                }
            }
            if (_packageAssociations.Where(x => x.TypeFlag == 2).Any())
            {
                foreach (var obj in _packageAssociations.Where(x => x.TypeFlag == 2))
                {
                    if (!_assigneeContactIDs.Any(x => x == obj.ContactID))
                        _assigneeContactIDs.Add(obj.ContactID);
                }
            }
        }

        var _taskEnd = await GetPackageTaskDueDate(_package.StartDate, _package.FinalDate, StageIndex);


        var taskService = new WFTaskService(db);
        foreach (var id in _assigneeContactIDs)
        {
            var _lastTask = await db.WFTasks.AsNoTracking()
          .Where(x => x.Entity==nameof(Package)
       && x.EntityID == _package.ID
       && x.StageIndex == StageIndex
       && x.StatusFlag == McvConstant.WFTASK_STATUSFLAG_COMPLETED)
          .Where(x => x.ContactID == id)
          .OrderByDescending(x => x.Created)
          .FirstOrDefaultAsync();

            var _nextRevision = 0;
            if (_lastTask != null)
                _nextRevision = _lastTask.StageRevision + 1;

            try
            {
                var _task = new WFTask
                {
                    ContactID = id,
                    Entity = nameof(Package),
                    EntityID = _package.ID,
                    StartDate = DateTime.UtcNow,
                    DueDate = _taskEnd,
                    Title = _title,
                    Subtitle = _project.Title + " | " + _package.Title,
                    StageIndex = StageIndex,
                    StageRevision = _nextRevision,
                };

                await taskService.CreateTask(_task);
            }
            catch (Exception e)
            {
                //skipped
            }
        }


        if (!IsAttached)
            db.Entry(_package).State = EntityState.Modified;
        await db.SaveChangesAsync();

        return _package.ActiveStage;

    }
    public string GetSubmissionMailBody(string Subject,
                                        string ProjectTitle,
                                        string ProjectCode,
                                        string FirstContact,
                                        string FirstContactLeaves,
                                        string SecondContact,
                                        string SecondContactLeaves,
                                        IEnumerable<String> Tos,
                                        IEnumerable<String> CCs,
                                        string SubmissionDate,
                                        string SubmissionTitle,
                                        string Message,
                                        string DownloadUrl,
                                        IEnumerable<PackageAttachment> PdfFiles,
                                        IEnumerable<PackageAttachment> SupportFiles,
                                        IEnumerable<MeetingAgenda> MeetingAgendas = null)
    {
        //Create a new StringBuilder object
        StringBuilder sb = new StringBuilder();

        sb.AppendLine("<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Transitional//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd\">");
        sb.AppendLine("<html xmlns=\"http://www.w3.org/1999/xhtml\">");
        sb.AppendLine("<head>");
        sb.AppendLine("    <meta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\" />");
        sb.AppendLine("    <title>Email Design</title>");
        sb.AppendLine("    <meta name=\"viewport\" content=\"width=device-width; initial-scale=1.0;\" />");
        sb.AppendLine("    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=9; IE=8; IE=7; IE=EDGE\" />");
        sb.AppendLine("    <meta name=\"format-detection\" content=\"telephone=no\" />");
        sb.AppendLine("    <!--[if gte mso 9]><xml>");
        sb.AppendLine("    <o:OfficeDocumentSettings>");
        sb.AppendLine("    <o:AllowPNG />");
        sb.AppendLine("    <o:PixelsPerInch>96</o:PixelsPerInch>");
        sb.AppendLine("    </o:OfficeDocumentSettings>");
        sb.AppendLine("    </xml><![endif]-->");
        sb.AppendLine("    <style type=\"text/css\">");
        sb.AppendLine("        /* Some resets and issue fixes */");
        sb.AppendLine("        #outlook a {");
        sb.AppendLine("            padding: 0;");
        sb.AppendLine("        }");
        sb.AppendLine("");
        sb.AppendLine("        body {");
        sb.AppendLine("            width: 100% !important;margin:0;");
        sb.AppendLine("            -webkit-text-size-adjust: 100%;");
        sb.AppendLine("            -ms-text-size-adjust: 100%;");
        sb.AppendLine("        }");

        sb.AppendLine("        table{");
        sb.AppendLine("            mso-table-lspace: 0px;");
        sb.AppendLine("            mso-table-rspace: 0px;");
        sb.AppendLine("        }");
        sb.AppendLine("");
        sb.AppendLine("        table td {");
        sb.AppendLine("            border-collapse: collapse;");
        sb.AppendLine("        }");
        sb.AppendLine("");
        sb.AppendLine("        .ExternalClass * {");
        sb.AppendLine("            line-height: 115%;");
        sb.AppendLine("        }");
        sb.AppendLine("        /* End reset */");


        sb.AppendLine("    </style>");
        sb.AppendLine("</head>");
        sb.AppendLine("");
        sb.AppendLine("<body>");
        sb.AppendLine("");

        sb.AppendLine("");
        sb.AppendLine("    <div style=\"margin: 0 auto;font-family:Calibri;font-size:14px;line-height:1.8;padding-left:5px;padding-right:5px; max-width:500px;\">");
        sb.AppendLine("");
        //HEADER
        sb.AppendLine("        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; border-collapse: collapse;\">");
        sb.AppendLine("            <tr>");
        sb.AppendLine("                <td style=\"font-size:16px; font-weight:bold;\">");
        sb.AppendLine(Subject);
        sb.AppendLine("                </td>");
        sb.AppendLine("            </tr>");
        sb.AppendLine("        </table>");
        sb.AppendLine("<div style=\"border-bottom: 1px solid #cccccc; margin-top: 15px; margin-bottom: 15px; \"></div>");

        //--------------------//

        sb.AppendLine("        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; border-collapse: collapse;\">");
        sb.AppendLine("            <tr>");
        sb.AppendLine("                <td valign=\"top\" width=\"120\" style=\"font-weight:bold;padding-bottom:5px;\">");
        sb.AppendLine("                    Date:");
        sb.AppendLine("                </td>");
        sb.AppendLine("                <td valign=\"top\" style=\"padding-bottom:5px;\">");
        sb.AppendLine(SubmissionDate);
        sb.AppendLine("                </td>");
        sb.AppendLine("            </tr>");
        sb.AppendLine("            <tr>");
        sb.AppendLine("                <td valign=\"top\" width=\"120\" style=\"font-weight:bold;padding-bottom:5px;\">");
        sb.AppendLine("                    Project Name:");
        sb.AppendLine("                </td>");
        sb.AppendLine("                <td valign=\"top\" style=\"padding-bottom:5px;\">");
        sb.AppendLine(ProjectTitle);
        sb.AppendLine("                </td>");
        sb.AppendLine("            </tr>");
        sb.AppendLine("            <tr>");
        sb.AppendLine("                <td valign=\"top\" width=\"120\" style=\"font-weight:bold;padding-bottom:5px;\">");
        sb.AppendLine("                    Project Code:");
        sb.AppendLine("                </td>");
        sb.AppendLine("                <td valign=\"top\" style=\"padding-bottom:5px;\">");
        sb.AppendLine(ProjectCode);
        sb.AppendLine("                </td>");
        sb.AppendLine("            </tr>");
        sb.AppendLine("        </table>");
        sb.AppendLine("");
        sb.AppendLine("        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; border-collapse: collapse;\">");
        sb.AppendLine("            <tr>");
        sb.AppendLine("                <td valign=\"top\" width=\"120\"  style=\"font-weight:bold;\">");
        sb.AppendLine("                    From:");
        sb.AppendLine("                </td>");
        sb.AppendLine("                <td valign=\"top\" >");
        sb.AppendLine("<div style=\"line-height: 1;\">" + FirstContact + "</div> ");
        sb.AppendLine($"<small><i>{FirstContactLeaves}</i></small>");
        sb.AppendLine("<div style=\"line-height: 1;margin-top: 3px;\">" + SecondContact + "</div> ");
        sb.AppendLine($"<small><i>{SecondContactLeaves}</i></small>");
        sb.AppendLine("                </td>");
        sb.AppendLine("            </tr>");

        //sb.AppendLine("            <tr>");
        //sb.AppendLine("                <td valign=\"top\" style=\"padding-bottom:5px;\">");
        //sb.AppendLine(FirstContact + "; " + SecondContact + "; ");
        //sb.AppendLine("                </td>");
        //sb.AppendLine("                <td valign=\"top\"  >");

        //sb.AppendLine("                </td>");
        //sb.AppendLine("            </tr>");

        sb.AppendLine("            <tr>");
        sb.AppendLine("                <td valign=\"top\" width=\"120\" style=\"font-weight:bold;\">");
        sb.AppendLine("                    To:");
        sb.AppendLine("                </td>");
        //sb.AppendLine("            </tr>");

        //sb.AppendLine("            <tr>");
        sb.AppendLine("                <td valign=\"top\" style=\"padding-bottom:5px;\">");
        foreach (var item in Tos)
        {
            sb.Append(item + "; ");
        }
        sb.AppendLine("                </td>");
        sb.AppendLine("            </tr>");

        sb.AppendLine("            <tr>");
        sb.AppendLine("                <td valign=\"top\" width=\"120\"  style=\"font-weight:bold;\">");
        sb.AppendLine("                    CC:");
        sb.AppendLine("                </td>");
        //sb.AppendLine("            </tr>");

        //sb.AppendLine("            <tr>");
        sb.AppendLine("                <td valign=\"top\" style=\"padding-bottom:5px;\">");
        foreach (var item in CCs)
        {
            sb.Append(item + "; ");
        }
        sb.AppendLine("                </td>");
        sb.AppendLine("            </tr>");

        sb.AppendLine("        </table>");
        sb.AppendLine("<div style=\"border-bottom: 1px solid #cccccc; margin-top: 15px; margin-bottom: 15px; \"></div>");
        sb.AppendLine("        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; border-collapse: collapse;\">");
        sb.AppendLine("            <tr>");
        sb.AppendLine("                <td valign=\"top\"  style=\"font-weight:bold;\">");
        sb.AppendLine("                    ");
        sb.AppendLine("                </td>");
        sb.AppendLine("            </tr>");

        sb.AppendLine("            <tr>");
        sb.AppendLine("                <td valign=\"top\" style=\"padding-bottom:5px;\">");
        sb.AppendLine("                    <pre style=\"font-family:Calibri;line-height:1.5; font-size: 16px;margin-top:0;margin-bottom:0; white-space: pre-wrap; white-space: -moz-pre-wrap;");
        sb.AppendLine("                                white-space: -pre-wrap; white-space: -o-pre-wrap; word-wrap: break-word;\">" + Message);
        sb.AppendLine("</pre>");
        sb.AppendLine("                </td>");
        sb.AppendLine("            </tr>");
        sb.AppendLine("        </table>");
        sb.AppendLine("");

        sb.AppendLine("<div style=\"border-bottom: 1px solid #cccccc; margin-top: 15px; margin-bottom: 15px; \"></div>");

        if (MeetingAgendas != null && MeetingAgendas.Any())
        {
            sb.AppendLine("        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; border-collapse: collapse;\">");
            //sb.AppendLine("            <tr>");
            //sb.AppendLine("                <td style=\"font-weight:bold;\">");
            //sb.AppendLine("                    History:");
            //sb.AppendLine("                </td>");
            //sb.AppendLine("            </tr>");
            var _index = 1;
            foreach (var _item in MeetingAgendas)
            {
                sb.AppendLine("            <tr>");
                sb.AppendLine("                <td>");
                sb.AppendLine("<span style=\"font-weight: 600;\">Package Agenda " + _index + "</span>");
                sb.AppendLine("                </td>");
                sb.AppendLine("            </tr>");
                sb.AppendLine("            <tr>");
                sb.AppendLine("                <td>");
                sb.AppendLine(_item.Title + " " + _item.Subtitle);
                sb.AppendLine("                </td>");
                sb.AppendLine("            </tr>");

                sb.AppendLine("            <tr>");
                sb.AppendLine("                <td>");
                sb.AppendLine("<span style=\"font-style:italic;\">[" + ClockTools.GetIST(_item.Meeting.StartDate).ToString("dd MMM yyyy HH:mm") + " " + _item.CreatedBy + "]</span>");
                sb.AppendLine("                </td>");
                sb.AppendLine("            </tr>");

                sb.AppendLine("            <tr>");
                sb.AppendLine("                <td>");
                sb.AppendLine("                    <pre style=\"font-family:Calibri; font-size: 14px;margin-top:0;margin-bottom:0; white-space: pre-wrap; white-space: -moz-pre-wrap;white-space: -pre-wrap; white-space: -o-pre-wrap; word-wrap: break-word;\">");
                sb.AppendLine(_item.Comment);
                sb.AppendLine("</pre>");
                sb.AppendLine("                </td>");
                sb.AppendLine("            </tr>");

                sb.AppendLine("            <tr>");
                sb.AppendLine("                <td >");
                sb.AppendLine("Action By: " + _item.ActionBy);
                if (_item.DueDate != null)
                {
                    sb.AppendLine(" | Due Date: " + ClockTools.GetIST(_item.DueDate.Value).ToString("dd MMM yyyy"));
                }
                sb.AppendLine("                </td>");
                sb.AppendLine("            </tr>");

                sb.AppendLine("            <tr>");
                sb.AppendLine("                <td>");
                sb.AppendLine("                    <pre style=\"font-style: italic;font-family: Calibri;font-size: 11px;margin-left:20px;margin-top:0;margin-bottom:0; white-space: pre-wrap; white-space: -moz-pre-wrap;white-space: -pre-wrap; white-space: -o-pre-wrap; word-wrap: break-word;\">");
                sb.AppendLine(_item.PreviousHistory);
                sb.AppendLine("</pre>");
                sb.AppendLine("                </td>");
                sb.AppendLine("            </tr>");

                _index++;
            }
            sb.AppendLine("        </table>");
            sb.AppendLine("");
        }
        sb.AppendLine("<div style=\"border-bottom: 1px solid #cccccc; margin-top: 15px; margin-bottom: 15px; \"></div>");

        sb.AppendLine("        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; border-collapse: collapse;\">");
        sb.AppendLine("            <tr>");
        sb.AppendLine("                <td style=\"font-weight:bold;\">");
        sb.AppendLine("                    Submission Set:");
        sb.AppendLine("                </td>");
        sb.AppendLine("            </tr>");

        sb.AppendLine("            <tr>");
        sb.AppendLine("                <td>");
        sb.AppendLine(SubmissionTitle);
        sb.AppendLine("                </td>");
        sb.AppendLine("            </tr>");
        sb.AppendLine("        </table>");
        sb.AppendLine("");

        sb.AppendLine("        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\" width:100%; border-collapse: collapse; margin-left:15px\">");
        foreach (var item in PdfFiles)
        {
            foreach (var attr in item.Attributes)
            {
                sb.AppendLine("            <tr>");
                sb.AppendLine("                <td>");
                sb.AppendLine(attr.Title);
                sb.AppendLine("                </td>");
                sb.AppendLine("                <td>");
                sb.AppendLine(attr.SheetNo);
                sb.AppendLine("                </td>");
                sb.AppendLine("            </tr>");
                sb.AppendLine("            <tr>");
                sb.AppendLine("                <td style=\"padding-left:10px;\" colspan=\"2\"> <i>");
                sb.AppendLine(attr.Description);
                sb.AppendLine("            </i>  </td>");
                sb.AppendLine("            </tr>");
            }
        }

        sb.AppendLine("        </table>");
        sb.AppendLine("");
        sb.AppendLine("        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; border-collapse: collapse;\">");
        sb.AppendLine("            <tr>");
        sb.AppendLine("                <td style=\"font-weight:bold;padding-top:10px;\">");
        sb.AppendLine("                    Support Files:");
        sb.AppendLine("                </td>");
        sb.AppendLine("            </tr>");
        foreach (var item in SupportFiles)
        {
            sb.AppendLine("            <tr>");
            sb.AppendLine("                <td>");
            sb.AppendLine(item.Filename);
            sb.AppendLine("                </td>");
            sb.AppendLine("            </tr>");
        }
        sb.AppendLine("        </table>");
        sb.AppendLine("");
        sb.AppendLine("<div style=\"border-bottom: 1px solid #cccccc; margin-top: 15px; margin-bottom: 15px; \"></div>");
        sb.AppendLine("        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; border-collapse: collapse;\">");



        sb.AppendLine("            <tr>");
        sb.AppendLine("                <td style=\"font-weight:bold;\">");
        sb.AppendLine("                    <a href=\"" + DownloadUrl + "\">SUBMISSION</a>");

        sb.AppendLine("                </td>");
        sb.AppendLine("            </tr>");
        sb.AppendLine("            <tr>");
        sb.AppendLine("                <td style=\"font-size:11px;color:#808080;\">");
        sb.AppendLine(" To view, kindly click the above link. ");
        sb.AppendLine("                </td>");
        sb.AppendLine("            </tr>");
        sb.AppendLine("        </table>");
        //FOOTER
        sb.AppendLine("<div style=\"border-bottom: 1px solid #cccccc; margin-top: 15px; margin-bottom: 15px; \"></div>");
        sb.AppendLine("        <table align=\"center\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; border-collapse: collapse;font-size:11px;\">");

        sb.AppendLine("");
        sb.AppendLine("            <tr>");
        sb.AppendLine("");
        sb.AppendLine("                <td align=\"center\" >");
        sb.AppendLine("This is a <b>MyCockpitView<sup>&copy;</sup></b> & <b>DesignScript<sup>&copy;</sup></b> generated e-mail for your information and necessary action.");
        sb.AppendLine("</td>");
        sb.AppendLine("            </tr>");
        sb.AppendLine("");
        sb.AppendLine("            <tr>");
        sb.AppendLine("");
        sb.AppendLine("                <td align=\"center\" >");
        sb.AppendLine("");
        sb.AppendLine("                    Powered by <b>Newarch<sup>&reg;</sup> Infotech LLP</b>");
        sb.AppendLine("");
        sb.AppendLine("                </td>");
        sb.AppendLine("            </tr>");
        sb.AppendLine("        </table>");
        sb.AppendLine("    </div>");

        sb.AppendLine("</body>");
        sb.AppendLine("");
        sb.AppendLine("</html>");


        return sb.ToString();
    }





    public IQueryable<UpcomingPackageDto> GetUpcomingPackages(IEnumerable<QueryFilter> Filters = null,
   string Search = null,
   string Sort = null)
    {
        var projectScopes = db.ProjectScopeServices.AsNoTracking();
        var projects = db.Projects.AsNoTracking();
        var designScriptEntities = db.DesignScriptEntities.AsNoTracking();
        var packages = db.Packages.AsNoTracking();

        var _query = projectScopes
          .Include(x => x.ProjectScope)
                   .Select(x => new
                   {
                       x.ProjectID,
                       x.ProjectScopeID,
                       Scope = x.ProjectScope.Title,
                       Service = x.Abbreviation,
                       ServiceOrder = x.OrderFlag
                   })
            .GroupJoin(designScriptEntities
                            
                            .Where(x => x.ProjectScopeID != null)
                        .Select(x => new
                        {
                            PhaseID = x.ID,
                            x.ProjectID,
                            x.ProjectScopeID,
                            Phase = x.Code + "-" + x.Title,
                        }),
                                        s => s.ProjectScopeID,
                                        p => p.ProjectScopeID,
                                        (s, p) => new { stageService = s, phases = p })
            .SelectMany(x => x.phases.DefaultIfEmpty(),
                        (x, y) => new
                        {
                            x.stageService.ProjectID,
                            x.stageService.ProjectScopeID,
                            x.stageService.Scope,
                            y.PhaseID,
                            y.Phase,
                            x.stageService.Service,
                            x.stageService.ServiceOrder,
                        })
            .Where(x => !packages
                                            .Where(p => p.TypeFlag == McvConstant.PACKAGE_TYPEFLAG_ACTIVE
                                                        || p.TypeFlag == McvConstant.PACKAGE_TYPEFLAG_EMPTY)
                                             .Where(c => c.DesignScriptEntityID == x.PhaseID)
                                            .Select(p => new
                                            {
                                                p.ProjectID,
                                                PhaseID = p.DesignScriptEntityID,
                                                p.Stage,
                                                Package = p.Title,

                                            })

                                          .Select(c => c.Stage).Any(c => c == x.Service)
                              )
              .GroupBy(x => new { x.ProjectID, x.ProjectScopeID, x.PhaseID })
            .Select(s => new
            {
                s.Key.ProjectID,
                s.OrderBy(x => x.ServiceOrder).FirstOrDefault().ProjectScopeID,
                s.OrderBy(x => x.ServiceOrder).FirstOrDefault().Scope,
                s.OrderBy(x => x.ServiceOrder).FirstOrDefault().Phase,
                s.OrderBy(x => x.ServiceOrder).FirstOrDefault().Service,


            })
            .Join(projects
                             .Include(x => x.Associations).ThenInclude(c => c.Contact)
                            .Include(x => x.CompanyAccount)
                            .Where(x => x.Code!="N/A")
                            .GroupJoin(db.StatusMasters.Where(s => s.Entity==nameof(Project)),
                            project => project.StatusFlag,
                            status => status.Value,
                            (x, y) => new { project = x, statuses = y })
                            .SelectMany(
                   x => x.statuses.DefaultIfEmpty(),
                   (x, y) => new
                   {
                       x.project.ID,
                       CompanyID = x.project.CompanyID,
                       Company = x.project.CompanyAccount.Title,
                       Project = x.project.Code + "-" + x.project.Title,
                       PartnerContactID = x.project.Associations.Where(a => a.TypeFlag == 0).Any() ? x.project.Associations.Where(a => a.TypeFlag == 0).FirstOrDefault().ContactID : (int?)null,
                       Partner = x.project.Associations.Where(a => a.TypeFlag == 0).Any() ? x.project.Associations.Where(a => a.TypeFlag == 0).FirstOrDefault().Contact.FullName : "Not Assigned",
                       Status = y.Title,
                       AssociateContactID = x.project.Associations.Where(a => a.TypeFlag == 1).Any() ? x.project.Associations.Where(a => a.TypeFlag == 1).FirstOrDefault().ContactID : (int?)null,
                       Associate = x.project.Associations.Where(a => a.TypeFlag == 1).Any() ? x.project.Associations.Where(a => a.TypeFlag == 1).FirstOrDefault().Contact.FullName : "Not Assigned",
                       Client = x.project.ClientContact != null ? x.project.ClientContact.FullName : "Not Assigned",
                       Created = x.project.Created,
                       x.project.ContractCompletionDate,
                       x.project.ExpectedCompletionDate,
                       Fee = x.project.CompanyFee,
                   }),
                s => s.ProjectID,
                p => p.ID,
                (s, p) => new UpcomingPackageDto
                {
                    CompanyID = p.CompanyID,
                    Company = p.Company,
                    Project = p.Project,
                    Scope = s.Scope,
                    Phase = s.Phase,
                    Service = s.Service,
                    Status = p.Status,
                    PartnerContactID = p.PartnerContactID,
                    Partner = p.Partner,
                    AssociateContactID = p.AssociateContactID,
                    Associate = p.Associate,
                    Client = p.Client,
                    Fee = p.Fee,
                });



        //Apply filters
        if (Filters != null)
        {


            if (Filters.Where(x => x.Key.Equals("status", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<UpcomingPackageDto>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("status", StringComparison.OrdinalIgnoreCase)))
                {

                    predicate = predicate.Or(x => x.Status == _item.Value);
                }
                _query = _query.Where(predicate);
            }

            if (Filters.Where(x => x.Key.Equals("partner", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<UpcomingPackageDto>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("partner", StringComparison.OrdinalIgnoreCase)))
                {

                    predicate = predicate.Or(x => x.Partner == _item.Value);
                }
                _query = _query.Where(predicate);
            }

            if (Filters.Where(x => x.Key.Equals("service", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<UpcomingPackageDto>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("service", StringComparison.OrdinalIgnoreCase)))
                {

                    predicate = predicate.Or(x => x.Service == _item.Value);
                }
                _query = _query.Where(predicate);
            }

            if (Filters.Where(x => x.Key.Equals("companyID", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<UpcomingPackageDto>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("companyID", StringComparison.OrdinalIgnoreCase)))
                {

                    var _value = Convert.ToInt32(_item.Value);
                    predicate = predicate.Or(x => x.CompanyID == _value);
                }
                _query = _query.Where(predicate);
            }

            if (Filters.Where(x => x.Key.Equals("partnerContactID", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<UpcomingPackageDto>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("partnerContactID", StringComparison.OrdinalIgnoreCase)))
                {

                    var _value = Convert.ToInt32(_item.Value);
                    predicate = predicate.Or(x => x.PartnerContactID == _value);
                }
                _query = _query.Where(predicate);
            }

            if (Filters.Where(x => x.Key.Equals("AssociateContactID", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<UpcomingPackageDto>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("AssociateContactID", StringComparison.OrdinalIgnoreCase)))
                {

                    var _value = Convert.ToInt32(_item.Value);
                    predicate = predicate.Or(x => x.AssociateContactID == _value);
                }
                _query = _query.Where(predicate);
            }

        }

        if (Search != null && Search != String.Empty)
        {
            _query = _query
          .Where(x => x.Project.Contains(Search)
                    || x.Partner.Contains(Search)
                    || x.Scope.Contains(Search)
                    || x.Service.Contains(Search.ToLower())
                    );
        }

        if (Sort != null && Sort != String.Empty)
        {
            var _orderedQuery = _query.OrderBy(l => 0);
            var keywords = Sort.Replace("asc", "").Split(',');

            foreach (var key in keywords)
            {
                if (key.Trim().Equals("project", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenBy(x => x.Project);
                else if (key.Trim().Equals("partner", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenByDescending(x => x.Partner);
                else if (key.Trim().Equals("status", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenByDescending(x => x.Status);
                else if (key.Trim().Equals("service", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenByDescending(x => x.Service);
            }

            return _orderedQuery;
        }

        return _query.OrderBy(x => x.Project).ThenBy(x => x.Scope);
    }

    public async Task<byte[]> GetUpcomingPackagesExcel(IEnumerable<QueryFilter>? Filters = null, string? Search = null, string? Sort = null)
    {

        var _dataSet = new DataSet();

        var data = await GetUpcomingPackages(Filters, Search, Sort).ToListAsync();
        _dataSet.Tables.Add(DataTools.ToDataTable(data));

        return ExcelUtility.ExportExcel(_dataSet);

    }

}

public class SubmissionDirectory
{
    public string Phase { get; set; }
    public int PhaseOrder { get; set; }
    public string Code { get; set; }
    public string Title { get; set; }
    public string SubmissionDate { get; set; }
    public bool IsLatest { get; set; }
    public string QRCode { get; set; }
    public string Url { get; set; }
}

public class SubmissionDirectorySheet
{
    public string Code { get; set; }
    public string SheetNo { get; set; }
    public string Description { get; set; }
    public string Title { get; set; }
}

public class PackageAnalysis
{
    public string Package { get; set; }
    public string Phase { get; set; }
    public string Stage { get; set; }
    public string Scope { get; set; }
    public decimal ScopePercentage { get; set; }
    public decimal ScopeAmount { get; set; }
    public string Revision { get; set; }
    public string Project { get; set; }

    public int ProjectStatusFlag { get; set; }

    public string ProjectStatus { get; set; }
    public string Partner { get; set; }
    public string Associate { get; set; }

    public decimal VHrRate { get; set; }
    public decimal VHrAssigned { get; set; }
    public decimal VHrConsumed { get; set; }

    public decimal VHrAssignedCost { get; set; }
    public decimal VHrConsumedCost { get; set; }
    public decimal VHrBalance { get; set; }

    public decimal VHrBalanceCost { get; set; }

    public decimal AssignedTaskCount { get; set; }

    public decimal AssignedTaskVHr { get; set; }

    public decimal CompletedTaskCount { get; set; }
    public decimal CompletedTaskVHr { get; set; }
    public decimal AssessedTaskCount { get; set; }
    public decimal AssessedTaskVHr { get; set; }
    public DateTime StartDate { get; set; }
    public DateTime FinalDate { get; set; }
    public DateTime SubmissionDate { get; set; }
    public decimal Delay { get; set; }
    public int ProjectID { get; set; }
    public int PackageID { get; set; }
    public int? PartnerContactID { get; set; }
    public int? AssociateContactID { get; set; }
    public string Status { get; set; }
    public int StatusFlag { get; set; }
    public string ActiveStage { get; set; }

    public DateTime? ProposedFinalDate { get; set; }
    public DateTime? ProposedStartDate { get; set; }
    public decimal ProposedVHrAssigned { get; set; }
    public decimal ProposedVHrAssignedCost { get; set; }
    public string ProposedPriority { get; set; }
    public decimal ProposedProbablity { get; set; }

    public decimal StageServicePercentage { get; set; }

    public decimal StageServiceAmount { get; set; }

    public decimal DValue { get; set; }

    public decimal RValue { get; set; }

    public int TypeFlag { get; set; }
    public string TypeValue { get; set; }


}


