﻿using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.Services;
using MyCockpitView.WebApi.WFStageModule.Entities;
using MyCockpitView.WebApi.WFTaskModule.Entities;

namespace MyCockpitView.WebApi.WFTaskModule.Services;

public interface IWFTaskService: IBaseEntityService<WFTask>
{
    Task AssignLeaveTasks(int LeaveID);
    Task<string> AssignPackageTask(int PackageID, int StageIndex = 1, bool IsAttached = false);
    Task AutoPauseTasks();
    Task CheckTaskCompletedDate();
    Task CompletePackageTasks(int WFTaskID, string taskStatus);
    Task CompleteTaskStage(int WFTaskID, bool IsSkipSystemStage = false);
    Task CompleteWFStage(string Entity, int EntityID, string StageCode, int WFTaskID);
    Task<int> CreateManual(WFTask Entity);
    Task<int> CreateTask(WFTask task, bool UseTime = false, bool AllowMultiple = false, bool AllowPreviousRevision = false, string? PreviousStageCode = null, int? PreviousStageRevision = null, int? PreviousTaskID = null);
    Task<WFTask> GetActiveStage(string Entity, int EntityID);
    string GetActivityStatus(WFTask _wfTask);
    int GetActivityStatusFlag(WFTask Task);
    Task<IEnumerable<WFTaskAnalysisDto>> GetAnalysisData(IEnumerable<QueryFilter>? Filters = null, string? Search = null, string? Sort = null);
    Task<WFTaskAnalysisDto> GetAnalysisDataTotal(IEnumerable<QueryFilter>? Filters = null, string? Search = null, string? Sort = null);
    Task<byte[]> GetAnalysisExcel(IEnumerable<QueryFilter>? Filters = null, string? Search = null, string? Sort = null);
    double GetDuration(DateTime Created, DateTime DueDate);
    Task<IEnumerable<string>> GetEntityOptions();
    Task<int> GetPendingAssessmentCount(int TaskID);
    Task<WFTask> GetTaskByStage(string Entity, int EntityID, string StageCode, DateTime? FollowUpDate = null);
    Task<IEnumerable<WFTaskVHrDto>> GetTaskVHrByEntity(string EntityName, IEnumerable<QueryFilter>? Filters = null, string? Search = null, string? Sort = null);
    Task<IEnumerable<WFTaskVHrAnalysisDto>> GetVHrAnalysisData(IEnumerable<QueryFilter>? Filters = null, string? Search = null, string? Sort = null);
    Task<IEnumerable<WFTaskVHrAnalysisDto>> GetVHrAnalysisDatabyPeriod(string Period, IEnumerable<QueryFilter>? Filters = null, string? Search = null, string? Sort = null);
    Task<byte[]> GetVHrAnalysisExcel(IEnumerable<WFTaskVHrAnalysisDto> Data);
    Task HandleParallelTasks(int CurrentTaskID);
    Task<bool> IsAllApproved(WFTask obj, int RequiredCount = 0);
    Task<bool> IsAllTasksCompleted(WFTask obj, int RequiredCount = 0);
    Task PauseOtherActiveTasks(int TaskID, int ContactID);
    Task PurgePendingTasks(string Entity, int EntityID);
    Task PurgeTasks(string Entity, int EntityID);
    Task RecalculateVHrData(WFTask wfTask);
    Task StartFlow(string Entity, int EntityTypeFlag, int EntityID, WFStage? Stage = null);
    Task StartPackageFlow(int packageID);
    Task Update(WFTask UpdatedTask, IEnumerable<Assessment>? Assessments = null);
    Task UpdatePackageTaskDue(int ID, DateTime StartDate, DateTime FinalDate);
    Task UpdateTaskDue(string Entity, int EntityID);
    Task ValidateTask(WFTask Entity, bool isUpdate = false, bool AllowPreviousRevision = false, bool AllowMultiple = false);
}