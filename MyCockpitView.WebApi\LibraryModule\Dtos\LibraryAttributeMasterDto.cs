﻿using AutoMapper;
using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.LibraryModule.Entities;
using System.ComponentModel.DataAnnotations;

namespace MyCockpitView.WebApi.LibraryModule.Dtos;

public class LibraryAttributeMasterDto : BaseEntityDto
{
    [Required]
    [StringLength(255)]
    public string Category { get; set; }

    [Required]
    [StringLength(255)]
    public string Attribute { get; set; }
}

public class LibraryAttributeMasterDtoMapperProfile : Profile
{
    public LibraryAttributeMasterDtoMapperProfile()
    {
        CreateMap<LibraryAttributeMaster, LibraryAttributeMasterDto>()
             .ReverseMap();

    }
}