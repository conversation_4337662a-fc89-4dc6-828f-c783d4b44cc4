﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using MyCockpitView.WebApi.ContactModule.Entities;
using MyCockpitView.CoreModule;

using System.ComponentModel.DataAnnotations;

namespace MyCockpitView.WebApi.InspectionModule.Entities;

public class Inspection : BaseEntity
{
    public int? ParentID { get; set; }
   
    [Required]
    [StringLength(255)]
    public string? Title { get; set; }


    [StringLength(255)]
    public string? Code { get; set; }

    public DateTime StartDate { get; set; }

    public DateTime EndDate { get; set; }

    [StringLength(255)]
    public string? Location { get; set; }
    public DateTime? ClosedOn { get; set; }
    public DateTime? FinalizedOn { get; set; }

    [Precision(18,2)] public decimal Version { get; set; }
    public int ContactID { get; set; }
    public virtual Contact? Contact { get; set; }
    public virtual ICollection<InspectionRecipient> Recipients { get; set; }= new HashSet<InspectionRecipient>();

    public virtual ICollection<InspectionItem> Items { get; set; }= new HashSet<InspectionItem>();

    public int? ProjectID { get; set; }

    public int? FunctionID { get; set; }

}

public class InspectionConfiguration : BaseEntityConfiguration<Inspection>, IEntityTypeConfiguration<Inspection>
{
    public void Configure(EntityTypeBuilder<Inspection> builder)
    {
      
        base.Configure(builder);

        builder.HasIndex(e => e.Title);
        builder.HasIndex(e => e.Code);
        builder.HasIndex(e => e.StartDate);
        builder.HasIndex(e => e.EndDate);
        builder.HasIndex(e => e.ProjectID);

    }
}