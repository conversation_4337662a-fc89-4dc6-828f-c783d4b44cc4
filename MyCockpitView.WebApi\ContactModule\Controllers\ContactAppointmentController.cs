﻿
using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.WebApi.Responses;
using MyCockpitView.WebApi.ContactModule.Dtos;
using MyCockpitView.WebApi.ContactModule.Entities;
using MyCockpitView.WebApi.ContactModule.Services;
using MyCockpitView.Utility.Common;
using MyCockpitView.WebApi.ActivityModule;
using MyCockpitView.CoreModule;
using MediatR;
using MyCockpitView.WebApi.ContactModule.Events;

namespace MyCockpitView.WebApi.ContactModule.Controllers;

[Authorize]
[ApiController]
[Route("[controller]")]
public class ContactAppointmentController : ControllerBase
{
    private readonly IContactAppointmentService service;
    private readonly EntitiesContext db;
    private readonly IActivityService activityService;
    private readonly IMapper mapper;
    private readonly IContactService contactService;
    private readonly IMediator mediator;

    public ContactAppointmentController(
        EntitiesContext db,
        IContactAppointmentService service,
        IMapper mapper,
        IActivityService activityService,
        IContactService contactService,
        IMediator mediator)
    {
        this.db = db;
        this.service = service;
        this.activityService = activityService;
        this.mapper = mapper;
        this.contactService = contactService;
        this.mediator = mediator;
    }

    [HttpGet]
    public async Task<ActionResult<IEnumerable<ContactAppointmentDto>>> Get(
        [FromQuery] string? filters = null,
        [FromQuery] string? search = null,
        [FromQuery] string? sort = null)
    {
        var query = service.Get(filters != null ? DataTools.GetObjectFromJsonString<APIFilter>(filters).Filters : null, search, sort).Include(x => x.Company)
                     .Include(x => x.ManagerContact);
        var results = mapper.Map<IEnumerable<ContactAppointmentDto>>(await query.ToListAsync());

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(ContactAppointment))
            .ToListAsync();

        foreach (var obj in results)
        {
            obj.Type = typeMasters.FirstOrDefault(x => x.Value == obj.TypeFlag)?.Title ?? "";
        }

        var statusMasters = await db.StatusMasters
          .AsNoTracking()
          .Where(x => x.Entity == nameof(ContactAppointment))
          .ToListAsync();

        foreach (var obj in results)
        {
            obj.Status = statusMasters.FirstOrDefault(x => x.Value == obj.StatusFlag)?.Title ?? "";
        }

        return Ok(results);
    }

    [HttpGet("Pages")]
    public async Task<ActionResult<PagedResponse<ContactAppointmentDto>>> GetPages(
        [FromQuery] int page = 0,
        [FromQuery] int pageSize = 50,
        [FromQuery] string? filters = null,
        [FromQuery] string? search = null,
        [FromQuery] string? sort = null)
    {
        var query = service.Get(filters != null ? DataTools.GetObjectFromJsonString<APIFilter>(filters).Filters : null, search, sort).Include(x => x.Company)
                     .Include(x => x.ManagerContact);

        var totalCount = await query.CountAsync();
        var totalPages = (int)Math.Ceiling((decimal)totalCount / pageSize);
        var results = mapper.Map<IEnumerable<ContactAppointmentDto>>(await query
            .Skip(pageSize * page)
            .Take(pageSize)
            .ToListAsync());

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(ContactAppointment))
            .ToListAsync();

        foreach (var obj in results)
        {
            obj.Type = typeMasters.FirstOrDefault(x => x.Value == obj.TypeFlag)?.Title ?? "";
        }

        var statusMasters = await db.StatusMasters
           .AsNoTracking()
           .Where(x => x.Entity == nameof(ContactAppointment))
           .ToListAsync();

        foreach (var obj in results)
        {
            obj.Status = statusMasters.FirstOrDefault(x => x.Value == obj.StatusFlag)?.Title ?? "";
        }

        return Ok(new PagedResponse<ContactAppointmentDto>(results, totalCount, totalPages));
    }

    [HttpGet("{id:int}")]
    public async Task<ActionResult<ContactAppointmentDto>> GetByID(int id)
    {
        var responseDto = mapper.Map<ContactAppointmentDto>(await service.GetById(id));
        if (responseDto == null)
        {
            return NotFound();
        }

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(ContactAppointment))
            .ToListAsync();

        responseDto.Type = typeMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        var statusMasters = await db.StatusMasters
          .AsNoTracking()
          .Where(x => x.Entity == nameof(ContactAppointment))
          .ToListAsync();

        responseDto.Status = statusMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        return Ok(responseDto);
    }

    [HttpPost]
    public async Task<ActionResult<ContactAppointmentDto>> Post(ContactAppointmentDto dto)
    {
        var id = await service.Create(mapper.Map<ContactAppointment>(dto));
        var responseDto = mapper.Map<ContactAppointmentDto>(await service.GetById(id));

        if (responseDto == null)
            return BadRequest("Not Created");

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(ContactAppointment))
            .ToListAsync();

        responseDto.Type = typeMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        var statusMasters = await db.StatusMasters
  .AsNoTracking()
  .Where(x => x.Entity == nameof(ContactAppointment))
  .ToListAsync();

        responseDto.Status = statusMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        if (!string.IsNullOrEmpty(User.Identity?.Name))
        {
            var currentContact = await contactService.Get().FirstOrDefaultAsync(x => x.Username == User.Identity.Name);
            if (currentContact != null)
            {
                var parent = await contactService.Get().SingleOrDefaultAsync(x => x.ID == responseDto.ContactID);
                if (parent != null)
                {
                    await activityService.LogUserActivity(currentContact, nameof(parent), parent.ID, $"{parent.Name}", $"{nameof(ContactAppointment).Replace(nameof(parent), "")} | {responseDto.Company.Title} | {responseDto.Designation} | {responseDto.ManValue}mV", "Created");
                }
            }
        }

        return CreatedAtAction(nameof(GetByID), new { id = responseDto.ID }, responseDto);
    }

    [HttpPut("{id:int}")]
    public async Task<ActionResult<ContactAppointmentDto>> Put(int id, ContactAppointmentDto dto)
    {
        if (id != dto.ID)
        {
            return BadRequest();
        }

        var entity = mapper.Map<ContactAppointment>(dto);

        await service.Update(entity);

        var updatedEntity = await service.GetById(id);

          await mediator.Publish(new OnContactAppointmentUpdate
        {
            ID = id,
            ContactID = updatedEntity.ContactID,
            CompanyID = updatedEntity.CompanyID,
            ManagerContactID = updatedEntity.ManagerContactID,
            StatusFlag = updatedEntity.StatusFlag
        });


        var responseDto = mapper.Map<ContactAppointmentDto>(updatedEntity);

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(ContactAppointment))
            .ToListAsync();

        responseDto.Type = typeMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        var statusMasters = await db.StatusMasters
  .AsNoTracking()
  .Where(x => x.Entity == nameof(ContactAppointment))
  .ToListAsync();

        responseDto.Status = statusMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        if (!string.IsNullOrEmpty(User.Identity?.Name))
        {
            var currentContact = await contactService.Get().FirstOrDefaultAsync(x => x.Username == User.Identity.Name);
            if (currentContact != null)
            {
                var parent = await contactService.Get().SingleOrDefaultAsync(x => x.ID == responseDto.ContactID);
                if (parent != null)
                {
                    await activityService.LogUserActivity(currentContact, nameof(parent), parent.ID, $"{parent.Name}", $"{nameof(ContactAppointment).Replace(nameof(parent), "")} | {responseDto.Company.Title} | {responseDto.Designation} | {responseDto.ManValue}mV", "Updated");
                }
            }
        }

        return Ok(responseDto);
    }

    [HttpDelete("{id:int}")]
    public async Task<IActionResult> Delete(int id)
    {
        var responseDto = mapper.Map<ContactAppointmentDto>(await service.GetById(id));
        if (responseDto == null) return BadRequest($"{nameof(ContactAppointment)} not found!");

        await service.Delete(id);

        if (!string.IsNullOrEmpty(User.Identity?.Name))
        {
            var currentContact = await contactService.Get().FirstOrDefaultAsync(x => x.Username == User.Identity.Name);
            if (currentContact != null)
            {
                var parent = await contactService.Get().SingleOrDefaultAsync(x => x.ID == responseDto.ContactID);
                if (parent != null)
                {
                    await activityService.LogUserActivity(currentContact, nameof(parent), parent.ID, $"{parent.Name}", $"{nameof(ContactAppointment).Replace(nameof(parent), "")} | {responseDto.Company.Title} | {responseDto.Designation} | {responseDto.ManValue}mV", "Deleted");
                }
            }
        }

        return NoContent();
    }


    [HttpGet("uid/{id:guid}")]
    [AllowAnonymous]
    public async Task<ActionResult<ContactAppointmentDto>> GetByGUID(Guid id)
    {
        var responseDto = mapper.Map<ContactAppointmentDto>(await service.GetById(id));
        if (responseDto == null)
        {
            return NotFound();
        }

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(ContactAppointment))
            .ToListAsync();

        responseDto.Type = typeMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        var statusMasters = await db.StatusMasters
                        .AsNoTracking()
                        .Where(x => x.Entity == nameof(ContactAppointment))
                        .ToListAsync();

        responseDto.Status = statusMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        return Ok(responseDto);
    }


    [HttpGet("SearchTagOptions")]
    public async Task<IActionResult> GetSearchTagOptions()
    {
        var results = await service.GetSearchTagOptions();
        return Ok(results);
    }


    [HttpGet("FieldOptions")]
    public async Task<IActionResult> GetFieldOptions(string field)
    {
        return Ok(await service.GetFieldOptions(field));
    }


}