﻿using AutoMapper;
using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.TodoModule.Entities;
namespace MyCockpitView.WebApi.TodoModule.Dtos;

public class TodoAttachmentDto : BaseBlobEntityDto
{

    public int TodoID { get; set; }

}

public class TodoAttachmentDtoMapperProfile : Profile
{
    public TodoAttachmentDtoMapperProfile()
    {
       
        CreateMap<TodoAttachment, TodoAttachmentDto>()
.ReverseMap().ForMember(dest => dest.Todo, opt => opt.Ignore());

    }
}