﻿using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.CoreModule;
using System.ComponentModel.DataAnnotations;


namespace MyCockpitView.WebApi.RequestTicketModule.Entities;

public class RequestTicketAssignee : BaseEntity
{
    [Required]
    public int ContactID { get; set; }

    [StringLength(255)]
    public string? FullName { get; set; }


    [StringLength(255)]
    public string? Email { get; set; }

    [StringLength(255)]
    public string? Company { get; set; }


    public int RequestTicketID { get; set; }
    public virtual RequestTicket? RequestTicket { get; set; }

}
public class RequestTicketAssigneeConfiguration : BaseEntityConfiguration<RequestTicketAssignee>, IEntityTypeConfiguration<RequestTicketAssignee>
{
    public void Configure(EntityTypeBuilder<RequestTicketAssignee> builder)
    {
        base.Configure(builder);
        builder.HasIndex(e => e.ContactID);

    }
}