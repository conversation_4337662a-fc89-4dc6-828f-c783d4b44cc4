﻿using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.CoreModule;


namespace MyCockpitView.WebApi.ProjectModule.Entities;

public class ProjectBillPaymentAttachment : BaseBlobEntity
{
    public int ProjectBillPaymentID { get; set; }

    public virtual ProjectBillPayment? ProjectBillPayment { get; set; }
}

public class ProjectBillPaymentAttachmentConfiguration : BaseBlobEntityConfiguration<ProjectBillPaymentAttachment>, IEntityTypeConfiguration<ProjectBillPaymentAttachment>
{
    public void Configure(EntityTypeBuilder<ProjectBillPaymentAttachment> builder)
    {
        base.Configure(builder);

      
    }
}