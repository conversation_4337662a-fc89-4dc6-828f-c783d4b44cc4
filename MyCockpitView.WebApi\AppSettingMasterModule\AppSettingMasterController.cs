﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.CoreModule;
using MyCockpitView.Utility.Common;
using MyCockpitView.WebApi.Exceptions;

namespace MyCockpitView.WebApi.AppSettingMasterModule;

[Authorize]
[Route("[controller]")]
[ApiController]
public class AppSettingMasterController : ControllerBase
{
    private readonly ILogger<AppSettingMasterController> _logger;
    private readonly IAppSettingMasterService _service;
    private readonly EntitiesContext _entitiesContext;

    public AppSettingMasterController(
        ILogger<AppSettingMasterController> logger,
        EntitiesContext entitiesContext,
        IAppSettingMasterService AppSettingMasterService
    )
    {
        _logger = logger;
        _entitiesContext = entitiesContext;
        _service = AppSettingMasterService;
    }

    [HttpGet]
    public async Task<IActionResult> Get(string? Filters = null, string? Search = null, string? Sort = null)
    {
        _logger.LogInformation("Get method called with Filters: {Filters}, Search: {Search}, Sort: {Sort}", Filters, Search, Sort);

        var query = _service.Get(Filters != null ? DataTools.GetObjectFromJsonString<APIFilter>(Filters).Filters : null, Search, Sort);
        var results = await query.ToListAsync();

        _logger.LogInformation("Get method completed with {Count} results", results.Count);
        return Ok(results);
    }


    [HttpGet("{id}")]
    public async Task<IActionResult> GetByID(int id)
    {
        _logger.LogInformation("GetByID method called with ID: {ID}", id);

        var obj = await _service.Get().SingleOrDefaultAsync(x => x.ID == id);
        if (obj == null)
        {
            _logger.LogWarning("GetByID method: {ID} not found", id);
            throw new NotFoundException($"{nameof(AppSettingMaster)} not found!");
        }

        _logger.LogInformation("GetByID method completed for ID: {ID}", id);
        return Ok(obj);
    }

    [HttpGet("uid/{id:guid}")]
    public async Task<IActionResult> GetByGUID(Guid id)
    {
        _logger.LogInformation("GetByGUID method called with GUID: {GUID}", id);

        var obj = await _service.Get().SingleOrDefaultAsync(x => x.UID == id);
        if (obj == null)
        {
            _logger.LogWarning("GetByGUID method: {GUID} not found", id);
            throw new NotFoundException($"{nameof(AppSettingMaster)} not found!");
        }

        _logger.LogInformation("GetByGUID method completed for GUID: {GUID}", id);
        return Ok(obj);
    }

    [HttpPost]
    public async Task<IActionResult> Post([FromBody] AppSettingMaster Dto)
    {
        _logger.LogInformation("Post method called");

        var id = await _service.Create(Dto);
        var obj = await _service.Get().SingleOrDefaultAsync(x => x.ID == id);
        if (obj == null)
        {
            _logger.LogError("Post method: {Name} could not be created", nameof(AppSettingMaster));
            throw new BadRequestException($"{nameof(AppSettingMaster)} could not be created!");
        }

        _logger.LogInformation("Post method completed for ID: {ID}", id);
        return Ok(obj);
    }

    [HttpPut("{id}")]
    public async Task<IActionResult> Put(int id, [FromBody] AppSettingMaster Dto)
    {
        _logger.LogInformation("Put method called with ID: {ID}", id);

        await _service.Update(Dto);
        var obj = await _service.Get().SingleOrDefaultAsync(x => x.ID == id);
        if (obj == null)
        {
            _logger.LogWarning("Put method: {ID} not found", id);
            throw new NotFoundException($"{nameof(AppSettingMaster)} not found!");
        }

        _logger.LogInformation("Put method completed for ID: {ID}", id);
        return Ok(obj);
    }

    [HttpDelete("{id}")]
    public async Task<IActionResult> Delete(int id)
    {
        _logger.LogInformation("Delete method called with ID: {ID}", id);

        var obj = await _service.Get().SingleOrDefaultAsync(x => x.ID == id);
        if (obj == null)
        {
            _logger.LogWarning("Delete method: {ID} not found", id);
            throw new NotFoundException($"{nameof(AppSettingMaster)} not found!");
        }

        await _service.Delete(id);
        _logger.LogInformation("Delete method completed for ID: {ID}", id);
        return Ok();
    }
}