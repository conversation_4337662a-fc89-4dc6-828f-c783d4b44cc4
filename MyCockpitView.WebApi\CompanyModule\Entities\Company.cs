﻿using System.ComponentModel.DataAnnotations;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.CoreModule;


namespace MyCockpitView.WebApi.CompanyModule.Entities;

public class Company : BaseEntity
{

    [StringLength(255)]
    public string? Title { get; set; }

    [StringLength(255)]
    public string? Initials { get; set; }
    [StringLength(255)]
    public string? GSTIN { get; set; }
    [StringLength(255)]
    public string? GSTStateCode { get; set; }
    [StringLength(255)]
    public string? PAN { get; set; }
    [StringLength(255)]
    public string? TAN { get; set; }
    [StringLength(255)]
    public string? UDHYAM { get; set; }
    public string? LogoUrl { get; set; }
    [StringLength(255)]
    public string? Bank { get; set; }
    public string? BankBranch { get; set; }
    [StringLength(255)]
    public string? BankIFSCCode { get; set; }
    [StringLength(255)]
    public string? SwiftCode { get; set; }
    [StringLength(255)]
    public string? BankAccount { get; set; }
    public string? Address { get; set; }
    public string? SignStampUrl { get; set; }
    [Precision(18, 2)]
    public decimal VHrRate { get; set; } = 0.0m;

}

public class CompanyConfiguration : BaseEntityConfiguration<Company>, IEntityTypeConfiguration<Company>
{
    public void Configure(EntityTypeBuilder<Company> builder)
    {
        base.Configure(builder);
    }
}