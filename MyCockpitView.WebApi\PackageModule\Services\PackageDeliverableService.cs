



using MyCockpitView.WebApi.Services;
using MyCockpitView.WebApi.PackageModule.Entities;
using MyCockpitView.CoreModule;

namespace MyCockpitView.WebApi.PackageModule.Services;
public class PackageDeliverableService : BaseEntityService<PackageDeliverable>, IPackageDeliverableService
{
    public PackageDeliverableService(EntitiesContext db) : base(db)
    {
    }

    public IQueryable<PackageDeliverable> Get(IEnumerable<QueryFilter>? Filters = null, string? Search = null, string? Sort = null)
    {

        IQueryable<PackageDeliverable> _query = base.Get(Filters);

        //Apply filters
        if (Filters != null)
        {


            if (Filters.Where(x => x.Key.Equals("PackageID", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<PackageDeliverable>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("PackageID", StringComparison.OrdinalIgnoreCase)))
                {
                    var isNumeric = Convert.ToInt32(_item.Value);

                    predicate = predicate.Or(x => x.PackageID == isNumeric);
                }
                _query = _query.Where(predicate);
            }

          
        }


        return _query;

    }
}