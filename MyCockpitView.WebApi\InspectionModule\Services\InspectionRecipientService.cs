﻿



using System.Text.RegularExpressions;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.WebApi.Exceptions;
using MyCockpitView.WebApi.InspectionModule.Entities;
using MyCockpitView.WebApi.Services;
using MyCockpitView.WebApi.WFTaskModule.Entities;
using MyCockpitView.WebApi.WFTaskModule.Services;

namespace MyCockpitView.WebApi.InspectionModule.Services;

public class InspectionRecipientService : BaseEntityService<InspectionRecipient>, IInspectionRecipientService
{
    public InspectionRecipientService(EntitiesContext db) : base(db) { }

    public async Task<int> Create(InspectionRecipient Entity)
    {

        Regex regex = new Regex(McvConstant.EMAIL_REGEX, RegexOptions.None);
        if (Entity.Email == null || !regex.IsMatch(Entity.Email))
            throw new EntityServiceException("Email Id is invalid.");

        Entity.ID = await base.Create(Entity);

        var _meeting = await db.Inspections.AsNoTracking()
            .Where(x => x.ID == Entity.InspectionID).SingleOrDefaultAsync();
        if (_meeting.StatusFlag != McvConstant.INSPECTION_STATUSFLAG_SCHEDULED && Entity.TypeFlag == McvConstant.INSPECTION_RECIPIENT_TYPEFLAG_TO)//
        {
            await LogRecipientTime(Entity.ContactID.Value, _meeting.ID, _meeting.StartDate, _meeting.EndDate, "INSPECTION_CLOSE");
        }

        return Entity.ID;

    }


    public async Task<bool> Update(InspectionRecipient Entity)
    {

        Regex regex = new Regex(McvConstant.EMAIL_REGEX, RegexOptions.None);
        if (Entity.Email == null || !regex.IsMatch(Entity.Email))
            throw new EntityServiceException("Email Id is invalid.");

        if (Entity.TypeFlag == 1)
        {
            if (Entity.ContactID != null)
            {
                var taskIDs = await db.WFTasks.AsNoTracking()
            .Include(x => x.TimeEntries)
            .Include(x => x.Attachments)
             .Where(x => x.Entity != null && x.Entity == nameof(Inspection)
                    && x.EntityID == Entity.InspectionID)
                    .Where(x => x.ContactID == Entity.ContactID)
                    .Select(x => x.ID)
                    .ToListAsync();

                var taskService = new WFTaskService(db);
                foreach (var task in taskIDs)
                {
                    await taskService.Delete(task);
                }
            }
        }
        else
        {
            if (Entity.TypeFlag == 0)//
            {
                var _meeting = await db.Inspections.AsNoTracking()
            .Where(x => x.ID == Entity.InspectionID).SingleOrDefaultAsync();

                await LogRecipientTime(Entity.ContactID.Value, _meeting.ID, _meeting.StartDate, _meeting.EndDate, "INSPECTION_CLOSE");
            }
        }

        db.Entry(Entity).State = EntityState.Modified;

        await db.SaveChangesAsync();

        return true;

    }

    public async Task Delete(int Id)
    {

        var _entity = await Get()
             .SingleOrDefaultAsync(i => i.ID == Id);

        if (_entity == null) throw new EntityServiceException($"{nameof(InspectionRecipient)} not found!");

        //var taskService = new WFTaskService(db);
        //var tasks = await taskService.Get()
        //    .Where(x => x.Entity == nameof(Inspection) && x.EntityID == _entity.InspectionID && x.ContactID==_entity.ContactID)
        //    .Select(x => x.ID)
        //    .ToListAsync();



        await base.Delete(Id);

    }


    public async Task ScaffoldPendingRecipients(int InspectionID)
    {

        var _meetingItems = await db.InspectionItems.AsNoTracking()
            .Where(x => x.InspectionID == InspectionID)
            .Where(x => x.ActionByContactID != null)
            //.Where(x => x.PreviousItemID != null)
            .ToListAsync();

        var _attendees = await db.InspectionRecipients.AsNoTracking()
                        
                        .Where(x => x.InspectionID == InspectionID
                        && x.ContactID != null).ToListAsync();



        Regex regex = new Regex(McvConstant.EMAIL_REGEX, RegexOptions.None);

        foreach (var item in _meetingItems)
        {
            if (!_attendees
                .Where(x => x.ContactID == item.ActionByContactID)
                .Any())
            {
                if (item.PreviousItemID != null)
                {
                    var _previousItem = await db.InspectionItems.AsNoTracking()
                 .Where(x => x.ID == item.PreviousItemID)
                 .FirstOrDefaultAsync();

                    if (_previousItem != null)
                    {
                        var _attendee = await db.InspectionRecipients.AsNoTracking()
                           
                           .Where(x => x.InspectionID == _previousItem.InspectionID
                           && x.ContactID != null
                           && x.ContactID == _previousItem.ActionByContactID)
                           .FirstOrDefaultAsync();
                        if (_attendee != null)
                        {
                            if (_attendee.Email != null && regex.IsMatch(_attendee.Email))
                            {

                                var _newRecipient = new InspectionRecipient
                                {
                                    InspectionID = InspectionID,
                                    Name = _attendee.Name,
                                    Company = _attendee.Company,
                                    Email = _attendee.Email,
                                    ContactID = _attendee.ContactID,
                                    TypeFlag = 1,
                                };
                                db.InspectionRecipients.Add(_newRecipient);
                                await db.SaveChangesAsync();
                                _attendees.Add(_newRecipient);
                            }
                        }
                    }
                }
                else
                {
                    var _contact = await db.Contacts.AsNoTracking()
                    .Include(x => x.AssociatedContacts)
                    .Include(x => x.AssociatedCompanies)
                    .Where(x => x.ID == item.ActionByContactID)
                    .FirstOrDefaultAsync();
                    if (_contact.Email1 != null && regex.IsMatch(_contact.Email1))
                    {
                        var _newRecipient = new InspectionRecipient
                        {
                            InspectionID = InspectionID,
                            Name = _contact.FullName,
                            Company = _contact.AssociatedCompanies.Any() ? _contact.AssociatedCompanies.FirstOrDefault().Company.FullName : "",
                            Email = _contact.Email1,
                            ContactID = _contact.ID,
                            TypeFlag = 1,
                        };
                        db.InspectionRecipients.Add(_newRecipient);
                        await db.SaveChangesAsync();
                        _attendees.Add(_newRecipient);
                    }
                }


            }
        }


    }

    public async Task LogRecipientTime(int ContactID, int InspectionID, DateTime Start, DateTime End, string StageCode)
    {

        var _meeting = await db.Inspections.AsNoTracking()
                         .SingleOrDefaultAsync(x => x.ID == InspectionID);

        var _contact = await db.Contacts.AsNoTracking()
                        .SingleOrDefaultAsync(x => x.ID == ContactID);

        if (_meeting == null || _meeting.StatusFlag == McvConstant.INSPECTION_STATUSFLAG_SCHEDULED
            || _contact == null || _contact.Username == null) return;

        var _manValue = 1.0m;
        var _companyID = 1;

        var sharedService = new SharedService(db); ;
        var _appointment = await sharedService.GetLastAppointment(_contact.ID);
        if (_appointment != null)
        {
            _manValue = _appointment.ManValue;
            _companyID = _appointment.CompanyID;
        }


        var _valueHourRate = Convert.ToDecimal(await sharedService.GetPresetValue(McvConstant.COMPANY_VHR_COST));


        var _task = await db.WFTasks
        .Where(x => x.Entity != null
           && x.Entity==nameof(Inspection)
           && x.EntityID == InspectionID
           && x.ContactID == ContactID
           && x.WFStageCode==StageCode).FirstOrDefaultAsync();
        if (_task != null)
        {
            _task.ManValue = _manValue;
            _task.VHrRate = _valueHourRate;
            _task.CompanyID = _companyID;
            _task.StartDate = Start;
            _task.CompletedDate = End;
            _task.DueDate = End;

            _task.MHrAssessed = Convert.ToDecimal((End - Start).TotalHours);
            _task.MHrAssigned = Convert.ToDecimal((End - Start).TotalHours);

            _task.VHrAssigned = Math.Round(_task.MHrAssigned * _task.ManValue, 2);
            _task.VHrAssessed = Math.Round(_task.MHrAssessed * _task.ManValue, 2);

            _task.VHrAssignedCost = Math.Round(_task.VHrAssigned * _task.VHrRate, 2);
            _task.VHrAssessedCost = Math.Round(_task.VHrAssessed * _task.VHrRate, 2);

            await db.SaveChangesAsync();
        }
        else
        {

            var taskService = new WFTaskService(db);

            var _taskEntity = await taskService.GetTaskByStage(nameof(Inspection), InspectionID, StageCode);

            _taskEntity.ContactID = ContactID;
            _taskEntity.ManValue = _manValue;
            _taskEntity.VHrRate = _valueHourRate;
            _taskEntity.CompanyID = _companyID;
            _taskEntity.StartDate = Start;
            _taskEntity.DueDate = End;
            _taskEntity.CompletedDate = End;
            _taskEntity.MHrAssessed = Convert.ToDecimal((End - Start).TotalHours);
            _taskEntity.MHrAssigned = Convert.ToDecimal((End - Start).TotalHours);
            _taskEntity.VHrAssigned = Math.Round(_taskEntity.MHrAssigned * _taskEntity.ManValue, 2);
            _taskEntity.VHrAssessed = Math.Round(_taskEntity.MHrAssessed * _taskEntity.ManValue, 2);
            _taskEntity.VHrAssignedCost = Math.Round(_taskEntity.VHrAssigned * _taskEntity.VHrRate, 2);
            _taskEntity.VHrAssessedCost = Math.Round(_taskEntity.VHrAssessed * _taskEntity.VHrRate, 2);
            _taskEntity.StatusFlag = 1;
            _taskEntity.Comment = "On behalf by " + _meeting.CreatedBy;

            var _taskID = await taskService.CreateTask(_taskEntity, true);
            _task = await db.WFTasks.AsNoTracking()
                    .Where(x => x.ID == _taskID).SingleOrDefaultAsync();


        }

        var _timeEntries = await db.TimeEntries
        .Where(x => x.WFTaskID == _task.ID)
        .ToListAsync();

        if (_timeEntries.Any())
        {
            db.TimeEntries.RemoveRange(_timeEntries);

            await db.SaveChangesAsync();
        }



        if (_task != null)
        {
            db.TimeEntries.Add(new TimeEntry
            {
                ContactID = ContactID,
                Entity = nameof(Inspection),
                EntityID = InspectionID,
                EntityTitle = _meeting.Code,
                StartDate = Start,
                EndDate = End,
                ManHours = Convert.ToDecimal((End - Start).TotalHours),
                WFTaskID = _task.ID,
                StatusFlag = 1,
                TaskTitle = (_task.Title + " " + _task.Subtitle).Trim(),
            });


            await db.SaveChangesAsync();
        }



    }



}