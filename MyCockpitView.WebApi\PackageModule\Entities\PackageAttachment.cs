﻿
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.CoreModule;
using MyCockpitView.Utility.Common;
using MyCockpitView.WebApi.InspectionModule.Entities;
using System.ComponentModel.DataAnnotations.Schema;

namespace MyCockpitView.WebApi.PackageModule.Entities;

public class PackageAttachment : BaseBlobEntity
{
    public int PackageID { get; set; }

    public virtual Package? Package { get; set; }

    [Column("Attributes")]
    public string?  _attributes { get; set; }

    [NotMapped]
    public virtual ICollection<PackageAttachmentAttribute> Attributes
    {
        get
        {
            return _attributes != null && _attributes != string.Empty ?
                DataTools.GetObjectFromJsonString<ICollection<PackageAttachmentAttribute>>(_attributes) :
                new List<PackageAttachmentAttribute>();
        }
        set
        {
            _attributes = DataTools.GetJsonStringFromObject(value);
        }
    }
}

public class PackageAttachmentAttribute
{
    public string?  Title { get; set; }

    public string?  SheetNo { get; set; }
    public string?  Description { get; set; }
}

public class PackageAttachmentConfiguration : BaseBlobEntityConfiguration<PackageAttachment>, IEntityTypeConfiguration<PackageAttachment>
{
    public void Configure(EntityTypeBuilder<PackageAttachment> builder)
    {

        base.Configure(builder);
       
    }
}
