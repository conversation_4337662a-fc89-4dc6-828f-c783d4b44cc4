﻿using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.WebApi.AuthModule.Entities;
using MyCockpitView.WebApi.ContactModule.Entities;
using MyCockpitView.WebApi.ContactModule.Services;

namespace MyCockpitView.WebApi.Services;

public interface ICurrentUserService
{
    string? GetCurrentUsername();
    Task<Contact?> GetCurrentContact();
    Task<User?> GetCurrentUser();
    string? GetRemoteIpAddress();
    string GetUserAgent();
    Dictionary<string, string> GetRequestHeaders();
    string GetHeaderValue(string headerName);
}

public class CurrentUserService : ICurrentUserService
{
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly UserManager<User> _userManager;
    private readonly IContactService _contactService;

    public CurrentUserService(
        IHttpContextAccessor httpContextAccessor,
        UserManager<User> userManager,
        IContactService contactService)
    {
        _httpContextAccessor = httpContextAccessor;
        _userManager = userManager;
        _contactService = contactService;
    }

    public string? GetCurrentUsername()
    {
        return _httpContextAccessor.HttpContext?.User?.Identity?.Name;
    }

    public async Task<Contact?> GetCurrentContact()
    {
        var username = GetCurrentUsername();
        if (string.IsNullOrEmpty(username))
            return null;

        return await _contactService.Get()
            .FirstOrDefaultAsync(x => x.Username == username);
    }

    public async Task<User?> GetCurrentUser()
    {
        var username = GetCurrentUsername();
        if (string.IsNullOrEmpty(username))
            return null;

        return await _userManager.FindByNameAsync(username);
    }

    public string? GetRemoteIpAddress()
    {
        return _httpContextAccessor.HttpContext?.Connection?.RemoteIpAddress?.ToString();
    }

    public string GetUserAgent()
    {
        return _httpContextAccessor.HttpContext?.Request.Headers["User-Agent"].ToString() ?? string.Empty;
    }

    public Dictionary<string, string> GetRequestHeaders()
    {
        var headers = new Dictionary<string, string>();

        if (_httpContextAccessor.HttpContext?.Request.Headers == null)
            return headers;

        foreach (var header in _httpContextAccessor.HttpContext.Request.Headers)
        {
            headers[header.Key] = header.Value.ToString();
        }

        return headers;
    }

    public string GetHeaderValue(string headerName)
    {
        return _httpContextAccessor.HttpContext?.Request.Headers[headerName].ToString() ?? string.Empty;
    }
}
