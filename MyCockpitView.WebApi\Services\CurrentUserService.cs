using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.WebApi.AuthModule.Entities;
using MyCockpitView.WebApi.AuthModule.Services;
using MyCockpitView.WebApi.ContactModule.Entities;
using MyCockpitView.WebApi.ContactModule.Services;
using System.Security.Claims;

namespace MyCockpitView.WebApi.Services;

public interface ICurrentUserService
{
    string? GetCurrentUsername();
    Task<Contact?> GetCurrentContact();
    Task<User?> GetCurrentUser();
    string? GetRemoteIpAddress();
    string GetUserAgent();
    Dictionary<string, string> GetRequestHeaders();
    string GetHeaderValue(string headerName);
    Task<string?> GetValidatedUsername();
    Task<bool> IsSessionValid();
    int? GetCurrentContactId();
    Guid? GetCurrentSessionId();
}

public class CurrentUserService : ICurrentUserService
{
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly UserManager<User> _userManager;
    private readonly IContactService _contactService;
    private readonly ILoginSessionService _loginSessionService;

    public CurrentUserService(
        IHttpContextAccessor httpContextAccessor,
        UserManager<User> userManager,
        IContactService contactService,
        ILoginSessionService loginSessionService)
    {
        _httpContextAccessor = httpContextAccessor;
        _userManager = userManager;
        _contactService = contactService;
        _loginSessionService = loginSessionService;
    }

    public string? GetCurrentUsername()
    {
        return _httpContextAccessor.HttpContext?.User?.Identity?.Name;
    }

    public async Task<Contact?> GetCurrentContact()
    {
        var username = GetCurrentUsername();
        if (string.IsNullOrEmpty(username))
            return null;

        return await _contactService.Get()
            .FirstOrDefaultAsync(x => x.Username == username);
    }

    public async Task<User?> GetCurrentUser()
    {
        var username = GetCurrentUsername();
        if (string.IsNullOrEmpty(username))
            return null;

        return await _userManager.FindByNameAsync(username);
    }

    public string? GetRemoteIpAddress()
    {
        return _httpContextAccessor.HttpContext?.Connection?.RemoteIpAddress?.ToString();
    }

    public string GetUserAgent()
    {
        return _httpContextAccessor.HttpContext?.Request.Headers["User-Agent"].ToString() ?? string.Empty;
    }

    public Dictionary<string, string> GetRequestHeaders()
    {
        var headers = new Dictionary<string, string>();

        if (_httpContextAccessor.HttpContext?.Request.Headers == null)
            return headers;

        foreach (var header in _httpContextAccessor.HttpContext.Request.Headers)
        {
            headers[header.Key] = header.Value.ToString();
        }

        return headers;
    }

    public string GetHeaderValue(string headerName)
    {
        return _httpContextAccessor.HttpContext?.Request.Headers[headerName].ToString() ?? string.Empty;
    }

    public async Task<string?> GetValidatedUsername()
    {
        var username = GetCurrentUsername();
        if (string.IsNullOrEmpty(username))
            return null;

        // Validate against active session
        var isValid = await IsSessionValid();
        return isValid ? username : null;
    }

    public async Task<bool> IsSessionValid()
    {
        var username = GetCurrentUsername();
        if (string.IsNullOrEmpty(username))
            return false;

        try
        {
            var activeSession = await _loginSessionService.Get()
                .Where(x => x.Username == username && x.IsActive)
                .OrderByDescending(x => x.Created)
                .FirstOrDefaultAsync();

            if (activeSession == null)
                return false;

            // Check if OTP is required and verified
            if (activeSession.IsOTPRequired && !activeSession.IsOTPVerified)
                return false;

            return true;
        }
        catch
        {
            return false;
        }
    }

    public int? GetCurrentContactId()
    {
        var contactIdClaim = _httpContextAccessor.HttpContext?.User?.FindFirst("ContactId");
        if (contactIdClaim != null && int.TryParse(contactIdClaim.Value, out int contactId))
        {
            return contactId;
        }
        return null;
    }

    public Guid? GetCurrentSessionId()
    {
        var sessionIdClaim = _httpContextAccessor.HttpContext?.User?.FindFirst("SessionId");
        if (sessionIdClaim != null && Guid.TryParse(sessionIdClaim.Value, out Guid sessionId))
        {
            return sessionId;
        }
        return null;
    }
}
