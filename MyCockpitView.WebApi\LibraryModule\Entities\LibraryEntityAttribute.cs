﻿using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.CoreModule;
using System.ComponentModel.DataAnnotations;

namespace MyCockpitView.WebApi.LibraryModule.Entities;

public class LibraryEntityAttribute : BaseEntity
{
    public int LibraryEntityID { get; set; }
    public virtual LibraryEntity? LibraryEntity { get; set; }

    [Required]
    [StringLength(255)]
    public string AttributeKey { get; set; }

    public string? AttributeValue { get; set; }

}
public class LibraryEntityAttributeConfiguration : BaseEntityConfiguration<LibraryEntityAttribute>, IEntityTypeConfiguration<LibraryEntityAttribute>
{
    public void Configure(EntityTypeBuilder<LibraryEntityAttribute> builder)
    {
       base.Configure(builder);
      
        builder.HasIndex(e => e.AttributeKey);
    }
}
