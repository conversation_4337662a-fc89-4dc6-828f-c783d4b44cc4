﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace MyCockpitView.WebApi.Migrations
{
    /// <inheritdoc />
    public partial class AK003 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "_searchTags",
                table: "DesignScriptDataCards");

            migrationBuilder.AlterColumn<Guid>(
                name: "UID",
                table: "DesignScriptDataCards",
                type: "uniqueidentifier",
                nullable: false,
                defaultValueSql: "NEWID()",
                oldClrType: typeof(Guid),
                oldType: "uniqueidentifier");

            migrationBuilder.AlterColumn<int>(
                name: "TypeFlag",
                table: "DesignScriptDataCards",
                type: "int",
                nullable: false,
                defaultValue: 0,
                oldClrType: typeof(int),
                oldType: "int");

            migrationBuilder.AlterColumn<int>(
                name: "StatusFlag",
                table: "DesignScriptDataCards",
                type: "int",
                nullable: false,
                defaultValue: 0,
                oldClrType: typeof(int),
                oldType: "int");

            migrationBuilder.AlterColumn<string>(
                name: "SearchTags",
                table: "DesignScriptDataCards",
                type: "nvarchar(max)",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)");

            migrationBuilder.AlterColumn<int>(
                name: "OrderFlag",
                table: "DesignScriptDataCards",
                type: "int",
                nullable: false,
                defaultValue: 0,
                oldClrType: typeof(int),
                oldType: "int");

            migrationBuilder.AlterColumn<string>(
                name: "ModifiedBy",
                table: "DesignScriptDataCards",
                type: "nvarchar(255)",
                maxLength: 255,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldNullable: true);

            migrationBuilder.AlterColumn<bool>(
                name: "IsDeleted",
                table: "DesignScriptDataCards",
                type: "bit",
                nullable: false,
                defaultValue: false,
                oldClrType: typeof(bool),
                oldType: "bit");

            migrationBuilder.AlterColumn<string>(
                name: "CreatedBy",
                table: "DesignScriptDataCards",
                type: "nvarchar(255)",
                maxLength: 255,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "Category",
                table: "DesignScriptDataCards",
                type: "nvarchar(450)",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldNullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_DesignScriptDataCards_Category",
                table: "DesignScriptDataCards",
                column: "Category");

            migrationBuilder.CreateIndex(
                name: "IX_DesignScriptDataCards_Code",
                table: "DesignScriptDataCards",
                column: "Code");

            migrationBuilder.CreateIndex(
                name: "IX_DesignScriptDataCards_CodeFlag",
                table: "DesignScriptDataCards",
                column: "CodeFlag");

            migrationBuilder.CreateIndex(
                name: "IX_DesignScriptDataCards_Created",
                table: "DesignScriptDataCards",
                column: "Created");

            migrationBuilder.CreateIndex(
                name: "IX_DesignScriptDataCards_CreatedByContactID",
                table: "DesignScriptDataCards",
                column: "CreatedByContactID");

            migrationBuilder.CreateIndex(
                name: "IX_DesignScriptDataCards_Filename",
                table: "DesignScriptDataCards",
                column: "Filename");

            migrationBuilder.CreateIndex(
                name: "IX_DesignScriptDataCards_IsDeleted",
                table: "DesignScriptDataCards",
                column: "IsDeleted");

            migrationBuilder.CreateIndex(
                name: "IX_DesignScriptDataCards_IsHidden",
                table: "DesignScriptDataCards",
                column: "IsHidden");

            migrationBuilder.CreateIndex(
                name: "IX_DesignScriptDataCards_IsReadOnly",
                table: "DesignScriptDataCards",
                column: "IsReadOnly");

            migrationBuilder.CreateIndex(
                name: "IX_DesignScriptDataCards_LibraryCode",
                table: "DesignScriptDataCards",
                column: "LibraryCode");

            migrationBuilder.CreateIndex(
                name: "IX_DesignScriptDataCards_Modified",
                table: "DesignScriptDataCards",
                column: "Modified");

            migrationBuilder.CreateIndex(
                name: "IX_DesignScriptDataCards_ModifiedByContactID",
                table: "DesignScriptDataCards",
                column: "ModifiedByContactID");

            migrationBuilder.CreateIndex(
                name: "IX_DesignScriptDataCards_OrderFlag",
                table: "DesignScriptDataCards",
                column: "OrderFlag");

            migrationBuilder.CreateIndex(
                name: "IX_DesignScriptDataCards_ProjectID",
                table: "DesignScriptDataCards",
                column: "ProjectID");

            migrationBuilder.CreateIndex(
                name: "IX_DesignScriptDataCards_ProjectUID",
                table: "DesignScriptDataCards",
                column: "ProjectUID");

            migrationBuilder.CreateIndex(
                name: "IX_DesignScriptDataCards_StatusFlag",
                table: "DesignScriptDataCards",
                column: "StatusFlag");

            migrationBuilder.CreateIndex(
                name: "IX_DesignScriptDataCards_Subtitle",
                table: "DesignScriptDataCards",
                column: "Subtitle");

            migrationBuilder.CreateIndex(
                name: "IX_DesignScriptDataCards_Title",
                table: "DesignScriptDataCards",
                column: "Title");

            migrationBuilder.CreateIndex(
                name: "IX_DesignScriptDataCards_TypeFlag",
                table: "DesignScriptDataCards",
                column: "TypeFlag");

            migrationBuilder.CreateIndex(
                name: "IX_DesignScriptDataCards_UID",
                table: "DesignScriptDataCards",
                column: "UID");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_DesignScriptDataCards_Category",
                table: "DesignScriptDataCards");

            migrationBuilder.DropIndex(
                name: "IX_DesignScriptDataCards_Code",
                table: "DesignScriptDataCards");

            migrationBuilder.DropIndex(
                name: "IX_DesignScriptDataCards_CodeFlag",
                table: "DesignScriptDataCards");

            migrationBuilder.DropIndex(
                name: "IX_DesignScriptDataCards_Created",
                table: "DesignScriptDataCards");

            migrationBuilder.DropIndex(
                name: "IX_DesignScriptDataCards_CreatedByContactID",
                table: "DesignScriptDataCards");

            migrationBuilder.DropIndex(
                name: "IX_DesignScriptDataCards_Filename",
                table: "DesignScriptDataCards");

            migrationBuilder.DropIndex(
                name: "IX_DesignScriptDataCards_IsDeleted",
                table: "DesignScriptDataCards");

            migrationBuilder.DropIndex(
                name: "IX_DesignScriptDataCards_IsHidden",
                table: "DesignScriptDataCards");

            migrationBuilder.DropIndex(
                name: "IX_DesignScriptDataCards_IsReadOnly",
                table: "DesignScriptDataCards");

            migrationBuilder.DropIndex(
                name: "IX_DesignScriptDataCards_LibraryCode",
                table: "DesignScriptDataCards");

            migrationBuilder.DropIndex(
                name: "IX_DesignScriptDataCards_Modified",
                table: "DesignScriptDataCards");

            migrationBuilder.DropIndex(
                name: "IX_DesignScriptDataCards_ModifiedByContactID",
                table: "DesignScriptDataCards");

            migrationBuilder.DropIndex(
                name: "IX_DesignScriptDataCards_OrderFlag",
                table: "DesignScriptDataCards");

            migrationBuilder.DropIndex(
                name: "IX_DesignScriptDataCards_ProjectID",
                table: "DesignScriptDataCards");

            migrationBuilder.DropIndex(
                name: "IX_DesignScriptDataCards_ProjectUID",
                table: "DesignScriptDataCards");

            migrationBuilder.DropIndex(
                name: "IX_DesignScriptDataCards_StatusFlag",
                table: "DesignScriptDataCards");

            migrationBuilder.DropIndex(
                name: "IX_DesignScriptDataCards_Subtitle",
                table: "DesignScriptDataCards");

            migrationBuilder.DropIndex(
                name: "IX_DesignScriptDataCards_Title",
                table: "DesignScriptDataCards");

            migrationBuilder.DropIndex(
                name: "IX_DesignScriptDataCards_TypeFlag",
                table: "DesignScriptDataCards");

            migrationBuilder.DropIndex(
                name: "IX_DesignScriptDataCards_UID",
                table: "DesignScriptDataCards");

            migrationBuilder.AlterColumn<Guid>(
                name: "UID",
                table: "DesignScriptDataCards",
                type: "uniqueidentifier",
                nullable: false,
                oldClrType: typeof(Guid),
                oldType: "uniqueidentifier",
                oldDefaultValueSql: "NEWID()");

            migrationBuilder.AlterColumn<int>(
                name: "TypeFlag",
                table: "DesignScriptDataCards",
                type: "int",
                nullable: false,
                oldClrType: typeof(int),
                oldType: "int",
                oldDefaultValue: 0);

            migrationBuilder.AlterColumn<int>(
                name: "StatusFlag",
                table: "DesignScriptDataCards",
                type: "int",
                nullable: false,
                oldClrType: typeof(int),
                oldType: "int",
                oldDefaultValue: 0);

            migrationBuilder.AlterColumn<string>(
                name: "SearchTags",
                table: "DesignScriptDataCards",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldNullable: true);

            migrationBuilder.AlterColumn<int>(
                name: "OrderFlag",
                table: "DesignScriptDataCards",
                type: "int",
                nullable: false,
                oldClrType: typeof(int),
                oldType: "int",
                oldDefaultValue: 0);

            migrationBuilder.AlterColumn<string>(
                name: "ModifiedBy",
                table: "DesignScriptDataCards",
                type: "nvarchar(max)",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(255)",
                oldMaxLength: 255,
                oldNullable: true);

            migrationBuilder.AlterColumn<bool>(
                name: "IsDeleted",
                table: "DesignScriptDataCards",
                type: "bit",
                nullable: false,
                oldClrType: typeof(bool),
                oldType: "bit",
                oldDefaultValue: false);

            migrationBuilder.AlterColumn<string>(
                name: "CreatedBy",
                table: "DesignScriptDataCards",
                type: "nvarchar(max)",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(255)",
                oldMaxLength: 255,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "Category",
                table: "DesignScriptDataCards",
                type: "nvarchar(max)",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(450)",
                oldNullable: true);

            migrationBuilder.AddColumn<string>(
                name: "_searchTags",
                table: "DesignScriptDataCards",
                type: "nvarchar(max)",
                nullable: true);
        }
    }
}
