﻿using MyCockpitView.WebApi.RequestTicketModule.Entities;
using MyCockpitView.WebApi.Services;

namespace MyCockpitView.WebApi.RequestTicketModule.Services;

public interface IRequestTicketService : IBaseEntityService<RequestTicket>
{
    Task<int> Create(RequestTicket Entity);
    Task<IEnumerable<int>> GetAssigneeContactIDs(int entityID, string stageCode, string propertyName);
    Task<dynamic> GetTaskByStage(string Entity, int EntityID, string StageCode, string StageTitle, decimal StageDuration, DateTime? FollowUpDate = null);
    Task<Guid> RecordReadonly(int ID);
    Task SendRequestTicket(int ID);
    Task TaskAction(int EntityID, string StageCode, int WFTaskID, string? taskComment = null);
}