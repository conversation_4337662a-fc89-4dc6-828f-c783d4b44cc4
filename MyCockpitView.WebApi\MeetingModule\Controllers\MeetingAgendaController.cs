﻿
using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.WebApi.Responses;
using MyCockpitView.Utility.Common;
using MyCockpitView.WebApi.ActivityModule;
using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.ContactModule.Services;
using MyCockpitView.WebApi.MeetingModule.Services;
using MyCockpitView.WebApi.MeetingModule.Dtos;
using MyCockpitView.WebApi.MeetingModule.Entities;
using MyCockpitView.WebApi.PackageModule.Services;
using MyCockpitView.WebApi.StatusMasterModule;
using MyCockpitView.WebApi.TypeMasterModule;
using MyCockpitView.WebApi.PackageModule.Entities;

namespace MyCockpitView.WebApi.MeetingModule.Controllers;

[Authorize]
[ApiController]
[Route("[controller]")]
public class MeetingAgendaController : ControllerBase
{
    private readonly IMeetingAgendaService service;
    private readonly EntitiesContext db;
    private readonly IActivityService activityService;
    private readonly IMapper mapper;
    private readonly IContactService contactService;
    private readonly IPackageService packageService;

    public MeetingAgendaController(EntitiesContext db, IMeetingAgendaService service, IMapper mapper, IActivityService activityService, IContactService contactService,IPackageService packageService  )
    {
        this.db = db;
        this.service = service;
        this.activityService = activityService;
        this.mapper = mapper;
        this.contactService = contactService;
        this.packageService = packageService;
    }

    [HttpGet]
    public async Task<ActionResult<IEnumerable<MeetingAgendaDto>>> Get(
        [FromQuery] string? filters = null,
        [FromQuery] string? search = null,
        [FromQuery] string? sort = null)
    {
        var query = service.Get(filters != null ? DataTools.GetObjectFromJsonString<APIFilter>(filters).Filters : null, search, sort).Include(x => x.Attachments);
        var results = mapper.Map<IEnumerable<MeetingAgendaDto>>(await query.ToListAsync());

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(MeetingAgenda))
            .ToListAsync();

        foreach (var obj in results)
        {
            obj.Type = typeMasters.FirstOrDefault(x => x.Value == obj.TypeFlag)?.Title ?? "";
        }

        var statusMasters = await db.StatusMasters
          .AsNoTracking()
          .Where(x => x.Entity == nameof(MeetingAgenda))
          .ToListAsync();

        foreach (var obj in results)
        {
            obj.Status = statusMasters.FirstOrDefault(x => x.Value == obj.StatusFlag)?.Title ?? "";
        }

        return Ok(results);
    }

    [HttpGet("Pages")]
    public async Task<ActionResult<PagedResponse<MeetingAgendaDto>>> GetPages(
        [FromQuery] int page = 0,
        [FromQuery] int pageSize = 50,
        [FromQuery] string? filters = null,
        [FromQuery] string? search = null,
        [FromQuery] string? sort = null)
    {
        var query = service.Get(filters != null ? DataTools.GetObjectFromJsonString<APIFilter>(filters).Filters : null, search, sort).Include(x => x.Attachments);

        var totalCount = await query.CountAsync();
        var totalPages = (int)Math.Ceiling((decimal)totalCount / pageSize);
        var results = mapper.Map<IEnumerable<MeetingAgendaDto>>(await query
            .Skip(pageSize * page)
            .Take(pageSize)
            .ToListAsync());

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(MeetingAgenda))
            .ToListAsync();

        foreach (var obj in results)
        {
            obj.Type = typeMasters.FirstOrDefault(x => x.Value == obj.TypeFlag)?.Title ?? "";
        }

        var statusMasters = await db.StatusMasters
           .AsNoTracking()
           .Where(x => x.Entity == nameof(MeetingAgenda))
           .ToListAsync();

        foreach (var obj in results)
        {
            obj.Status = statusMasters.FirstOrDefault(x => x.Value == obj.StatusFlag)?.Title ?? "";
        }

        return Ok(new PagedResponse<MeetingAgendaDto>(results, totalCount, totalPages));
    }

    [HttpGet("{id:int}")]
    public async Task<ActionResult<MeetingAgendaDto>> GetByID(int id)
    {
        var responseDto = mapper.Map<MeetingAgendaDto>(await service.GetById(id));
        if (responseDto == null)
        {
            return NotFound();
        }

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(MeetingAgenda))
            .ToListAsync();

        responseDto.Type = typeMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        var statusMasters = await db.StatusMasters
          .AsNoTracking()
          .Where(x => x.Entity == nameof(MeetingAgenda))
          .ToListAsync();

        responseDto.Status = statusMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        return Ok(responseDto);
    }

    [HttpPost]
    public async Task<ActionResult<MeetingAgendaDto>> Post(MeetingAgendaDto dto)
    {
        var id = await service.Create(mapper.Map<MeetingAgenda>(dto));
        var responseDto = mapper.Map<MeetingAgendaDto>(await service.GetById(id));

        if (responseDto == null)
            return BadRequest("Not Created");

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(MeetingAgenda))
            .ToListAsync();

        responseDto.Type = typeMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        var statusMasters = await db.StatusMasters
  .AsNoTracking()
  .Where(x => x.Entity == nameof(MeetingAgenda))
  .ToListAsync();

        responseDto.Status = statusMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        //if (!string.IsNullOrEmpty(User.Identity?.Name))
        //{
        //    var currentContact = await contactService.Get().FirstOrDefaultAsync(x => x.Username == User.Identity.Name);
        //    if (currentContact != null)
        //    {
        //        var parent = await contactService.Get().SingleOrDefaultAsync(x => x.ID == responseDto.ContactID);
        //        if (parent != null)
        //        {
        //            await activityService.LogUserActivity(currentContact, nameof(parent), parent.ID, $"{parent.Name}", $"{nameof(MeetingAgenda).Replace(nameof(parent),"")} | {responseDto.Company.Title} | {responseDto.Designation} | {responseDto.ManValue}mV", "Created");
        //        }
        //    }
        //}

        return CreatedAtAction(nameof(GetByID), new { id = responseDto.ID }, responseDto);
    }

    [HttpPut("{id:int}")]
    public async Task<ActionResult<MeetingAgendaDto>> Put(int id, MeetingAgendaDto dto)
    {
        if (id != dto.ID)
        {
            return BadRequest();
        }

        await service.Update(mapper.Map<MeetingAgenda>(dto));
        var responseDto = mapper.Map<MeetingAgendaDto>(await service.GetById(id));

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(MeetingAgenda))
            .ToListAsync();

        responseDto.Type = typeMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        var statusMasters = await db.StatusMasters
  .AsNoTracking()
  .Where(x => x.Entity == nameof(MeetingAgenda))
  .ToListAsync();

        responseDto.Status = statusMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        //if (!string.IsNullOrEmpty(User.Identity?.Name))
        //{
        //    var currentContact = await contactService.Get().FirstOrDefaultAsync(x => x.Username == User.Identity.Name);
        //    if (currentContact != null)
        //    {
        //        var parent = await contactService.Get().SingleOrDefaultAsync(x => x.ID == responseDto.ContactID);
        //        if (parent != null)
        //        {
        //            await activityService.LogUserActivity(currentContact, nameof(parent), parent.ID, $"{parent.Name}", $"{nameof(MeetingAgenda).Replace(nameof(parent), "")} | {responseDto.Company.Title} | {responseDto.Designation} | {responseDto.ManValue}mV", "Updated");
        //        }
        //    }
        //}

        return Ok(responseDto);
    }

    [HttpDelete("{id:int}")]
    public async Task<IActionResult> Delete(int id)
    {
        var responseDto = mapper.Map<MeetingAgendaDto>(await service.GetById(id));
        if (responseDto == null) return BadRequest($"{nameof(MeetingAgenda)} not found!");

        await service.Delete(id);

        //if (!string.IsNullOrEmpty(User.Identity?.Name))
        //{
        //    var currentContact = await contactService.Get().FirstOrDefaultAsync(x => x.Username == User.Identity.Name);
        //    if (currentContact != null)
        //    {
        //        var parent = await contactService.Get().SingleOrDefaultAsync(x => x.ID == responseDto.ContactID);
        //        if (parent != null)
        //        {
        //            await activityService.LogUserActivity(currentContact, nameof(parent), parent.ID, $"{parent.Name}", $"{nameof(MeetingAgenda).Replace(nameof(parent), "")} | {responseDto.Company.Title} | {responseDto.Designation} | {responseDto.ManValue}mV", "Deleted");
        //        }
        //    }
        //}

        return NoContent();
    }


    [HttpGet("uid/{id:guid}")]
    [AllowAnonymous]
    public async Task<ActionResult<MeetingAgendaDto>> GetByGUID(Guid id)
    {
        var responseDto = mapper.Map<MeetingAgendaDto>(await service.GetById(id));
        if (responseDto == null)
        {
            return NotFound();
        }

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(MeetingAgenda))
            .ToListAsync();

        responseDto.Type = typeMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        var statusMasters = await db.StatusMasters
                        .AsNoTracking()
                        .Where(x => x.Entity == nameof(MeetingAgenda))
                        .ToListAsync();

        responseDto.Status = statusMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        return Ok(responseDto);
    }
    [HttpGet("Count")]
    public async Task<IActionResult> GetCount(
        [FromQuery] string? filters = null,
        [FromQuery] string? search = null,
        [FromQuery] string? sort = null)
    {

        var query = service.Get(filters != null ? DataTools.GetObjectFromJsonString<APIFilter>(filters).Filters : null, search, sort);

        return Ok(await query.CountAsync());

    }

    [HttpGet("SearchTagOptions")]
    public async Task<IActionResult> GetSearchTagOptions()
    {
        var results = await service.GetSearchTagOptions();
        return Ok(results);
    }


    [HttpGet("FieldOptions")]
    public async Task<IActionResult> GetFieldOptions(string field)
    {
        return Ok(await service.GetFieldOptions(field));
    }



    [HttpPut("SendAsCNote/{id}")]
    public async Task<IActionResult> SendAsCNote(int id, [FromBody] CreateCNoteDto Dto)
    {


        if (id != Dto.MeetingAgenda.ID)
        {
            return BadRequest("Id are not equal");
        }

        var contact = await contactService.Get()
                  .SingleOrDefaultAsync(x => x.Username == User.Identity.Name);

        await service.SendAsCNote(mapper.Map<MeetingAgenda>(Dto.MeetingAgenda), contact, mapper.Map<IEnumerable<MeetingAttendee>>(Dto.Attendees));

        return Ok();

    }

    public class AgendaViewModel
    {
        public int ID { get; set; }
        public int MeetingID { get; set; }
        public DateTime? MeetingDate { get; set; }
        public string? MeetingTitle { get; set; } 
        public string? Title { get; set; } 
        public string? Subtitle { get; set; }
        public string? Zone { get; set; }
        public string? Space { get; set; }
        public string? Element { get; set; }
        public DateTime? DueDate { get; set; }
        public string? ActionBy { get; set; }
        public string? ActionByCompany { get; set; }
        public int? ActionByContactID { get; set; }
        public string? ActionByEmail { get; set; }
        public string? ActionByName { get; set; }
        public string? Comment { get; set; }
        public DateTime Created { get; set; }
        public string? CreatedBy { get; set; } 
        public int? CreatedByContactID { get; set; }
        public DateTime Modified { get; set; }
        public string? ModifiedBy { get; set; } 
        public int? ModifiedByContactID { get; set; }
        public int StatusFlag { get; set; }
        public int TypeFlag { get; set; }
        public string? UpdateFrom { get; set; }
        public int ReminderCount { get; set; }
        public int? PackageID { get; set; }
        public int? ProjectID { get; set; }
        public string? StatusValue { get; set; } 
        public string? TypeValue { get; set; } 
        public object? Package { get; set; }
    }

    [HttpGet("CockpitPagedList")]
    public async Task<ActionResult<PagedResponse<AgendaViewModel>>> GetCockpitPagedList(
        [FromQuery] int page = 0,
        [FromQuery] int pageSize = 20,
        [FromQuery] string? filters = null,
        [FromQuery] string? search = null,
        [FromQuery] string? sort = null,
        [FromQuery] bool showAll = false)
    {
        var query = service.Get(filters != null ? DataTools.GetObjectFromJsonString<APIFilter>(filters).Filters : null, search, sort);

        var totalCount = await query.CountAsync();
        var totalPages = pageSize == 0 ? 1 : (int)Math.Ceiling((decimal)totalCount / pageSize);

        if (pageSize > 0)
        {
            query = query
                .Skip(pageSize * page)
                .Take(pageSize);
        }

        var agenda = await query
            .Select(x => new
            {
                x.ID,
                x.MeetingID,
                x.MeetingDate,
                x.MeetingTitle,
                x.Title,
                x.Subtitle,
                x.Zone,
                x.Space,
                x.Element,
                x.DueDate,
                x.ActionBy,
                x.ActionByCompany,
                x.ActionByContactID,
                x.ActionByEmail,
                x.ActionByName,
                x.Comment,
                x.Created,
                x.CreatedBy,
                x.CreatedByContactID,
                x.Modified,
                x.ModifiedBy,
                x.ModifiedByContactID,
                x.StatusFlag,
                x.TypeFlag,
                x.UpdateFrom,
                x.ReminderCount,
                x.PackageID,
                x.ProjectID,
            })
            .ToListAsync();

        var statusMasters = await db.StatusMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(Package) || x.Entity == nameof(MeetingAgenda))
            .Select(x => new { x.Entity, x.Title, x.Value })
            .ToListAsync();

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(Package) || x.Entity == nameof(Meeting))
            .Select(x => new { x.Entity, x.Title, x.Value })
            .ToListAsync();

        var agendaData = agenda
            .Join(
                statusMasters.Where(x => x.Entity == nameof(MeetingAgenda)),
                a => a.StatusFlag,
                b => b.Value,
                (a, b) => new { Agenda = a, StatusValue = b.Title })
            .Join(
                typeMasters.Where(x => x.Entity == nameof(Meeting)),
                a => a.Agenda.TypeFlag,
                b => b.Value,
                (a, b) => new
                {
                    a.Agenda.ID,
                    a.Agenda.MeetingID,
                    a.Agenda.MeetingDate,
                    a.Agenda.MeetingTitle,
                    a.Agenda.Title,
                    a.Agenda.Subtitle,
                    a.Agenda.Zone,
                    a.Agenda.Space,
                    a.Agenda.Element,
                    a.Agenda.DueDate,
                    a.Agenda.ActionBy,
                    a.Agenda.ActionByCompany,
                    a.Agenda.ActionByContactID,
                    a.Agenda.ActionByEmail,
                    a.Agenda.ActionByName,
                    a.Agenda.Comment,
                    a.Agenda.Created,
                    a.Agenda.CreatedBy,
                    a.Agenda.CreatedByContactID,
                    a.Agenda.Modified,
                    a.Agenda.ModifiedBy,
                    a.Agenda.ModifiedByContactID,
                    a.Agenda.StatusFlag,
                    a.Agenda.TypeFlag,
                    a.Agenda.UpdateFrom,
                    a.Agenda.ReminderCount,
                    a.Agenda.PackageID,
                    a.Agenda.ProjectID,
                    a.StatusValue,
                    TypeValue = b.Title
                });

        var packageIds = agenda
            .Where(a => a.PackageID != null)
            .Select(a => a.PackageID);

        var packages = await packageService.Get()
            .Where(x => packageIds.Any(p => p == x.ID))
            .Select(x => new
            {
                x.ID,
                x.Title,
                x.StartDate,
                x.FinalDate,
                x.SubmissionDate,
                x.StatusFlag,
                x.TypeFlag
            })
            .ToListAsync();

        var packageData = packages
            .Join(
                statusMasters.Where(x => x.Entity == nameof(Package)),
                a => a.StatusFlag,
                b => b.Value,
                (a, b) => new { Package = a, StatusValue = b.Title })
            .Join(
                typeMasters.Where(x => x.Entity == nameof(Package)),
                a => a.Package.TypeFlag,
                b => b.Value,
                (a, b) => new
                {
                    a.Package.ID,
                    a.Package.Title,
                    a.Package.StartDate,
                    a.Package.FinalDate,
                    a.Package.SubmissionDate,
                    a.Package.StatusFlag,
                    a.Package.TypeFlag,
                    a.StatusValue,
                    TypeValue = b.Title
                });

        var result = agendaData
            .GroupJoin(
                packageData,
                a => a.PackageID,
                b => b.ID,
                (a, b) => new { agenda = a, packages = b.DefaultIfEmpty() })
            .SelectMany(
                x => x.packages,
                (x, pkg) => new AgendaViewModel
                {
                    ID = x.agenda.ID,
                    MeetingID = x.agenda.MeetingID,
                    MeetingDate = x.agenda.MeetingDate,
                    MeetingTitle = x.agenda.MeetingTitle,
                    Title = x.agenda.Title,
                    Subtitle = x.agenda.Subtitle,
                    Zone = x.agenda.Zone,
                    Space = x.agenda.Space,
                    Element = x.agenda.Element,
                    DueDate = x.agenda.DueDate,
                    ActionBy = x.agenda.ActionBy,
                    ActionByCompany = x.agenda.ActionByCompany,
                    ActionByContactID = x.agenda.ActionByContactID,
                    ActionByEmail = x.agenda.ActionByEmail,
                    ActionByName = x.agenda.ActionByName,
                    Comment = x.agenda.Comment,
                    Created = x.agenda.Created,
                    CreatedBy = x.agenda.CreatedBy,
                    CreatedByContactID = x.agenda.CreatedByContactID,
                    Modified = x.agenda.Modified,
                    ModifiedBy = x.agenda.ModifiedBy,
                    ModifiedByContactID = x.agenda.ModifiedByContactID,
                    StatusFlag = x.agenda.StatusFlag,
                    TypeFlag = x.agenda.TypeFlag,
                    UpdateFrom = x.agenda.UpdateFrom,
                    ReminderCount = x.agenda.ReminderCount,
                    PackageID = x.agenda.PackageID,
                    ProjectID = x.agenda.ProjectID,
                    StatusValue = x.agenda.StatusValue,
                    TypeValue = x.agenda.TypeValue,
                    Package = pkg
                });

        return Ok(new PagedResponse<AgendaViewModel>(result, totalCount, totalPages));
    }

    [HttpGet("GroupPages")]
    public async Task<ActionResult<PagedResponse<MeetingAgendaGroup>>> GetGroupPages(
        [FromQuery] int page = 0,
        [FromQuery] int pageSize = 20,
        [FromQuery] string? filters = null,
        [FromQuery] string? search = null,
        [FromQuery] string? sort = null,
        [FromQuery] string? groupBy = null)
    {
        var query = service.Get(filters != null ? DataTools.GetObjectFromJsonString<APIFilter>(filters).Filters : null, search, sort)
            .Include(x => x.Meeting);

        var groups = service.GetGroups(
            query,
            groupBy != null ? DataTools.GetObjectFromJsonString<string[]>(groupBy) : null
        );

        var totalCount = await groups.CountAsync();
        var totalPages = pageSize == 0 ? 1 : (int)Math.Ceiling((decimal)totalCount / pageSize);

        if (pageSize > 0)
        {
            groups = groups
                .Skip(pageSize * page)
                .Take(pageSize);
        }

        var results = await groups.ToListAsync();

        foreach (var obj in results)
        {
            obj.IsDelayed = obj.DueDate?.ToUniversalTime() < DateTime.UtcNow;
        }

        return Ok(new PagedResponse<MeetingAgendaGroup>(results, totalCount, totalPages));
    }

    [AllowAnonymous]
    [HttpGet("GenerateTasks")]
    public async Task<IActionResult> GenerateTasks()
    {

        await service.GenerateTasks();
        return Ok();

    }

    [AllowAnonymous]
    [HttpGet("SendAgendaFollowUpEmails")]
    public async Task<IActionResult> SendAgendaFollowUpEmails()
    {

        await service.SendAgendaFollowUpEmails();
        return Ok();

    }

    [AllowAnonymous]
    [HttpGet("SendPartnerAgendaReports")]
    public async Task<IActionResult> SendPartnerAgendaReports()
    {

        //await _service.SendPartnerAgendaReports();
        return Ok();

    }
}