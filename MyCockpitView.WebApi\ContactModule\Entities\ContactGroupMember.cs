﻿using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.CoreModule;
using System.ComponentModel.DataAnnotations;


namespace MyCockpitView.WebApi.ContactModule.Entities;

public class ContactGroupMember : BaseEntity
{
    [Required]
    public int ContactID { get; set; }
    public virtual Contact? Contact { get; set; }
    [Required]
    public int ContactGroupID { get; set; }
    public virtual ContactGroup? ContactGroup { get; set; }

}
public class ContactGroupMemberConfiguration : BaseEntityConfiguration<ContactGroupMember>, IEntityTypeConfiguration<ContactGroupMember>
{
    public void Configure(EntityTypeBuilder<ContactGroupMember> builder)
    {
        base.Configure(builder);

        builder
         .HasOne(u => u.Contact)
          .WithMany()
          .HasForeignKey(x => x.ContactID)
          .IsRequired()
          .OnDelete(DeleteBehavior.Restrict);

    }
}