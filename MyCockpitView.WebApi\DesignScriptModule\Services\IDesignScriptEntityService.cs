﻿using MyCockpitView.CoreModule;
using MyCockpitView.Utility.RDLCClient;
using MyCockpitView.WebApi.DesignScriptModule.Entities;
using MyCockpitView.WebApi.ProjectModule.Entities;
using MyCockpitView.WebApi.Services;

namespace MyCockpitView.WebApi.DesignScriptModule.Services;

public interface IDesignScriptEntityService : IBaseEntityService<DesignScriptEntity>
{
    Task<ReportDefinition> GetDesignCanvasPDF(Guid projectUID, string pageSize = "a4", IEnumerable<QueryFilter>? filters = null, bool AutoPageNumber = true, int TotalPages = 0, int LastPageNumber = 0);
    Task<ReportDefinition> GetDesignIntentPDF(Guid projectUID, string reportSize = "a4", IEnumerable<QueryFilter>? filters = null);
    Task<ReportDefinition> GetDesignIntentPDF(string reportSize, Project project, bool showAttributes, List<DesignIntentReportData> data);
    Task<ReportDefinition> GetDesignScriptPDF(Guid projectUID, string reportSize = "a4", IEnumerable<QueryFilter>? filters = null, bool AutoPageNumber = true, int TotalPages = 0, int LastPageNumber = 0);
    Task<ReportDefinition> GetElementBOQ(Guid projectUID, string reportSize = "a4", IEnumerable<QueryFilter>? filters = null, string renderType = "PDF", bool AutoPageNumber = true, int TotalPages = 0, int LastPageNumber = 0, string reportTitle = "BILL OF QUANTITIES [E]", string filename = "EBOQ");
    Task<ReportDefinition> GetElementBOQSummary(Guid projectUID, string reportSize = "a4", IEnumerable<QueryFilter>? filters = null, string renderType = "PDF", bool AutoPageNumber = true, int TotalPages = 0, int LastPageNumber = 0);
    Task<ReportDefinition> GetElementEstimate(Guid projectUID, string pageSize = "a4", IEnumerable<QueryFilter>? filters = null, string renderType = "PDF", bool AutoPageNumber = true, int TotalPages = 0, int LastPageNumber = 0);
    Task<ReportDefinition> GetElementEstimatedBOQ(Guid projectUID, string reportSize = "a4", IEnumerable<QueryFilter>? filters = null, string renderType = "PDF", bool AutoPageNumber = true, int TotalPages = 0, int LastPageNumber = 0);
    Task<byte[]> GetElementEstimateExcel(Guid ProjectUID, IEnumerable<QueryFilter>? filters = null);
    Task<ReportDefinition> GetElementMeasurementSheet(Guid projectUID, string reportSize = "a4", IEnumerable<QueryFilter> filters = null, string renderType = "PDF", bool AutoPageNumber = true, int TotalPages = 0, int LastPageNumber = 0, string reportTitle = "MEASUREMENT SHEET [E]", string filename = "Measurement Sheet [E]");
    Task<byte[]> GetEntityListExcel(Guid ProjectUID, IEnumerable<QueryFilter>? filters = null);
    Task<ReportDefinition> GetItemList(Guid projectUID, string reportSize = "a4", IEnumerable<QueryFilter>? filters = null, string renderType = "PDF", bool AutoPageNumber = true, int TotalPages = 0, int LastPageNumber = 0, bool showDWGSpec = false);
    Task<byte[]> GetItemListExcel(Guid ProjectUID, IEnumerable<QueryFilter>? filters = null);
    Task<ReportDefinition> GetMaterialListPDF(Guid projectUID, string reportSize = "a4", IEnumerable<QueryFilter>? filters = null, bool AutoPageNumber = true, int TotalPages = 0, int LastPageNumber = 0);
    Task<string> GetNextCode(int TypeFlag, int ProjectID, int? ParentID = null);
    Task<ReportDefinition> GetPackageSetPDF(Guid projectUID, string reportSize = "a4", IEnumerable<QueryFilter>? filters = null);
    Task<ReportDefinition> GetPalettePDF(Guid projectUID, string reportSize = "a3", IEnumerable<QueryFilter>? filters = null, bool AutoPageNumber = true, int TotalPages = 0, int LastPageNumber = 0);
    List<int> GetRecursiveChildrenIDs(IEnumerable<DesignScriptEntity> entities, IEnumerable<int> _entityIDs);
    Task<List<int>> GetRecursiveChildrenIDsAsync(IEnumerable<int> _entityIDs, IEnumerable<DesignScriptEntity>? SourceList = null);
    Task<ReportDefinition> GetSchedule(Guid projectUID, string reportSize = "a4", IEnumerable<QueryFilter>? filters = null, bool AutoPageNumber = true, int TotalPages = 0, int LastPageNumber = 0, string reportType = "PDF");
    Task<byte[]> GetScheduleExcel(Guid projectUID, IEnumerable<QueryFilter>? filters = null);
    Task<IEnumerable<string>> GetSearchTagOptions(int ProjectID);
    Task<ReportDefinition> GetSpaceEstimate(Guid projectUID, string pageSize = "a4", IEnumerable<QueryFilter>? filters = null, string renderType = "PDF", bool AutoPageNumber = true, int TotalPages = 0, int LastPageNumber = 0);
    Task<byte[]> GetSpaceEstimateExcel(Guid ProjectUID, IEnumerable<QueryFilter>? filters = null);
    Task<ReportDefinition> GetZoneBOQ(Guid projectUID, string reportSize = "a4", IEnumerable<QueryFilter>? filters = null, string renderType = "PDF", bool AutoPageNumber = true, int TotalPages = 0, int LastPageNumber = 0, string reportTitle = "BILL OF QUANTITIES [Z]", string filename = "ZBOQ");
    Task<ReportDefinition> GetZoneBOQSummary(Guid projectUID, string reportSize = "a4", IEnumerable<QueryFilter>? filters = null, string renderType = "PDF", bool AutoPageNumber = true, int TotalPages = 0, int LastPageNumber = 0);
    Task<ReportDefinition> GetZoneEstimatedBOQ(Guid projectUID, string reportSize = "a4", IEnumerable<QueryFilter>? filters = null, string renderType = "PDF", bool AutoPageNumber = true, int TotalPages = 0, int LastPageNumber = 0);
    Task<ReportDefinition> GetZoneMeasurementSheet(Guid projectUID, string reportSize = "a4", IEnumerable<QueryFilter>? filters = null, string renderType = "PDF", bool AutoPageNumber = true, int TotalPages = 0, int LastPageNumber = 0, string reportTitle = "MEASUREMENT SHEET [Z]", string filename = "Measurement Sheet [Z]");
    Task SetMasterPhase(int ProjectID);
}