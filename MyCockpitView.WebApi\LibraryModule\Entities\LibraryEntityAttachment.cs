﻿using System.ComponentModel.DataAnnotations;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.CoreModule;


namespace MyCockpitView.WebApi.LibraryModule.Entities;

public class LibraryEntityAttachment : BaseBlobEntity
{
    [Required]
    public int LibraryEntityID { get; set; }

    public virtual LibraryEntity? LibraryEntity { get; set; }

    [StringLength(255)]
    public string? Title { get; set; }
}

public class LibraryEntityAttachmentConfiguration : BaseBlobEntityConfiguration<LibraryEntityAttachment>, IEntityTypeConfiguration<LibraryEntityAttachment>
{
    public void Configure(EntityTypeBuilder<LibraryEntityAttachment> builder)
    {
      base.Configure(builder);

    }
}
