﻿using AutoMapper;
using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.ProjectModule.Entities;

namespace MyCockpitView.WebApi.ProjectModule.Dtos;

public class ProjectGigPointDto : BaseEntityDto
{

    public int ProjectID { get; set; }

    public string? Comment { get; set; }

    public decimal RecordValue { get; set; }

    public DateTime RecordDate { get; set; }

}

public class ProjectGigPointDtoMapperProfile : Profile
{
    public ProjectGigPointDtoMapperProfile()
    {
        CreateMap<ProjectGigPoint, ProjectGigPointDto>()
      .ReverseMap()
   .ForMember(dest => dest.Project, opt => opt.Ignore());

    }
}
