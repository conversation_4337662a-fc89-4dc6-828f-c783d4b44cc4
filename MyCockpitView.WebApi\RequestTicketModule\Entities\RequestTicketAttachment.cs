﻿using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.CoreModule;
using System.ComponentModel.DataAnnotations;


namespace MyCockpitView.WebApi.RequestTicketModule.Entities;

public class RequestTicketAttachment : BaseBlobEntity
{
    [Required]
    public int RequestTicketID { get; set; }
    public virtual RequestTicket? RequestTicket { get; set; }
}
public class RequestTicketAttachmentConfiguration : BaseBlobEntityConfiguration<RequestTicketAttachment>, IEntityTypeConfiguration<RequestTicketAttachment>
{
    public void Configure(EntityTypeBuilder<RequestTicketAttachment> builder)
    {
        base.Configure(builder);
       

    }
}