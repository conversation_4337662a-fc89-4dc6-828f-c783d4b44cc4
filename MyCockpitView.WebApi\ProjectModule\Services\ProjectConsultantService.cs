﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.WebApi.ProjectModule.Entities;
using MyCockpitView.WebApi.Services;





namespace MyCockpitView.WebApi.ProjectModule.Services;

public class ProjectConsultantService : BaseEntityService<ProjectConsultant>, IProjectConsultantService
{
    public ProjectConsultantService(EntitiesContext db) : base(db) { }

    public async Task<ProjectConsultant?> GetById(int Id)
    {

        return await db.ProjectConsultants.AsNoTracking()
            .Include(x => x.Contact)
             .SingleOrDefaultAsync(i => i.ID == Id);

    }


}