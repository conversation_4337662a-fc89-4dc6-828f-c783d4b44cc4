﻿using AutoMapper;
using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.DesignScriptModule.Entities;

namespace MyCockpitView.WebApi.DesignScriptModule.Dtos;

public class DesignScriptItemDto : BaseEntityDto
{


    public string? DSR { get; set; }


    public string? DSRNumber { get; set; }


    public string? Title { get; set; }

    public string? ItemGroup { get; set; }


    public string? Category { get; set; }
    public int CategoryOrderFlag { get; set; }
    public string? SubCategory { get; set; }
    public int SubCategoryOrderFlag { get; set; }
    public string? Reference { get; set; }
    public string? Specification { get; set; }
    public string? DrawingSpecification { get; set; }
    public string? Units { get; set; }
    public decimal Rate { get; set; }


    public int CodeFlag { get; set; }


    public string? Code { get; set; }


    public string? ProjectCode { get; set; }
    public int ProjectID { get; set; }

    //public virtual ICollection<DesignScriptEntityItemMapDto> Entities { get; set; } = new List<DesignScriptEntityItemMapDto>();

    public int? MasterID { get; set; }
}

public class DesignScriptItemWithoutMapDto : BaseEntityDto
{


    public string? DSR { get; set; }


    public string? DSRNumber { get; set; }


    public string? Title { get; set; }

    public string? ItemGroup { get; set; }


    public string? Category { get; set; }
    public int CategoryOrderFlag { get; set; }
    public string? SubCategory { get; set; }
    public int SubCategoryOrderFlag { get; set; }
    public string? Reference { get; set; }
    public string? Specification { get; set; }
    public string? DrawingSpecification { get; set; }
    public string? Units { get; set; }
    public decimal Rate { get; set; }


    public int CodeFlag { get; set; }


    public string? Code { get; set; }


    public string? ProjectCode { get; set; }
    public int ProjectID { get; set; }

    public virtual ICollection<DesignScriptEntityItemMapDto> Entities { get; set; } = new List<DesignScriptEntityItemMapDto>();

    public int? MasterID { get; set; }
}

public class DesignScriptItemDtoMapperProfile : Profile
{
    public DesignScriptItemDtoMapperProfile()
    {
        CreateMap<DesignScriptItem, DesignScriptItemWithoutMapDto>();

        CreateMap<DesignScriptItem, DesignScriptItemDto>()
             //.ForMember(dest => dest.Entities, opt => opt.MapFrom(src => src.Entities))
         .ReverseMap()
         //.ForMember(dest => dest.Project, opt => opt.Ignore())
         .ForMember(dest => dest.Entities, opt => opt.Ignore());

    }
}
