﻿
using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.WebApi.Responses;
using MyCockpitView.WebApi.ContactModule.Dtos;
using MyCockpitView.WebApi.ContactModule.Entities;
using MyCockpitView.WebApi.ContactModule.Services;
using MyCockpitView.Utility.Common;
using MyCockpitView.WebApi.ActivityModule;
using MyCockpitView.CoreModule;

namespace MyCockpitView.WebApi.ContactModule.Controllers;

[Authorize]
[ApiController]
[Route("[controller]")]
public class ContactGroupMemberController : ControllerBase
{
    private readonly IContactGroupMemberService service;
    private readonly EntitiesContext db;
    private readonly IActivityService activityService;
    private readonly IMapper mapper;
    private readonly IContactService contactService;

    public ContactGroupMemberController(EntitiesContext db, IContactGroupMemberService service, IMapper mapper, IActivityService activityService, IContactService contactService   )
    {
        this.db = db;
        this.service = service;
        this.activityService = activityService;
        this.mapper = mapper;
        this.contactService = contactService;
    }

    [HttpGet]
    public async Task<ActionResult<IEnumerable<ContactGroupMemberDto>>> Get(
        [FromQuery] string? filters = null,
        [FromQuery] string? search = null,
        [FromQuery] string? sort = null)
    {
        var query = service.Get(filters != null ? DataTools.GetObjectFromJsonString<APIFilter>(filters).Filters : null, search, sort).Include(x => x.Contact);
        var results = mapper.Map<IEnumerable<ContactGroupMemberDto>>(await query.ToListAsync());

        //var typeMasters = await db.TypeMasters
        //    .AsNoTracking()
        //    .Where(x => x.Entity == nameof(ContactGroupMember))
        //    .ToListAsync();

        //foreach (var obj in results)
        //{
        //    obj.TypeValue = typeMasters.FirstOrDefault(x => x.Value == obj.TypeFlag)?.Title ?? "";
        //}

        //var statusMasters = await db.StatusMasters
        //  .AsNoTracking()
        //  .Where(x => x.Entity == nameof(ContactGroupMember))
        //  .ToListAsync();

        //foreach (var obj in results)
        //{
        //    obj.StatusValue = statusMasters.FirstOrDefault(x => x.Value == obj.StatusFlag)?.Title ?? "";
        //}

        return Ok(results);
    }

    [HttpGet("Pages")]
    public async Task<ActionResult<PagedResponse<ContactGroupMemberDto>>> GetPages(
        [FromQuery] int page = 0,
        [FromQuery] int pageSize = 50,
        [FromQuery] string? filters = null,
        [FromQuery] string? search = null,
        [FromQuery] string? sort = null)
    {
        var query = service.Get(filters != null ? DataTools.GetObjectFromJsonString<APIFilter>(filters).Filters : null, search, sort).Include(x => x.Contact);

        var totalCount = await query.CountAsync();
        var totalPages = (int)Math.Ceiling((decimal)totalCount / pageSize);
        var results = mapper.Map<IEnumerable<ContactGroupMemberDto>>(await query
            .Skip(pageSize * page)
            .Take(pageSize)
            .ToListAsync());

        //var typeMasters = await db.TypeMasters
        //    .AsNoTracking()
        //    .Where(x => x.Entity == nameof(ContactGroupMember))
        //    .ToListAsync();

        //foreach (var obj in results)
        //{
        //    obj.TypeValue = typeMasters.FirstOrDefault(x => x.Value == obj.TypeFlag)?.Title ?? "";
        //}

        //var statusMasters = await db.StatusMasters
        //   .AsNoTracking()
        //   .Where(x => x.Entity == nameof(ContactGroupMember))
        //   .ToListAsync();

        //foreach (var obj in results)
        //{
        //    obj.StatusValue = statusMasters.FirstOrDefault(x => x.Value == obj.StatusFlag)?.Title ?? "";
        //}

        return Ok(new PagedResponse<ContactGroupMemberDto>(results, totalCount, totalPages));
    }

    [HttpGet("{id:int}")]
    public async Task<ActionResult<ContactGroupMemberDto>> GetByID(int id)
    {
        var responseDto = mapper.Map<ContactGroupMemberDto>(await service.GetById(id));
        if (responseDto == null)
        {
            return NotFound();
        }

        //var typeMasters = await db.TypeMasters
        //    .AsNoTracking()
        //    .Where(x => x.Entity == nameof(ContactGroupMember))
        //    .ToListAsync();

        //responseDto.TypeValue = typeMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        //var statusMasters = await db.StatusMasters
        //  .AsNoTracking()
        //  .Where(x => x.Entity == nameof(ContactGroupMember))
        //  .ToListAsync();

        //responseDto.StatusValue = statusMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        return Ok(responseDto);
    }

    [HttpPost]
    public async Task<ActionResult<ContactGroupMemberDto>> Post(ContactGroupMemberDto dto)
    {
        var id = await service.Create(mapper.Map<ContactGroupMember>(dto));
        var responseDto = mapper.Map<ContactGroupMemberDto>(await service.GetById(id));

        if (responseDto == null)
            return BadRequest("Not Created");

  //      var typeMasters = await db.TypeMasters
  //          .AsNoTracking()
  //          .Where(x => x.Entity == nameof(ContactGroupMember))
  //          .ToListAsync();

  //      responseDto.TypeValue = typeMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

  //      var statusMasters = await db.StatusMasters
  //.AsNoTracking()
  //.Where(x => x.Entity == nameof(ContactGroupMember))
  //.ToListAsync();

  //      responseDto.StatusValue = statusMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        //if (!string.IsNullOrEmpty(User.Identity?.Name))
        //{
        //    var currentContact = await contactService.Get().FirstOrDefaultAsync(x => x.Username == User.Identity.Name);
        //    if (currentContact != null)
        //    {
        //        var parent = await contactService.Get().SingleOrDefaultAsync(x => x.ID == responseDto.ContactID);
        //        if (parent != null)
        //        {
        //            await activityService.LogUserActivity(currentContact, nameof(parent), parent.ID, $"{parent.Name}", $"{nameof(ContactGroupMember).Replace(nameof(parent),"")} | {responseDto.Title} | {responseDto.Members.Count} members", "Created");
        //        }
        //    }
        //}

        return CreatedAtAction(nameof(GetByID), new { id = responseDto.ID }, responseDto);
    }

    [HttpPut("{id:int}")]
    public async Task<ActionResult<ContactGroupMemberDto>> Put(int id, ContactGroupMemberDto dto)
    {
        if (id != dto.ID)
        {
            return BadRequest();
        }

        await service.Update(mapper.Map<ContactGroupMember>(dto));
        var responseDto = mapper.Map<ContactGroupMemberDto>(await service.GetById(id));

  //      var typeMasters = await db.TypeMasters
  //          .AsNoTracking()
  //          .Where(x => x.Entity == nameof(ContactGroupMember))
  //          .ToListAsync();

  //      responseDto.TypeValue = typeMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

  //      var statusMasters = await db.StatusMasters
  //.AsNoTracking()
  //.Where(x => x.Entity == nameof(ContactGroupMember))
  //.ToListAsync();

  //      responseDto.StatusValue = statusMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        //if (!string.IsNullOrEmpty(User.Identity?.Name))
        //{
        //    var currentContact = await contactService.Get().FirstOrDefaultAsync(x => x.Username == User.Identity.Name);
        //    if (currentContact != null)
        //    {
        //        var parent = await contactService.Get().SingleOrDefaultAsync(x => x.ID == responseDto.ContactID);
        //        if (parent != null)
        //        {
        //            await activityService.LogUserActivity(currentContact, nameof(parent), parent.ID, $"{parent.Name}", $"{nameof(ContactGroupMember).Replace(nameof(parent), "")} | {responseDto.Title} | {responseDto.Members.Count} members", "Updated");
        //        }
        //    }
        //}

        return Ok(responseDto);
    }

    [HttpDelete("{id:int}")]
    public async Task<IActionResult> Delete(int id)
    {
        var responseDto = mapper.Map<ContactGroupMemberDto>(await service.GetById(id));
        if (responseDto == null) return BadRequest($"{nameof(ContactGroupMember)} not found!");

        await service.Delete(id);

        //if (!string.IsNullOrEmpty(User.Identity?.Name))
        //{
        //    var currentContact = await contactService.Get().FirstOrDefaultAsync(x => x.Username == User.Identity.Name);
        //    if (currentContact != null)
        //    {
        //        var parent = await contactService.Get().SingleOrDefaultAsync(x => x.ID == responseDto.ContactID);
        //        if (parent != null)
        //        {
        //            await activityService.LogUserActivity(currentContact, nameof(parent), parent.ID, $"{parent.Name}", $"{nameof(ContactGroupMember).Replace(nameof(parent), "")} | {responseDto.Title} | {responseDto.Members.Count} members", "Deleted");
        //        }
        //    }
        //}

        return NoContent();
    }


    [HttpGet("uid/{id:guid}")]
    [AllowAnonymous]
    public async Task<ActionResult<ContactGroupMemberDto>> GetByGUID(Guid id)
    {
        var responseDto = mapper.Map<ContactGroupMemberDto>(await service.GetById(id));
        if (responseDto == null)
        {
            return NotFound();
        }

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(ContactGroupMember))
            .ToListAsync();

        responseDto.Type = typeMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        var statusMasters = await db.StatusMasters
                        .AsNoTracking()
                        .Where(x => x.Entity == nameof(ContactGroupMember))
                        .ToListAsync();

        responseDto.Status = statusMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        return Ok(responseDto);
    }


    [HttpGet("SearchTagOptions")]
    public async Task<IActionResult> GetSearchTagOptions()
    {
        var results = await service.GetSearchTagOptions();
        return Ok(results);
    }

    [HttpGet("FieldOptions")]
    public async Task<IActionResult> GetFieldOptions(string field)
    {
        return Ok(await service.GetFieldOptions(field));
    }


}