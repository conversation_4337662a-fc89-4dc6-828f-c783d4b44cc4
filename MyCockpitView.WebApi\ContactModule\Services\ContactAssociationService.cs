﻿




using Microsoft.EntityFrameworkCore;
using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.ContactModule.Entities;
using MyCockpitView.WebApi.Services;

namespace MyCockpitView.WebApi.ContactModule.Services;

public class ContactAssociationService : BaseEntityService<ContactAssociation>, IContactAssociationService
{
    public ContactAssociationService(EntitiesContext db) : base(db) { }

    public IQueryable<ContactAssociation> Get(IEnumerable<QueryFilter>? Filters = null, string? Search = null, string? Sort = null)
    {

        IQueryable<ContactAssociation> _query = base.Get(Filters);

        //Apply filters
        if (Filters != null)
        {

            if (Filters.Where(x => x.Key.Equals("ContactID", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<ContactAssociation>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("ContactID", StringComparison.OrdinalIgnoreCase)))
                {
                    var isNumeric = Convert.ToInt32(_item.Value);

                    predicate = predicate.Or(x => x.PersonContactID == isNumeric);
                }
                _query = _query.Where(predicate);
            }

        }



        return _query.OrderByDescending(x => x.Created);

    }
    public async Task<ContactAssociation?> GetById(int Id)
    {

        return await db.ContactAssociations.AsNoTracking()
             .Include(x => x.Person)
              .Include(x => x.Company)
              .SingleOrDefaultAsync(i => i.ID == Id);


    }

    public async Task<ContactAssociation?> GetByGUID(Guid Id)
    {

        return await db.ContactAssociations.AsNoTracking()
            .Include(x => x.Person)
             .Include(x => x.Company)
             .SingleOrDefaultAsync(i => i.UID == Id);


    }

}