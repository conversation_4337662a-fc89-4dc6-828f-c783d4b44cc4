using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.WebApi.ContactModule.Entities;
using MyCockpitView.CoreModule;

using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace MyCockpitView.WebApi.WFTaskModule.Entities;
public class WFTask : BaseEntity
{
    public int? AssignerContactID { get; set; }

    public virtual Contact? Assigner { get; set; }

    [Required]
    public int ContactID { get; set; }

    public virtual Contact? Contact { get; set; }

    
    [StringLength(255)]
    public string? Title { get; set; }

    [StringLength(255)]
    public string? Subtitle { get; set; }

    
    [StringLength(255)]
    public string? WFStageCode { get; set; }

    
    public int? StageIndex { get; set; }

    
    public int StageRevision { get; set; }

    
    [Required]

    [Column(TypeName = "datetime2")]
    public DateTime StartDate { get; set; }

    
    [Column(TypeName = "datetime2")]
    public DateTime? CompletedDate { get; set; }

    
    [Required]

    [Column(TypeName = "datetime2")]
    public DateTime DueDate { get; set; }

    [Column(TypeName = "datetime2")]
    public DateTime? FollowUpDate { get; set; }
    public int OutcomeFlag { get; set; }

    public string? Comment { get; set; }
    public string? History { get; set; }



    public bool IsPreAssignedTimeTask { get; set; }
    public int CompanyID { get; set; }
    public decimal ManValue { get; set; }

    public decimal AssessmentPoints { get; set; }
    public decimal MHrAssigned { get; set; }
    public decimal MHrConsumed { get; set; }
    public decimal MHrAssessed { get; set; }
    public decimal VHrAssigned { get; set; }
    public decimal VHrConsumed { get; set; }
    public decimal VHrAssessed { get; set; }
    public decimal VHrAssignedCost { get; set; }
    public decimal VHrConsumedCost { get; set; }
    public decimal VHrAssessedCost { get; set; }
    public decimal VHrRate { get; set; }
    public bool IsAssessmentRequired { get; set; }
    public string? PreviousStageCode { get; set; }
    public int? PreviousStageRevision { get; set; }
    public int? PreviousTaskID { get; set; }

    public string? AssessmentRemark { get; set; }

    
    [StringLength(255)]
    public string? Entity { get; set; }

    
    public int? EntityID { get; set; }

    public virtual ICollection<TimeEntry> TimeEntries { get; set; } = new List<TimeEntry>();

    public virtual ICollection<Assessment> Assessments { get; set; } = new List<Assessment>();
    public virtual ICollection<WFTaskAttachment> Attachments { get; set; } = new List<WFTaskAttachment>();

    public virtual ICollection<TaskRequest> Requests { get; set; } = new List<TaskRequest>();
  
    public int? ProjectID { get; set; }
}
public class WFTaskConfiguration : BaseEntityConfiguration<WFTask>, IEntityTypeConfiguration<WFTask>
{
    public void Configure(EntityTypeBuilder<WFTask> builder)
    {
        base.Configure(builder);

        builder.HasOne(t => t.Assigner)
            .WithMany()
            .HasForeignKey(t => t.AssignerContactID)
            .OnDelete(DeleteBehavior.SetNull);

        builder.HasOne(t => t.Contact)
            .WithMany()
            .HasForeignKey(t => t.ContactID)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasIndex(e => e.Entity);
        builder.HasIndex(e => e.EntityID);
        builder.HasIndex(e => e.DueDate);
        builder.HasIndex(e => e.StageRevision);
        builder.HasIndex(e => e.IsAssessmentRequired);
        builder.HasIndex(e => e.IsPreAssignedTimeTask);
        builder.HasIndex(e => e.StartDate);
        builder.HasIndex(e => e.CompletedDate);
        builder.HasIndex(e => e.FollowUpDate);
        builder.HasIndex(e => e.OutcomeFlag);
        builder.HasIndex(e => e.WFStageCode);

    }
}