﻿using Newtonsoft.Json.Linq;
using System.Text;
using MyCockpitView.WebApi.Exceptions;
using MyCockpitView.WebApi.ContactModule.Entities;
using MyCockpitView.WebApi.Services;
using MyCockpitView.WebApi.MeetingModule.Entities;
using MyCockpitView.WebApi.PackageModule.Services;
using MyCockpitView.WebApi.ProjectModule.Entities;
using MyCockpitView.WebApi.TodoModule.Entities;
using MyCockpitView.WebApi.WFTaskModule.Services;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.Utility.Common;
using MyCockpitView.CoreModule;

namespace MyCockpitView.WebApi.MeetingModule.Services;

public class MeetingAgendaService : BaseEntityService<MeetingAgenda>, IMeetingAgendaService
{
    public MeetingAgendaService(EntitiesContext db) : base(db)
    {
    }


    public IQueryable<MeetingAgenda> Get(IEnumerable<QueryFilter>? Filters = null, string? Search = null, string? Sort = null)
    {

        IQueryable<MeetingAgenda> _query = base.Get(Filters)
                               ;

        //Apply filters
        if (Filters != null)
        {


            if (Filters.Where(x => x.Key.Equals("isForwarded", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var _item = Filters.First(x => x.Key.Equals("isForwarded", StringComparison.OrdinalIgnoreCase));

                var isBoolean = Boolean.TryParse(_item.Value, out bool result);

                if (isBoolean && result)
                    _query = _query.Where(x => x.IsForwarded == true);
                else
                    _query = _query.Where(x => x.IsForwarded == false);
            }

            if (Filters.Where(x => x.Key.Equals("IsInspection", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var _item = Filters.First(x => x.Key.Equals("IsInspection", StringComparison.OrdinalIgnoreCase));

                var isBoolean = Boolean.TryParse(_item.Value, out bool result);

                if (isBoolean && result)
                    _query = _query.Where(x => x.IsInspection == true);
                else
                    _query = _query.Where(x => x.IsInspection == false);
            }

            if (Filters.Where(x => x.Key.Equals("IsVersion", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var _item = Filters.First(x => x.Key.Equals("IsVersion", StringComparison.OrdinalIgnoreCase));

                var isBoolean = Boolean.TryParse(_item.Value, out bool result);

                if (isBoolean && result)
                    _query = _query.Where(x => x.IsVersion == true);
                else
                    _query = _query.Where(x => x.IsVersion == false);
            }


            if (Filters.Where(x => x.Key.Equals("isDelayed", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var _today = ClockTools.GetISTNow();
                var _item = Convert.ToBoolean(Filters.First(x => x.Key.Equals("isDelayed", StringComparison.OrdinalIgnoreCase)).Value);

                var predicate = PredicateBuilder.False<MeetingAgenda>();

                if (_item)
                {
                    predicate = predicate.Or(x => x.DueDate.HasValue && x.DueDate.Value.Date <= _today);
                }
                else
                {
                    predicate = predicate.Or(x => x.DueDate.HasValue && x.DueDate.Value.Date > _today);
                }

                _query = _query.Where(x => !x.IsForwarded).Where(predicate);
            }

            if (Filters.Where(x => x.Key.Equals("entity", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<MeetingAgenda>();
                if (Filters.Where(x => x.Key.Equals("Entity", StringComparison.OrdinalIgnoreCase)
                    && x.Value.Equals(nameof(Project), StringComparison.OrdinalIgnoreCase)).Any())
                {
                    var _activeProjects = db.Projects.AsNoTracking()
                                          .Where(x =>
               x.StatusFlag == McvConstant.PROJECT_STATUSFLAG_INQUIRY //Inquiry
            || x.StatusFlag == McvConstant.PROJECT_STATUSFLAG_PREPOPOSAL //pre-proposal
            || x.StatusFlag == McvConstant.PROJECT_STATUSFLAG_INPROGRESS //inprogress
                                 || x.StatusFlag == McvConstant.PROJECT_STATUSFLAG_LOCKED //locked
            );

                    if (Filters.Where(x => x.Key.Equals("projectPartnerContactID", StringComparison.OrdinalIgnoreCase)).Any())
                    {
                        var predicate2 = PredicateBuilder.False<Project>();
                        foreach (var _item in Filters.Where(x => x.Key.Equals("projectPartnerContactID", StringComparison.OrdinalIgnoreCase)))
                        {
                            int _value = Convert.ToInt32(_item.Value);
                            predicate2 = predicate2.Or(x => x.Associations
                            .Where(a => a.TypeFlag == 0
                            && a.ContactID == _value)
                            .Any()
                            );
                        }
                        _activeProjects = _activeProjects.Include(x => x.Associations).Where(predicate2);
                    }
                    if (Filters.Where(x => x.Key.Equals("projectAssociateContactID", StringComparison.OrdinalIgnoreCase)).Any())
                    {
                        var predicate2 = PredicateBuilder.False<Project>();
                        foreach (var _item in Filters.Where(x => x.Key.Equals("projectAssociateContactID", StringComparison.OrdinalIgnoreCase)))
                        {
                            int _value = Convert.ToInt32(_item.Value);
                            predicate2 = predicate2.Or(x => x.Associations
                            .Where(a => a.TypeFlag == 1
                            && a.ContactID == _value)
                            .Any()
                            );
                        }
                        _activeProjects = _activeProjects.Include(x => x.Associations).Where(predicate2);
                    }
                    if (Filters.Where(x => x.Key.Equals("PartnerOrAssociateContactID", StringComparison.OrdinalIgnoreCase)).Any())
                    {
                        var predicate2 = PredicateBuilder.False<Project>();
                        foreach (var _item in Filters.Where(x => x.Key.Equals("PartnerOrAssociateContactID", StringComparison.OrdinalIgnoreCase)))
                        {
                            int _value = Convert.ToInt32(_item.Value);
                            predicate2 = predicate2.Or(x => x.Associations
                            .Where(a => a.TypeFlag == McvConstant.PROJECT_ASSOCIATION_TYPEFLAG_PARTNER || a.TypeFlag == McvConstant.PROJECT_ASSOCIATION_TYPEFLAG_ASSOCIATE)
                            .Where(a => a.ContactID == _value)
                            .Any()
                            );
                        }
                        _activeProjects = _activeProjects.Include(x => x.Associations).Where(predicate2);
                    }

                    predicate = predicate.Or(x => x.ProjectID != null
                        && _activeProjects.Select(p => p.ID).Where(p => p == x.ProjectID.Value).Any());
                }

                if (Filters.Where(x => x.Key.Equals("Entity", StringComparison.OrdinalIgnoreCase)
                    && x.Value.Equals("NO-ENTITY", StringComparison.OrdinalIgnoreCase)).Any())
                {
                    predicate = predicate.Or(x => x.ProjectID == null);
                }
                _query = _query.Where(predicate);
            }

            if (Filters.Where(x => x.Key.Equals("ActionBy", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<MeetingAgenda>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("ActionBy", StringComparison.OrdinalIgnoreCase)))
                {
                    predicate = predicate.Or(x => x.ActionBy != null && x.ActionBy == _item.Value);
                }
                _query = _query.Where(predicate);
            }

            if (Filters.Where(x => x.Key.Equals("actionByContactID", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<MeetingAgenda>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("actionByContactID", StringComparison.OrdinalIgnoreCase)))
                {
                    var isNumeric = Convert.ToInt32(_item.Value);

                    predicate = predicate.Or(x => x.ActionByContactID != null && x.ActionByContactID == isNumeric);
                }
                _query = _query.Where(predicate);
            }

            if (Filters.Where(x => x.Key.Equals("ProjectID", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<MeetingAgenda>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("ProjectID", StringComparison.OrdinalIgnoreCase)))
                {
                    var isNumeric = Convert.ToInt32(_item.Value);

                    predicate = predicate.Or(x => x.ProjectID != null && x.ProjectID == isNumeric);
                }
                _query = _query.Where(predicate);
            }

            if (Filters.Where(x => x.Key.Equals("meetingContactID", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<MeetingAgenda>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("meetingContactID", StringComparison.OrdinalIgnoreCase)))
                {
                    var isNumeric = Convert.ToInt32(_item.Value);

                    predicate = predicate.Or(x => x.Meeting.ContactID == isNumeric);
                }
                _query = _query.Include(x => x.Meeting).Where(predicate);
            }

            if (Filters.Where(x => x.Key.Equals("meetingstatusFlag", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<MeetingAgenda>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("meetingstatusFlag", StringComparison.OrdinalIgnoreCase)))
                {
                    var isNumeric = Convert.ToInt32(_item.Value);

                    predicate = predicate.Or(x => x.Meeting.StatusFlag == isNumeric);
                }
                _query = _query.Include(x => x.Meeting).Where(predicate);
            }


            //if (Filters.Where(x => x.Key.Equals("contactID", StringComparison.OrdinalIgnoreCase)).Any())
            //{
            //    var _associatedProjectsForContact = db.Projects.AsNoTracking()
            //          .Where(x => x.StatusFlag != 4 //completed
            //          && x.StatusFlag != -1); //discarded

            //    var predicate2 = PredicateBuilder.False<Project>();
            //    foreach (var _item in Filters.Where(x => x.Key.Equals("contactID", StringComparison.OrdinalIgnoreCase)))
            //    {
            //        int _value = Convert.ToInt32(_item.Value);
            //        predicate2 = predicate2.Or(x => x.Associations
            //        .Where(a => a.ContactID == _value).Any());
            //    }

            //    _associatedProjectsForContact = _associatedProjectsForContact.Include(x => x.Associations).Where(predicate2);

            //    var predicate = PredicateBuilder.False<MeetingAgenda>();

            //    foreach (var _item in Filters.Where(x => x.Key.Equals("contactID", StringComparison.OrdinalIgnoreCase)))
            //    {
            //        var isNumeric = Convert.ToInt32(_item.Value);

            //        predicate = predicate.Or(x => (x.ActionByContactID != null && x.ActionByContactID == isNumeric)
            //         || (x.Meeting.ContactID == isNumeric)
            //         || (x.ProjectID != null
            //                && _associatedProjectsForContact.Where(p => p.ID == x.ProjectID.Value).Any())
            //        );
            //    }
            //    _query = _query.Include(x => x.Meeting).Where(predicate);
            //}

            if (Filters.Where(x => x.Key.Equals("PackageID", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<MeetingAgenda>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("PackageID", StringComparison.OrdinalIgnoreCase)))
                {
                    var isNumeric = Convert.ToInt32(_item.Value);

                    predicate = predicate.Or(x => x.PackageID != null && x.PackageID == isNumeric);
                }
                _query = _query.Where(predicate);
            }

            if (Filters.Where(x => x.Key.Equals("DesignScriptEntityID", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<MeetingAgenda>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("DesignScriptEntityID", StringComparison.OrdinalIgnoreCase)))
                {
                    var isNumeric = Convert.ToInt32(_item.Value);

                    predicate = predicate.Or(x => x.DesignScriptEntityID != null && x.DesignScriptEntityID == isNumeric);
                }
                _query = _query.Where(predicate);
            }

            if (Filters.Where(x => x.Key.Equals("DesignScriptItemID", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<MeetingAgenda>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("DesignScriptItemID", StringComparison.OrdinalIgnoreCase)))
                {
                    var isNumeric = Convert.ToInt32(_item.Value);

                    predicate = predicate.Or(x => x.DesignScriptItemID != null && x.DesignScriptItemID == isNumeric);
                }
                _query = _query.Where(predicate);
            }

            if (Filters.Where(x => x.Key.Equals("dueDate", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var _item = Filters.First(x => x.Key.Equals("dueDate", StringComparison.OrdinalIgnoreCase));
                DateTime result = Convert.ToDateTime(_item.Value);
                _query = _query.Where(x => x.DueDate.HasValue && x.DueDate.Value.Date == result.Date);
            }

            if (Filters.Where(x => x.Key.Equals("rangestart", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var _item = Filters.First(x => x.Key.Equals("rangestart", StringComparison.OrdinalIgnoreCase));

                DateTime result = Convert.ToDateTime(_item.Value);
                _query = _query.Where(x => x.DueDate >= result);
            }

            if (Filters.Where(x => x.Key.Equals("rangeend", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var _item = Filters.First(x => x.Key.Equals("rangeend", StringComparison.OrdinalIgnoreCase));

                DateTime result = Convert.ToDateTime(_item.Value);
                var end = result.AddDays(1);
                _query = _query.Where(x => x.DueDate < end);
            }

            if (Filters.Where(x => x.Key.Equals("subject", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<MeetingAgenda>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("subject", StringComparison.OrdinalIgnoreCase)))
                {
                    predicate = predicate.Or(x => x.Meeting.Title == _item.Value);
                }
                _query = _query.Include(x => x.Meeting).Where(predicate);
            }
            if (Filters.Where(x => x.Key.Equals("meetingstatusFlag", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<MeetingAgenda>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("meetingstatusFlag", StringComparison.OrdinalIgnoreCase)))
                {
                    var isNumeric = Convert.ToInt32(_item.Value);

                    predicate = predicate.Or(x => x.Meeting.StatusFlag == isNumeric);
                }
                _query = _query.Include(x => x.Meeting).Where(predicate);
            }

            if (Filters.Where(x => x.Key.Equals("usersOnly", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var _activeUserContactIDs = db.Contacts.AsNoTracking()
                   .Where(x => x.Username != null)
                    .Select(x => x.ID).ToList();

                var predicate = PredicateBuilder.False<MeetingAgenda>();
                predicate = predicate.Or(x => (x.ActionByContactID != null
                && _activeUserContactIDs.Any(c => c == x.ActionByContactID))
                //|| (_activeUserContactIDs.Any(c => c == x.Meeting.ContactID))
                );

                _query = _query.Include(x => x.Meeting).Where(predicate);
            }
        }

        if (Search != null && Search != string.Empty)
        {
            var _keywords = Search.Split(' ');

            foreach (var _key in _keywords)
            {
                _query = _query
                         .Where(x => x.Title.ToLower().Contains(_key.ToLower())
                         || x.Subtitle.ToLower().Contains(_key.ToLower())
                         || x.MeetingTitle.ToLower().Contains(_key.ToLower())
                         || x.ActionBy.ToLower().Contains(_key.ToLower())
                         || x.CreatedBy.ToLower().Contains(_key.ToLower()));
            }
        }

        if (Sort != null && Sort != String.Empty)
        {
            var _orderedQuery = _query.Include(x => x.Meeting).OrderBy(l => 0);
            var keywords = Sort.Split(',');

            foreach (var key in keywords)
            {
                if (key.Equals("dueDate", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenBy(x => x.DueDate);
                else if (key.Equals("dueDate-desc", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenByDescending(x => x.DueDate);
                else if (key.Equals("actionby", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenBy(x => x.ActionBy);
                else if (key.Equals("subject", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenBy(x => x.Meeting.Title);
            }

            return _orderedQuery;
        }

        return _query
          .OrderBy(x => x.DueDate).ThenBy(x => x.ActionBy);

    }

    public async Task<MeetingAgenda?> GetById(int Id)
    {

        return await db.MeetingAgendas
            .AsNoTracking()

            .Include(x => x.Attachments)
             .SingleOrDefaultAsync(i => i.ID == Id);

    }

    public async Task<MeetingAgenda?> GetById(Guid Id)
    {

        return await db.MeetingAgendas
            .AsNoTracking()
            .Include(x => x.Attachments)
            .SingleOrDefaultAsync(x => x.UID == Id);

    }

    public IQueryable<MeetingAgenda> GetByMeeting(int MeetingID)
    {

        return db.MeetingAgendas
            .AsNoTracking()
            .Include(x => x.Attachments)
                //.Include(x => x.Links)
                .Where(x => x.MeetingID == MeetingID)
                .OrderBy(x => x.OrderFlag);

    }

    public IQueryable<MeetingAgendaGroup> GetGroups(IQueryable<MeetingAgenda> AgendaQuery, string[] GroupBy = null)
    {

        if (GroupBy != null
            && GroupBy.Where(x => x.Equals("DueDate", StringComparison.OrdinalIgnoreCase)).Any()
            && GroupBy.Where(x => x.Equals("ActionBy", StringComparison.OrdinalIgnoreCase)).Any()
            && GroupBy.Where(x => x.Equals("Subject", StringComparison.OrdinalIgnoreCase)).Any())
        {
            return AgendaQuery
                    .Include(x => x.Meeting)
                    .GroupBy(x => new { x.DueDate, x.ActionBy, Subject = x.Meeting.Title })

                    .Select(x => new MeetingAgendaGroup
                    {
                        Guid = Guid.NewGuid().ToString(),
                        DueDate = x.Key.DueDate,
                        Subject = x.Key.Subject,
                        ActionBy = x.Key.ActionBy,
                        ItemCount = x.Count()
                    })
                    .OrderBy(x => x.DueDate)
                    .ThenBy(x => x.ActionBy)
                    .ThenBy(x => x.Subject);
        }
        else if (GroupBy != null && GroupBy.Where(x => x.Equals("DueDate", StringComparison.OrdinalIgnoreCase)).Any()
           && GroupBy.Where(x => x.Equals("ActionBy", StringComparison.OrdinalIgnoreCase)).Any())
        {
            return AgendaQuery
                    .GroupBy(x => new { x.DueDate, x.ActionBy })

                    .Select(x => new MeetingAgendaGroup
                    {
                        Guid = Guid.NewGuid().ToString(),
                        DueDate = x.Key.DueDate,
                        ActionBy = x.Key.ActionBy,
                        ItemCount = x.Count()
                    })
                    .OrderBy(x => x.DueDate)
                    .ThenBy(x => x.ActionBy);
        }
        else if (GroupBy != null && GroupBy.Where(x => x.Equals("DueDate", StringComparison.OrdinalIgnoreCase)).Any()
           && GroupBy.Where(x => x.Equals("Subject", StringComparison.OrdinalIgnoreCase)).Any())
        {
            return AgendaQuery
                    .Include(x => x.Meeting)
                    .GroupBy(x => new { x.DueDate, Subject = x.Meeting.Title })

                    .Select(x => new MeetingAgendaGroup
                    {
                        Guid = Guid.NewGuid().ToString(),
                        DueDate = x.Key.DueDate,
                        Subject = x.Key.Subject,
                        ItemCount = x.Count()
                    })
                    .OrderBy(x => x.DueDate)
                    .ThenBy(x => x.Subject);
        }
        else if (GroupBy != null && GroupBy.Where(x => x.Equals("ActionBy", StringComparison.OrdinalIgnoreCase)).Any()
           && GroupBy.Where(x => x.Equals("Subject", StringComparison.OrdinalIgnoreCase)).Any())
        {
            return AgendaQuery
                    .Include(x => x.Meeting)
                    .GroupBy(x => new { x.ActionBy, Subject = x.Meeting.Title })

                    .Select(x => new MeetingAgendaGroup
                    {
                        Guid = Guid.NewGuid().ToString(),
                        Subject = x.Key.Subject,
                        ActionBy = x.Key.ActionBy,
                        ItemCount = x.Count()
                    })
                    .OrderBy(x => x.ActionBy)
                    .ThenBy(x => x.Subject);
        }
        else if (GroupBy != null && GroupBy.Where(x => x.Equals("ActionBy", StringComparison.OrdinalIgnoreCase)).Any())
        {
            return AgendaQuery
                    .GroupBy(x => new { x.ActionBy })

                    .Select(x => new MeetingAgendaGroup
                    {
                        Guid = Guid.NewGuid().ToString(),
                        ActionBy = x.Key.ActionBy,
                        ItemCount = x.Count()
                    })
                    .OrderBy(x => x.ActionBy);
        }
        else if (GroupBy != null && GroupBy.Where(x => x.Equals("Subject", StringComparison.OrdinalIgnoreCase)).Any())
        {
            return AgendaQuery
                    .Include(x => x.Meeting)
                    .GroupBy(x => new { Subject = x.Meeting.Title })

                    .Select(x => new MeetingAgendaGroup
                    {
                        Guid = Guid.NewGuid().ToString(),
                        Subject = x.Key.Subject,
                        ItemCount = x.Count()
                    })
                    .OrderBy(x => x.Subject);
        }
        else //if (GroupBy == null || GroupBy.Where(x => x.Equals("DueDate", StringComparison.OrdinalIgnoreCase)).Any())
        {
            return AgendaQuery
                    .GroupBy(x => new { x.DueDate })

                    .Select(x => new MeetingAgendaGroup
                    {
                        Guid = Guid.NewGuid().ToString(),
                        DueDate = x.Key.DueDate,
                        ItemCount = x.Count()
                    })
                    .OrderBy(x => x.DueDate);
        }

    }

    public async Task<IEnumerable<MeetingAgenda>> GetPreviousAgenda(int AgendaID)
    {

        var _history = new List<MeetingAgenda>();

        var _agenda = await db.MeetingAgendas.AsNoTracking()
            .SingleOrDefaultAsync(x => x.ID == AgendaID);

        if (_agenda != null && _agenda.PreviousAgendaID != null)
        {
            var _previousAgendaID = _agenda.PreviousAgendaID;
            do
            {
                var _previousAgenda = await db.MeetingAgendas.AsNoTracking()

                    .Include(x => x.Attachments)
                    .Where(x => !x.IsVersion)
                .SingleOrDefaultAsync(x => x.ID == _agenda.PreviousAgendaID);
                _history.Add(_previousAgenda);
                _previousAgendaID = _previousAgenda.PreviousAgendaID;
            } while (_previousAgendaID != null);
        }

        return _history.OrderByDescending(x => x.Modified);

    }
    public async Task SendAgendaFollowUpEmails()
    {
        var sharedService = new SharedService(db); ;
        var _reportDate = ClockTools.GetISTNow();
        //var _reportUrlRoot = await sharedService.GetPresetValue(McvConstant.AGENDA_REPORT_URL_ROOT);
        var _senderEmail = await sharedService.GetPresetValue(McvConstant.AGENDA_FOLLOW_UP_EMAIL_SENDER_ID);
        var _senderName = await sharedService.GetPresetValue(McvConstant.AGENDA_FOLLOW_UP_EMAIL_SENDER_NAME);
        var _message = await sharedService.GetPresetValue(McvConstant.AGENDA_FOLLOW_UP_MESSAGE);
        var _maxReminderCountForEscalation = Convert.ToInt32(await sharedService.GetPresetValue(McvConstant.AGENDA_MAX_REMINDER_COUNT_FOR_ESCALATION));

        var _filters = new List<QueryFilter>
            {
                new QueryFilter { Key = "statusFlag", Value = McvConstant.MEETING_AGENDA_STATUSFLAG_PENDING.ToString() },
                new QueryFilter { Key = "isForwarded", Value = "false" },
                new QueryFilter { Key = "entity", Value = nameof(Project) },
                new QueryFilter { Key = "isDelayed", Value = "true" },
                new QueryFilter { Key = "typeFlag", Value = McvConstant.MEETING_TYPEFLAG_MEETING.ToString() },
                              new QueryFilter { Key = "typeFlag", Value = McvConstant.MEETING_TYPEFLAG_CNOTE.ToString() },
            };

        var _activeProjectIDs = db.Projects
            .Where(x =>
               x.StatusFlag == McvConstant.PROJECT_STATUSFLAG_INQUIRY //Inquiry
            || x.StatusFlag == McvConstant.PROJECT_STATUSFLAG_PREPOPOSAL //pre-proposal
            || x.StatusFlag == McvConstant.PROJECT_STATUSFLAG_INPROGRESS //inprogress
                                                                         //|| x.StatusFlag == 6 //locked
            )
            .Select(x => x.ID);

        var _activeUserContactIDs = db.Contacts.AsNoTracking()
                  .Where(x => x.Username != null)
                   .Select(x => x.ID).ToList();

        //var _contactProjectGroup = await Get(_filters)
        var _contactProjectGroup = await db.MeetingAgendas.AsNoTracking()
            .Where(x => !x.IsVersion)
                .Where(x => x.StatusFlag == McvConstant.MEETING_AGENDA_STATUSFLAG_PENDING && !x.IsForwarded && x.ProjectID != null)
            //.Where(x => x.PackageID==null && x.TodoID== null)
            .Where(x => x.DueDate.HasValue && x.DueDate.Value.Date < _reportDate)
         .Where(x => x.ActionByContactID != null)
         .Where(x => _activeProjectIDs.Any(p => p == x.ProjectID))
          .Where(x => !_activeUserContactIDs.Any(p => p == x.ActionByContactID))
         .Select(x => new { ContactID = x.ActionByContactID, ProjectID = x.ProjectID })
         .Distinct()
         .ToListAsync();

        var associationTypeMasters = await db.TypeMasters.AsNoTracking()
                            .Where(x => x.Entity == nameof(ProjectAssociation)).ToListAsync();

        foreach (var group in _contactProjectGroup)
        {
            try
            {
                var _contact = await db.Contacts.AsNoTracking().Where(x => x.ID == group.ContactID).SingleOrDefaultAsync();
                if (_contact != null)
                {
                    var _project = await db.Projects
                        .Where(x => x.ID == group.ProjectID)
                        .Include(x => x.Associations).ThenInclude(c => c.Contact)
                        .SingleOrDefaultAsync();

                    var _groupAgendas = await Get(_filters)
                       .Where(x => !x.IsVersion)
                         .Where(x => x.DueDate.HasValue && x.DueDate.Value.Date < _reportDate)
                          .Where(x => x.ActionByContactID != null)
                        .Where(x => x.ProjectID == group.ProjectID && x.ActionByContactID == group.ContactID)
                        .OrderBy(x => x.DueDate)
                        .ToListAsync();

                    if (_groupAgendas.Any())
                    {
                        try
                        {
                            var _meetingID = _groupAgendas.FirstOrDefault().MeetingID;
                            var _meeting = await db.Meetings.AsNoTracking()
                                .Where(x => x.ID == _meetingID)
                                .Where(x => !x.IsVersion)
                                .Include(x => x.Contact)
                                .SingleOrDefaultAsync();

                            if (_meeting != null && _meeting.StatusFlag == McvConstant.MEETING_STATUSFLAG_SENT)
                            {
                                var toList = new List<MeetingEmailContact>() {
                                                  new MeetingEmailContact {
                                                      Name = _contact.FullName,
                                                      Email = _contact.Email1
                                                  }
                                                };

                                var ccList = new List<MeetingEmailContact>() {
                                                    new MeetingEmailContact {
                                                        ID=339,
                                                        Name = "Newarch Landscapes LLP",
                                                        Email = "<EMAIL>"
                                                    }
                                                };


                                var _maxReminderCount = _groupAgendas.Max(x => x.ReminderCount);



                                foreach (var association in _project.Associations)
                                {
                                    var _typeMaster = associationTypeMasters.FirstOrDefault(x => x.Value == association.TypeFlag);

                                    if (_typeMaster != null && _typeMaster.Description != null)
                                    {
                                        JObject jsonObject = JObject.Parse(_typeMaster.Description);
                                        var includeInAgendaFollowUp = (bool)jsonObject["includeInAgendaFollowUp"];
                                        var includeInAgendaFollowUpCC = (bool)jsonObject["includeInAgendaFollowUpCC"];
                                        var agendaFollowupMinReminderCount = (int)jsonObject["agendaFollowupMinReminderCount"];


                                        //var data = JsonConvert.DeserializeObject<dynamic>(_typeMaster.Description);

                                        //var includeInAgendaFollowUp = Convert.ToBoolean(data.GetType().GetProperty("includeInAgendaFollowUp").GetValue(data, null));
                                        //var includeInAgendaFollowUpCC = Convert.ToBoolean(data.GetType().GetProperty("includeInAgendaFollowUpCC").GetValue(data, null));
                                        //var agendaFollowupMinReminderCount = Convert.ToInt32(data.GetType().GetProperty("agendaFollowupMinReminderCount").GetValue(data, null));

                                        if (_maxReminderCount >= agendaFollowupMinReminderCount)
                                        {
                                            if (includeInAgendaFollowUp)
                                            {
                                                if (!toList.Any(x => x.Email == association.Contact.Email1) && !ccList.Any(x => x.Email == association.Contact.Email1))
                                                    toList.Add(new MeetingEmailContact
                                                    {
                                                        ID = association.Contact.ID,
                                                        Name = association.Contact.FullName,
                                                        Email = association.Contact.Email1
                                                    });
                                            }
                                            else if (includeInAgendaFollowUpCC)
                                            {
                                                if (!toList.Any(x => x.Email == association.Contact.Email1) && !ccList.Any(x => x.Email == association.Contact.Email1))
                                                    ccList.Add(new MeetingEmailContact
                                                    {
                                                        ID = association.Contact.ID,
                                                        Name = association.Contact.FullName,
                                                        Email = association.Contact.Email1
                                                    });
                                            }
                                        }
                                    }


                                }



                                var _reportTitle = $"Request for Action | {_project.Title} | {_contact.FullName} | {_reportDate.ToString("dd MMM yyyy")}";


                                StringBuilder sb = new StringBuilder();
                                sb.AppendLine("<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Transitional//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd\">");
                                sb.AppendLine("<html xmlns=\"http://www.w3.org/1999/xhtml\">");
                                sb.AppendLine("<head>");
                                sb.AppendLine("    <meta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\" />");
                                sb.AppendLine("    <title>Email Design</title>");
                                sb.AppendLine("    <meta name=\"viewport\" content=\"width=device-width; initial-scale=1.0;\" />");
                                sb.AppendLine("    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=9; IE=8; IE=7; IE=EDGE\" />");
                                sb.AppendLine("    <meta name=\"format-detection\" content=\"telephone=no\" />");
                                sb.AppendLine("    <!--[if gte mso 9]><xml>");
                                sb.AppendLine("    <o:OfficeDocumentSettings>");
                                sb.AppendLine("    <o:AllowPNG />");
                                sb.AppendLine("    <o:PixelsPerInch>96</o:PixelsPerInch>");
                                sb.AppendLine("    </o:OfficeDocumentSettings>");
                                sb.AppendLine("    </xml><![endif]-->");
                                sb.AppendLine("    <style type=\"text/css\">");
                                sb.AppendLine("        /* Some resets and issue fixes */");
                                sb.AppendLine("        #outlook a {");
                                sb.AppendLine("            padding: 0;");
                                sb.AppendLine("        }");
                                sb.AppendLine("");
                                sb.AppendLine("        body {");
                                sb.AppendLine("            width: 100% !important;margin:0;");
                                sb.AppendLine("            -webkit-text-size-adjust: 100%;");
                                sb.AppendLine("            -ms-text-size-adjust: 100%;");
                                sb.AppendLine("        }");

                                sb.AppendLine("        table{");
                                sb.AppendLine("            mso-table-lspace: 0px;");
                                sb.AppendLine("            mso-table-rspace: 0px;");
                                sb.AppendLine("        }");
                                sb.AppendLine("");
                                sb.AppendLine("        table td {");
                                sb.AppendLine("            border-collapse: collapse;");
                                sb.AppendLine("        }");
                                sb.AppendLine("");
                                sb.AppendLine("        .ExternalClass * {");
                                sb.AppendLine("            line-height: 115%;");
                                sb.AppendLine("        }");
                                sb.AppendLine("        /* End reset */");

                                sb.AppendLine("    </style>");
                                sb.AppendLine("</head>");
                                sb.AppendLine("");
                                sb.AppendLine("<body>");
                                sb.AppendLine("");

                                sb.AppendLine("");
                                sb.AppendLine("    <div style=\"margin: 0 auto;font-family:Calibri;font-size:14px;line-height:1.8;padding-left:5px;padding-right:5px; max-width:500px;\">");
                                sb.AppendLine("");
                                sb.AppendLine("        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; border-collapse: collapse;\">");
                                sb.AppendLine("            <tr>");
                                sb.AppendLine("                <td style=\"font-size:16px; font-weight:bold;\">");
                                sb.AppendLine(_reportTitle.ToUpper());
                                sb.AppendLine("                </td>");
                                sb.AppendLine("            </tr>");
                                sb.AppendLine("        </table>");
                                sb.AppendLine("");

                                sb.AppendLine("<div style=\"border-bottom: 1px solid #cccccc; margin-top: 15px; margin-bottom: 15px; \"></div>");

                                sb.AppendLine("        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width: 100%; border-collapse: collapse\">");
                                sb.AppendLine("            <tr>");
                                sb.AppendLine("                <td valign=\"top\" width=\"120\" style=\"font-weight:bold;padding-bottom:5px;\">");
                                sb.AppendLine("                    FollowUp Date:");
                                sb.AppendLine("                </td>");
                                sb.AppendLine("                <td valign=\"top\" style=\"padding-bottom: 5px\">");
                                sb.AppendLine(ClockTools.GetISTNow().ToString("dd MMM yyyy"));
                                sb.AppendLine("                </td>");
                                sb.AppendLine("            </tr>");
                                sb.AppendLine("            <tr>");
                                sb.AppendLine("                <td valign=\"top\" width=\"120\" style=\"font-weight:bold;padding-bottom:5px;\">");
                                sb.AppendLine("                    Project:");
                                sb.AppendLine("                </td>");
                                sb.AppendLine("                <td valign=\"top\" style=\"padding-bottom: 5px\">");
                                sb.AppendLine(_project.Code + "-" + _project.Title);
                                sb.AppendLine("                </td>");
                                sb.AppendLine("            </tr>");
                                //sb.AppendLine("            <tr>");
                                //sb.AppendLine("                <td valign=\"top\" width=\"120\" style=\"font-weight:bold;padding-bottom:5px;\">");
                                //sb.AppendLine("                    Action By:");
                                //sb.AppendLine("                </td>");
                                //sb.AppendLine("                <td valign=\"top\" style=\"padding-bottom: 5px\">");
                                //sb.AppendLine(_contact.FullName);
                                //sb.AppendLine("                </td>");
                                //sb.AppendLine("            </tr>");
                                sb.AppendLine("            <tr>");
                                sb.AppendLine("                <td valign=\"top\" width=\"120\" style=\"font-weight:bold;padding-bottom:5px;\">");
                                sb.AppendLine("                    Recorded By:");
                                sb.AppendLine("                </td>");
                                sb.AppendLine("                <td valign=\"top\" style=\"padding-bottom: 5px\">");
                                sb.AppendLine(_meeting.Contact.FullName);
                                sb.AppendLine("                </td>");
                                sb.AppendLine("            </tr>");
                                sb.AppendLine("            <tr>");
                                sb.AppendLine("                <td valign=\"top\" width=\"120\" style=\"font-weight:bold;padding-bottom:5px;\">");
                                sb.AppendLine("                    Meeting Date:");
                                sb.AppendLine("                </td>");
                                sb.AppendLine("                <td valign=\"top\" style=\"padding-bottom: 5px\">");
                                sb.AppendLine(ClockTools.GetIST(_meeting.StartDate).ToString("dd MMM yyyy HH:mm"));
                                sb.AppendLine("                </td>");
                                sb.AppendLine("            </tr>");

                                sb.AppendLine("        </table>");
                                sb.AppendLine("");
                                sb.AppendLine("        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width: 100%; border-collapse: collapse\">");
                                sb.AppendLine("            <tr>");
                                sb.AppendLine("                <td valign=\"top\"  style=\"font-weight:bold;\">");
                                sb.AppendLine("                    To:");
                                sb.AppendLine("                </td>");
                                sb.AppendLine("            </tr>");
                                sb.AppendLine("            <tr>");
                                sb.AppendLine("                <td valign=\"top\" style=\"padding-bottom: 5px;\">");
                                sb.AppendLine("                    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width: 100%; border-collapse: collapse\">");
                                foreach (var obj in toList)
                                {
                                    sb.AppendLine("                        <tr>");
                                    sb.AppendLine("                            <td>");
                                    sb.AppendLine(obj.Name + " <i> (" + obj.Email + ")</i>");
                                    sb.AppendLine("                            </td>");
                                    sb.AppendLine("                        </tr>");
                                }
                                sb.AppendLine("                    </table>");
                                sb.AppendLine("                </td>");
                                sb.AppendLine("            </tr>");
                                sb.AppendLine("            <tr>");
                                sb.AppendLine("                <td valign=\"top\"  style=\"font-weight:bold;\">");
                                sb.AppendLine("                    CC:");
                                sb.AppendLine("                </td>");
                                sb.AppendLine("            </tr>");
                                sb.AppendLine("            <tr>");
                                sb.AppendLine("                <td valign=\"top\" style=\"padding-bottom: 5px;\">");
                                sb.AppendLine("                    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width: 100%; border-collapse: collapse\">");
                                foreach (var obj in ccList)
                                {
                                    sb.AppendLine("                        <tr>");
                                    sb.AppendLine("                            <td>");
                                    sb.AppendLine(obj.Name + " <i> (" + obj.Email + ")</i>");
                                    sb.AppendLine("                            </td>");
                                    sb.AppendLine("                        </tr>");
                                }
                                sb.AppendLine("                    </table>");
                                sb.AppendLine("                </td>");
                                sb.AppendLine("            </tr>");

                                sb.AppendLine("        </table>");
                                sb.AppendLine("<div style=\"border-bottom: 1px solid #cccccc; margin-top: 15px; margin-bottom: 15px; \"></div>");

                                sb.AppendLine("        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width: 100%; border-collapse: collapse\">");
                                sb.AppendLine("            <tr>");
                                sb.AppendLine("                <td valign=\"top\" style=\"padding-top: 5px; padding-bottom: 5px;\">");
                                sb.AppendLine("                    <pre style=\"font-family: Calibri;font-size: 14px;margin-top: 0;margin-bottom: 0;white-space: pre-wrap;white-space: -moz-pre-wrap;white-space: -pre-wrap;white-space: -o-pre-wrap;word-wrap: break-word;\">");
                                sb.AppendLine(_message);

                                sb.AppendLine("</pre>");
                                sb.AppendLine("                </td>");
                                sb.AppendLine("            </tr>");
                                sb.AppendLine("        </table>");
                                sb.AppendLine("");
                                sb.AppendLine("");

                                var _index = 1;
                                foreach (var obj in _groupAgendas)
                                {
                                    sb.AppendLine("        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width: 100%; border-collapse: collapse;\">");

                                    sb.AppendLine("                        <tr>");

                                    sb.AppendLine("                            <td colspan=\"3\" valign=\"top\" style=\"padding-bottom:5px; \">");
                                    sb.AppendLine($"<small>#{_index.ToString("D2")}: </small>");
                                    sb.AppendLine($"<strong>{obj.Title + " " + obj.Subtitle}</strong>");
                                    sb.AppendLine("                            </td>");

                                    sb.AppendLine("                        </tr>");

                                    sb.AppendLine("                        <tr>");
                                    sb.AppendLine("                            <td valign=\"top\" style=\"padding-bottom:5px; \">");
                                    sb.AppendLine($"<small>Due: </small><strong>{ClockTools.GetIST(obj.DueDate.Value).ToString("dd MMM yyyy")}</strong>");
                                    sb.AppendLine("                            </td>");
                                    sb.AppendLine("                            <td valign=\"top\" style=\"padding-bottom:5px; \">");
                                    sb.AppendLine($"<small>Action By: </small><strong>{obj.ActionBy}</strong>");
                                    sb.AppendLine("                            </td>");
                                    if ((obj.ReminderCount + 1) > 5)
                                    {
                                        sb.AppendLine("                       <td valign=\"top\" style=\"padding-bottom:5px;font-weight:bold;color:red; \">");
                                    }
                                    else
                                    {
                                        sb.AppendLine("                            <td valign=\"top\" style=\"padding-bottom:5px; \">");
                                    }
                                    sb.AppendLine($"<small>Reminder: </small><strong>{(obj.ReminderCount + 1).ToString("00")}</strong>");
                                    sb.AppendLine("                            </td>");
                                    sb.AppendLine("                        </tr>");

                                    sb.AppendLine("                        <tr>");
                                    sb.AppendLine("                            <td colspan=\"3\" valign=\"top\" style=\"padding-bottom:5px; font-weight: bold;font-size: 14px; \">");
                                    sb.AppendLine("                    <pre style=\"font-family: Calibri;font-size: 14px;margin-top: 0;margin-bottom: 0;white-space: pre-wrap;white-space: -moz-pre-wrap;white-space: -pre-wrap;white-space: -o-pre-wrap;word-wrap: break-word;\">");
                                    sb.AppendLine(obj.Comment);
                                    sb.AppendLine("</pre>");
                                    sb.AppendLine("                            </td>");
                                    sb.AppendLine("                        </tr>");


                                    if (obj.NotDiscussed)
                                    {
                                        sb.AppendLine("                        <tr>");
                                        sb.AppendLine("                            <td colspan=\"3\" valign=\"top\" style=\"padding-bottom:5px; \">");
                                        sb.AppendLine($"<small>Previous Comment:</small>");
                                        sb.AppendLine("                    <pre style=\"font-family: Calibri;font-size: 12px;margin-top: 0;margin-bottom: 0;white-space: pre-wrap;white-space: -moz-pre-wrap;white-space: -pre-wrap;white-space: -o-pre-wrap;word-wrap: break-word;font-style: italic;\">");
                                        sb.AppendLine(obj.PreviousComment);
                                        sb.AppendLine("</pre>");
                                        sb.AppendLine("                            </td>");
                                        sb.AppendLine("                        </tr>");
                                    }

                                    //sb.AppendLine("                        <tr>");
                                    //sb.AppendLine("                            <td colspan=\"2\" valign=\"top\" style=\"padding-bottom:5px; \">");
                                    //sb.AppendLine("<small>History: </small>");
                                    //sb.AppendLine("                            </td>");
                                    //sb.AppendLine("                        </tr>");

                                    //sb.AppendLine("                        <tr>");
                                    //sb.AppendLine("                            <td colspan=\"2\" valign=\"top\" >");
                                    //sb.AppendLine("                    <pre style=\"font-style: italic;font-family: Calibri;font-size: 11px;margin-left:20px;margin-top:0;margin-bottom:0; white-space: pre-wrap; white-space: -moz-pre-wrap;white-space: -pre-wrap; white-space: -o-pre-wrap; word-wrap: break-word;\">");
                                    //sb.AppendLine(obj.PreviousHistory);
                                    //sb.AppendLine("</pre>");

                                    //sb.AppendLine("                            </td>");
                                    //sb.AppendLine("                        </tr>");

                                    sb.AppendLine("        </table>");
                                    _index++;

                                    if (_index <= _groupAgendas.Count)
                                    {
                                        sb.AppendLine("<div style=\"border-bottom: 1px solid #cccccc; margin-bottom: 10px; \"></div>");
                                    }
                                }

                                //FOOTER
                                sb.AppendLine("<div style=\"border-bottom: 1px solid #cccccc; margin-top: 15px; margin-bottom: 15px; \"></div>");
                                sb.AppendLine("        <table align=\"center\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; border-collapse: collapse;font-size:11px;\">");

                                sb.AppendLine("");
                                sb.AppendLine("            <tr>");
                                sb.AppendLine("");
                                sb.AppendLine("                <td align=\"center\" >");
                                sb.AppendLine("This is a <b>MyCockpitView<sup>&copy;</sup></b> & <b>DesignScript<sup>&copy;</sup></b> generated e-mail for your information and necessary action.");
                                sb.AppendLine("</td>");
                                sb.AppendLine("            </tr>");
                                sb.AppendLine("");
                                sb.AppendLine("            <tr>");
                                sb.AppendLine("");
                                sb.AppendLine("                <td align=\"center\" >");
                                sb.AppendLine("");
                                sb.AppendLine("                    Powered by <b>Newarch<sup>&reg;</sup> Infotech LLP</b>");
                                sb.AppendLine("");
                                sb.AppendLine("                </td>");
                                sb.AppendLine("            </tr>");
                                sb.AppendLine("        </table>");
                                sb.AppendLine("    </div>");

                                sb.AppendLine("</body>");
                                sb.AppendLine("");
                                sb.AppendLine("</html>");

                                var _emailBody = sb.ToString();

                                var emailTo = new List<(string name, string email)>();
                                foreach (var obj in toList)
                                    emailTo.Add((obj.Name, obj.Email));

                                var emailCC = new List<(string name, string email)>();
                                foreach (var obj in ccList)
                                    emailCC.Add((obj.Name, obj.Email));

                                //if (_defaultCCList != null)
                                //{
                                //    Regex myRegex = new Regex(McvConstant.EMAIL_REGEX, RegexOptions.None);
                                //    foreach (Match myMatch in myRegex.Matches(_defaultCCList))
                                //    {
                                //        if (!emailTo.Any(a => a.email.Equals(myMatch.Value.Trim())) && !emailCC.Any(a => a.email.Equals(myMatch.Value.Trim())))
                                //            emailCC.Add(("Company", myMatch.Value.Trim()));

                                //    }
                                //}

                                await sharedService.SendMail(_reportTitle, _senderName, _senderEmail, _emailBody, emailTo, emailCC);



                                foreach (var item in _groupAgendas)
                                {
                                    item.ReminderCount++;
                                    try
                                    {

                                        db.Entry(item).State = EntityState.Modified;
                                    }
                                    catch (Exception)
                                    {

                                    }
                                }

                                await db.SaveChangesAsync();
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine(ex.Message);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.Message);
            }
        }

    }

    public async Task Update(MeetingAgenda UpdatedEntity)
    {

        if (UpdatedEntity.ActionByContactID == 0)
        {
            UpdatedEntity.ActionByContactID = null;
        }
        var _originalEntity = await db.MeetingAgendas.AsNoTracking().SingleOrDefaultAsync(x => x.ID == UpdatedEntity.ID);

        if (_originalEntity == null) throw new EntityServiceException("Meeting agenda not found!");

        if (UpdatedEntity.Title == null || UpdatedEntity.Title == String.Empty) throw new EntityServiceException("Agenda title cannot be empty!");


        if (UpdatedEntity.Subtitle == null || UpdatedEntity.Subtitle == String.Empty) throw new EntityServiceException("Agenda subtitle cannot be empty!");



        var _meeting = await db.Meetings.AsNoTracking().Where(x => x.ID == UpdatedEntity.MeetingID).SingleOrDefaultAsync();
        if (_meeting == null) throw new EntityServiceException("Meeting not found for this agenda!");
        UpdatedEntity.ProjectID = _meeting.ProjectID;
        UpdatedEntity.MeetingDate = _meeting.StartDate;
        UpdatedEntity.MeetingTitle = _meeting.Title;
        //await RecordVersion(Entity.ID);
        UpdatedEntity.NotDiscussed = false;
        if (_meeting.TypeFlag == McvConstant.MEETING_TYPEFLAG_MEETING && (UpdatedEntity.Comment == null || UpdatedEntity.Comment.Trim() == String.Empty))
        {
            UpdatedEntity.Comment = "Not Discussed";
            UpdatedEntity.NotDiscussed = true;
        }
        var sharedService = new SharedService(db); ;
        if (UpdatedEntity.DueDate != null)
            UpdatedEntity.DueDate = ClockTools.GetUTC(ClockTools.GetIST(UpdatedEntity.DueDate.Value).Date.AddMinutes(await sharedService.GetBusinessEndMinutesIST()));

        if (UpdatedEntity.ActionByContactID == null)
        {
            UpdatedEntity.ActionBy = null;
        }

        if (UpdatedEntity.StatusFlag == McvConstant.MEETING_AGENDA_STATUSFLAG_RESOLVED)
        {
            UpdatedEntity.DueDate = null;
            UpdatedEntity.ActionBy = null;
            UpdatedEntity.ActionByContactID = null;
            if (UpdatedEntity.PackageID != null)
            {
                var packageService = new PackageService(db);
                var package = await packageService.Get()
                                .SingleOrDefaultAsync(x => x.ID == UpdatedEntity.PackageID);
                if (package != null)
                {
                    if (package.StatusFlag == McvConstant.PACKAGE_STATUSFLAG_ACTIVE)
                    {
                        var _otherAgenda = await Get().Where(x => !x.IsVersion && !x.IsForwarded && x.StatusFlag == McvConstant.MEETING_AGENDA_STATUSFLAG_PENDING && x.ID != UpdatedEntity.ID)
                            .Where(x => x.PackageID == package.ID && x.DueDate != null)
                            .OrderBy(x => x.DueDate)
                            .ToListAsync();

                        if (_otherAgenda.Any())
                        {
                            package.FinalDate = _otherAgenda.FirstOrDefault().DueDate.Value;

                            await packageService.Update(package);
                        }
                    }

                }
                else
                {
                    UpdatedEntity.PackageID = null;
                }
            }

            if (UpdatedEntity.TodoID != null)
            {
                //TODO
            }
        }

        if (_meeting.TypeFlag == McvConstant.MEETING_TYPEFLAG_MEETING
            && _meeting.StatusFlag >= McvConstant.MEETING_STATUSFLAG_SENT) //after Sent
        {
            if (UpdatedEntity.Comment != _originalEntity.Comment
              || UpdatedEntity.StatusFlag != _originalEntity.StatusFlag
              || UpdatedEntity.ActionBy != _originalEntity.ActionBy
              || UpdatedEntity.DueDate != _originalEntity.DueDate
              || UpdatedEntity.Progress != _originalEntity.Progress
              )  //Any change after Minutes sent
            {
                UpdatedEntity.ReminderCount = 0;
                UpdatedEntity.UpdateFrom = "POST-MEETING";
                if (!UpdatedEntity.SendUpdate)
                {
                    UpdatedEntity.PreviousActionBy = _originalEntity.ActionBy;
                    UpdatedEntity.PreviousDueDate = _originalEntity.DueDate;
                    UpdatedEntity.PreviousComment = _originalEntity.Comment;
                    UpdatedEntity.PreviousProgress = _originalEntity.PreviousProgress;

                    var meetingService = new MeetingService(db);
                    UpdatedEntity.PreviousHistory = await meetingService.GetMeetingAgendaHistoryString(UpdatedEntity);
                }

                //UpdatedEntity.SendUpdate = true;
            }
        }

        if (UpdatedEntity.PackageID != null && UpdatedEntity.StatusFlag == 0 && UpdatedEntity.DueDate != null)
        {
            var _package = await db.Packages.AsNoTracking().SingleOrDefaultAsync(x => x.ID == UpdatedEntity.PackageID);
            if (_package != null && _package.StatusFlag == 0 && UpdatedEntity.DueDate != null)
            {
                _package.FinalDate = ClockTools.GetUTC(ClockTools.GetIST((DateTime)UpdatedEntity.DueDate.Value).Date
               .AddMinutes(await sharedService.GetBusinessEndMinutesIST()));
                var packageService = new PackageService(db);
                await packageService.Update(_package);
            }

            //Check if agenda task is pending
            var _currentAgendaTasks = await db.WFTasks.Where(x => x.Entity == nameof(MeetingAgenda)
     && x.EntityID == UpdatedEntity.ID
     && x.StatusFlag != 1).ToListAsync();

            if (_currentAgendaTasks.Any())
            {
                foreach (var task in _currentAgendaTasks)
                {
                    task.StatusFlag = 1;
                    task.Comment = $"Package created | {_package.Code}";
                    task.CompletedDate = DateTime.UtcNow;
                }
            }
        }

        if (UpdatedEntity.TodoID != null && UpdatedEntity.StatusFlag == 0 && UpdatedEntity.DueDate != null)
        {
            var _todo = await db.Todos.AsNoTracking().SingleOrDefaultAsync(x => x.ID == UpdatedEntity.TodoID);
            if (_todo != null && _todo.StatusFlag == 0 && UpdatedEntity.DueDate != null)
            {
                _todo.DueDate = UpdatedEntity.DueDate.Value;
                db.Entry(_todo).State = EntityState.Modified;

                await db.SaveChangesAsync();
                var taskService = new WFTaskService(db);
                await taskService.UpdateTaskDue(nameof(Todo), _todo.ID);
            }

            //Check if agenda task is pending
            var _currentAgendaTasks = await db.WFTasks.Where(x => x.Entity == nameof(MeetingAgenda)
     && x.EntityID == UpdatedEntity.ID
     && x.StatusFlag != 1).ToListAsync();

            if (_currentAgendaTasks.Any())
            {
                foreach (var task in _currentAgendaTasks)
                {
                    task.StatusFlag = 1;
                    task.Comment = $"Todo created | {_todo.Title}-{_todo.Subtitle}";
                    task.CompletedDate = DateTime.UtcNow;
                }
            }
        }

        await base.Update(UpdatedEntity);

        if (_meeting.StatusFlag >= McvConstant.MEETING_STATUSFLAG_SENT
            && UpdatedEntity.PackageID == null && UpdatedEntity.TodoID == null)
        {
            await AssignAgendaTasks(UpdatedEntity.ID);
        }


    }

    public async Task<int> Create(MeetingAgenda Entity)
    {
        if (Entity.ActionByContactID == 0)
        {
            Entity.ActionByContactID = null;
        }
        if (Entity.Title == null
                || Entity.Title == String.Empty) throw new EntityServiceException("Agenda title cannot be empty!");

        //avoiding duplicate title
        var _exist = await db.MeetingAgendas
           .Where(x => x.MeetingID == Entity.MeetingID
           && x.Title==Entity.Title
           && x.Subtitle==Entity.Subtitle)
                        .FirstOrDefaultAsync();

        if (_exist != null) return _exist.ID;
        // throw new CustomException($"Agenda {_exist.Title} : {_exist.SubTitle} already exists!");

        if (Entity.ActionByContactID == null) Entity.ActionBy = null;
        var sharedService = new SharedService(db); ;
        if (Entity.DueDate != null)
            Entity.DueDate = ClockTools.GetUTC(ClockTools.GetIST(Entity.DueDate.Value).Date.AddMinutes(await sharedService.GetBusinessEndMinutesIST()));


        var _meeting = await db.Meetings.AsNoTracking().Where(x => x.ID == Entity.MeetingID).SingleOrDefaultAsync();
        if (_meeting == null) throw new EntityServiceException("Meeting not found!");

        Entity.ProjectID = _meeting.ProjectID;
        Entity.MeetingDate = _meeting.StartDate;
        Entity.MeetingTitle = _meeting.Title;
        Entity.NotDiscussed = false;
        if (_meeting.TypeFlag == McvConstant.MEETING_TYPEFLAG_MEETING && (Entity.Comment == null || Entity.Comment.Trim() == String.Empty))
        {
            Entity.Comment = "Not Discussed";
            Entity.NotDiscussed = true;
        }

        Entity.TypeFlag = _meeting.TypeFlag;

        if (Entity.TypeFlag == McvConstant.MEETING_TYPEFLAG_INSPECTION)//
        {


            var _lastAgenda = await db.MeetingAgendas
                .Where(x => !x.IsVersion)
                .Where(x => x.TypeFlag == McvConstant.MEETING_TYPEFLAG_INSPECTION)
                .Where(x => x.DesignScriptEntityID == Entity.DesignScriptEntityID)
                .OrderByDescending(x => x.Created)
                .FirstOrDefaultAsync();

            if (_lastAgenda != null)
            {
                Entity.PreviousAgendaID = _lastAgenda.ID;
                Entity.PreviousActionBy = _lastAgenda.ActionBy;
                Entity.PreviousProgress = _lastAgenda.Progress;
                Entity.PreviousDueDate = _lastAgenda.DueDate;
                Entity.PreviousComment = _lastAgenda.Comment;
                var meetingService = new MeetingService(db);
                Entity.PreviousHistory = await meetingService.GetMeetingAgendaHistoryString(_lastAgenda);
                _lastAgenda.IsForwarded = true;

            }


        }

        if (Entity.StatusFlag == 1)
        {
            Entity.DueDate = null;
            Entity.ActionBy = null;
            Entity.ActionByContactID = null;
        }

        if (Entity.PreviousAgendaID != null)
        {
            //Check if previous agenda task is pending
            await CompleteAgendaTasks(Entity.PreviousAgendaID.Value);
        }

        if (Entity.DesignScriptEntityID != null)
        {
            var dsEntity = await db.DesignScriptEntities.AsNoTracking()
                .Include(x => x.Parent)
                .SingleOrDefaultAsync(x => x.ID == Entity.DesignScriptEntityID);
            if (dsEntity != null)
            {
                if (dsEntity.TypeFlag == McvConstant.DESIGN_SCRIPT_ENTITY_TYPEFLAG_ZONE)
                {
                    Entity.Zone = $"{dsEntity.Code}-{dsEntity.Title}";
                }
                else if (dsEntity.TypeFlag == McvConstant.DESIGN_SCRIPT_ENTITY_TYPEFLAG_SPACE)
                {
                    Entity.Zone = $"{dsEntity.Parent.Code}-{dsEntity.Parent.Title}";
                    Entity.Space = $"{dsEntity.Code}-{dsEntity.Title}";
                }
                else
                {
                    var zone = await db.DesignScriptEntities.AsNoTracking()
                .SingleOrDefaultAsync(x => x.ID == dsEntity.Parent.ParentID);
                    if (zone != null)
                        Entity.Zone = $"{zone.Code}-{zone.Title}";

                    Entity.Space = $"{dsEntity.Parent.Code}-{dsEntity.Parent.Title}";
                    Entity.Element = $"{dsEntity.Code}-{dsEntity.Title}";
                }
            }
        }


        return await base.Create(Entity);

    }

    public async Task ScaffoldPendingAgenda(int MeetingID, string Subject, int? ProjectID = null, int? typeFlag = null)
    {

        var _currentAgendas = await db.MeetingAgendas
            .Where(x => x.MeetingID == MeetingID)
            .ToListAsync();

        //var _pendingAgendas = new List<MeetingAgenda>();

        var _query = db.MeetingAgendas
            
            .Where(x => x.MeetingID != MeetingID) //not from same meeting
             .Include(x => x.Meeting)
             .Where(x => !x.Meeting.IsVersion)
                        .Where(x => x.Meeting.StatusFlag == McvConstant.MEETING_STATUSFLAG_ATTENDED || x.Meeting.StatusFlag == McvConstant.MEETING_STATUSFLAG_SENT)
                      .Where(x => !x.IsForwarded)
                      .Where(x => x.StatusFlag == McvConstant.MEETING_STATUSFLAG_SCHEDULED);

        if (typeFlag != null)
        {
            _query = _query.Where(x => x.TypeFlag == typeFlag);
        }
        else
        {
            _query = _query.Where(x => x.TypeFlag != McvConstant.MEETING_TYPEFLAG_INSPECTION);
        }

        if (ProjectID != null)
            _query = _query
                .Where(x => x.ProjectID != null && x.ProjectID == ProjectID);
        else
            _query = _query.Where(x => x.Meeting.Title==Subject);

        var _pendingList = await _query
                      .ToListAsync();

        if (!_pendingList.Any()) return;
        var meetingService = new MeetingService(db);
        foreach (var previousPending in _pendingList)
        {

            //check if current meeting includes agenda with same Title & Subtitle

            var _currentAgenda = _currentAgendas.Where(x => x.Title == previousPending.Title && x.Subtitle == previousPending.Subtitle).FirstOrDefault();

            var _previousAgendaHistory = await meetingService.GetMeetingAgendaHistoryString(previousPending);

            if (_currentAgenda == null)
            {
                _currentAgenda = new MeetingAgenda
                {
                    Title = previousPending.Title,
                    Subtitle = previousPending.Subtitle,
                    IsCustomSubtitle = previousPending.IsCustomSubtitle,
                    MeetingID = MeetingID,
                    PreviousComment = previousPending.Comment,
                    PreviousHistory = _previousAgendaHistory,
                    PreviousDueDate = previousPending.DueDate != null ? previousPending.DueDate : previousPending.PreviousDueDate,
                    PreviousActionBy = previousPending.ActionBy != null ? previousPending.ActionBy : previousPending.PreviousActionBy,
                    PreviousProgress = previousPending.Progress,
                    PreviousAgendaID = previousPending.ID,
                    Progress = previousPending.Progress,
                    DesignScriptEntityID = previousPending.DesignScriptEntityID,
                    ActionBy = previousPending.ActionBy,
                    DueDate = previousPending.DueDate,
                    ActionByContactID = previousPending.ActionByContactID,
                    ReminderCount = previousPending.ReminderCount,
                    TypeFlag = previousPending.TypeFlag,
                };

                await Create(_currentAgenda);
            }
            else
            {

                _currentAgenda.PreviousActionBy = previousPending.ActionBy != null ? previousPending.ActionBy : previousPending.PreviousActionBy;

                _currentAgenda.PreviousDueDate = previousPending.DueDate != null ? previousPending.DueDate : previousPending.PreviousDueDate;

                _currentAgenda.PreviousComment = previousPending.Comment;

                _currentAgenda.PreviousHistory = _previousAgendaHistory;

                _currentAgenda.PreviousAgendaID = _currentAgenda.ID != previousPending.ID ? previousPending.ID : (int?)null;
                _currentAgenda.PreviousProgress = previousPending.Progress;

                if (_currentAgenda.DueDate == null)
                    _currentAgenda.DueDate = previousPending.DueDate;

                if (_currentAgenda.ActionByContactID == null || _currentAgenda.ActionBy == null)
                {
                    _currentAgenda.ActionByContactID = previousPending.ActionByContactID;
                    _currentAgenda.ActionBy = previousPending.ActionBy;
                    _currentAgenda.ReminderCount = previousPending.ReminderCount;
                }
                _currentAgenda.TypeFlag = previousPending.TypeFlag;
                _currentAgenda.NotDiscussed = false;
                if ((_currentAgenda.Comment == null || _currentAgenda.Comment.Trim() == String.Empty))
                {
                    _currentAgenda.Comment = "Not Discussed";
                    _currentAgenda.NotDiscussed = true;
                }
            }

            //Set as Forwarded
            previousPending.IsForwarded = true;

            if (previousPending.TodoID != null)
            {

                var _todo = await db.Todos.AsNoTracking()
                    .SingleOrDefaultAsync(x => x.ID == previousPending.TodoID);

                if (_todo != null && _todo.StatusFlag == 0)
                {


                    _currentAgenda.TodoID = previousPending.TodoID;
                    previousPending.TodoID = null;

                }

            }
            if (previousPending.PackageID != null)
            {

                var _package = await db.Packages.AsNoTracking()
                    .SingleOrDefaultAsync(x => x.ID == previousPending.PackageID);

                if (_package != null && _package.StatusFlag == 0)
                {


                    //_currentAgenda.IsPackageRequired = true;
                    _currentAgenda.PackageID = previousPending.PackageID;

                    //previousPending.IsPackageRequired = false;
                    previousPending.PackageID = null;

                }

            }

            //Check if agenda task is pending
            var _currentAgendaTasks = await db.WFTasks.Where(x => x.Entity == nameof(MeetingAgenda)
     && x.EntityID == previousPending.ID
     && x.StatusFlag != 1).ToListAsync();

            if (_currentAgendaTasks.Any())
            {

                foreach (var task in _currentAgendaTasks)
                {
                    task.StatusFlag = 1;
                    task.Comment = $"Meeting created | {Subject}";
                    task.CompletedDate = DateTime.UtcNow;
                }
            }




        }

        await db.SaveChangesAsync();


    }


    public async Task SendAsCNote(MeetingAgenda UpdatedAgenda, Contact contact = null, IEnumerable<MeetingAttendee> attendees = null)
    {
        var _originalAgenda = await db.MeetingAgendas.AsNoTracking()
               .Include(x => x.Meeting.Attendees)
               .Include(x => x.Attachments)
               .SingleOrDefaultAsync(x => x.ID == UpdatedAgenda.ID);

        if (_originalAgenda == null) throw new EntityServiceException("Meeting agenda not found!");


        UpdatedAgenda.ReminderCount = 0;
        UpdatedAgenda.UpdateFrom = "COMMUNICATION NOTE";
        UpdatedAgenda.DesignScriptEntityID = _originalAgenda.DesignScriptEntityID;
        UpdatedAgenda.ProjectID = _originalAgenda.ProjectID;
        UpdatedAgenda.PreviousAgendaID = _originalAgenda.ID;
        UpdatedAgenda.PreviousActionBy = _originalAgenda.ActionBy;
        UpdatedAgenda.PreviousDueDate = _originalAgenda.DueDate;
        UpdatedAgenda.PreviousComment = _originalAgenda.Comment;
        UpdatedAgenda.PreviousProgress = _originalAgenda.Progress;
        UpdatedAgenda.ReminderCount = _originalAgenda.ReminderCount;
        var meetingService = new MeetingService(db);
        UpdatedAgenda.PreviousHistory = await meetingService.GetMeetingAgendaHistoryString(_originalAgenda);

        if (UpdatedAgenda.DueDate == null)
            UpdatedAgenda.DueDate = _originalAgenda.DueDate;

        if (UpdatedAgenda.ActionByContactID == null || UpdatedAgenda.ActionBy == null)
        {
            UpdatedAgenda.ActionByContactID = _originalAgenda.ActionByContactID;
            UpdatedAgenda.ActionBy = _originalAgenda.ActionBy;
        }

        UpdatedAgenda.NotDiscussed = false;
        if ((UpdatedAgenda.Comment == null || UpdatedAgenda.Comment.Trim() == String.Empty))
        {
            UpdatedAgenda.Comment = "Not Discussed";
            UpdatedAgenda.NotDiscussed = true;
        }
        var sharedService = new SharedService(db); ;
        if (UpdatedAgenda.PackageID != null)
        {
            var _package = await db.Packages.AsNoTracking().SingleOrDefaultAsync(x => x.ID == UpdatedAgenda.PackageID);
            if (_package != null && _package.StatusFlag == McvConstant.PACKAGE_STATUSFLAG_SENT)
            {
                UpdatedAgenda.PackageID = null;
            }
            else
            {
                UpdatedAgenda.PackageID = _originalAgenda.PackageID;

                _originalAgenda.PackageID = null;
            }

            if (UpdatedAgenda.StatusFlag == McvConstant.MEETING_AGENDA_STATUSFLAG_PENDING && UpdatedAgenda.DueDate != null)
            {
                if (_package != null && _package.StatusFlag == McvConstant.PACKAGE_STATUSFLAG_ACTIVE && UpdatedAgenda.DueDate != null)
                {
                    _package.FinalDate = ClockTools.GetUTC(ClockTools.GetIST((DateTime)UpdatedAgenda.DueDate.Value).Date
              .AddMinutes(await sharedService.GetBusinessEndMinutesIST()));
                    var packageService = new PackageService(db);
                    await packageService.Update(_package);
                }

            }

        }

        if (UpdatedAgenda.TodoID != null)
        {
            var _todo = await db.Todos.AsNoTracking().SingleOrDefaultAsync(x => x.ID == UpdatedAgenda.TodoID);
            if (_todo != null && _todo.StatusFlag == 1)
            {
                UpdatedAgenda.TodoID = null;
            }
            else
            {
                UpdatedAgenda.TodoID = _originalAgenda.TodoID;

                _originalAgenda.TodoID = null;
            }

            if (UpdatedAgenda.StatusFlag == 0 && UpdatedAgenda.DueDate != null)
            {
                if (_todo != null && _todo.StatusFlag == 0 && UpdatedAgenda.DueDate != null)
                {
                    _todo.DueDate = UpdatedAgenda.DueDate.Value;
                    db.Entry(_todo).State = EntityState.Modified;
                    await db.SaveChangesAsync();

                    var taskService = new WFTaskService(db);
                    await taskService.UpdateTaskDue(nameof(Todo), _todo.ID);

                }

            }

        }



        var _id = -1;
        var _cNote = new Meeting
        {
            Title = _originalAgenda.Meeting.Title,
            ProjectID = _originalAgenda.Meeting.ProjectID,
            ContactID = contact != null ? contact.ID : _originalAgenda.Meeting.ContactID,
            StartDate = DateTime.UtcNow,
            EndDate = DateTime.UtcNow,
            Location = "Post-Meeting",
            TypeFlag = McvConstant.MEETING_TYPEFLAG_CNOTE,
            StatusFlag = McvConstant.MEETING_STATUSFLAG_SENT
        };

        var _attendees = attendees != null ? attendees.ToList() : _originalAgenda.Meeting.Attendees.ToList();

        if (contact != null && !_attendees.Any(x => x.ContactID != null && x.ContactID == contact.ID))
        {
            var _company = "Newarch";
            var _appointment = await sharedService.GetLastAppointment(contact.ID);
            if (_appointment != null)
            {
                _company = _appointment.Company.Title;
            }
            _attendees.Add(new MeetingAttendee
            {
                ContactID = contact.ID,
                Name = contact.FullName,
                Email = contact.Email1,
                Company = _company,
            });
        }

        if (!_attendees.Where(x => x.TypeFlag == McvConstant.MEETING_ATTENDEE_TYPEFLAG_TO).Any())
        {
            foreach (var item in _attendees)
            {
                item.TypeFlag = McvConstant.MEETING_ATTENDEE_TYPEFLAG_TO;
            }
        }

        _id = await meetingService.Create(_cNote, _attendees);
        UpdatedAgenda.ID = default(int);
        UpdatedAgenda.MeetingID = _id;

        if (UpdatedAgenda.StatusFlag == 1)
        {
            UpdatedAgenda.DueDate = null;
            UpdatedAgenda.ActionBy = null;
            UpdatedAgenda.ActionByContactID = null;
        }
        if (UpdatedAgenda.PreviousAgendaID != null)
        {
            //Check if previous agenda task is pending
            await CompleteAgendaTasks(UpdatedAgenda.PreviousAgendaID.Value);
        }
        UpdatedAgenda.Attachments.Clear();
        foreach (var attachment in _originalAgenda.Attachments)
        {

            UpdatedAgenda.Attachments.Add(new MeetingAgendaAttachment
            {
                Url = attachment.Url,
                ThumbUrl = attachment.ThumbUrl,
                OriginalUrl = attachment.OriginalUrl,
                Filename = attachment.Filename,
                Container = attachment.Container,
                BlobPath = attachment.BlobPath,
                TypeFlag = attachment.TypeFlag,
                StatusFlag = attachment.StatusFlag,
                OrderFlag = attachment.OrderFlag,
            });
        }
        db.MeetingAgendas.Add(UpdatedAgenda);
        _originalAgenda.IsForwarded = true;
        await CompleteAgendaTasks(_originalAgenda.ID);
        db.Entry(_originalAgenda).State = EntityState.Modified;
        await db.SaveChangesAsync();


        await meetingService.SendMinutes(_id);

        _cNote.FinalizedOn = DateTime.UtcNow;
        _cNote.StatusFlag = McvConstant.MEETING_STATUSFLAG_SENT;
        db.Entry(_cNote).State = EntityState.Modified;
        await db.SaveChangesAsync();


    }
    public async Task Delete(int Id)
    {

        var Entity = await Get()
        .Include(x => x.Attachments)
             .SingleOrDefaultAsync(i => i.ID == Id);

        if (Entity == null) throw new EntityServiceException($"{nameof(MeetingAttendee)} not found!");

        if (Entity.PreviousAgendaID != null)
        {
            var _previousAgenda = await Get()
                .Where(x => !x.IsVersion)
                .Where(x => x.ID == Entity.PreviousAgendaID.Value).SingleOrDefaultAsync();

            if (_previousAgenda != null)
            {

                _previousAgenda.IsForwarded = false;
                _previousAgenda.PackageID = Entity.PackageID;
                _previousAgenda.TodoID = Entity.TodoID;
                db.Entry(_previousAgenda).State = EntityState.Modified;
                await db.SaveChangesAsync();

                if (_previousAgenda.StatusFlag == 0 && _previousAgenda.PackageID == null && _previousAgenda.TodoID == null)
                    await AssignAgendaTasks(_previousAgenda.ID);

            }

        }

        var attachmentService = new BaseAttachmentService<MeetingAgendaAttachment>(db);
        foreach (var x in Entity.Attachments)
        {
            await attachmentService.Delete(x.ID);
        }



        var taskService = new WFTaskService(db);
        var tasks = await taskService.Get()
            .Where(x => x.Entity == nameof(MeetingAgenda) && x.EntityID == Id)
            .Select(x => x.ID)
            .ToListAsync();

        foreach (var x in tasks)
        {
            await taskService.Delete(x);
        }

        await base.Delete(Id);

    }
    public async Task AssignAgendaTasks(int agendaID)
    {

        var _agenda = await db.MeetingAgendas
            .SingleOrDefaultAsync(x => x.ID == agendaID);
        if (_agenda == null || _agenda.IsVersion) return;

        var _activeProjectIDs = await db.Projects.AsNoTracking()
           .Where(x =>
              x.StatusFlag == 0 //Inquiry
           || x.StatusFlag == 1 //pre-proposal
           || x.StatusFlag == 2 //inprogress
                                //|| x.StatusFlag == 6 //locked
           )
           .Select(x => x.ID).ToListAsync();

        if (_agenda.TypeFlag == McvConstant.MEETING_TYPEFLAG_INSPECTION
           || _agenda.StatusFlag != 0
           || _agenda.ActionByContactID == null
           || _agenda.IsForwarded
           || _agenda.TodoID != null
           || (_agenda.ProjectID != null && !_activeProjectIDs.Any(p => p == _agenda.ProjectID))
           )
        {
            await CompleteAgendaTasks(_agenda.ID);
            return;
        }

        if (_agenda.PackageID != null)
        {
            var _package = await db.Packages.AsNoTracking().Where(x => x.ID == _agenda.PackageID).FirstOrDefaultAsync();

            if (_package != null && _package.StatusFlag == 0)
            {
                await CompleteAgendaTasks(_agenda.ID);
                return;
            }

        }


        if (!await db.Contacts
            .Where(c => c.Username != null)
            .Select(c => c.ID).AnyAsync(c => c == _agenda.ActionByContactID))
        {
            await CompleteAgendaTasks(_agenda.ID); return;
        }
        //Check if current agenda task is pending


        if (_agenda.PreviousAgendaID != null)
        {
            var _previousAgenda = await db.MeetingAgendas.AsNoTracking()
                .Where(x => !x.IsVersion)
            .SingleOrDefaultAsync(x => x.ID == _agenda.PreviousAgendaID);
            if (_previousAgenda != null)
            {
                //Check if previous agenda task is pending
                await CompleteAgendaTasks(_previousAgenda.ID);
            }
        }


        _agenda.PackageID = null;
        _agenda.TodoID = null;
        _agenda.IsForwarded = false;

        var taskService = new WFTaskService(db);
        await taskService.StartFlow(nameof(MeetingAgenda), _agenda.TypeFlag, _agenda.ID);

    }
    public async Task CompleteAgendaTasks(int agendaID)
    {

        var _currentAgendaTasks = await db.WFTasks.Where(x => x.Entity == nameof(MeetingAgenda)
        && x.EntityID == agendaID
        && x.StatusFlag != 1).ToListAsync();

        if (_currentAgendaTasks.Any())
        {

            foreach (var task in _currentAgendaTasks)
            {
                task.StatusFlag = 1;
                task.Comment = "Agenda Updated";
                task.CompletedDate = DateTime.UtcNow;


                await db.SaveChangesAsync();
            }
        }

    }

    public async Task<IEnumerable<int>> GetAssigneeContactIDs(int entityID, string stageCode, string propertyName)
    {
        var Entity = await Get()
            .Where(x => x.ID == entityID).SingleOrDefaultAsync();

        if (Entity == null) throw new EntityServiceException($"{nameof(Meeting)} not found!");

        var _list = new List<int>();
        if (stageCode.Equals("AGENDA_ACTION", StringComparison.OrdinalIgnoreCase))//Log Travel time
        {

            if (await db.Contacts
            .Where(c => c.Username != null)
            .Select(c => c.ID).AnyAsync(x => x == Entity.ActionByContactID))
                _list.Add(Entity.ActionByContactID.Value);

            if (Entity.ProjectID != null)
            {
                var _projectAssociates = await db.ProjectAssociations.AsNoTracking()
                    .Where(x => x.ProjectID == Entity.ProjectID)
                    .Where(x => x.TypeFlag == McvConstant.PROJECT_ASSOCIATION_TYPEFLAG_PARTNER || x.TypeFlag == McvConstant.PROJECT_ASSOCIATION_TYPEFLAG_ASSOCIATE)
                    .Select(x => x.ContactID)
                    .ToListAsync();

                _list = _list.Concat(_projectAssociates).ToList();
            }
        }
        else
        {
            throw new EntityServiceException($"{nameof(MeetingAgenda)} Task assignee not found for stage {stageCode}!");
        }
        return _list;
    }

    public async Task<dynamic> GetTaskByStage(string Entity, int EntityID, string StageCode, string StageTitle, decimal StageDuration, DateTime? FollowUpDate = null)
    {


        var sharedService = new SharedService(db); ;

        var _entity = await Get()
            .Include(x => x.Meeting)
            .SingleOrDefaultAsync(x => x.ID == EntityID);

        if (_entity == null) throw new EntityServiceException($"{nameof(MeetingAgenda)} not found!");

        var _startTimeSpan = TimeSpan.Parse(await sharedService.GetPresetValue(McvConstant.OFFICE_OPEN_TIME));
        var _endTimeSpan = TimeSpan.Parse(await sharedService.GetPresetValue(McvConstant.OFFICE_CLOSE_TIME));

        var _nextDue = DateTime.UtcNow.AddDays(Decimal.ToDouble(StageDuration)).Date;

        if (FollowUpDate != null)
            _nextDue = FollowUpDate.Value.Date;

        if (StageCode == "AGENDA_ACTION")
        {
            _nextDue = _entity.Meeting.EndDate.AddDays(1);
        }

        return new
        {
            Title = StageTitle,
            Entity = Entity,
            EntityID = EntityID,
            Subtitle = $"{_entity.MeetingTitle}-{_entity.Title}-{_entity.Subtitle}",
            WFStageCode = StageCode,
            StartDate = ClockTools.GetUTC(ClockTools.GetISTNow().Date
              .AddMinutes(_startTimeSpan.TotalMinutes)),
            DueDate = ClockTools.GetUTC(ClockTools.GetIST(_nextDue).Date
              .AddMinutes(_endTimeSpan.TotalMinutes)),
            MHrAssigned = 0,
            IsPreAssignedTimeTask = false
        };
    }

    public async Task GenerateTasks()
    {
        var count = 0;
        var _userContacts = await db.Contacts.AsNoTracking()
            .Where(x => x.Username != null)
               .Select(x => x.ID)
               .ToListAsync();

        var _activeProjects = await db.Projects.AsNoTracking()
                                          .Where(x =>
               x.StatusFlag == 0 //Inquiry
            || x.StatusFlag == 1 //pre-proposal
            || x.StatusFlag == 2 //inprogress
                                 || x.StatusFlag == 6 //locked
            ).Select(x => x.ID).ToListAsync();

        var items = await db.MeetingAgendas.AsNoTracking()
            .Include(x => x.Meeting)
            .Where(x => x.Meeting.StatusFlag == McvConstant.MEETING_STATUSFLAG_SENT)
            .Where(x => x.StatusFlag == 0 && x.ActionByContactID != null && !x.IsForwarded && !x.IsVersion
            && x.PackageID == null && x.TodoID == null)
            .Where(x => _userContacts.Any(c => c == x.ActionByContactID))
            .Where(x => x.ProjectID == null || _activeProjects.Any(c => c == x.ProjectID))
            .OrderByDescending(x => x.Created)
            .ToListAsync();

        foreach (var item in items)
        {
            try
            {
                Console.WriteLine($"Meeting:{item.MeetingTitle}");
                Console.WriteLine($"Agenda:{item.Title}|{item.Subtitle}");
                //Check if agenda task is pending
                var _currentAgendaTasks = await db.WFTasks.Where(x => x.Entity == nameof(MeetingAgenda)
         && x.EntityID == item.ID && x.StatusFlag == 0).ToListAsync();

                if (!_currentAgendaTasks.Any())
                {
                    await AssignAgendaTasks(item.ID);

                    count++;
                }
            }
            catch (Exception e)
            {
                Console.WriteLine(e.Message);
            }
            Console.WriteLine(count + " of " + items.Count() + " processed");
            Console.WriteLine("***************************************************");
        }
    }

}

public class MeetingAgendaGroup
{
    public string Guid { get; set; }
    public DateTime? DueDate { get; set; }
    public string ActionBy { get; set; }
    public int? ActionByContactID { get; set; }
    public string Subject { get; set; }
    public bool IsDelayed { get; set; }
    public int ItemCount { get; set; }
}