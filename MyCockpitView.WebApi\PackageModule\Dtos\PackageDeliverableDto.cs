﻿

using AutoMapper;
using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.PackageModule.Entities;

namespace MyCockpitView.WebApi.PackageModule.Dtos;

public class PackageDeliverableDto : BaseEntityDto
{
    public string? Title { get; set; }
    public string? Category { get; set; }
    public string? Code { get; set; }
    public string? StageService { get; set; }
    public int PackageID { get; set; }
    public virtual ICollection<PackageDeliverableTaskMapDto> PackageDeliverableTaskMaps { get; set; } = new List<PackageDeliverableTaskMapDto>();
}

public class PackageDeliverableDtoMapperProfile : Profile
{
    public PackageDeliverableDtoMapperProfile()
    {
        CreateMap<PackageDeliverable, PackageDeliverableDto>()
              .ReverseMap()
              .ForMember(dest => dest.PackageDeliverableTaskMaps, opt => opt.Ignore())
           .ForMember(dest => dest.Package, opt => opt.Ignore());
    }
}