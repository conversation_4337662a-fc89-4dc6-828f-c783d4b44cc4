﻿using System.ComponentModel.DataAnnotations;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.CoreModule;
using Newtonsoft.Json;


namespace MyCockpitView.WebApi.ContactModule.Entities;

public class ContactGroup : BaseEntity
{
    [Required]
    public int ContactID { get; set; }
    public virtual Contact? Contact { get; set; }
    [StringLength(255)]
    public string? Title { get; set; }
    public bool IsDefault { get; set; }

    public virtual ICollection<ContactGroupMember> Members { get; set; } = new List<ContactGroupMember>();

}


public class ContactGroupConfiguration : BaseEntityConfiguration<ContactGroup>, IEntityTypeConfiguration<ContactGroup>
{
    public void Configure(EntityTypeBuilder<ContactGroup> builder)
    {
        base.Configure(builder);

        builder.HasIndex(e => e.Title);
        builder.HasIndex(e => e.IsDefault);
    }
}


