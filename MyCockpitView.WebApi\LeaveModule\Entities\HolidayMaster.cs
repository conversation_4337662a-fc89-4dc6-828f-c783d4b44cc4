﻿using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using MyCockpitView.CoreModule;

namespace MyCockpitView.WebApi.LeaveModule.Entities;

public class HolidayMaster : BaseEntity
{
    [Required]
    [StringLength(50)]
    public string?  Title { get; set; }
    public DateTime HolidayDate { get; set; }
}

public class HolidayMasterConfiguration : BaseEntityConfiguration<HolidayMaster>, IEntityTypeConfiguration<HolidayMaster>
{
    public void Configure(EntityTypeBuilder<HolidayMaster> builder)
    {
        base.Configure(builder);
       
        builder.HasIndex(x => x.Title);
        builder.HasIndex(x => x.HolidayDate);
    }
}
