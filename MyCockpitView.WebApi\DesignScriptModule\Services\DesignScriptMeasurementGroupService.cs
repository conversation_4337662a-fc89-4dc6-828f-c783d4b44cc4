﻿using Microsoft.EntityFrameworkCore;
using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.DesignScriptModule.Entities;
using MyCockpitView.WebApi.Services;


using System.Data;




namespace MyCockpitView.WebApi.DesignScriptModule.Services;

public class DesignScriptMeasurementGroupService : BaseEntityService<DesignScriptMeasurementGroup>, IDesignScriptMeasurementGroupService
{
    public DesignScriptMeasurementGroupService(EntitiesContext db) : base(db) { }

    public IQueryable<DesignScriptMeasurementGroup> Get(IEnumerable<QueryFilter>? Filters = null, string? Search = null, string? Sort = null)
    {

        IQueryable<DesignScriptMeasurementGroup> _query = base.Get(Filters);


        //Apply filters
        if (Filters != null)
        {
            if (Filters.Where(x => x.Key.Equals("deleted", StringComparison.OrdinalIgnoreCase)).Any())
            {

                _query = db.DesignScriptMeasurementGroups
                        .AsNoTracking();
            }


            if (Filters.Where(x => x.Key.Equals("projectid", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<DesignScriptMeasurementGroup>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("projectid", StringComparison.OrdinalIgnoreCase)))
                {
                    var isNumeric = Convert.ToInt32(_item.Value);

                    predicate = predicate.Or(x => x.ProjectID == isNumeric);
                }
                _query = _query.Where(predicate);
            }

            if (Filters.Where(x => x.Key.Equals("DesignScriptDataCardID", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<DesignScriptMeasurementGroup>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("DesignScriptDataCardID", StringComparison.OrdinalIgnoreCase)))
                {
                    var isNumeric = Convert.ToInt32(_item.Value);

                    predicate = predicate.Or(x => x.DesignScriptDataCardID == isNumeric);
                }
                _query = _query.Where(predicate);
            }

            if (Filters.Where(x => x.Key.Equals("DesignScriptItemID", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<DesignScriptMeasurementGroup>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("DesignScriptItemID", StringComparison.OrdinalIgnoreCase)))
                {
                    var isNumeric = Convert.ToInt32(_item.Value);

                    predicate = predicate.Or(x => x.DesignScriptItemID == isNumeric);
                }
                _query = _query.Where(predicate);
            }

            if (Filters.Where(x => x.Key.Equals("DesignScriptEntityID", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<DesignScriptMeasurementGroup>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("DesignScriptEntityID", StringComparison.OrdinalIgnoreCase)))
                {
                    var isNumeric = Convert.ToInt32(_item.Value);

                    predicate = predicate.Or(x => x.DesignScriptEntityID == isNumeric);
                }
                _query = _query.Where(predicate);
            }

        }

        if (Search != null && Search != String.Empty)
        {
            var _keywords = Search.Split(' ');

            foreach (var _key in _keywords)
            {
                _query = _query
                     .Where(x =>
                      x._searchTags.ToLower().Contains(_key.ToLower()));
            }
        }

        if (Sort != null && Sort != String.Empty)
        {
            switch (Sort.ToLower())
            {
                case "createddate":
                    return _query
                            .OrderBy(x => x.Created);

                case "modifieddate":
                    return _query
                            .OrderBy(x => x.Modified);

                case "createddate desc":
                    return _query
                            .OrderByDescending(x => x.Created);

                case "modifieddate desc":
                    return _query
                            .OrderByDescending(x => x.Modified);


            }
        }
        return _query.OrderByDescending(x => x.Created);

    }

    public async Task<DesignScriptMeasurementGroup?> GetById(int Id)
    {

        return await db.DesignScriptMeasurementGroups.AsNoTracking()
         .Include(x => x.Measurements)
             .SingleOrDefaultAsync(i => i.ID == Id);

    }

}