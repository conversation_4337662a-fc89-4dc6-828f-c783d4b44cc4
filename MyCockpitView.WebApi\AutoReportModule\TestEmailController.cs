﻿
using Microsoft.AspNetCore.Mvc;
using MyCockpitView.Utility.Common;
using MyCockpitView.WebApi.ContactModule.Services;
using MyCockpitView.WebApi.Services;
using System.Text;

namespace MyCockpitView.WebApi.AutoReportModule;

[ApiController]
[Route("[controller]")]
    public class TestEmailController : ControllerBase
{
    private readonly ISharedService sharedService;

    public TestEmailController(ISharedService sharedService)
        {
        this.sharedService = sharedService;
    }

        [HttpGet]
        public async Task<IActionResult> Get()
    {
        var istTimeZone = TimeZoneInfo.FindSystemTimeZoneById("India Standard Time");
        var todayIST = TimeZoneInfo.ConvertTime(DateTime.UtcNow, istTimeZone).Date;

        var _senderEmail = await sharedService.GetPresetValue(McvConstant.LOCK_PROJECT_EMAIL_SENDER_ID);
                var _senderName = await sharedService.GetPresetValue(McvConstant.LOCK_PROJECT_EMAIL_SENDER_NAME);

                var toList = new List<EmailContact>() {
                                                      new EmailContact {
                                                          Name = "People | Newarch",
                                                          Email = "<EMAIL>"
                                                      }
                                                    };

                var ccList = new List<EmailContact>() {
                                    new EmailContact {
                                                          Name = "Core | Newarch",
                                                          Email = "<EMAIL>"
                                                      },
                                                        new EmailContact {
                                                            Name = "Backup | Newarch",
                                                            Email = "<EMAIL>"
                                                        }
                                                    };

                var dateFormat= await sharedService.GetPresetValue(McvConstant.DATE_FORMAT);
                var _reportTitle = $"Test Email | {ClockTools.GetIST(todayIST).ToString(dateFormat)}";


        String HTMLBuilder()
        {
            //Create a new StringBuilder object
            StringBuilder sb = new StringBuilder();

            sb.AppendLine("<!DOCTYPE html>");
            sb.AppendLine("<html lang=\"en\">");
            sb.AppendLine(" ");
            sb.AppendLine("<head>");
            sb.AppendLine("    <meta charset=\"UTF-8\" />");
            sb.AppendLine("    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\" />");
            sb.AppendLine("    <title>Inspection Report Email</title>");
            sb.AppendLine("    <style>");
            sb.AppendLine("        @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;600&display=swap');");
            sb.AppendLine(" ");
            sb.AppendLine("        body,");
            sb.AppendLine("        table,");
            sb.AppendLine("        td,");
            sb.AppendLine("        div,");
            sb.AppendLine("        a {");
            sb.AppendLine("            font-family: 'Poppins', 'Segoe UI', Arial, sans-serif !important;");
            sb.AppendLine("        }");
            sb.AppendLine("    </style>");
            sb.AppendLine("</head>");
            sb.AppendLine(" ");
            sb.AppendLine("<body style=\"margin: 0; padding: 0; background-color: #f5f5f5; font-family: 'Poppins', 'Segoe UI', Arial, sans-serif;\">");
            sb.AppendLine("    <table cellpadding=\"0\" cellspacing=\"0\" width=\"100%\" style=\"background-color: #f5f5f5; padding: 30px 0;\">");
            sb.AppendLine(" ");
            sb.AppendLine("        <tr>");
            sb.AppendLine("            <td align=\"center\">");
            sb.AppendLine("                <table cellpadding=\"0\" cellspacing=\"0\" width=\"600\"");
            sb.AppendLine("                    style=\"background-color: #ffffff; border-radius: 2px; overflow: hidden;\">");
            sb.AppendLine("                    <!-- Header -->");
            sb.AppendLine("                    <tr>");
            sb.AppendLine("                        <td align=\"right\" style=\"padding: 20px 24px 10px 24px;\">");
            sb.AppendLine("                            <img src=\"https://blob.mycockpitview.in/assets/logo.png\" alt=\"Newarch Logo\"");
            sb.AppendLine("                                style=\"display: block; max-width: 150px; height: auto;\">");
            sb.AppendLine("                        </td>");
            sb.AppendLine("                    </tr>");
            sb.AppendLine("                    <tr>");
            sb.AppendLine("                        <td>");
            sb.AppendLine("                            <div");
            sb.AppendLine("                                style=\"background-color: #FE0000; color: #ffffff; padding: 17px 34px; font-size: 18px; font-weight: 600; border-radius: 2px;\">");
            sb.AppendLine("                                INSPECTION REPORT | 1331–24 KARAT | #003");
            sb.AppendLine("                                <div style=\"font-size: 14px; font-weight: normal; margin-top: 6px;\">02 May 2025 | 10:00");
            sb.AppendLine("                                </div>");
            sb.AppendLine("                            </div>");
            sb.AppendLine("                        </td>");
            sb.AppendLine("                    </tr>");
            sb.AppendLine(" ");
            sb.AppendLine("                    <!-- Main Body Content -->");
            sb.AppendLine("                    <tr>");
            sb.AppendLine("                        <td style=\"padding: 20px 20px 0px 20px;\">");
            sb.AppendLine("                            <!-- TO and CC Section -->");
            sb.AppendLine("                            <table width=\"100%\" cellpadding=\"0\" cellspacing=\"0\" style=\"border: 1px solid #ccc;\">");
            sb.AppendLine("                                <tr>");
            sb.AppendLine("                                    <td style=\"padding: 16px;\">");
            sb.AppendLine("                                        <div style=\"margin-bottom: 10px; font-weight: bold;\">TO:</div>");
            sb.AppendLine("                                        <div style=\"margin-bottom: 12px;\">");
            sb.AppendLine("                                            <span style=\"color: #333;\">Mr. Omkar Patil</span><br />");
            sb.AppendLine("                                            <span style=\"font-size: 12px; color: #888;\">[<EMAIL>]</span>");
            sb.AppendLine("                                        </div>");
            sb.AppendLine("                                        <div style=\"margin-bottom: 12px;\">");
            sb.AppendLine("                                            <span style=\"color: #333;\">Ms. Jugal Jain</span><br />");
            sb.AppendLine("                                            <span style=\"font-size: 12px; color: #888;\">[<EMAIL>]</span>");
            sb.AppendLine("                                        </div>");
            sb.AppendLine(" ");
            sb.AppendLine("                                        <div style=\"margin-top: 20px; margin-bottom: 10px; font-weight: bold;\">CC:</div>");
            sb.AppendLine("                                        <div style=\"margin-bottom: 12px;\">");
            sb.AppendLine("                                            <span style=\"color: #333;\">Mr. Vikas Ligam</span><br />");
            sb.AppendLine("                                            <span");
            sb.AppendLine("                                                style=\"font-size: 12px; color: #888;\">[<EMAIL>]</span>");
            sb.AppendLine("                                        </div>");
            sb.AppendLine("                                        <div style=\"margin-bottom: 12px;\">");
            sb.AppendLine("                                            <span style=\"color: #333;\">Ms. Mrunal Dabholkar</span><br />");
            sb.AppendLine("                                            <span style=\"font-size: 12px; color: #888;\">[<EMAIL>]</span>");
            sb.AppendLine("                                        </div>");
            sb.AppendLine("                                        <div style=\"margin-bottom: 12px;\">");
            sb.AppendLine("                                            <span style=\"color: #333;\">Ms. Hrishikesh Phadke</span><br />");
            sb.AppendLine("                                            <span");
            sb.AppendLine("                                                style=\"font-size: 12px; color: #888;\">[<EMAIL>]</span>");
            sb.AppendLine("                                        </div>");
            sb.AppendLine("                                    </td>");
            sb.AppendLine("                                </tr>");
            sb.AppendLine("                            </table>");
            sb.AppendLine(" ");
            sb.AppendLine("                            <!-- Divider -->");
            sb.AppendLine("                            <div style=\"height: 20px;\"></div>");
            sb.AppendLine("                            <hr style=\"border: none; border-top: 1px solid #ff9b9b; margin: 0;\" />");
            sb.AppendLine("                            <div style=\"height: 10px;\"></div>");
            sb.AppendLine(" ");
            sb.AppendLine("                            <!-- Sent By -->");
            sb.AppendLine("                            <div style=\"font-size: 14px; color: #333;\">");
            sb.AppendLine("                                This email was sent by <strong>Mr. Omkar Patil</strong> [<EMAIL>]");
            sb.AppendLine("                            </div>");
            sb.AppendLine(" ");
            sb.AppendLine("                            <!-- Divider -->");
            sb.AppendLine("                            <div style=\"height: 10px;\"></div>");
            sb.AppendLine("                            <hr style=\"border: none; border-top: 1px solid #ff9b9b; margin: 0;\" />");
            sb.AppendLine("                            <div style=\"height: 20px;\"></div>");
            sb.AppendLine(" ");
            sb.AppendLine("                            <!-- Button -->");
            sb.AppendLine("                            <div style=\"text-align: center;\">");
            sb.AppendLine("                                <a href=\"#\" target=\"_blank\"");
            sb.AppendLine("                                    style=\"background-color: #fe0000; color: #ffffff; text-decoration: none; padding: 12px 24px; display: inline-block; border-radius: 4px; font-size: 14px; font-weight: bold;\">");
            sb.AppendLine("                                    VIEW INSPECTION REPORT");
            sb.AppendLine("                                </a>");
            sb.AppendLine("                            </div>");
            sb.AppendLine(" ");
            sb.AppendLine("                            <div style=\"height: 20px;\"></div>");
            sb.AppendLine("                            <hr style=\"border: none; border-top: 1px solid #ff9b9b; margin: 0;\" />");
            sb.AppendLine("                            <div style=\"height: 20px;\"></div>");
            sb.AppendLine(" ");
            sb.AppendLine(" ");
            sb.AppendLine("                            <!-- Footer -->");
            sb.AppendLine(" ");
            sb.AppendLine("                        </td>");
            sb.AppendLine("                    </tr>");
            sb.AppendLine("                    <tr>");
            sb.AppendLine("                        <td");
            sb.AppendLine("                            style=\"padding: 0px 20px 20px 20px;; text-align: center; font-size: 11px; color: #707070; line-height: 1.5;\">");
            sb.AppendLine("                            <div style=\"margin-bottom: 12px;\">");
            sb.AppendLine("                                <img src=\"https://blob.mycockpitview.in/assets/logo.png\" alt=\"Newarch Logo\" height=\"28\"");
            sb.AppendLine("                                    style=\"vertical-align: middle;\">");
            sb.AppendLine("                                <a href=\"https://www.linkedin.com/company/newarch-landscapes-llp/\" target=\"_blank\"");
            sb.AppendLine("                                    style=\"margin-left: 8px; text-decoration: none;\">");
            sb.AppendLine("                                    <img src=\"https://cdn-icons-png.flaticon.com/24/174/174857.png\" alt=\"LinkedIn\"");
            sb.AppendLine("                                        width=\"16\" height=\"16\" style=\"vertical-align: middle;\">");
            sb.AppendLine("                                </a>");
            sb.AppendLine("                                <a href=\"https://www.instagram.com/newarchllp?igsh=NDN6bGZ1NzF1MTdp\" target=\"_blank\"");
            sb.AppendLine("                                    style=\"margin-left: 8px; text-decoration: none;\">");
            sb.AppendLine("                                    <img src=\"https://cdn-icons-png.flaticon.com/24/2111/2111463.png\" alt=\"Instagram\"");
            sb.AppendLine("                                        width=\"16\" height=\"16\" style=\"vertical-align: middle;\">");
            sb.AppendLine("                                </a>");
            sb.AppendLine("                            </div>");
            sb.AppendLine("                            <div style=\"margin-bottom: 10px;\">");
            sb.AppendLine("                                System generated email by <strong>MyCockpitView®</strong> +");
            sb.AppendLine("                                <strong>DesignScript®</strong>");
            sb.AppendLine("                            </div>");
            sb.AppendLine("                            <div>");
            sb.AppendLine("                                Powered by <br>");
            sb.AppendLine("                                <strong>Newarch® Infotech LLP</strong>");
            sb.AppendLine("                            </div>");
            sb.AppendLine("                        </td>");
            sb.AppendLine("                    </tr>");
            sb.AppendLine("                </table>");
            sb.AppendLine("            </td>");
            sb.AppendLine("        </tr>");
            sb.AppendLine("    </table>");
            sb.AppendLine("</body>");
            sb.AppendLine(" ");
            sb.AppendLine("</html>");

            return sb.ToString();
        }




        var _emailBody = HTMLBuilder();

        var emailTo = new List<(string name, string email)>();
        foreach (var obj in toList)
            emailTo.Add((obj.Name, obj.Email));

        var emailCC = new List<(string name, string email)>();
        foreach (var obj in ccList)
            emailCC.Add((obj.Name, obj.Email));

        await sharedService.SendMail(_reportTitle, _senderName, _senderEmail, _emailBody, emailTo, emailCC);


            return Ok(_emailBody);
           
        }

    }
