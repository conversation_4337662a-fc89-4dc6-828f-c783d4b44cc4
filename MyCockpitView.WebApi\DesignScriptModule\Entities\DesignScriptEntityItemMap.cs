﻿
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using MyCockpitView.CoreModule;


namespace MyCockpitView.WebApi.DesignScriptModule.Entities;

public class DesignScriptEntityItemMap : BaseEntity
{
    [StringLength(50)]
    public string? CostingUnit { get; set; } = "sqmt";
    [Precision(14, 2)]
    public decimal CostingQuantity { get; set; } = 0;
    [Precision(14, 2)]
    public decimal CostingRate { get; set; } = 0;
    [Precision(14, 2)]
    public decimal CostingAmount { get; set; } = 0;
    public string? CostingRemark { get; set; }
    public int DesignScriptEntityID { get; set; }
    public int DesignScriptItemID { get; set; }
    public int ProjectID { get; set; }

    [StringLength(255)]
    public string?  ProjectCode { get; set; }


    [StringLength(255)]
    public string?  DesignScriptEntityCode { get; set; }

    [StringLength(255)]
    public string?  DesignScriptItemCode { get; set; }
    public virtual DesignScriptEntity? DesignScriptEntity { get; set; }
    public virtual DesignScriptItem? DesignScriptItem { get; set; }


    public virtual ICollection<DesignScriptMeasurementGroup> MeasurementGroups { get; set; } = new List<DesignScriptMeasurementGroup>();
 
}
public class DesignScriptEntityItemMapConfiguration : BaseEntityConfiguration<DesignScriptEntityItemMap>, IEntityTypeConfiguration<DesignScriptEntityItemMap>
{
    public void Configure(EntityTypeBuilder<DesignScriptEntityItemMap> builder)
    {
     base.Configure(builder);

        builder.HasIndex(e => e.ProjectID);
        builder.HasIndex(e => e.ProjectCode);
        builder.HasIndex(e => e.DesignScriptEntityCode);
        builder.HasIndex(e => e.DesignScriptItemCode);
    }
}