﻿using MyCockpitView.WebApi.HabitModule.Entities;
using MyCockpitView.WebApi.Services;

namespace MyCockpitView.WebApi.HabitModule.Services;

public interface IHabitService : IBaseEntityService<Habit>
{
    Task<int> Create(Habit Entity, ICollection<HabitResponse>? Respones = null);
    Task<IEnumerable<int>> GetAssigneeContactIDs(int entityID, string stageCode, string propertyName);
    Task<dynamic> GetTaskByStage(string Entity, int EntityID, string StageCode, string StageTitle, decimal StageDuration, DateTime? FollowUpDate = null);
    Task TaskAction(int EntityID, string StageCode, int WFTaskID, string? taskComment = null);
    Task<bool> Update(Habit UpdatedEntity, ICollection<HabitResponse>? Responses = null);
}