﻿

using System.Data;
using MyCockpitView.WebApi.Exceptions;
using PdfSharp.Pdf.IO;
using PdfSharp.Pdf;
using MyCockpitView.WebApi.Services;
using MyCockpitView.WebApi.DesignScriptModule.Entities;
using MyCockpitView.WebApi.ProjectModule.Entities;
using MyCockpitView.WebApi.ProjectModule.Services;
using MyCockpitView.CoreModule;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.Utility.Common;
using MyCockpitView.Utility.RDLCClient;
using MyCockpitView.Utility.PDFSharp;
using MyCockpitView.Utility.Excel;
using System.Linq;

namespace MyCockpitView.WebApi.DesignScriptModule.Services;


public class DesignScriptEntityService : BaseEntityService<DesignScriptEntity>, IDesignScriptEntityService
{
    public DesignScriptEntityService(EntitiesContext db) : base(db) { }

    public IQueryable<DesignScriptEntity> Get(IEnumerable<QueryFilter>? Filters = null, string? Search = null, string? Sort = null)
    {

        IQueryable<DesignScriptEntity> _query = base.Get(Filters);

        //Apply filters
        if (Filters != null)
        {
            if (Filters.Where(x => x.Key.Equals("deleted", StringComparison.OrdinalIgnoreCase)).Any())
            {

                _query = db.DesignScriptEntities
                        .AsNoTracking();
            }


            if (Filters.Where(x => x.Key.Equals("projectid", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<DesignScriptEntity>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("projectid", StringComparison.OrdinalIgnoreCase)))
                {
                    var isNumeric = Convert.ToInt32(_item.Value);

                    predicate = predicate.Or(x => x.ProjectID == isNumeric);
                }
                _query = _query.Where(predicate);
            }

            if (Filters.Where(x => x.Key.Equals("parentid", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<DesignScriptEntity>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("parentid", StringComparison.OrdinalIgnoreCase)))
                {
                    var isNumeric = Convert.ToInt32(_item.Value);

                    predicate = predicate.Or(x => x.ParentID != null && x.ParentID == isNumeric);
                }
                _query = _query.Where(predicate);
            }

            if (Filters.Where(x => x.Key.Equals("IsMasterPhase", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<DesignScriptEntity>();
                var _item = Filters.FirstOrDefault(x => x.Key.Equals("IsMasterPhase", StringComparison.OrdinalIgnoreCase));
                var value = Convert.ToBoolean(_item.Value);

                predicate = predicate.Or(x => x.isMasterPhase == value);

                _query = _query.Where(predicate);
            }
        }

        if (Search != null && Search != String.Empty)
        {
            var _keywords = Search.Split(' ');

            foreach (var _key in _keywords)
            {
                _query = _query
                     .Where(x => x.Title.ToLower().Contains(_key.ToLower())
                     || x.Code.ToLower().Contains(_key.ToLower())
                     || x._searchTags.ToLower().Contains(_key.ToLower()));
            }
        }

        if (Sort != null && Sort != String.Empty)
        {
            switch (Sort.ToLower())
            {
                case "createddate":
                    return _query
                            .OrderBy(x => x.Created);

                case "modifieddate":
                    return _query
                            .OrderBy(x => x.Modified);

                case "createddate desc":
                    return _query
                            .OrderByDescending(x => x.Created);

                case "modifieddate desc":
                    return _query
                            .OrderByDescending(x => x.Modified);


            }
        }

        return _query.OrderBy(x => x.OrderFlag);

    }

    public async Task<DesignScriptEntity?> GetById(int Id)
    {

        return await db.DesignScriptEntities.AsNoTracking()
                      .Include(x => x.ItemMaps).ThenInclude(s => s.DesignScriptItem)
                       .Include(x => x.ItemMaps).ThenInclude(s => s.MeasurementGroups).ThenInclude(c => c.Measurements)
                         .Include(x => x.DataCardMaps).ThenInclude(d => d.DataCard.Attachments)
          .Include(x => x.DataCardMaps).ThenInclude(d => d.DataCard.Attributes)
              .SingleOrDefaultAsync(i => i.ID == Id);


    }

    public async Task<DesignScriptEntity?> GetById(Guid Id)
    {

        return await db.DesignScriptEntities.AsNoTracking()
                 .Include(x => x.ItemMaps).ThenInclude(s => s.DesignScriptItem)
                      .Include(x => x.ItemMaps).ThenInclude(s => s.MeasurementGroups).ThenInclude(c => c.Measurements)
                        .Include(x => x.DataCardMaps).ThenInclude(d => d.DataCard.Attachments)
         .Include(x => x.DataCardMaps).ThenInclude(d => d.DataCard.Attributes)
             .SingleOrDefaultAsync(i => i.UID == Id);


    }


    public async Task<int> Create(DesignScriptEntity Entity)
    {

        if (Entity.Title == null || Entity.Title == string.Empty)
            throw new EntityServiceException("Title is required. Please enter proper title!");

        var projectService = new ProjectService(db);
        var _project = await projectService.Get().SingleOrDefaultAsync(x => x.ID == Entity.ProjectID);
        if (_project == null) throw new EntityServiceException("Project not found!");

        //      var _exist = await Get()
        //.FirstOrDefaultAsync(x => x.ProjectID == Entity.ProjectID && x.ParentID == Entity.ParentID && x.Title == Entity.Title);

        //      if (_exist != null) throw new EntityServiceException($"Entity with same title aleady exists {_exist.Code}!");

        Entity.ProjectCode = _project.Code;
        Entity.ProjectUID = _project.UID;

        Entity = await GetCode(Entity);

        Entity.OrderFlag = Entity.CodeFlag;

        if (await db.DesignScriptEntities.AsNoTracking()
            .Where(x => x.ProjectID == Entity.ProjectID && x.Code.Equals(Entity.Code))
            .AnyAsync())
            throw new EntityServiceException("Entity already exists!");


        return await base.Create(Entity);

    }

    private async Task<DesignScriptEntity> GetCode(DesignScriptEntity Entity)
    {
        var query = db.DesignScriptEntities.AsNoTracking()
                .Where(x => x.ProjectID == Entity.ProjectID);

        if (Entity.TypeFlag == McvConstant.DESIGN_SCRIPT_ENTITY_TYPEFLAG_ZONE) //PHASE
        {

            var _count = await query
                .AnyAsync(x => x.TypeFlag == Entity.TypeFlag) ?
               await query.Where(x => x.TypeFlag == Entity.TypeFlag).MaxAsync(x => x.CodeFlag) :
               -1;

            Entity.CodeFlag = _count + 1;
            Entity.Code = Entity.CodeFlag.ToString("D2");

        }
        else if (Entity.TypeFlag == McvConstant.DESIGN_SCRIPT_ENTITY_TYPEFLAG_SPACE)// SPACE
        {
            var _parent = await query.SingleOrDefaultAsync(p => p.ID == Entity.ParentID);
            if (_parent == null) throw new EntityServiceException("Parent Item not found!");

            var _count = await query
                .AnyAsync(x => x.TypeFlag == Entity.TypeFlag && x.ParentID == Entity.ParentID) ?
               await query
               .Where(x => x.TypeFlag == Entity.TypeFlag && x.ParentID == Entity.ParentID)
               .MaxAsync(x => x.CodeFlag) :
               0;

            Entity.CodeFlag = _count + 1;
            Entity.Code = _parent.Code + DataTools.GetAlphaCode(Entity.CodeFlag);

        }
        else if (Entity.TypeFlag == McvConstant.DESIGN_SCRIPT_ENTITY_TYPEFLAG_ELEMENT)//ELEMENT
        {
            var _parent = await query
                .SingleOrDefaultAsync(p => p.ID == Entity.ParentID);
            if (_parent == null) throw new EntityServiceException("Parent Item not found!");

            var _count = await query
                .AnyAsync(x => x.TypeFlag == Entity.TypeFlag && x.ParentID == Entity.ParentID) ?
                  await query
                  .Where(x => x.TypeFlag == Entity.TypeFlag && x.ParentID == Entity.ParentID)
                  .MaxAsync(x => x.CodeFlag) :
                  0;

            Entity.CodeFlag = _count + 1;
            Entity.Code = _parent.Code + Entity.CodeFlag.ToString("D2");

        }

        return Entity;
    }

    public async Task Update(DesignScriptEntity Entity)
    {

        if (Entity == null) throw new EntityServiceException("Object is null!");

        var _exist = await db.DesignScriptEntities.AsNoTracking()
.FirstOrDefaultAsync(x => x.ProjectID == Entity.ProjectID && x.ParentID == Entity.ParentID && x.Title == Entity.Title
&& x.ID != Entity.ID);

        if (_exist != null) throw new EntityServiceException($"Entity with same title aleady exists {_exist.Code}!");

        var _originalEntity = await db.DesignScriptEntities.AsNoTracking()
            .SingleOrDefaultAsync(x => x.ID == Entity.ID);

        if (_originalEntity.ParentID != Entity.ParentID)
        {
            Entity = await GetCode(Entity);

            Entity.OrderFlag = Entity.CodeFlag;
        }


        await base.Update(Entity);



    }

    public async Task Delete(int Id)
    {
        //var entity = await Get()
        //    .Include(x=>x.DataCards)
        //    .SingleOrDefaultAsync(x => x.ID == Id);

        //entity.DataCards.Clear();
        //await base.Update(entity);

        var childrenIDs = await Get().Where(x => x.ParentID == Id)
            .Select(x => x.ID)
            .ToListAsync();

        foreach (var child in childrenIDs)
            await Delete(child);


        await base.Delete(Id);

    }

    public async Task<string> GetNextCode(int TypeFlag, int ProjectID, int? ParentID = null)
    {

        if (TypeFlag == 0) //PHASE
        {
            var _count = await Get().AnyAsync(x => x.ProjectID == ProjectID && x.TypeFlag == TypeFlag) ?
               await Get().Where(x => x.ProjectID == ProjectID && x.TypeFlag == TypeFlag).MaxAsync(x => x.CodeFlag) : 0;


            var CodeFlag = _count + 1;
            return CodeFlag.ToString("D2");

        }
        else if (TypeFlag == 1)// SPACE
        {

            var _count = await Get().AnyAsync(x => x.ProjectID == ProjectID && x.TypeFlag == TypeFlag && x.ParentID == ParentID) ?
               await Get().Where(x => x.ProjectID == ProjectID && x.TypeFlag == TypeFlag && x.ParentID == ParentID).MaxAsync(x => x.CodeFlag) : 64;

            if (ParentID != null)
            {
                var _parent = await Get().SingleOrDefaultAsync(x => x.ID == ParentID);


                var CodeFlag = _count + 1;
                return _parent.Code + DataTools.GetAlphaCode(CodeFlag);
            }

            throw new EntityServiceException("Parent ID is required for this level");

        }
        else if (TypeFlag == 2)//ELEMENT
        {
            var _count = await Get().AnyAsync(x => x.ProjectID == ProjectID && x.TypeFlag == TypeFlag && x.ParentID == ParentID) ?
                  await Get().Where(x => x.ProjectID == ProjectID && x.TypeFlag == TypeFlag && x.ParentID == ParentID).MaxAsync(x => x.CodeFlag) : 0;

            if (ParentID != null)
            {
                var _parent = await Get().SingleOrDefaultAsync(x => x.ID == ParentID);

                var CodeFlag = _count + 1;
                return _parent.Code + CodeFlag.ToString("D2");
            }

            throw new EntityServiceException("Parent ID is required for this level");


        }

        throw new EntityServiceException($"Typeflag {TypeFlag} not defined for getting next code!");


    }

    public async Task<IEnumerable<string>> GetSearchTagOptions(int ProjectID)
    {

        var _options = new List<string>();


        var _tags = (await db.DesignScriptDataCards
            .AsNoTracking()
            .Where(x => x.ProjectID == ProjectID)
            .ToListAsync())
            .Select(x => x.SearchTags);

        foreach (var item in _tags)
        {
            foreach (var tag in item)
            {
                _options.Add(tag);
            }
        }

        return _options.Distinct().OrderBy(x => x);

    }

    public async Task<ReportDefinition> GetPackageSetPDF(
       Guid projectUID,
       string reportSize = "a4",
       IEnumerable<QueryFilter> filters = null)
    {

        var projectService = new ProjectService(db);
        var project = await projectService.Get()
                                           .Where(x => x.UID == projectUID)
                                           .SingleOrDefaultAsync();

        if (project == null) throw new EntityServiceException("Project not found!");


        var sharedService = new SharedService(db); ;
        var _reportContainerUrl = await sharedService.GetPresetValue(McvConstant.RDLC_REPORT_CONTAINER_URL);
        var DEVMODE = Convert.ToBoolean((await sharedService.GetPresetValue(McvConstant.DEVMODE)));

        var _reportPath = $"PackageSetCover-{reportSize}.rdlc";



        var lastPage = 0;
        var totalPages = 0;
        var _pdfs = new List<byte[]>();
        var contents = new List<PackageCoverItem>();

        //DESIGNSCRIPT
        var designScript = await GetDesignScriptPDF(projectUID, reportSize, filters, false, totalPages, lastPage);

        _pdfs.Add(designScript.FileContent);

        contents.Add(new PackageCoverItem() { Title = designScript.ReportName, Page = lastPage + 1 });

        PdfDocument document = PdfReader.Open(new MemoryStream(designScript.FileContent), PdfDocumentOpenMode.Import);
        lastPage = lastPage + document.PageCount;
        document.Close();


        //Material+Product List
        var materialList = await GetMaterialListPDF(projectUID, reportSize, filters, false, totalPages, lastPage);

        _pdfs.Add(materialList.FileContent);

        contents.Add(new PackageCoverItem() { Title = materialList.ReportName, Page = lastPage + 1 });

        document = PdfReader.Open(new MemoryStream(materialList.FileContent), PdfDocumentOpenMode.Import);
        lastPage = lastPage + document.PageCount;
        document.Close();

        //Item List
        var itemList = await GetItemList(projectUID, reportSize, filters, "PDF", false, totalPages, lastPage);

        _pdfs.Add(itemList.FileContent);

        contents.Add(new PackageCoverItem() { Title = itemList.ReportName, Page = lastPage + 1 });

        document = PdfReader.Open(new MemoryStream(itemList.FileContent), PdfDocumentOpenMode.Import);
        lastPage = lastPage + document.PageCount;
        document.Close();

        //Element Estimate
        var elementEstimate = await GetElementEstimate(projectUID, reportSize, filters, "PDF", false, totalPages, lastPage);

        _pdfs.Add(elementEstimate.FileContent);

        contents.Add(new PackageCoverItem() { Title = elementEstimate.ReportName, Page = lastPage + 1 });

        if (!_pdfs.Any()) throw new EntityServiceException("No reports generated!");

        var _reportProperties = new List<ReportProperties>
                {
                    new ReportProperties() { PropertyName = "Title", PropertyValue = $"{project.Code}-{project.Title}" },
                                      new ReportProperties() { PropertyName = "Filter_Categories", PropertyValue = materialList.ReportProperties.FirstOrDefault(x=>x.PropertyName=="Filter_Categories").PropertyValue },
                  new ReportProperties() { PropertyName = "Filter_Zones", PropertyValue = materialList.ReportProperties.FirstOrDefault(x=>x.PropertyName=="Filter_Zones").PropertyValue },

                };
        var _reportDef = new ReportDefinition()
        {
            ReportName = "PackageSetCover",
            ReportPath = DEVMODE ? $"{_reportContainerUrl}DEV/{_reportPath}" : $"{_reportContainerUrl}{_reportPath}",
            ReportDataSet = DataTools.ToDataTable(contents),
            ReportProperties = _reportProperties,
            Filename = $"PackageSetCover-{project.Code}-{project.Title}-{ClockTools.GetISTNow().ToString("yyMMddHHmm")}",
            RenderType = "PDF"
        };

        var reportServiceApi = await sharedService.GetPresetValue(McvConstant.RDLC_PROCESSOR_API);
        var cover = await ReportClient.GenerateReport(_reportDef, reportServiceApi);

        _pdfs = (new List<byte[]>() { cover.FileContent }).Concat(_pdfs).ToList();

        return new ReportDefinition()
        {
            FileContent = PdfUtility.CombinePDFs(_pdfs),
            Filename = $"PackageSet-{project.Code}-{project.Title}-{ClockTools.GetISTNow().ToString("yyMMddHHmm")}",
            RenderType = "PDF"
        };
    }


    public async Task<ReportDefinition> GetDesignScriptPDF(
    Guid projectUID,
    string reportSize = "a4",
    IEnumerable<QueryFilter> filters = null,
        bool AutoPageNumber = true,
                int TotalPages = 0,
    int LastPageNumber = 0)
    {

        var projectService = new ProjectService(db);
        var project = await projectService.Get()
                                           .Where(x => x.UID == projectUID)
                                           .SingleOrDefaultAsync();

        if (project == null) throw new EntityServiceException("Project not found!");


        var entities = await Get()
                        .Where(x => x.ProjectID == project.ID)
                           .ToListAsync();

        var filteredZones = entities
                .Where(x => x.ParentID == null && x.TypeFlag == McvConstant.DESIGN_SCRIPT_ENTITY_TYPEFLAG_ZONE)
                .ToList();

        var hideAttributes = false;
        var hideIntent = false;
        var hideImages = false;

        if (filters != null)
        {
            if (filters.Where(x => x.Key.Equals("hideAttributes", StringComparison.OrdinalIgnoreCase)).Any())
            {
                hideAttributes = filters.Where(x => x.Key.Equals("hideAttributes", StringComparison.OrdinalIgnoreCase)).Select(x => Convert.ToBoolean(x.Value.Trim())).FirstOrDefault();
            }

            if (filters.Where(x => x.Key.Equals("hideIntent", StringComparison.OrdinalIgnoreCase)).Any())
            {
                hideIntent = filters.Where(x => x.Key.Equals("hideIntent", StringComparison.OrdinalIgnoreCase)).Select(x => Convert.ToBoolean(x.Value.Trim())).FirstOrDefault();
            }

            if (filters.Where(x => x.Key.Equals("hideImages", StringComparison.OrdinalIgnoreCase)).Any())
            {
                hideImages = filters.Where(x => x.Key.Equals("hideImages", StringComparison.OrdinalIgnoreCase)).Select(x => Convert.ToBoolean(x.Value.Trim())).FirstOrDefault();
            }

            if (filters.Where(x => x.Key.Equals("zone", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var _zoneUIDs = filters.Where(x => x.Key.Equals("zone", StringComparison.OrdinalIgnoreCase)).Select(x => Guid.Parse(x.Value.Trim()));

                filteredZones = filteredZones.Where(x => _zoneUIDs.Any(z => z == x.UID)).ToList();
            }
        }


        var _entityIDs = filteredZones.Select(x => x.ID);
        var _designScriptEntityIds = _entityIDs.ToList();
        while (_entityIDs.Any())
        {
            _entityIDs = await GetRecursiveChildrenIDsAsync(_entityIDs, entities);
            _designScriptEntityIds = _designScriptEntityIds.Concat(_entityIDs).ToList();
        }

        if (_designScriptEntityIds != null && _designScriptEntityIds.Any())
        {
            entities = entities.Where(x => _designScriptEntityIds.Any(c => c == x.ID)).ToList();
        }

        var sharedService = new SharedService(db);
        var originalHostName = await sharedService.GetPresetValue(McvConstant.AZURE_STORAGE_BLOB_ORIGINAL_HOSTNAME);
        var cdnHostName = await sharedService.GetPresetValue(McvConstant.AZURE_STORAGE_BLOB_CDN_HOSTNAME);

        var dataCards = await db.DesignScriptDataCards.AsNoTracking()
       .Where(x => !x.IsDeleted && !x.IsHidden && x.ProjectID == project.ID && x.Category == null && x.Attachments.Where(c => !c.IsDeleted).Any())
        .Include(x => x.Maps)
                         .Include(x => x.Attachments)
                          .SelectMany(x => x.Maps.Select(e => new
                          {
                              EntityID = e.DesignScriptEntityID,
                              DataCardID = x.ID,
                              DataCardDescription = x.Description,
                              Attachments = x.Attachments.Where(c => !c.IsDeleted && !c.IsHidden).Select(a => new
                              {
                                  Url = a.ThumbUrl != null ? a.ThumbUrl : a.Url
                              }),
                              e.OrderFlag
                          }))

        .ToListAsync();


        var typeMasters = await db.TypeMasters.AsNoTracking()
                        .Where(t => t.Entity == nameof(DesignScriptEntity))
                        .ToListAsync();


        var dataSet = entities.Where(x => _designScriptEntityIds.Any(c => c == x.ID))
                                .Select(x => new
                                {
                                    DSEntityID = x.ID,
                                    DSEntityCode = x.Code,
                                    DSEntityTitle = x.Title,
                                    DSEntityIntent = x.Description,
                                    DsEntityOrderFlag = x.OrderFlag,
                                    DSEntityParentID = x.ParentID != null ? x.ParentID : 0,
                                    DSEntityType = typeMasters.Where(t => t.Value == x.TypeFlag).Any() ? typeMasters.Where(t => t.Value == x.TypeFlag).FirstOrDefault().Title : "",
                                }).ToList();

        var dataSetImages = dataCards
                                 .SelectMany(x => x.Attachments

                                 .Select(a => new
                                 {
                                     DataCardID = x.DataCardID,
                                     Description = x.DataCardDescription,
                                     Url = a.Url.Replace(cdnHostName, originalHostName),
                                     x.OrderFlag,
                                     DSEntityID = x.EntityID

                                 })).ToList();


        var _reportProperties = new List<ReportProperties>
            {
            new ReportProperties() { PropertyName="ReportTitle", PropertyValue="DESIGNSCRIPT©" },
                new ReportProperties() { PropertyName = "Title", PropertyValue = $"{project.Code}-{project.Title}" },
                 new ReportProperties() { PropertyName = "HideIntent", PropertyValue = $"{hideIntent}" },
                   new ReportProperties() { PropertyName = "HideImages", PropertyValue = $"{hideImages}" },
                  new ReportProperties() { PropertyName = "Filter_Zones", PropertyValue = $"{string.Join(", ",filteredZones.Select(x=> x.Code+"-"+x.Title))}" },
                     new ReportProperties() { PropertyName = "AutoPageNumber", PropertyValue = $"{AutoPageNumber}" },
                     new ReportProperties() { PropertyName = "TotalPages", PropertyValue = $"{TotalPages}" },
                        new ReportProperties() { PropertyName = "LastPageNumber", PropertyValue = $"{LastPageNumber}" },
            };

        var _reportContainerUrl = await sharedService.GetPresetValue(McvConstant.RDLC_REPORT_CONTAINER_URL);
        var DEVMODE = Convert.ToBoolean((await sharedService.GetPresetValue(McvConstant.DEVMODE)));

        var _reportPath = $"DesignScript_{reportSize}.rdlc";

        var _reportDef = new ReportDefinition()
        {
            ReportName = "DesignScript",
            ReportPath = DEVMODE ? $"{_reportContainerUrl}DEV/{_reportPath}" : $"{_reportContainerUrl}{_reportPath}",
            ReportDataSet = DataTools.ToDataTable(dataSet),
            ReportProperties = _reportProperties,
            Filename = $"DesignScript-{project.Code}-{project.Title}-{ClockTools.GetISTNow().ToString("yyMMddHHmm")}",
            RenderType = "PDF"
        };

        _reportDef.SubReports.Add(new ReportDefinition()
        {
            ReportName = $"DesignScript_{reportSize}_Images",
            ReportPath = DEVMODE ? $"{_reportContainerUrl}DEV/DesignScript_{reportSize}_Images.rdlc" : $"{_reportContainerUrl}DesignScript_{reportSize}_Images.rdlc",
            ReportDataSet = DataTools.ToDataTable(dataSetImages),
        });

        var reportServiceApi = await sharedService.GetPresetValue(McvConstant.RDLC_PROCESSOR_API);
        return await ReportClient.GenerateReport(_reportDef, reportServiceApi);


    }

    public async Task<ReportDefinition> GetDesignCanvasPDF(Guid projectUID, string pageSize = "a4", IEnumerable<QueryFilter> filters = null,
            bool AutoPageNumber = true,
                    int TotalPages = 0,
        int LastPageNumber = 0)
    {

        var projectService = new ProjectService(db);
        var project = await projectService.Get()
                                           .Where(x => x.UID == projectUID)
                                           .SingleOrDefaultAsync();

        if (project == null) throw new EntityServiceException("Project not found!");

        var _query = await db.DesignScriptDataCards.AsNoTracking()
                
                .Where(x => !x.IsHidden)
                .Include(x => x.Maps).ThenInclude(c => c.Entity)
                .Where(x => x.ProjectID == project.ID)
                    .Where(x => x.TypeFlag != McvConstant.DESIGN_SCRIPT_DATA_CARD_TYPEFLAG_MATERIAL)
                    .Include(x => x.Maps).ThenInclude(c => c.Entity)
                    .Where(x => x.Maps.Any(e => e.Entity.TypeFlag == McvConstant.DESIGN_SCRIPT_ENTITY_TYPEFLAG_ZONE))

                .ToListAsync();

        var _dataset = _query.Select(x => new
        {
            ID = x.UID,
            DataCardKey = x.Title,
            DataCardValue = x.Description,
            DataCardOrderFlag = x.OrderFlag,
            Links = string.Join(", ", _query.Where(l => x.Links.Any(s => s == l.UID))
                                            .Select(i => Convert.ToInt32(i.OrderFlag).ToString("D2"))
                                            )
        });

        var _reportProperties = new List<ReportProperties>
            {
                //new ReportProperties() { PropertyName="ReportTitle", PropertyValue="DESIGNCANVAS©" },
                new ReportProperties() { PropertyName = "Title", PropertyValue = $"{project.Code}-{project.Title}" },
                         new ReportProperties() { PropertyName = "AutoPageNumber", PropertyValue = $"{AutoPageNumber}" },
                     new ReportProperties() { PropertyName = "TotalPages", PropertyValue = $"{TotalPages}" },
                        new ReportProperties() { PropertyName = "LastPageNumber", PropertyValue = $"{LastPageNumber}" },
            };
        var sharedService = new SharedService(db); ;
        var _reportContainerUrl = await sharedService.GetPresetValue(McvConstant.RDLC_REPORT_CONTAINER_URL);
        var DEVMODE = Convert.ToBoolean((await sharedService.GetPresetValue(McvConstant.DEVMODE)));

        var _reportPath = $"DesignCanvas-{pageSize}.rdlc";

        var _reportDef = new ReportDefinition()
        {
            ReportName = "DesignCanvas",
            ReportPath = DEVMODE ? $"{_reportContainerUrl}DEV/{_reportPath}" : $"{_reportContainerUrl}{_reportPath}",
            ReportDataSet = DataTools.ToDataTable(_dataset),
            ReportProperties = _reportProperties,
            Filename = $"DesignCanvas-{project.Code}-{project.Title}-{ClockTools.GetISTNow().ToString("yyMMddHHmm")}",
            RenderType = "PDF"
        };

        var reportServiceApi = await sharedService.GetPresetValue(McvConstant.RDLC_PROCESSOR_API);
        return await ReportClient.GenerateReport(_reportDef, reportServiceApi);

    }

    public async Task<byte[]> GetEntityListExcel(
        Guid ProjectUID,
        IEnumerable<QueryFilter> filters = null)
    {
        var projectService = new ProjectService(db);
        var _project = await projectService.Get()
              .Where(x => x.UID == ProjectUID)
              .SingleOrDefaultAsync();

        if (_project == null) throw new EntityServiceException("Project not found!");



        var _query = Get()
                                   .Include(x => x.DataCardMaps).ThenInclude(d => d.DataCard)
                        .Where(x => x.ProjectID == _project.ID);

        IEnumerable<Guid> _phases = null;
        //IEnumerable<string> _categories = null;

        if (filters != null)
        {
            //if (filters.Where(x => x.Key.Equals("category", StringComparison.OrdinalIgnoreCase)).Any())
            //{
            //    _categories = filters.Where(x => x.Key.Equals("category", StringComparison.OrdinalIgnoreCase)).Select(x => x.Value.Trim());
            //}

            if (filters.Where(x => x.Key.Equals("phase", StringComparison.OrdinalIgnoreCase)).Any())
            {
                _phases = filters.Where(x => x.Key.Equals("phase", StringComparison.OrdinalIgnoreCase)).Select(x => Guid.Parse(x.Value.Trim()));
            }
        }

        if (_phases == null)
        {
            _phases = await Get()
            .Where(x => x.ProjectID == _project.ID)
            .Where(x => x.ParentID == null && x.TypeFlag == McvConstant.DESIGN_SCRIPT_ENTITY_TYPEFLAG_ZONE)
            .Select(x => x.UID)
            .ToListAsync();
        }


        var _data = await
        (from p in _query.Where(x => _phases.Any(i => i == x.UID))
         join s in _query on p.ID equals s.ParentID
         join e in _query on s.ID equals e.ParentID

         orderby p.OrderFlag, s.OrderFlag, e.OrderFlag
         select new
         {
             Phase = p.Code + "-" + p.Title,
             Space = s.Code + "-" + s.Title,
             SpaceIntent = s.Description,
             Element = e.Code + "-" + e.Title,
             ElementIntent = e.Description,
             Civil = e.DataCardMaps.Count(x => !x.IsDeleted && x.DataCard.Category == "CIVIL"),
             Finishing = e.DataCardMaps.Count(x => !x.IsDeleted && x.DataCard.Category == "FINISHING"),
             Furniture = e.DataCardMaps.Count(x => !x.IsDeleted && x.DataCard.Category == "FURNITURE"),
             Lighting = e.DataCardMaps.Count(x => !x.IsDeleted && x.DataCard.Category == "LIGHTING"),
             Planting = e.DataCardMaps.Count(x => !x.IsDeleted && x.DataCard.Category == "PLANTING"),
             Services = e.DataCardMaps.Count(x => !x.IsDeleted && x.DataCard.Category == "SERVICES"),

         })
            .ToListAsync();

        var _dataSet = new DataSet();
        _dataSet.Tables.Add(DataTools.ToDataTable(_data));

        return ExcelUtility.ExportExcel(_dataSet);

    }

    public async Task<ReportDefinition> GetSpaceEstimate(
Guid projectUID,
string pageSize = "a4",
IEnumerable<QueryFilter> filters = null, string renderType = "PDF", bool AutoPageNumber = true,
                    int TotalPages = 0,
        int LastPageNumber = 0)
    {

        var projectService = new ProjectService(db);
        var project = await projectService.Get()
                                           .Where(x => x.UID == projectUID)
                                           .SingleOrDefaultAsync();

        if (project == null) throw new EntityServiceException("Project not found!");

        var entities = await Get()
               .Where(x => x.ProjectID == project.ID)
               .ToListAsync();


        var _masterPhase = entities
                .Where(x => x.ProjectID == project.ID && x.TypeFlag == McvConstant.DESIGN_SCRIPT_ENTITY_TYPEFLAG_ZONE
                && x.isMasterPhase).SingleOrDefault();

        if (_masterPhase == null) throw new EntityServiceException("Master phase not found!");

        var filteredZones = entities
           .Where(x => x.ParentID == null && x.TypeFlag == McvConstant.DESIGN_SCRIPT_ENTITY_TYPEFLAG_ZONE)
           .ToList();

        if (filters != null)
        {
            if (filters.Where(x => x.Key.Equals("zone", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var _zoneUIDs = filters.Where(x => x.Key.Equals("zone", StringComparison.OrdinalIgnoreCase)).Select(x => Guid.Parse(x.Value.Trim()));

                filteredZones = filteredZones.Where(x => _zoneUIDs.Any(z => z == x.UID)).ToList();
            }
        }

        var _entityIDs = filteredZones.Select(x => x.ID);
        var _designScriptEntityIds = _entityIDs.ToList();
        while (_entityIDs.Any())
        {
            _entityIDs = await GetRecursiveChildrenIDsAsync(_entityIDs, entities);
            _designScriptEntityIds = _designScriptEntityIds.Concat(_entityIDs).ToList();
        }

        if (_designScriptEntityIds != null && _designScriptEntityIds.Any())
        {
            entities = entities.Where(x => _designScriptEntityIds.Any(c => c == x.ID)).ToList();
        }

        var _data = entities.Where(x => x.TypeFlag == McvConstant.DESIGN_SCRIPT_ENTITY_TYPEFLAG_ZONE)
        .Join(entities.Where(x => x.TypeFlag == McvConstant.DESIGN_SCRIPT_ENTITY_TYPEFLAG_SPACE),
        a => a.ID,
        b => b.ParentID,
        (p, s) => new
        {
            Phase = p.Code + "-" + p.Title,
            Space = s.Code + "-" + s.Title,
            Intent = s.Description,
            Quantity = s.CostingQuantity,
            Unit = s.CostingUnit,
            Rate = s.CostingRate,
            Amount = s.CostingAmount,
            Remark = s.CostingRemark

        });

        var message = "All prices shown are in Indian Rupee (₹). This is a summary estimate, not a quote. The rates mentioned are subject to change.";

        if (_masterPhase.EstimateRemark != null && _masterPhase.EstimateRemark != string.Empty)
            message += "\n" + _masterPhase.EstimateRemark;


        var _reportProperties = new List<ReportProperties>
            {
                new ReportProperties() { PropertyName="ReportTitle", PropertyValue="SPACE ESTIMATE" },
                new ReportProperties() { PropertyName = "Title", PropertyValue = $"{project.Code}-{project.Title}" },
                new ReportProperties() { PropertyName = "Contingency", PropertyValue = $"{_masterPhase.EstimateContingency}" },
                new ReportProperties() { PropertyName = "GST", PropertyValue = $"{_masterPhase.EstimateGST}" },
                new ReportProperties() { PropertyName = "Message", PropertyValue = message },
                            new ReportProperties() { PropertyName = "Filter_Zones", PropertyValue = $"{string.Join(", ",filteredZones.Select(x=> x.Code+"-"+x.Title))}" },
                     new ReportProperties() { PropertyName = "AutoPageNumber", PropertyValue = $"{AutoPageNumber}" },
                     new ReportProperties() { PropertyName = "TotalPages", PropertyValue = $"{TotalPages}" },
                        new ReportProperties() { PropertyName = "LastPageNumber", PropertyValue = $"{LastPageNumber}" },
            };
        var sharedService = new SharedService(db); ;
        var _reportContainerUrl = await sharedService.GetPresetValue(McvConstant.RDLC_REPORT_CONTAINER_URL);
        var DEVMODE = Convert.ToBoolean((await sharedService.GetPresetValue(McvConstant.DEVMODE)));

        var _reportPath = $"SpaceEstimate.rdlc";

        var _reportDef = new ReportDefinition()
        {
            ReportName = "Space Estimate",
            ReportPath = DEVMODE ? $"{_reportContainerUrl}DEV/{_reportPath}" : $"{_reportContainerUrl}{_reportPath}",
            ReportDataSet = DataTools.ToDataTable(_data),
            ReportProperties = _reportProperties,
            Filename = $"SpaceEstimate-{project.Code}-{project.Title}-{ClockTools.GetISTNow().ToString("yyMMddHHmm")}",
            RenderType = renderType
        };

        var reportServiceApi = await sharedService.GetPresetValue(McvConstant.RDLC_PROCESSOR_API);
        return await ReportClient.GenerateReport(_reportDef, reportServiceApi);

    }
    public async Task<byte[]> GetSpaceEstimateExcel(
Guid ProjectUID,
IEnumerable<QueryFilter> filters = null)
    {
        var projectService = new ProjectService(db);
        var _project = await projectService.Get()
              .Where(x => x.UID == ProjectUID)
              .SingleOrDefaultAsync();

        if (_project == null) throw new EntityServiceException("Project not found!");



        var _query = Get()
                        .Where(x => x.ProjectID == _project.ID);

        IEnumerable<Guid> _phases = null;

        if (filters != null)
        {

            if (filters.Where(x => x.Key.Equals("phase", StringComparison.OrdinalIgnoreCase)).Any())
            {
                _phases = filters.Where(x => x.Key.Equals("phase", StringComparison.OrdinalIgnoreCase)).Select(x => Guid.Parse(x.Value.Trim()));
            }
        }

        if (_phases == null)
        {
            _phases = await Get()
            .Where(x => x.ProjectID == _project.ID)
            .Where(x => x.ParentID == null && x.TypeFlag == McvConstant.DESIGN_SCRIPT_ENTITY_TYPEFLAG_ZONE)
            .Select(x => x.UID)
            .ToListAsync();
        }


        var _data = await
        (from p in _query.Where(x => _phases.Any(i => i == x.UID))
         join s in _query on p.ID equals s.ParentID

         orderby p.OrderFlag,
         s.OrderFlag

         select new
         {
             Phase = p.Code + "-" + p.Title,
             Space = s.Code + "-" + s.Title,
             //SpaceIntent = s.Description,
             Quantity = s.CostingQuantity,
             Unit = s.CostingUnit,
             Rate = s.CostingRate,
             Amount = s.CostingAmount,
             Remark = s.CostingRemark

         })
            .ToListAsync();

        var _dataSet = new DataSet();
        _dataSet.Tables.Add(DataTools.ToDataTable(_data));

        return ExcelUtility.ExportExcel(_dataSet);

    }

    public async Task<ReportDefinition> GetElementEstimate(Guid projectUID,
                                                              string pageSize = "a4",
                                                              IEnumerable<QueryFilter> filters = null, string renderType = "PDF", bool AutoPageNumber = true,
                    int TotalPages = 0,
        int LastPageNumber = 0)
    {

        var projectService = new ProjectService(db);
        var project = await projectService.Get()
                                           .Where(x => x.UID == projectUID)
                                           .SingleOrDefaultAsync();

        if (project == null) throw new EntityServiceException("Project not found!");

        var entities = await Get()
               .Where(x => x.ProjectID == project.ID)
               .ToListAsync();



        var _masterPhase = entities
                                      .Where(x => x.ProjectID == project.ID && x.TypeFlag == McvConstant.DESIGN_SCRIPT_ENTITY_TYPEFLAG_ZONE
                                      && x.isMasterPhase).SingleOrDefault();

        if (_masterPhase == null) throw new EntityServiceException("Master phase not found!");

        var filteredZones = entities
          .Where(x => x.ParentID == null && x.TypeFlag == McvConstant.DESIGN_SCRIPT_ENTITY_TYPEFLAG_ZONE)
          .ToList();

        if (filters != null)
        {
            if (filters.Where(x => x.Key.Equals("zone", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var _zoneUIDs = filters.Where(x => x.Key.Equals("zone", StringComparison.OrdinalIgnoreCase)).Select(x => Guid.Parse(x.Value.Trim()));

                filteredZones = filteredZones.Where(x => _zoneUIDs.Any(z => z == x.UID)).ToList();
            }
        }

        var _entityIDs = filteredZones.Select(x => x.ID);
        var _designScriptEntityIds = _entityIDs.ToList();
        while (_entityIDs.Any())
        {
            _entityIDs = await GetRecursiveChildrenIDsAsync(_entityIDs, entities);
            _designScriptEntityIds = _designScriptEntityIds.Concat(_entityIDs).ToList();
        }

        if (_designScriptEntityIds != null && _designScriptEntityIds.Any())
        {
            entities = entities.Where(x => _designScriptEntityIds.Any(c => c == x.ID)).ToList();
        }

        var _data = entities.Where(x => x.TypeFlag == McvConstant.DESIGN_SCRIPT_ENTITY_TYPEFLAG_ZONE)
            .Join(entities.Where(x => x.TypeFlag == McvConstant.DESIGN_SCRIPT_ENTITY_TYPEFLAG_SPACE),
            a => a.ID,
            b => b.ParentID,
            (p, s) => new { phase = p, space = s })
         .Join(entities.Where(x => x.TypeFlag == McvConstant.DESIGN_SCRIPT_ENTITY_TYPEFLAG_ELEMENT),
            a => a.space.ID,
            b => b.ParentID,
            (ps, e) => new
            {
                Phase = ps.phase.Code + "-" + ps.phase.Title,
                Space = ps.space.Code + "-" + ps.space.Title,
                //SpaceIntent = s.Description,
                Element = e.Code + "-" + e.Title,
                Intent = e.Description,
                Quantity = e.CostingQuantity,
                Unit = e.CostingUnit,
                Rate = e.CostingRate,
                Amount = e.CostingAmount,
                Remark = e.CostingRemark

            });

        var message = "All prices shown are in Indian Rupee (₹). This is a summary estimate, not a quote. The rates mentioned are subject to change.";

        if (_masterPhase.EstimateRemark != null && _masterPhase.EstimateRemark != string.Empty)
            message += "\n" + _masterPhase.EstimateRemark;

        var _reportProperties = new List<ReportProperties>
                {
                    new ReportProperties() { PropertyName="ReportTitle", PropertyValue="ELEMENT ESTIMATE" },
                    new ReportProperties() { PropertyName = "Title", PropertyValue = $"{project.Code}-{project.Title}" },
                                        new ReportProperties() { PropertyName = "Contingency", PropertyValue = $"{_masterPhase.EstimateContingency}" },
                    new ReportProperties() { PropertyName = "GST", PropertyValue = $"{_masterPhase.EstimateGST}" },
                    new ReportProperties() { PropertyName = "Message", PropertyValue =message },
                                                 new ReportProperties() { PropertyName = "Filter_Zones", PropertyValue = $"{string.Join(", ",filteredZones.Select(x=> x.Code+"-"+x.Title))}" },
                         new ReportProperties() { PropertyName = "AutoPageNumber", PropertyValue = $"{AutoPageNumber}" },
                         new ReportProperties() { PropertyName = "TotalPages", PropertyValue = $"{TotalPages}" },
                            new ReportProperties() { PropertyName = "LastPageNumber", PropertyValue = $"{LastPageNumber}" },
                };
        var sharedService = new SharedService(db); ;
        var _reportContainerUrl = await sharedService.GetPresetValue(McvConstant.RDLC_REPORT_CONTAINER_URL);
        var DEVMODE = Convert.ToBoolean((await sharedService.GetPresetValue(McvConstant.DEVMODE)));

        var _reportPath = $"ElementEstimate.rdlc";

        var _reportDef = new ReportDefinition()
        {
            ReportName = "Element Estimate",
            ReportPath = DEVMODE ? $"{_reportContainerUrl}DEV/{_reportPath}" : $"{_reportContainerUrl}{_reportPath}",
            ReportDataSet = DataTools.ToDataTable(_data),
            ReportProperties = _reportProperties,
            Filename = $"ElementEstimate-{project.Code}-{project.Title}-{ClockTools.GetISTNow().ToString("yyMMddHHmm")}",
            RenderType = renderType
        };

        var reportServiceApi = await sharedService.GetPresetValue(McvConstant.RDLC_PROCESSOR_API);
        return await ReportClient.GenerateReport(_reportDef, reportServiceApi);

    }

    public async Task<byte[]> GetElementEstimateExcel(
Guid ProjectUID,
IEnumerable<QueryFilter> filters = null)
    {
        var projectService = new ProjectService(db);
        var _project = await projectService.Get()
              .Where(x => x.UID == ProjectUID)
              .SingleOrDefaultAsync();

        if (_project == null) throw new EntityServiceException("Project not found!");



        var _query = Get()
                        .AsNoTracking()
                        .Where(x => x.ProjectID == _project.ID);

        IEnumerable<Guid> _phases = null;

        if (filters != null)
        {

            if (filters.Where(x => x.Key.Equals("phase", StringComparison.OrdinalIgnoreCase)).Any())
            {
                _phases = filters.Where(x => x.Key.Equals("phase", StringComparison.OrdinalIgnoreCase)).Select(x => Guid.Parse(x.Value.Trim()));
            }
        }

        if (_phases == null)
        {
            _phases = await Get()
            .Where(x => x.ProjectID == _project.ID)
            .Where(x => x.ParentID == null && x.TypeFlag == McvConstant.DESIGN_SCRIPT_ENTITY_TYPEFLAG_ZONE)
            .Select(x => x.UID)
            .ToListAsync();
        }


        var _data = await
        (from p in _query.Where(x => _phases.Any(i => i == x.UID))
         join s in _query on p.ID equals s.ParentID
         join e in _query on s.ID equals e.ParentID

         orderby p.OrderFlag, s.OrderFlag, e.OrderFlag

         select new
         {
             Phase = p.Code + "-" + p.Title,
             Space = s.Code + "-" + s.Title,
             //SpaceIntent = s.Description,
             Element = e.Code + "-" + e.Title,
             Quantity = e.CostingQuantity,
             Unit = e.CostingUnit,
             Rate = e.CostingRate,
             Amount = e.CostingAmount,
             Remark = e.CostingRemark

         })
            .ToListAsync();

        var _dataSet = new DataSet();
        _dataSet.Tables.Add(DataTools.ToDataTable(_data));

        return ExcelUtility.ExportExcel(_dataSet);

    }


    public async Task<ReportDefinition> GetMaterialListPDF(Guid projectUID, string reportSize = "a4", IEnumerable<QueryFilter> filters = null, bool AutoPageNumber = true,
                    int TotalPages = 0,
        int LastPageNumber = 0)
    {

        var projectService = new ProjectService(db);
        var project = await projectService.Get()
                                           .Where(x => x.UID == projectUID)
                                           .SingleOrDefaultAsync();

        if (project == null) throw new EntityServiceException("Project not found!");

        var projectEntities = await Get()
                .Where(x => x.ProjectID == project.ID)
                         .Include(x => x.DataCardMaps)
                //.Include(x=>x.Quantities.Select(q=>q.Measurements))
                .ToListAsync();

        var filteredZones = projectEntities
                .Where(x => x.ParentID == null && x.TypeFlag == McvConstant.DESIGN_SCRIPT_ENTITY_TYPEFLAG_ZONE)
                .ToList();


        var filteredCategories = await db.DesignScriptDataCards.AsNoTracking()
                                            
                                            .Where(x => !x.IsHidden)
                                            .Where(x => x.Category != null && x.Category != string.Empty)
                                            .Where(x => x.TypeFlag == McvConstant.DESIGN_SCRIPT_DATA_CARD_TYPEFLAG_MATERIAL)
                                            .Select(x => x.Category)
                                            .Distinct()
                                            .ToListAsync();

        if (filters != null)
        {

            if (filters.Where(x => x.Key.Equals("zone", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var _zoneUIDs = filters.Where(x => x.Key.Equals("zone", StringComparison.OrdinalIgnoreCase)).Select(x => Guid.Parse(x.Value.Trim()));

                filteredZones = filteredZones.Where(x => _zoneUIDs.Any(z => z == x.UID)).ToList();
            }

            if (filters.Where(x => x.Key.Equals("category", StringComparison.OrdinalIgnoreCase)).Any())
            {
                filteredCategories = filters.Where(x => x.Key.Equals("category", StringComparison.OrdinalIgnoreCase)).Select(x => x.Value.Trim()).ToList();
            }


        }

        var _entityIDs = filteredZones.Select(x => x.ID).ToList();
        var _designScriptEntityIds = _entityIDs.ToList();
        while (_entityIDs.Any())
        {
            _entityIDs = await GetRecursiveChildrenIDsAsync(_entityIDs, projectEntities);
            _designScriptEntityIds = _designScriptEntityIds.Concat(_entityIDs).ToList();
        }

        projectEntities = projectEntities.Where(x => _designScriptEntityIds.Any(e => e == x.ID)).ToList();

        var _dataCards = await db.DesignScriptDataCards.AsNoTracking()
            
            .Where(x => !x.IsHidden)
                       .Include(x => x.Attributes)
                       .Include(x => x.Attachments)
                       .Include(x => x.Maps).ThenInclude(d => d.Entity)
                                  .Where(x => x.TypeFlag == McvConstant.DESIGN_SCRIPT_DATA_CARD_TYPEFLAG_MATERIAL)
                                  .Where(x => x.Attachments.Where(a => !a.IsDeleted).Any())
                                  .Where(x => x.ProjectID == project.ID)
                                  .Where(x => x.Category != null && x.Category != string.Empty)
                                    .Where(x => x.Title != null && x.Title != string.Empty)
                                    .Where(x => filteredCategories.Any(c => c == x.Category))
                  .Where(x => x.Maps.Where(d => !d.IsDeleted).Any())
                  //.Where(x => x.Maps.Any(map => _designScriptEntityIds.Any(e => e == map.DesignScriptEntityID)))
                  .ToListAsync();

        _dataCards = _dataCards.Where(x => x.Maps.Any(map => _designScriptEntityIds.Any(e => e == map.DesignScriptEntityID))).ToList();

        var sharedService = new SharedService(db);
        var originalHostName = await sharedService.GetPresetValue(McvConstant.AZURE_STORAGE_BLOB_ORIGINAL_HOSTNAME);
        var cdnHostName = await sharedService.GetPresetValue(McvConstant.AZURE_STORAGE_BLOB_CDN_HOSTNAME);

        var distinctDataCards = new List<ReportMaterialListDataCard>();
        var dataCardAttributes = new List<ReportDataCardAttribute>();
        foreach (var dataCard in _dataCards)
        {
            if (!distinctDataCards.Any(x => x.ID == dataCard.ID))
            {
                var card = new ReportMaterialListDataCard
                {
                    ID = dataCard.ID,
                    Title = dataCard.Title,
                    Subtitle = dataCard.Subtitle,
                    Description = dataCard.Description,
                    Category = dataCard.Category != null ? dataCard.Category : "",
                    Tags = string.Join(", ", dataCard.SearchTags),
                    OrderFlag = dataCard.OrderFlag,
                    GFCTag = dataCard.GFCTag,

                };
                distinctDataCards.Add(card);

                //Console.WriteLine($"DATACARD: {dataCard.ID} | {dataCard.Attachments.Count}");

                foreach (var attr in dataCard.Attributes.Where(a => !a.IsDeleted).Where(a => !a.IsHidden).Where(x => x.AttributeValue != null && x.AttributeValue != string.Empty))
                {
                    if (!dataCardAttributes.Any(x => x.DataCardID == dataCard.ID && x.AttributeKey == attr.AttributeKey))
                    {
                        dataCardAttributes.Add(new ReportDataCardAttribute
                        {
                            DataCardID = dataCard.ID,
                            AttributeKey = attr.AttributeKey,
                            AttributeValue = attr.AttributeValue
                        });
                    }

                    //Console.WriteLine($"Attribute: {attr.AttributeKey} | {attr.AttributeValue}");
                }


                foreach (var attr in dataCard.Attachments.Where(a => !a.IsHidden).Where(x => x.Url != null))
                {
                    if (attr.ThumbUrl != null)
                    {
                        if (!dataCardAttributes.Any(x => x.DataCardID == dataCard.ID && x.AttributeValue == attr.ThumbUrl))
                        {
                            dataCardAttributes.Add(new ReportDataCardAttribute
                            {
                                DataCardID = dataCard.ID,
                                AttributeValue = attr.ThumbUrl.Replace(cdnHostName, originalHostName),
                                AttributeKey = "ImageUrl"
                            });
                        }
                    }
                    else
                    {
                        if (!dataCardAttributes.Any(x => x.DataCardID == dataCard.ID && x.AttributeValue == attr.Url))
                        {
                            dataCardAttributes.Add(new ReportDataCardAttribute
                            {
                                DataCardID = dataCard.ID,
                                AttributeValue = attr.Url.Replace(cdnHostName, originalHostName),
                                AttributeKey = "ImageUrl"
                            });
                        }
                    }
                    //Console.WriteLine($"Image: {attr.ThumbUrl}");
                }
            }
        }


        var dataCardPerAttribute = distinctDataCards
                .GroupJoin(dataCardAttributes,
                a => a.ID,
                b => b.DataCardID,
                (a, b) => new { dataCard = a, attributes = b.DefaultIfEmpty() })
                .SelectMany(join => join.attributes.DefaultIfEmpty(),
                (t1, t2) => new
                {
                    t1.dataCard,
                    attribute = t2
                })
                // .Join(filteredZones
                //  .SelectMany(join => join.DataCardMaps,
                //    (a, b) => new
                //    {
                //        entity = a,
                //        b.DesignScriptDataCardID
                //    }),
                // a=>a.dataCard.ID,
                // b=>b.DesignScriptDataCardID,
                //(t1, t2) => new
                //{
                //    t1.dataCard,
                //    t1.attribute,
                //    zone = t2.entity
                //})
                .Select(x => new
                {
                    ID = x.dataCard.ID,
                    Title = x.dataCard.Title,
                    Subtitle = x.dataCard.Subtitle,
                    Description = x.dataCard.Description,
                    Category = x.dataCard.Category,
                    Tags = x.dataCard.Tags,
                    OrderFlag = x.dataCard.OrderFlag,
                    GFCTag = x.dataCard.GFCTag,
                    AttributeKey = x.attribute != null ? x.attribute.AttributeKey : null,
                    AttributeValue = x.attribute != null ? x.attribute.AttributeValue : null,
                    //ZoneCode=x.zone.Code,
                    //ZoneTitle=x.zone.Title
                })
                //.OrderBy(x => x.ZoneCode)
                .OrderBy(x => x.Category)
                .ThenBy(x => x.GFCTag)
                .ToList();



        var measurements = await db.DesignScriptMeasurementGroups.AsNoTracking()
          
           .Include(x => x.DesignScriptEntityItemMap)
            .Where(x => !x.DesignScriptEntityItemMap.IsDeleted)
          .Include(x => x.DesignScriptEntityItemMap.DesignScriptEntity)
            .Where(x => !x.DesignScriptEntityItemMap.DesignScriptEntity.IsDeleted)
          .Where(x => x.DesignScriptEntityItemMap.ProjectID == project.ID)
          .Where(x => x.DesignScriptDataCardID != null)
          .Select(x => new
          {
              Title = x.DesignScriptEntityItemMap.DesignScriptEntity.Title,
              Code = x.DesignScriptEntityItemMap.DesignScriptEntity.Code,
              DataCardID = x.DesignScriptDataCardID.Value,
              ParentID = x.DesignScriptEntityItemMap.DesignScriptEntity.ParentID,
              EntityID = x.DesignScriptEntityItemMap.DesignScriptEntity.ID
          })
          .ToListAsync();

        var applications = projectEntities
                            .Where(x => x.DataCardMaps.Any(m => distinctDataCards.Select(d => d.ID).Any(d => d == m.DesignScriptDataCardID)))
                            .SelectMany(join => join.DataCardMaps,
                            (a, b) => new
                            {
                                Title = a.Title,
                                Code = a.Code,
                                DataCardID = b.DesignScriptDataCardID,
                                ParentID = a.ParentID,
                                EntityID = a.ID
                            }).Concat(measurements).Distinct().ToList();

        var _scriptData = projectEntities.Where(x => x.TypeFlag == McvConstant.DESIGN_SCRIPT_ENTITY_TYPEFLAG_ZONE)
                    .Join(projectEntities, p => p.ID, s => s.ParentID, (p, s) => new { zone = p, space = s })
                    .Join(projectEntities, p => p.space.ID, s => s.ParentID, (p, e) => new { p.zone, p.space, element = e })
                   .Select(x => new
                   {
                       Zone = x.zone.Code + "-" + x.zone.Title.ToUpper(),
                       Space = x.space.Code + "-" + x.space.Title.ToUpper(),
                       Element = x.element.Code + "-" + x.element.Title.ToUpper(),
                       ElementID = x.element.ID
                   })
                   .Join(applications, a => a.ElementID, b => b.EntityID, (a, b) => new { a, b })
                   .Select(x => new
                   {
                       x.a.Zone,
                       x.a.Space,
                       x.a.Element,
                       x.b.DataCardID
                   })
                   .Distinct().ToList();



        var _reportProperties = new List<ReportProperties>
                {
                    new ReportProperties(){ PropertyName="ReportTitle" , PropertyValue="MATERIAL LIST" },
                    new ReportProperties() { PropertyName = "Title", PropertyValue = $"{project.Code}-{project.Title}" },
                                      new ReportProperties() { PropertyName = "Filter_Categories", PropertyValue = $"{string.Join(", ",filteredCategories.OrderBy(x=>x))}" },
                  new ReportProperties() { PropertyName = "Filter_Zones", PropertyValue = $"{string.Join(", ",filteredZones.OrderBy(x=>x.Code).Select(x=> x.Code+"-"+x.Title))}" },
                     new ReportProperties() { PropertyName = "AutoPageNumber", PropertyValue = $"{AutoPageNumber}" },
                     new ReportProperties() { PropertyName = "TotalPages", PropertyValue = $"{TotalPages}" },
                        new ReportProperties() { PropertyName = "LastPageNumber", PropertyValue = $"{LastPageNumber}" },
                };

        var _reportContainerUrl = await sharedService.GetPresetValue(McvConstant.RDLC_REPORT_CONTAINER_URL);
        var DEVMODE = Convert.ToBoolean((await sharedService.GetPresetValue(McvConstant.DEVMODE)));

        var _reportPath = $"MaterialList-{reportSize}.rdlc";

        var _reportDef = new ReportDefinition()
        {
            ReportName = "Material + Product List",
            ReportPath = DEVMODE ? $"{_reportContainerUrl}DEV/{_reportPath}" : $"{_reportContainerUrl}{_reportPath}",
            ReportDataSet = DataTools.ToDataTable(dataCardPerAttribute),
            ReportProperties = _reportProperties,
            Filename = $"MaterialProductList-{project.Code}-{project.Title}-{ClockTools.GetISTNow().ToString("yyMMddHHmm")}",
            RenderType = "PDF"
        };

        _reportDef.SubReports.Add(
        new ReportDefinition()
        {
            ReportName = $"MaterialListApplications",
            ReportPath = DEVMODE ? $"{_reportContainerUrl}DEV/MaterialListApplications.rdlc" : $"{_reportContainerUrl}MaterialListApplications.rdlc",
            ReportDataSet = DataTools.ToDataTable(_scriptData),
        });
        var reportServiceApi = await sharedService.GetPresetValue(McvConstant.RDLC_PROCESSOR_API);
        return await ReportClient.GenerateReport(_reportDef, reportServiceApi);

    }

    public async Task<ReportDefinition> GetPlantList(Guid projectUID, string reportSize = "a4", IEnumerable<QueryFilter> filters = null, string renderType = "PDF", bool AutoPageNumber = true,
                      int TotalPages = 0,
          int LastPageNumber = 0)
    {

        // Validate input project
        var project = await db.Projects.AsNoTracking()
            
            .Where(p => p.UID == projectUID)
            .SingleOrDefaultAsync();

        if (project == null)
            throw new EntityServiceException("Project not found!");

        // Fetch project entities with optimization
        var projectEntities = await Get()
            .Where(x => x.ProjectID == project.ID)
            .ToListAsync();

        // Filter zones
        var filteredZones = projectEntities
            .Where(x => x.ParentID == null && x.TypeFlag == McvConstant.DESIGN_SCRIPT_ENTITY_TYPEFLAG_ZONE)
            .ToList();

        // Apply zone filter if specified
        if (filters != null && filters.Any(x => x.Key.Equals("zone", StringComparison.OrdinalIgnoreCase)))
        {
            var zoneUIDs = filters
                .Where(x => x.Key.Equals("zone", StringComparison.OrdinalIgnoreCase))
                .Select(x => Guid.Parse(x.Value.Trim()));

            filteredZones = filteredZones
                .Where(x => zoneUIDs.Contains(x.UID))
                .ToList();
        }

        // Recursive child entity retrieval with optimization
        var designScriptEntityIds = GetRecursiveChildrenIDs(filteredZones.Select(x => x.ID).ToList(), projectEntities);

        // Fetch data cards with efficient filtering
        var dataCardQuery = db.DesignScriptDataCards.AsNoTracking()
            .Where(x => !x.IsDeleted && !x.IsHidden)
            .Where(x => x.Category == "Planting")
            .Where(x => x.TypeFlag == McvConstant.DESIGN_SCRIPT_DATA_CARD_TYPEFLAG_MATERIAL)
            .Where(x => x.Attachments.Any(a => !a.IsDeleted))
            .Where(x => x.ProjectID == project.ID)
.Where(x => x.Category != null && x.Category != "") // Replace IsNullOrWhiteSpace
    .Where(x => x.Title != null && x.Title != "") // Replace IsNullOrWhiteSpace
            .Include(x => x.Attributes)
            .Include(x => x.Attachments)
            .Include(x => x.Maps).ThenInclude(d => d.Entity)
            .Where(x => x.Maps.Any(map => designScriptEntityIds.Contains(map.DesignScriptEntityID)));

        var dataCards = await dataCardQuery.ToListAsync();

        // Prepare hostnames for image URL processing
        var sharedService = new SharedService(db);
        var originalHostName = await sharedService.GetPresetValue(McvConstant.AZURE_STORAGE_BLOB_ORIGINAL_HOSTNAME);
        var cdnHostName = await sharedService.GetPresetValue(McvConstant.AZURE_STORAGE_BLOB_CDN_HOSTNAME);

        // Process and distinct data cards
        var distinctDataCards = dataCards
            .GroupBy(d => d.ID)
            .Select(g => new ReportPlantListDataCard
            {
                ID = g.Key,
                Title = g.First().Title,
                Subtitle = g.First().Subtitle,
                Description = g.First().Description,
                Category = g.First().Category ?? string.Empty,
                Tags = string.Join(", ", g.First().SearchTags),
                OrderFlag = g.First().OrderFlag,
                GFCTag = g.First().GFCTag,
                WaterRequirement = g.First().Attributes
                    .FirstOrDefault(a => !a.IsDeleted && a.AttributeKey == "Water Requirement Min")?.AttributeValue,
                ImageUrl = g.First().Attachments
                    .Where(x => !x.IsDeleted && !x.IsHidden && x.ThumbUrl != null)
                    .Select(x => x.ThumbUrl.Replace(cdnHostName, originalHostName))
                    .FirstOrDefault()
            })
            .ToList();

        var _reportProperties = new List<ReportProperties>
                    {
                        new ReportProperties(){ PropertyName="ReportTitle" , PropertyValue="PLANT LIST" },
                        new ReportProperties() { PropertyName = "Title", PropertyValue = $"{project.Code}-{project.Title}" },
                                          new ReportProperties() { PropertyName = "Filter_Categories", PropertyValue = $"  " },
                      new ReportProperties() { PropertyName = "Filter_Zones", PropertyValue = $"{string.Join(", ",filteredZones.OrderBy(x=>x.Code).Select(x=> x.Code+"-"+x.Title))}" },
                         new ReportProperties() { PropertyName = "AutoPageNumber", PropertyValue = $"{AutoPageNumber}" },
                         new ReportProperties() { PropertyName = "TotalPages", PropertyValue = $"{TotalPages}" },
                            new ReportProperties() { PropertyName = "LastPageNumber", PropertyValue = $"{LastPageNumber}" },
                    };

        var _reportContainerUrl = await sharedService.GetPresetValue(McvConstant.RDLC_REPORT_CONTAINER_URL);
        var DEVMODE = Convert.ToBoolean((await sharedService.GetPresetValue(McvConstant.DEVMODE)));

        var _reportPath = $"PlantList-{reportSize}.rdlc";

        var _reportDef = new ReportDefinition()
        {
            ReportName = "Plant List",
            ReportPath = DEVMODE ? $"{_reportContainerUrl}DEV/{_reportPath}" : $"{_reportContainerUrl}{_reportPath}",
            ReportDataSet = DataTools.ToDataTable(distinctDataCards),
            ReportProperties = _reportProperties,
            Filename = $"Plant List-{project.Code}-{project.Title}-{ClockTools.GetISTNow().ToString("yyMMddHHmm")}",
            RenderType = renderType
        };

        var reportServiceApi = await sharedService.GetPresetValue(McvConstant.RDLC_PROCESSOR_API);
        return await ReportClient.GenerateReport(_reportDef, reportServiceApi);

    }

    // Helper method for recursive child ID retrieval
    private List<int> GetRecursiveChildrenIDs(List<int> initialEntityIDs, List<DesignScriptEntity> projectEntities)
    {
        var allEntityIDs = new HashSet<int>(initialEntityIDs);
        var currentLevelIDs = new HashSet<int>(initialEntityIDs);

        while (currentLevelIDs.Any())
        {
            var childIDs = projectEntities
                .Where(x => currentLevelIDs.Contains(x.ParentID ?? -1))
                .Select(x => x.ID)
                .ToList();

            currentLevelIDs = new HashSet<int>(childIDs);
            allEntityIDs.UnionWith(currentLevelIDs);
        }

        return allEntityIDs.ToList();
    }

    private class ReportPlantListDataCard
    {
        public int ID { get; set; }
        public string? Category { get; set; }
        public string? Title { get; set; }
        public string? Subtitle { get; set; }
        public string? Description { get; set; }
        public string? Tags { get; set; }
        public string? GFCTag { get; set; }
        public int OrderFlag { get; set; }

        public string? ImageUrl { get; set; }
        public string? WaterRequirement { get; set; }
    }


    public async Task<ReportDefinition> GetSchedule(Guid projectUID, string reportSize = "a4", IEnumerable<QueryFilter> filters = null, bool AutoPageNumber = true,
                  int TotalPages = 0,
      int LastPageNumber = 0, string reportType = "PDF")
    {

        var projectService = new ProjectService(db);
        var project = await projectService.Get()
                                           .Where(x => x.UID == projectUID)
                                           .SingleOrDefaultAsync();

        if (project == null) throw new EntityServiceException("Project not found!");

        var entities = await Get()
                .Where(x => x.ProjectID == project.ID)
                         .Include(x => x.DataCardMaps)
                //.Include(x=>x.Quantities.Select(q=>q.Measurements))
                .ToListAsync();

        var filteredZones = entities
                .Where(x => x.ParentID == null && x.TypeFlag == McvConstant.DESIGN_SCRIPT_ENTITY_TYPEFLAG_ZONE)
                .ToList();


        var filteredCategories = await db.DesignScriptDataCards.AsNoTracking()
                                            
                                            .Where(x => !x.IsHidden)
                                            .Where(x => x.Category != null && x.Category != string.Empty)
                                            .Where(x => x.TypeFlag == McvConstant.DESIGN_SCRIPT_DATA_CARD_TYPEFLAG_MATERIAL)
                                            .Select(x => x.Category)
                                            .Distinct()
                                            .ToListAsync();

        if (filters != null)
        {

            if (filters.Where(x => x.Key.Equals("zone", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var _zoneUIDs = filters.Where(x => x.Key.Equals("zone", StringComparison.OrdinalIgnoreCase)).Select(x => Guid.Parse(x.Value.Trim()));

                filteredZones = filteredZones.Where(x => _zoneUIDs.Any(z => z == x.UID)).ToList();
            }

            if (filters.Where(x => x.Key.Equals("category", StringComparison.OrdinalIgnoreCase)).Any())
            {
                filteredCategories = filters.Where(x => x.Key.Equals("category", StringComparison.OrdinalIgnoreCase)).Select(x => x.Value.Trim()).ToList();
            }


        }

        var _entityIDs = filteredZones.Select(x => x.ID);
        var _designScriptEntityIds = _entityIDs.ToList();
        while (_entityIDs.Any())
        {
            _entityIDs = await GetRecursiveChildrenIDsAsync(_entityIDs);
            _designScriptEntityIds = _designScriptEntityIds.Concat(_entityIDs).ToList();
        }

        var _dataCards = await db.DesignScriptDataCards.AsNoTracking()
            
            .Where(x => !x.IsHidden)
                       .Include(x => x.Attributes)
                       .Include(x => x.Attachments)
                       .Include(x => x.Maps).ThenInclude(d => d.Entity)
                                  .Where(x => x.TypeFlag == McvConstant.DESIGN_SCRIPT_DATA_CARD_TYPEFLAG_MATERIAL)
                                  .Where(x => x.Attachments.Where(a => !a.IsDeleted).Any())
                                  .Where(x => x.ProjectID == project.ID)
                                  .Where(x => x.Category != null && x.Category != string.Empty)
                                    .Where(x => x.Title != null && x.Title != string.Empty)
                                    .Where(x => filteredCategories.Any(c => c == x.Category))
                  .Where(x => x.Maps.Where(d => !d.IsDeleted).Any())

                  .ToListAsync();

        var sharedService = new SharedService(db);
        var originalHostName = await sharedService.GetPresetValue(McvConstant.AZURE_STORAGE_BLOB_ORIGINAL_HOSTNAME);
        var cdnHostName = await sharedService.GetPresetValue(McvConstant.AZURE_STORAGE_BLOB_CDN_HOSTNAME);

        var dataCards = new List<ReportMaterialListDataCard>();
        var dataCardAttributes = new List<ReportDataCardAttribute>();
        foreach (var dataCard in _dataCards)
        {
            if (!dataCards.Any(x => x.ID == dataCard.ID))
            {
                dataCards.Add(new ReportMaterialListDataCard
                {
                    ID = dataCard.ID,
                    Title = dataCard.Title,
                    Subtitle = dataCard.Subtitle,
                    Description = dataCard.Description,
                    Category = dataCard.Category != null ? dataCard.Category : "",
                    Tags = string.Join(", ", dataCard.SearchTags),
                    OrderFlag = dataCard.OrderFlag,
                    GFCTag = dataCard.GFCTag,

                });

                //Console.WriteLine($"DATACARD: {dataCard.ID} | {dataCard.Attachments.Count}");

                foreach (var attr in dataCard.Attributes.Where(a => !a.IsDeleted).Where(a => !a.IsHidden).Where(x => x.AttributeValue != null && x.AttributeValue != string.Empty))
                {
                    if (!dataCardAttributes.Any(x => x.DataCardID == dataCard.ID && x.AttributeKey == attr.AttributeKey))
                    {
                        dataCardAttributes.Add(new ReportDataCardAttribute
                        {
                            DataCardID = dataCard.ID,
                            AttributeKey = attr.AttributeKey,
                            AttributeValue = attr.AttributeValue
                        });
                    }

                    //Console.WriteLine($"Attribute: {attr.AttributeKey} | {attr.AttributeValue}");
                }


                foreach (var attr in dataCard.Attachments.Where(a => !a.IsHidden).Where(x => x.Url != null))
                {
                    if (attr.ThumbUrl != null)
                    {
                        if (!dataCardAttributes.Any(x => x.DataCardID == dataCard.ID && x.AttributeValue == attr.ThumbUrl))
                        {
                            dataCardAttributes.Add(new ReportDataCardAttribute
                            {
                                DataCardID = dataCard.ID,
                                AttributeValue = attr.ThumbUrl.Replace(cdnHostName, originalHostName),
                                AttributeKey = "ImageUrl"
                            });
                        }
                    }
                    else
                    {
                        if (!dataCardAttributes.Any(x => x.DataCardID == dataCard.ID && x.AttributeValue == attr.Url))
                        {
                            dataCardAttributes.Add(new ReportDataCardAttribute
                            {
                                DataCardID = dataCard.ID,
                                AttributeValue = attr.Url.Replace(cdnHostName, originalHostName),
                                AttributeKey = "ImageUrl"
                            });
                        }
                    }
                    //Console.WriteLine($"Image: {attr.ThumbUrl}");
                }
            }
        }


        var reportData = dataCards
                .GroupJoin(dataCardAttributes,
                a => a.ID,
                b => b.DataCardID,
                (a, b) => new { dataCard = a, attributes = b.DefaultIfEmpty() })
                .SelectMany(join => join.attributes.DefaultIfEmpty(),
            (t1, t2) => new
            {
                t1.dataCard,
                attribute = t2
            })
                .Join(filteredZones
                 .SelectMany(join => join.DataCardMaps,
                   (a, b) => new
                   {
                       entity = a,
                       b.DesignScriptDataCardID
                   }),
                a => a.dataCard.ID,
                b => b.DesignScriptDataCardID,
               (t1, t2) => new
               {
                   t1.dataCard,
                   t1.attribute,
                   zone = t2.entity
               })
                .Select(x => new
                {
                    ID = x.dataCard.ID,
                    Title = x.dataCard.Title,
                    Subtitle = x.dataCard.Subtitle,
                    Description = x.dataCard.Description,
                    Category = x.dataCard.Category,
                    Tags = x.dataCard.Tags,
                    OrderFlag = x.dataCard.OrderFlag,
                    GFCTag = x.dataCard.GFCTag,
                    AttributeKey = x.attribute != null ? x.attribute.AttributeKey : null,
                    AttributeValue = x.attribute != null ? x.attribute.AttributeValue : null,
                    ZoneCode = x.zone.Code,
                    ZoneTitle = x.zone.Title
                }).OrderBy(x => x.ZoneCode).ThenBy(x => x.Category).ThenBy(x => x.GFCTag);


        var _reportProperties = new List<ReportProperties>
                {
                    new ReportProperties() { PropertyName = "Title", PropertyValue = $"{project.Code}-{project.Title}" },
                                      new ReportProperties() { PropertyName = "Filter_Categories", PropertyValue = $"{string.Join(", ",filteredCategories)}" },
                  new ReportProperties() { PropertyName = "Filter_Zones", PropertyValue = $"{string.Join(", ",filteredZones.Select(x=> x.Code+"-"+x.Title))}" },
                     new ReportProperties() { PropertyName = "AutoPageNumber", PropertyValue = $"{AutoPageNumber}" },
                     new ReportProperties() { PropertyName = "TotalPages", PropertyValue = $"{TotalPages}" },
                        new ReportProperties() { PropertyName = "LastPageNumber", PropertyValue = $"{LastPageNumber}" },
                };

        var _reportContainerUrl = await sharedService.GetPresetValue(McvConstant.RDLC_REPORT_CONTAINER_URL);
        var DEVMODE = Convert.ToBoolean((await sharedService.GetPresetValue(McvConstant.DEVMODE)));

        var _reportPath = $"Schedule.rdlc";

        var _reportDef = new ReportDefinition()
        {
            ReportName = "Schedule",
            ReportPath = DEVMODE ? $"{_reportContainerUrl}DEV/{_reportPath}" : $"{_reportContainerUrl}{_reportPath}",
            ReportDataSet = DataTools.ToDataTable(reportData),
            ReportProperties = _reportProperties,
            Filename = $"Schedule-{project.Code}-{project.Title}-{ClockTools.GetISTNow().ToString("yyMMddHHmm")}",
            RenderType = reportType
        };

        var reportServiceApi = await sharedService.GetPresetValue(McvConstant.RDLC_PROCESSOR_API);
        return await ReportClient.GenerateReport(_reportDef, reportServiceApi);

    }

    public async Task<ReportDefinition> GetPalettePDF(Guid projectUID, string reportSize = "a3", IEnumerable<QueryFilter> filters = null, bool AutoPageNumber = true,
                    int TotalPages = 0,
        int LastPageNumber = 0)
    {

        var projectService = new ProjectService(db);
        var project = await projectService.Get()
                                           .Where(x => x.UID == projectUID)
                                           .SingleOrDefaultAsync();

        if (project == null) throw new EntityServiceException("Project not found!");

        var filteredZones = await Get()
            .Where(x => x.ProjectID == project.ID)
            .Where(x => x.ParentID == null && x.TypeFlag == McvConstant.DESIGN_SCRIPT_ENTITY_TYPEFLAG_ZONE)
            .Include(x => x.DataCardMaps)
            .ToListAsync();

        var filteredCategories = await db.DesignScriptDataCards.AsNoTracking()
                                        .Where(x => !x.IsHidden)
                                        .Where(x => x.Category != null && x.Category != string.Empty)
                                        .Where(x => x.TypeFlag == McvConstant.DESIGN_SCRIPT_DATA_CARD_TYPEFLAG_MATERIAL)
                                        .Select(x => x.Category)
                                        .Distinct()
                                        .ToListAsync();


        if (filters != null)
        {

            if (filters.Where(x => x.Key.Equals("zone", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var _zoneUIDs = filters.Where(x => x.Key.Equals("zone", StringComparison.OrdinalIgnoreCase)).Select(x => Guid.Parse(x.Value.Trim()));

                filteredZones = filteredZones.Where(x => _zoneUIDs.Any(z => z == x.UID)).ToList();
            }

            if (filters.Where(x => x.Key.Equals("category", StringComparison.OrdinalIgnoreCase)).Any())
            {
                filteredCategories = filters.Where(x => x.Key.Equals("category", StringComparison.OrdinalIgnoreCase)).Select(x => x.Value.Trim()).ToList();
            }


        }

        var _entityIDs = filteredZones.Select(x => x.ID);
        var _designScriptEntityIds = _entityIDs.ToList();
        while (_entityIDs.Any())
        {
            _entityIDs = await GetRecursiveChildrenIDsAsync(_entityIDs);
            _designScriptEntityIds = _designScriptEntityIds.Concat(_entityIDs).ToList();
        }


        var _dataCards = await db.DesignScriptDataCards.AsNoTracking()
            
            .Where(x => !x.IsHidden)
                       .Include(x => x.Attributes)
                       .Include(x => x.Attachments)
                       .Include(x => x.Maps)
                                  .Where(x => x.TypeFlag == McvConstant.DESIGN_SCRIPT_DATA_CARD_TYPEFLAG_MATERIAL)
                                  .Where(x => x.Attachments.Where(a => !a.IsDeleted).Any())
                                  .Where(x => x.ProjectID == project.ID)
                                  .Where(x => x.Category != null && x.Category != string.Empty)
                                    .Where(x => x.Title != null && x.Title != string.Empty)
                                    .Where(x => filteredCategories.Any(c => c == x.Category))
                  .Where(x => x.Maps.Where(d => !d.IsDeleted).Any())
                  //   .Where(x => x.Maps.Where(d => !d.IsDeleted).Any(d => _filterIDs.Any(c => c == d.DesignScriptEntityID)))
                  .ToListAsync();

        var dataCards = new List<ReportMaterialListDataCard>();
        var dataCardAttributes = new List<ReportDataCardAttribute>();

        var sharedService = new SharedService(db);
        var originalHostName = await sharedService.GetPresetValue(McvConstant.AZURE_STORAGE_BLOB_ORIGINAL_HOSTNAME);
        var cdnHostName = await sharedService.GetPresetValue(McvConstant.AZURE_STORAGE_BLOB_CDN_HOSTNAME);

        foreach (var dataCard in _dataCards)
        {
            if (!dataCards.Any(x => x.ID == dataCard.ID))
            {
                var card = new ReportMaterialListDataCard
                {
                    ID = dataCard.ID,
                    Title = dataCard.Title,
                    Subtitle = dataCard.Subtitle,
                    Description = dataCard.Description,
                    Category = dataCard.Category != null ? dataCard.Category : "",
                    Tags = string.Join(", ", dataCard.SearchTags),
                    OrderFlag = dataCard.OrderFlag,
                    GFCTag = dataCard.GFCTag,

                };
                dataCards.Add(card);




                foreach (var attr in dataCard.Attachments.Where(a => !a.IsHidden).Where(x => x.Url != null))
                {
                    if (attr.ThumbUrl != null)
                    {
                        if (!dataCardAttributes.Any(x => x.DataCardID == dataCard.ID && x.AttributeValue == attr.ThumbUrl))
                        {
                            dataCardAttributes.Add(new ReportDataCardAttribute
                            {
                                DataCardID = dataCard.ID,
                                AttributeValue = attr.ThumbUrl.Replace(cdnHostName, originalHostName),
                                AttributeKey = "ImageUrl"
                            });
                        }
                    }
                    else
                    {
                        if (!dataCardAttributes.Any(x => x.DataCardID == dataCard.ID && x.AttributeValue == attr.Url))
                        {
                            dataCardAttributes.Add(new ReportDataCardAttribute
                            {
                                DataCardID = dataCard.ID,
                                AttributeValue = attr.Url.Replace(cdnHostName, originalHostName),
                                AttributeKey = "ImageUrl"
                            });
                        }
                    }
                    //Console.WriteLine($"Image: {attr.ThumbUrl}");
                }

            }
        }


        var reportData = dataCards
                .GroupJoin(dataCardAttributes,
                a => a.ID,
                b => b.DataCardID,
                (a, b) => new { dataCard = a, attributes = b.DefaultIfEmpty() })
                .SelectMany(join => join.attributes.DefaultIfEmpty(),
            (t1, t2) => new
            {
                t1.dataCard,
                attribute = t2
            })
                .Join(filteredZones
                 .SelectMany(join => join.DataCardMaps,
                   (a, b) => new
                   {
                       entity = a,
                       b.DesignScriptDataCardID
                   }),
                a => a.dataCard.ID,
                b => b.DesignScriptDataCardID,
               (t1, t2) => new
               {
                   t1.dataCard,
                   t1.attribute,
                   zone = t2.entity
               })
                .Select(x => new
                {
                    ID = x.dataCard.ID,
                    Title = x.dataCard.Title,
                    Subtitle = x.dataCard.Subtitle,
                    Description = x.dataCard.Description,
                    Category = x.dataCard.Category,
                    Tags = x.dataCard.Tags,
                    OrderFlag = x.dataCard.OrderFlag,
                    GFCTag = x.dataCard.GFCTag,
                    AttributeKey = x.attribute != null ? x.attribute.AttributeKey : null,
                    AttributeValue = x.attribute != null ? x.attribute.AttributeValue : null,
                    ZoneCode = x.zone.Code,
                    ZoneTitle = x.zone.Title
                }).OrderBy(x => x.ZoneCode).ThenBy(x => x.Category).ThenBy(x => x.GFCTag);

        var _reportProperties = new List<ReportProperties>
            {
                 new ReportProperties() { PropertyName="ReportTitle", PropertyValue="PALETTE" },
                new ReportProperties() { PropertyName = "Title", PropertyValue = $"{project.Code}-{project.Title}" },
                new ReportProperties() { PropertyName = "Filter_Categories", PropertyValue = $"{string.Join(", ",filteredCategories)}" },
                  new ReportProperties() { PropertyName = "Filter_Zones", PropertyValue = $"{string.Join(", ",filteredZones.Select(x=> x.Code+"-"+x.Title))}" },
                     new ReportProperties() { PropertyName = "AutoPageNumber", PropertyValue = $"{AutoPageNumber}" },
                     new ReportProperties() { PropertyName = "TotalPages", PropertyValue = $"{TotalPages}" },
                        new ReportProperties() { PropertyName = "LastPageNumber", PropertyValue = $"{LastPageNumber}" },
            };

        var _reportContainerUrl = await sharedService.GetPresetValue(McvConstant.RDLC_REPORT_CONTAINER_URL);
        var DEVMODE = Convert.ToBoolean((await sharedService.GetPresetValue(McvConstant.DEVMODE)));

        var _reportPath = $"DesignScript-Palette-{reportSize}.rdlc";

        var _reportDef = new ReportDefinition()
        {
            ReportName = "DesignScript-Palette",
            ReportPath = DEVMODE ? $"{_reportContainerUrl}DEV/{_reportPath}" : $"{_reportContainerUrl}{_reportPath}",
            ReportDataSet = DataTools.ToDataTable(reportData),
            ReportProperties = _reportProperties,
            Filename = $"Palette-{project.Code}-{project.Title}-{ClockTools.GetISTNow().ToString("yyMMddHHmm")}",
            RenderType = "PDF"
        };

        var reportServiceApi = await sharedService.GetPresetValue(McvConstant.RDLC_PROCESSOR_API);
        return await ReportClient.GenerateReport(_reportDef, reportServiceApi);

    }

    public async Task<ReportDefinition> GetDesignIntentPDF(Guid projectUID, string reportSize = "a4", IEnumerable<QueryFilter> filters = null
       )
    {
        var projectService = new ProjectService(db);
        var project = await projectService.Get()
               .Where(x => x.UID == projectUID)
               .SingleOrDefaultAsync();

        if (project == null) throw new EntityServiceException("Project not found!");

        IEnumerable<Guid> _designScriptEntityIDs = null;
        IEnumerable<string> _categories = null;
        var showAttributes = false;

        if (filters != null)
        {
            if (filters.Where(x => x.Key.Equals("category", StringComparison.OrdinalIgnoreCase)).Any())
            {
                _categories = filters.Where(x => x.Key.Equals("category", StringComparison.OrdinalIgnoreCase)).Select(x => x.Value.Trim());
            }


            if (filters.Where(x => x.Key.Equals("showAttributes", StringComparison.OrdinalIgnoreCase)).Any())
            {
                showAttributes = filters.Where(x => x.Key.Equals("showAttributes", StringComparison.OrdinalIgnoreCase)).Select(x => Convert.ToBoolean(x.Value.Trim())).FirstOrDefault();
            }

            if (filters.Where(x => x.Key.Equals("phase", StringComparison.OrdinalIgnoreCase)).Any())
            {
                _designScriptEntityIDs = filters.Where(x => x.Key.Equals("phase", StringComparison.OrdinalIgnoreCase)).Select(x => Guid.Parse(x.Value.Trim()));
            }
        }

        if (_designScriptEntityIDs == null)
        {
            _designScriptEntityIDs = await Get()
            .Where(x => x.ProjectID == project.ID)
            .Where(x => x.ParentID == null && x.TypeFlag == McvConstant.DESIGN_SCRIPT_ENTITY_TYPEFLAG_ZONE)
            .Select(x => x.UID)
            .ToListAsync();
        }

        var _filterIDs = new List<int>();
        if (_designScriptEntityIDs != null)
        {
            var _entityIDs = await Get()
                .Where(x => _designScriptEntityIDs.Any(i => i == x.UID))
                .Select(x => x.ID)
                .ToListAsync();

            _filterIDs = _entityIDs.ToList();
            while (_entityIDs.Count > 0)
            {
                _entityIDs = await GetRecursiveChildrenIDsAsync(_entityIDs);
                _filterIDs = _filterIDs.Concat(_entityIDs).ToList();
            }

        }

        var designScriptEntities = await Get()
                                    .Where(x => x.ProjectID == project.ID)
                                .Where(x => _filterIDs.Any(c => c == x.ID))
                                .GroupJoin(
            db.TypeMasters.Where(t => t.Entity == nameof(DesignScriptEntity)),
            e => e.TypeFlag,
            t => t.Value,
            (e, t) => new { e, TypeValue = t.DefaultIfEmpty() }
                                )
                                .Select(x => new DesignIntentReportData
                                {
                                    ID = x.e.ID,
                                    Code = x.e.Code,
                                    Title = x.e.Title,
                                    Description = x.e.Description,
                                    OrderFlag = x.e.OrderFlag,
                                    TypeFlag = x.e.TypeFlag,
                                    ParentID = x.e.ParentID != null ? x.e.ParentID : 0,
                                    TypeValue = x.TypeValue.Any() ? x.TypeValue.FirstOrDefault().Title : "UNDEFINED",
                                })
                                .ToListAsync();
        return await GetDesignIntentPDF(reportSize, project, showAttributes, designScriptEntities);

    }

    public async Task<ReportDefinition> GetDesignIntentPDF(string reportSize, Project project, bool showAttributes, List<DesignIntentReportData> data)
    {
        var _reportProperties = new List<ReportProperties>
            {
                new ReportProperties() { PropertyName = "Title", PropertyValue = $"{project.Code}-{project.Title}" },
                new ReportProperties() { PropertyName = "ShowAttributes", PropertyValue = $"{showAttributes}" }
            };
        var sharedService = new SharedService(db); ;
        var _reportContainerUrl = await sharedService.GetPresetValue(McvConstant.RDLC_REPORT_CONTAINER_URL);
        var DEVMODE = Convert.ToBoolean((await sharedService.GetPresetValue(McvConstant.DEVMODE)));

        var _reportPath = $"DesignIntent-{reportSize}.rdlc";

        var _reportDef = new ReportDefinition()
        {
            ReportName = "DesignIntent",
            ReportPath = DEVMODE ? $"{_reportContainerUrl}DEV/{_reportPath}" : $"{_reportContainerUrl}{_reportPath}",
            ReportDataSet = DataTools.ToDataTable<DesignIntentReportData>(data),//_dataSet.Tables[0],
            ReportProperties = _reportProperties,
            Filename = $"DesignIntent {project.Code}-{project.Title} {ClockTools.GetISTNow().ToString("yyMMddHHmm")}",
            RenderType = "PDF"
        };

        var reportServiceApi = await sharedService.GetPresetValue(McvConstant.RDLC_PROCESSOR_API);
        return await ReportClient.GenerateReport(_reportDef, reportServiceApi);
    }

    public async Task<List<int>> GetRecursiveChildrenIDsAsync(IEnumerable<int> _entityIDs, IEnumerable<DesignScriptEntity> SourceList = null)
    {
        if (SourceList != null)
        {
            return SourceList.Where(x => _entityIDs.Any(i => i == x.ParentID))
                                 .Select(x => x.ID).ToList();
        }
        return await Get()
                                .Where(x => _entityIDs.Any(i => i == x.ParentID))
                                .Select(x => x.ID)
                                .ToListAsync();
    }

    public List<int> GetRecursiveChildrenIDs(IEnumerable<DesignScriptEntity> entities, IEnumerable<int> _entityIDs)
    {
        return entities
                                .Where(x => _entityIDs.Any(i => i == x.ParentID))
                                .Select(x => x.ID)
                                .ToList();
    }

    public async Task SetMasterPhase(int ProjectID)
    {

        var _exist = await Get()
                        .AnyAsync(p => p.ProjectID == ProjectID
                            && p.TypeFlag == 0
                                        && p.CodeFlag == 0
                                        && p.Title == "MASTER");
        if (!_exist)
        {
            var projectService = new ProjectService(db);
            var _project = await projectService.Get().SingleOrDefaultAsync(x => x.ID == ProjectID);
            if (_project == null) throw new EntityServiceException("Project not found!");

            var _master = new DesignScriptEntity()
            {
                ProjectID = ProjectID,
                Title = "MASTER",
                //Phase = "PHASE 0",
                CodeFlag = 0,
                ProjectCode = _project.Code,
                Code = "00",
                TypeFlag = 0,
                isMasterPhase = true
            };
            db.DesignScriptEntities.Add(_master);
            await db.SaveChangesAsync();
        }

        return;

    }


    public async Task<ReportDefinition> GetItemList(Guid projectUID, string reportSize = "a4", IEnumerable<QueryFilter>? filters = null, string renderType = "PDF", bool AutoPageNumber = true,
                    int TotalPages = 0,
        int LastPageNumber = 0, bool showDWGSpec = false)
    {

        var projectService = new ProjectService(db);

        var project = await projectService.Get()
                                           .Where(x => x.UID == projectUID)
                                           .SingleOrDefaultAsync();

        if (project == null) throw new EntityServiceException("Project not found!");

        var projectEntities = await Get()
               .Where(x => x.ProjectID == project.ID)
               .ToListAsync();

        var filteredZones = projectEntities
                .Where(x => x.ParentID == null && x.TypeFlag == McvConstant.DESIGN_SCRIPT_ENTITY_TYPEFLAG_ZONE)
                .ToList();



        var filteredCategories = await db.DesignScriptDataCards.AsNoTracking()
            
                                            .Where(x => !x.IsHidden)
                                            .Where(x => x.Category != null && x.Category != string.Empty)
                                            .Where(x => x.TypeFlag == McvConstant.DESIGN_SCRIPT_DATA_CARD_TYPEFLAG_MATERIAL)
                                            .Select(x => x.Category)
                                            .Distinct()
                                            .ToListAsync();

        if (filters != null)
        {

            if (filters.Where(x => x.Key.Equals("zone", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var _zoneUIDs = filters.Where(x => x.Key.Equals("zone", StringComparison.OrdinalIgnoreCase)).Select(x => Guid.Parse(x.Value.Trim()));

                filteredZones = filteredZones.Where(x => _zoneUIDs.Any(z => z == x.UID)).ToList();
            }

            if (filters.Where(x => x.Key.Equals("category", StringComparison.OrdinalIgnoreCase)).Any())
            {
                filteredCategories = filters.Where(x => x.Key.Equals("category", StringComparison.OrdinalIgnoreCase)).Select(x => x.Value.Trim()).ToList();
            }

        }

        var _entityIDs = filteredZones.Select(x => x.ID);
        var _designScriptEntityIds = _entityIDs.ToList();
        while (_entityIDs.Any())
        {
            _entityIDs = await GetRecursiveChildrenIDsAsync(_entityIDs, projectEntities);
            _designScriptEntityIds = _designScriptEntityIds.Concat(_entityIDs).ToList();
        }

        projectEntities = projectEntities
            .Where(p => _designScriptEntityIds.Any(i => i == p.ID)).ToList();

        var zones = projectEntities
     .Where(z => z.ParentID == null)
     .Select(z => new
     {
         Zone = z.Code + "-" + z.Title.ToUpper(),
         Space = (string)null,
         Element = (string)null,
         DesignScriptEntityID = z.ID
     });

        var zoneSpaces = projectEntities
            .Where(s => s.ParentID != null)
            .Join(projectEntities.Where(z => z.ParentID == null),
                space => space.ParentID,
                zone => zone.ID,
                (space, zone) => new
                {
                    Zone = zone.Code + "-" + zone.Title.ToUpper(),
                    Space = space.Code + "-" + space.Title.ToUpper(),
                    Element = (string)null,
                    DesignScriptEntityID = space.ID
                });

        var zoneSpaceElements = projectEntities
            .Join(projectEntities, e => e.ParentID, s => s.ID, (element, space) => new { element, space })
            .Join(projectEntities.Where(z => z.ParentID == null),
                es => es.space.ParentID,
                zone => zone.ID,
                (es, zone) => new
                {
                    Zone = zone.Code + "-" + zone.Title.ToUpper(),
                    Space = es.space.Code + "-" + es.space.Title.ToUpper(),
                    Element = es.element.Code + "-" + es.element.Title.ToUpper(),
                    DesignScriptEntityID = es.element.ID
                });

        var designScriptEntities = zones
            .Concat(zoneSpaces)
            .Concat(zoneSpaceElements)
            .OrderBy(x => x.Zone)
            .ThenBy(x => x.Space)
            .ThenBy(x => x.Element)
            .ToList();


        var dsEntityItemMaps = await db.DesignScriptEntityItemMaps.AsNoTracking()
                       .Include(x => x.DesignScriptItem)
                                   .Where(x => x.DesignScriptEntityID != null)
                                    .Where(x => x.DesignScriptItem != null)
            .Where(x => _designScriptEntityIds.Contains(x.DesignScriptEntityID))
            .Select(x => new
            {
                x.ID,
                x.DesignScriptEntityID,
                x.CostingAmount,
                x.CostingQuantity,
                x.CostingRate,
                x.CostingRemark,
                x.CostingUnit,
                x.DesignScriptItemID,
                Category = x.DesignScriptItem.Category != null ? x.DesignScriptItem.Category.ToUpper() : null,
                SubCategory = x.DesignScriptItem.SubCategory != null ? x.DesignScriptItem.SubCategory.ToUpper() : null,
                x.DesignScriptItem.CategoryOrderFlag,
                x.DesignScriptItem.SubCategoryOrderFlag,
                x.DesignScriptItem.ItemGroup,
                x.DesignScriptItem.Title,
                x.DesignScriptItem.Specification,
                x.DesignScriptItem.DSR,
                x.DesignScriptItem.DSRNumber,
                x.DesignScriptItem.Code,
                x.DesignScriptItem.OrderFlag,
                x.DesignScriptItem.Units,
                x.DesignScriptItem.DrawingSpecification
            })
           
            .ToListAsync();

        var dsMeasurementGroups = await db.DesignScriptMeasurementGroups.AsNoTracking()
             .Include(x => x.Measurements)
              .Where(x => x.DesignScriptEntityID != null)
            .Where(x => _designScriptEntityIds.Contains(x.DesignScriptEntityID.Value))
               .SelectMany(join => join.Measurements,
             (a, b) => new
             {
                 a.DesignScriptEntityID,
                 a.DesignScriptEntityItemMapID,
                 a.DesignScriptDataCardID,
                 b.Area,
                 b.Number,
                 b.Length,
                 b.Breadth,
                 b.Height,
                 b.Quantity,
                 b.Tag,
                 b.Total,
                 b.Percentage,
                 b.CenterToCenter,
                 b.TypeFlag,
                 b.Unit
             })
                .ToListAsync();

        var dsDataCards = await db.DesignScriptDataCards.AsNoTracking()
             .Where(x => !x.IsHidden)
             .Include(x => x.Attachments)
             .Where(x => x.ProjectID == project.ID)
             .Select(x=> new
             {
                 x.ID,
                 x.Title,
                 x.Subtitle,
                 x.Description,
                 x.GFCTag,
                 x.Attachments
             })
             .ToListAsync();

        var _data = dsEntityItemMaps
        .GroupJoin(dsMeasurementGroups,
             x => x.ID,
             y => y.DesignScriptEntityItemMapID,
             (x, y) => new
             {

                 x.DesignScriptEntityID,
                 qty = x,
                 mmtGrp = y.DefaultIfEmpty()
             })
        
              .SelectMany(join => join.mmtGrp, (a, b) => new
              {
                  a.DesignScriptEntityID,
                  a.qty,
                  mmtGrp = b
              }).Where(x => x.mmtGrp != null)
              .GroupJoin(dsDataCards,
             x => x.mmtGrp.DesignScriptDataCardID,
             y => y.ID,
             (x, y) => new
             {
                 x.DesignScriptEntityID,
                  x.qty,
                  x.mmtGrp,
                 dc = y.DefaultIfEmpty()
             })
              .SelectMany(join => join.dc, (a, b) => new
              {
                  a.DesignScriptEntityID,
                  a.qty,
                  a.mmtGrp,
                  dc = b
              })
                  .Select(x => new
                  {
                      x.DesignScriptEntityID,
                      ItemCategory = x.qty != null ? x.qty.Category : null,
                      ItemSubCategory = x.qty != null ? x.qty.SubCategory : null,
                      ItemCategoryOrder = x.qty != null ? x.qty.CategoryOrderFlag : 0,
                      ItemSubCategoryOrder = x.qty != null ? x.qty.SubCategoryOrderFlag : 0,
                      ItemGroup = x.qty != null ? x.qty.ItemGroup : null,
                      ItemTitle = x.qty != null ? x.qty.Title : null,
                      ItemSpecification = x.qty != null ? x.qty.Specification : null,
                      ItemDrawingSpecification = x.qty != null ? x.qty.DrawingSpecification : null,
                      ItemDSR = x.qty != null ? x.qty.DSR : null,
                      ItemDSRNumber = x.qty != null ? x.qty.DSRNumber : null,
                      ItemCode = x.qty != null ? x.qty.Code : null,
                      ItemOrder = x.qty != null ? x.qty.OrderFlag : 0,
                      Quantity = x.qty != null ? x.qty.CostingQuantity : 0m,
                      Unit = x.qty != null ? x.qty.CostingUnit : "UNIT",
                      Rate = x.qty != null ? x.qty.CostingRate : 0m,
                      Amount = x.qty != null ? x.qty.CostingAmount : 0m,
                      Remark = x.qty != null ? x.qty.CostingRemark : null,
                      //Measurements = x.qty != null ? x.qty.Measurements : null,
                      MArea = x.mmtGrp != null ? x.mmtGrp.Area : (decimal?)null,
                      MLength = x.mmtGrp != null ? x.mmtGrp.Length : (decimal?)null,
                      MHeight = x.mmtGrp != null ? x.mmtGrp.Height : (decimal?)null,
                      MBreadth = x.mmtGrp != null ? x.mmtGrp.Breadth : (decimal?)null,
                      MCenterToCenter = x.mmtGrp != null ? x.mmtGrp.CenterToCenter : (decimal?)null,
                      MPercentage = x.mmtGrp != null ? x.mmtGrp.Percentage : (decimal?)null,
                      MNumber = x.mmtGrp != null ? x.mmtGrp.Number : (decimal?)null,
                      MTag = x.mmtGrp != null ? x.mmtGrp.Tag : null,
                      MType = x.mmtGrp != null && x.mmtGrp.TypeFlag != 0 ? x.mmtGrp.TypeFlag : 1,
                      MTotal = x.mmtGrp != null ? x.mmtGrp.Total : 0,
                      MQuantity = x.mmtGrp != null ? x.mmtGrp.Quantity : 0,
                      MUnit = x.mmtGrp != null ? x.mmtGrp.Unit : "",
                      MDTitle = x.dc != null ? x.dc.Title : null,
                      MDSubtitle = x.dc != null ? x.dc.Subtitle : null,
                      MDDescription = x.dc != null ? x.dc.Description : null,
                      MDGFCTag = x.dc != null ? x.dc.GFCTag : null,
                  })
                  .Where(x => filteredCategories.Any(c => c == x.ItemCategory))
             .ToList();

        var reportData = _data
            .Join(designScriptEntities,
            a => a.DesignScriptEntityID,
            b => b.DesignScriptEntityID,
            (a, b) => new
            {
               Phase= b.Zone,
                b.Space,
                b.Element,
                a.ItemCategory,
                a.ItemGroup,
                a.ItemTitle,
                ItemSpecification = showDWGSpec ? a.ItemDrawingSpecification : a.ItemSpecification,
                a.ItemDSR,
                a.ItemDSRNumber,
                a.ItemCode,
                a.ItemCategoryOrder,
                a.ItemSubCategoryOrder,
                a.ItemOrder,
                ItemCategoryPrefix = a.ItemCode != null ? McvConstant.DESIGN_SCRIPT_CATEGORY_ABBREIVIATIONS.FirstOrDefault(c => c.Key.ToUpper() == a.ItemCategory.ToUpper()).Value : "",

                a.Quantity,
                a.Unit,
                a.Rate,
                a.Amount,
                a.Remark,
                a.MArea,
                a.MLength,
                a.MHeight,
                a.MBreadth,
                a.MCenterToCenter,
                a.MPercentage,
                a.MNumber,
                a.MTag,
                a.MType,
                a.MTotal,
                a.MQuantity,
                a.MUnit,
                a.MDTitle,
                a.MDSubtitle,
                a.MDDescription,
                a.MDGFCTag,
                //a.MDUrl ,
            })
            .OrderBy(x => x.ItemCategoryOrder)
            .ThenBy(x => x.ItemSubCategoryOrder)
            .ThenBy(x => x.ItemOrder)
             .ToList();

        var _reportProperties = new List<ReportProperties>
            {
            new ReportProperties() { PropertyName="ReportTitle", PropertyValue="ITEM LIST" },
                new ReportProperties() { PropertyName = "Title", PropertyValue = $"{project.Code}-{project.Title}" },
                                   new ReportProperties() { PropertyName = "Filter_Categories", PropertyValue = $"{string.Join(", ",filteredCategories)}" },
                  new ReportProperties() { PropertyName = "Filter_Zones", PropertyValue = $"{string.Join(", ",filteredZones.Select(x=> x.Code+"-"+x.Title))}" },
                     new ReportProperties() { PropertyName = "AutoPageNumber", PropertyValue = $"{AutoPageNumber}" },
                     new ReportProperties() { PropertyName = "TotalPages", PropertyValue = $"{TotalPages}" },
                        new ReportProperties() { PropertyName = "LastPageNumber", PropertyValue = $"{LastPageNumber}" },
            };
        var sharedService = new SharedService(db);
        var _reportContainerUrl = await sharedService.GetPresetValue(McvConstant.RDLC_REPORT_CONTAINER_URL);
        var DEVMODE = Convert.ToBoolean((await sharedService.GetPresetValue(McvConstant.DEVMODE)));

        var _reportPath = $"ItemList-{reportSize}.rdlc";

        var _reportDef = new ReportDefinition()
        {
            ReportName = "Item List",
            ReportPath = DEVMODE ? $"{_reportContainerUrl}DEV/{_reportPath}" : $"{_reportContainerUrl}{_reportPath}",
            ReportDataSet = DataTools.ToDataTable(reportData),
            ReportProperties = _reportProperties,
            Filename = $"ItemList-{project.Code}-{project.Title}-{ClockTools.GetISTNow().ToString("yyMMddHHmm")}",
            RenderType = renderType
        };

        var reportServiceApi = await sharedService.GetPresetValue(McvConstant.RDLC_PROCESSOR_API);
        return await ReportClient.GenerateReport(_reportDef, reportServiceApi);

    }

    public async Task<ReportDefinition> GetElementMeasurementSheet(Guid projectUID, string reportSize = "a4", IEnumerable<QueryFilter> filters = null, string renderType = "PDF", bool AutoPageNumber = true,
                    int TotalPages = 0,
        int LastPageNumber = 0, string reportTitle = "MEASUREMENT SHEET [E]", string filename = "Measurement Sheet [E]")
    {

        var projectService = new ProjectService(db);
        var project = await projectService.Get()
                                           .Where(x => x.UID == projectUID)
                                           .SingleOrDefaultAsync();

        if (project == null) throw new EntityServiceException("Project not found!");

        var entities = await Get()
               .Where(x => x.ProjectID == project.ID)
               .ToListAsync();

        var filteredZones = entities
         .Where(x => x.ParentID == null && x.TypeFlag == McvConstant.DESIGN_SCRIPT_ENTITY_TYPEFLAG_ZONE)
         .ToList();


        var filteredCategories = await db.DesignScriptDataCards.AsNoTracking()
            
                                            .Where(x => !x.IsHidden)
                                            .Where(x => x.Category != null && x.Category != string.Empty)
                                            .Where(x => x.TypeFlag == McvConstant.DESIGN_SCRIPT_DATA_CARD_TYPEFLAG_MATERIAL)
                                            .Select(x => x.Category)
                                            .Distinct()
                                            .ToListAsync();

        if (filters != null)
        {
            if (filters.Where(x => x.Key.Equals("zone", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var _zoneUIDs = filters.Where(x => x.Key.Equals("zone", StringComparison.OrdinalIgnoreCase)).Select(x => Guid.Parse(x.Value.Trim()));

                filteredZones = filteredZones.Where(x => _zoneUIDs.Any(z => z == x.UID)).ToList();
            }

            if (filters.Where(x => x.Key.Equals("category", StringComparison.OrdinalIgnoreCase)).Any())
            {
                filteredCategories = filters.Where(x => x.Key.Equals("category", StringComparison.OrdinalIgnoreCase)).Select(x => x.Value.Trim()).ToList();
            }
        }

        var _entityIDs = filteredZones.Select(x => x.ID);
        var _designScriptEntityIds = _entityIDs.ToList();
        while (_entityIDs.Any())
        {
            _entityIDs = await GetRecursiveChildrenIDsAsync(_entityIDs);
            _designScriptEntityIds = _designScriptEntityIds.Concat(_entityIDs).ToList();
        }

        if (_designScriptEntityIds != null && _designScriptEntityIds.Any())
        {
            entities = entities.Where(x => _designScriptEntityIds.Any(c => c == x.ID)).ToList();
        }




        var dataCards = await db.DesignScriptDataCards.AsNoTracking()

                 .Where(x => !x.IsHidden)
                 .Include(x => x.Attachments)
                   .Where(x => filteredCategories.Any(c => c == x.Category))
                 .Where(x => x.ProjectID == project.ID && x.Attachments.Any())
                 .Select(
                     x => new
                     {
                         x.ID,
                         x.Title,
                         x.Subtitle,
                         x.Description,
                         x.GFCTag,
                         ThumbUrl = x.Attachments.Where(a => a.ThumbUrl != null).Any() ? x.Attachments.Where(a => a.ThumbUrl != null).FirstOrDefault().ThumbUrl : null
                     })
                 .Where(x => x.ThumbUrl != null)
                 .ToListAsync();

        var dsEntityItemMaps = await db.DesignScriptEntityItemMaps.AsNoTracking()
      .Include(x => x.DesignScriptItem)
      .Where(x => _designScriptEntityIds.Contains(x.DesignScriptEntityID) && x.DesignScriptItem != null)
      .Select(x => new
      {
          x.ID,
          x.DesignScriptEntityID,
          x.CostingAmount,
          x.CostingQuantity,
          x.CostingRate,
          x.CostingRemark,
          x.CostingUnit,
          x.DesignScriptItemID,
          Category = x.DesignScriptItem.Category != null ? x.DesignScriptItem.Category.ToUpper() : null,
          SubCategory = x.DesignScriptItem.SubCategory != null ? x.DesignScriptItem.SubCategory.ToUpper() : null,
          x.DesignScriptItem.CategoryOrderFlag,
          x.DesignScriptItem.SubCategoryOrderFlag,
          x.DesignScriptItem.ItemGroup,
          x.DesignScriptItem.Title,
          x.DesignScriptItem.Specification,
          x.DesignScriptItem.DSR,
          x.DesignScriptItem.DSRNumber,
          x.DesignScriptItem.Code,
          x.DesignScriptItem.OrderFlag,
          x.DesignScriptItem.Units

      })
      .ToListAsync();

        var measurementGroupsPerMeasurement = await db.DesignScriptMeasurementGroups.AsNoTracking()
                                             .Include(x => x.Measurements)
                                             .SelectMany(join => join.Measurements,
                                             (a, b) => new
                                             {
                                                 a.DesignScriptEntityItemMapID,
                                                 a.DesignScriptDataCardID,
                                                 b.Area,
                                                 b.Number,
                                                 b.Length,
                                                 b.Breadth,
                                                 b.Height,
                                                 b.Quantity,
                                                 b.Tag,
                                                 b.Total,
                                                 b.Percentage,
                                                 b.CenterToCenter,
                                                 b.TypeFlag,
                                                 a.Rate,
                                                 a.Amount,
                                                 GroupQty = a.Total
                                             })
                                             .Where(x => dsEntityItemMaps.Select(x => x.ID).Contains(x.DesignScriptEntityItemMapID))
                                             .ToListAsync();

        var _data =  entities
      .Join(entities, p => p.ID, s => s.ParentID, (p, s) => new { p, s })
      .Join(entities, ps => ps.s.ID, e => e.ParentID, (ps, e) => new { ps.p, ps.s, e })

      //Entity-Item maps
      .Join(dsEntityItemMaps,
           ese => ese.e.ID,
           qty => qty.DesignScriptEntityID,
           (ese, qty) => new { ese.p, ese.s, ese.e, qty })

         //sort by zone-space-element
         .OrderBy(ese => ese.p.OrderFlag)
         .ThenBy(ese => ese.s.OrderFlag)
         .ThenBy(ese => ese.e.OrderFlag)

      .GroupJoin(measurementGroupsPerMeasurement,
                 x => x.qty.ID,
                 y => y.DesignScriptEntityItemMapID,
                 (x, y) => new
                 {
                     p = x.p,
                     s = x.s,
                     e = x.e,
                     qty = x.qty,
                     mmt = y.DefaultIfEmpty()
                 })
                  .SelectMany(join => join.mmt.DefaultIfEmpty(), (a, b) => new
                  {
                      p = a.p,
                      s = a.s,
                      e = a.e,
                      qty = a.qty,
                      mmt = b
                  }).Where(x=>x.mmt!=null)
      .GroupJoin(dataCards,
                 x => x.mmt.DesignScriptDataCardID,
                 y => y.ID,
                 (x, y) => new
                 {
                     p = x.p,
                     s = x.s,
                     e = x.e,
                     qty = x.qty,
                     mmt = x.mmt,
                     dc = y.DefaultIfEmpty()
                 })
                  .SelectMany(join => join.dc.DefaultIfEmpty(), (a, b) => new
                  {
                      p = a.p,
                      s = a.s,
                      e = a.e,
                      qty = a.qty,
                      mmt = a.mmt,
                      dc = b
                  })
                      .Select(x => new
                      {
                          Phase = x.p.Code + "-" + x.p.Title.ToUpper(),
                          Space = x.s.Code + "-" + x.s.Title.ToUpper(),
                          Element = x.e.Code + "-" + x.e.Title.ToUpper(),
                          ItemCategory = x.qty != null ? x.qty.Category.ToUpper() : null,
                          ItemSubCategory = x.qty != null ? x.qty.SubCategory : null,
                          ItemCategoryOrder = x.qty != null ? x.qty.CategoryOrderFlag : 0,
                          ItemSubCategoryOrder = x.qty != null ? x.qty.SubCategoryOrderFlag : 0,
                          ItemGroup = x.qty != null ? x.qty.ItemGroup : null,
                          ItemTitle = x.qty != null ? x.qty.Title : null,
                          ItemSpecification = x.qty != null ? x.qty.Specification : null,
                          ItemDSR = x.qty != null ? x.qty.DSR : null,
                          ItemDSRNumber = x.qty != null ? x.qty.DSRNumber : null,
                          ItemCode = x.qty != null ? x.qty.Code : null,
                          ItemOrder = x.qty != null ? x.qty.OrderFlag : 0,

                          MArea = x.mmt != null ? x.mmt.Area : (decimal?)null,
                          MLength = x.mmt != null ? x.mmt.Length : (decimal?)null,
                          MHeight = x.mmt != null ? x.mmt.Height : (decimal?)null,
                          MBreadth = x.mmt != null ? x.mmt.Breadth : (decimal?)null,
                          MCenterToCenter = x.mmt != null ? x.mmt.CenterToCenter : (decimal?)null,
                          MPercentage = x.mmt != null ? x.mmt.Percentage : (decimal?)null,
                          MNumber = x.mmt != null ? x.mmt.Number : (decimal?)null,
                          MTag = x.mmt != null ? x.mmt.Tag : null,
                          MType = x.mmt != null && x.mmt.TypeFlag != 0 ? x.mmt.TypeFlag : 1,
                          MTotal = x.mmt != null ? x.mmt.Total : 0,
                          MQuantity = x.mmt != null ? x.mmt.Quantity : 0,
                          MUnit = x.qty != null ? x.qty.Units : "UNITS",
                          MGroupQty = x.mmt != null ? x.mmt.GroupQty : 0,
                          MRate = x.mmt != null ? x.mmt.Rate : 0,
                          MAmount = x.mmt != null ? x.mmt.Amount : 0,
                          MDTitle = x.dc != null ? x.dc.Title : null,
                          MDSubtitle = x.dc != null ? x.dc.Subtitle : null,
                          MDDescription = x.dc != null ? x.dc.Description : null,
                          MDGFCTag = x.dc != null ? x.dc.GFCTag : null,
                          MDUrl = x.dc != null ? x.dc.ThumbUrl : null,
                      })
                        .OrderBy(x => x.ItemCategoryOrder)
                .ThenBy(x => x.ItemSubCategoryOrder)
                .ThenBy(x => x.ItemOrder)
                 .ToList();



        var _reportProperties = new List<ReportProperties>
            {
                new ReportProperties() { PropertyName="ReportTitle", PropertyValue=reportTitle },
                new ReportProperties() { PropertyName = "Title", PropertyValue = $"{project.Code}-{project.Title}" },
                new ReportProperties() { PropertyName = "Filter_Categories", PropertyValue = $"{string.Join(", ",filteredCategories)}" },
                  new ReportProperties() { PropertyName = "Filter_Zones", PropertyValue = $"{string.Join(", ",filteredZones.Select(x=> x.Code+"-"+x.Title))}" },
                     new ReportProperties() { PropertyName = "AutoPageNumber", PropertyValue = $"{AutoPageNumber}" },
                     new ReportProperties() { PropertyName = "TotalPages", PropertyValue = $"{TotalPages}" },
                        new ReportProperties() { PropertyName = "LastPageNumber", PropertyValue = $"{LastPageNumber}" },
            };
        var sharedService = new SharedService(db);
        var _reportContainerUrl = await sharedService.GetPresetValue(McvConstant.RDLC_REPORT_CONTAINER_URL);
        var DEVMODE = Convert.ToBoolean((await sharedService.GetPresetValue(McvConstant.DEVMODE)));

        var _reportPath = $"MeasurementSheet-{reportSize}.rdlc";

        var _reportDef = new ReportDefinition()
        {
            ReportName = "Measurement Sheet",
            ReportPath = DEVMODE ? $"{_reportContainerUrl}DEV/{_reportPath}" : $"{_reportContainerUrl}{_reportPath}",
            ReportDataSet = DataTools.ToDataTable(_data

            ),
            ReportProperties = _reportProperties,
            Filename = $"{filename}-{project.Code}-{project.Title}-{ClockTools.GetISTNow().ToString("yyMMddHHmm")}",
            RenderType = renderType
        };

        var reportServiceApi = await sharedService.GetPresetValue(McvConstant.RDLC_PROCESSOR_API);
        return await ReportClient.GenerateReport(_reportDef, reportServiceApi);

    }

    public async Task<ReportDefinition> GetZoneMeasurementSheet(Guid projectUID, string reportSize = "a4", IEnumerable<QueryFilter> filters = null, string renderType = "PDF", bool AutoPageNumber = true,
                  int TotalPages = 0,
      int LastPageNumber = 0, string reportTitle = "MEASUREMENT SHEET [Z]", string filename = "Measurement Sheet [Z]")
    {

        var projectService = new ProjectService(db);
        var project = await projectService.Get()
                                           .Where(x => x.UID == projectUID)
                                           .SingleOrDefaultAsync();

        if (project == null) throw new EntityServiceException("Project not found!");

        var entities = await Get()
               .Where(x => x.ProjectID == project.ID)
               .ToListAsync();

        var filteredZones = entities
         .Where(x => x.ParentID == null && x.TypeFlag == McvConstant.DESIGN_SCRIPT_ENTITY_TYPEFLAG_ZONE)
         .ToList();


        var filteredCategories = await db.DesignScriptDataCards.AsNoTracking()
            
                                            .Where(x => !x.IsHidden)
                                            .Where(x => x.Category != null && x.Category != string.Empty)
                                            .Where(x => x.TypeFlag == McvConstant.DESIGN_SCRIPT_DATA_CARD_TYPEFLAG_MATERIAL)
                                            .Select(x => x.Category)
                                            .Distinct()
                                            .ToListAsync();

        if (filters != null)
        {
            if (filters.Where(x => x.Key.Equals("zone", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var _zoneUIDs = filters.Where(x => x.Key.Equals("zone", StringComparison.OrdinalIgnoreCase)).Select(x => Guid.Parse(x.Value.Trim()));

                filteredZones = filteredZones.Where(x => _zoneUIDs.Any(z => z == x.UID)).ToList();
            }

            if (filters.Where(x => x.Key.Equals("category", StringComparison.OrdinalIgnoreCase)).Any())
            {
                filteredCategories = filters.Where(x => x.Key.Equals("category", StringComparison.OrdinalIgnoreCase)).Select(x => x.Value.Trim()).ToList();
            }
        }

        var _entityIDs = filteredZones.Select(x => x.ID);
        var _designScriptEntityIds = _entityIDs.ToList();
        //while (_entityIDs.Any())
        //{
        //    _entityIDs = await GetRecursiveChildrenIDsAsync(_entityIDs);
        //    _designScriptEntityIds = _designScriptEntityIds.Concat(_entityIDs).ToList();
        //}

        if (_designScriptEntityIds != null && _designScriptEntityIds.Any())
        {
            entities = entities.Where(x => _designScriptEntityIds.Any(c => c == x.ID)).ToList();
        }


       

        var dataCards =await db.DesignScriptDataCards.AsNoTracking()
            
                 .Where(x => !x.IsHidden)
                 .Include(x => x.Attachments)
                   .Where(x => filteredCategories.Any(c => c == x.Category))
                 .Where(x => x.ProjectID == project.ID && x.Attachments.Any())
                 .Select(
                     x => new
                     {
                         x.ID,
                         x.Title,
                         x.Subtitle,
                         x.Description,
                         x.GFCTag,
                         ThumbUrl=x.Attachments.Where(a => a.ThumbUrl != null).Any() ? x.Attachments.Where(a => a.ThumbUrl != null).FirstOrDefault().ThumbUrl : null
                     })
                 .Where(x=>x.ThumbUrl!=null)
                 .ToListAsync();

        var dsEntityItemMaps = await db.DesignScriptEntityItemMaps.AsNoTracking()
      .Include(x => x.DesignScriptItem)
      .Where(x=> _designScriptEntityIds.Contains(x.DesignScriptEntityID) && x.DesignScriptItem!=null)
      .Select(x => new
      {
          x.ID,
          x.DesignScriptEntityID,
          x.CostingAmount,
          x.CostingQuantity,
          x.CostingRate,
          x.CostingRemark,
          x.CostingUnit,
          x.DesignScriptItemID,
          Category = x.DesignScriptItem.Category != null ? x.DesignScriptItem.Category.ToUpper() : null,
          SubCategory = x.DesignScriptItem.SubCategory!=null ? x.DesignScriptItem.SubCategory.ToUpper() : null,
          x.DesignScriptItem.CategoryOrderFlag,
          x.DesignScriptItem.SubCategoryOrderFlag,
          x.DesignScriptItem.ItemGroup,
          x.DesignScriptItem.Title,
          x.DesignScriptItem.Specification,
          x.DesignScriptItem.DSR,
          x.DesignScriptItem.DSRNumber,
          x.DesignScriptItem.Code,
          x.DesignScriptItem.OrderFlag,
          x.DesignScriptItem.Units

      })
      .ToListAsync();

        var measurementGroupsPerMeasurement = await db.DesignScriptMeasurementGroups.AsNoTracking()
                                             .Include(x => x.Measurements)
                                             .SelectMany(join => join.Measurements,
                                             (a, b) => new
                                             {
                                                 a.DesignScriptEntityItemMapID,
                                                 a.DesignScriptDataCardID,
                                                 b.Area,
                                                 b.Number,
                                                 b.Length,
                                                 b.Breadth,
                                                 b.Height,
                                                 b.Quantity,
                                                 b.Tag,
                                                 b.Total,
                                                 b.Percentage,
                                                 b.CenterToCenter,
                                                 b.TypeFlag,
                                                 a.Rate,
                                                 a.Amount,
                                                 GroupQty = a.Total
                                             })
                                             .Where(x=> dsEntityItemMaps.Select(x=>x.ID).Contains(x.DesignScriptEntityItemMapID))
                                             .ToListAsync();

        var partA = entities
                    .Join(dsEntityItemMaps,
                             ese => ese.ID,
                            qty => qty.DesignScriptEntityID,
                            (ese, qty) => new { ese, qty })
                            .OrderBy(a => a.ese.OrderFlag)
                            .ToList();

        var partB = partA
            .GroupJoin(measurementGroupsPerMeasurement,
                 x => x.qty.ID,
                 y => y.DesignScriptEntityItemMapID,
                 (x, y) => new
                 {
                     p = x.ese,
                     qty = x.qty,
                     mmt = y.DefaultIfEmpty()
                 })
                      .SelectMany(join => join.mmt, (a, b) => new
                      {
                          p = a.p,
                          qty = a.qty,
                          mmt = b
                      }).Where(x => x.mmt != null)
                      .ToList();

        var partC = partB

                .GroupJoin(dataCards,
                 x => x.mmt.DesignScriptDataCardID,
                 y => y.ID,
                 (x, y) => new
                 {
                     p = x.p,
                     qty = x.qty,
                     mmt = x.mmt,
                     dc = y.DefaultIfEmpty()
                 })
                      .SelectMany(join => join.dc.DefaultIfEmpty(), (a, b) => new
                      {
                          p = a.p,
                          qty = a.qty,
                          mmt = a.mmt,
                          dc = b
                      }).ToList();


        var _data = partC
                      .Select(x => new
                      {
                          Phase = "",
                          Space = "",
                          Element = x.p.Code + "-" + x.p.Title.ToUpper(),
                          ItemCategory = x.qty != null ? x.qty.Category : null,
                          ItemSubCategory = x.qty != null ? x.qty.SubCategory : null,
                          ItemCategoryOrder = x.qty != null ? x.qty.CategoryOrderFlag : 0,
                          ItemSubCategoryOrder = x.qty != null ? x.qty.SubCategoryOrderFlag : 0,
                          ItemGroup = x.qty != null ? x.qty.ItemGroup : null,
                          ItemTitle = x.qty != null ? x.qty.Title : null,
                          ItemSpecification = x.qty != null ? x.qty.Specification : null,
                          ItemDSR = x.qty != null ? x.qty.DSR : null,
                          ItemDSRNumber = x.qty != null ? x.qty.DSRNumber : null,
                          ItemCode = x.qty != null ? x.qty.Code : null,
                          ItemOrder = x.qty != null ? x.qty.OrderFlag : 0,

                          MArea = x.mmt != null ? x.mmt.Area : (decimal?)null,
                          MLength = x.mmt != null ? x.mmt.Length : (decimal?)null,
                          MHeight = x.mmt != null ? x.mmt.Height : (decimal?)null,
                          MBreadth = x.mmt != null ? x.mmt.Breadth : (decimal?)null,
                          MCenterToCenter = x.mmt != null ? x.mmt.CenterToCenter : (decimal?)null,
                          MPercentage = x.mmt != null ? x.mmt.Percentage : (decimal?)null,
                          MNumber = x.mmt != null ? x.mmt.Number : (decimal?)null,
                          MTag = x.mmt != null ? x.mmt.Tag : null,
                          MType = x.mmt != null && x.mmt.TypeFlag != 0 ? x.mmt.TypeFlag : 1,
                          MTotal = x.mmt != null ? x.mmt.Total : 0,
                          MQuantity = x.mmt != null ? x.mmt.Quantity : 0,
                          MUnit = x.qty != null ? x.qty.Units : "UNITS",
                          MGroupQty = x.mmt != null ? x.mmt.GroupQty : 0,
                          MRate = x.mmt != null ? x.mmt.Rate : 0,
                          MAmount = x.mmt != null ? x.mmt.Amount : 0,

                          MDTitle = x.dc != null ? x.dc.Title : null,
                          MDSubtitle = x.dc != null ? x.dc.Subtitle : null,
                          MDDescription = x.dc != null ? x.dc.Description : null,
                          MDGFCTag = x.dc != null ? x.dc.GFCTag : null,
                          MDUrl = x.dc != null ? x.dc.ThumbUrl : null,
                      })
                        .OrderBy(x => x.ItemCategoryOrder)
                .ThenBy(x => x.ItemSubCategoryOrder)
                .ThenBy(x => x.ItemOrder)
                 .ToList();



        var _reportProperties = new List<ReportProperties>
            {
             new ReportProperties() { PropertyName = "ReportTitle", PropertyValue = $"{reportTitle}" },
                new ReportProperties() { PropertyName = "Title", PropertyValue = $"{project.Code}-{project.Title}" },
                new ReportProperties() { PropertyName = "Filter_Categories", PropertyValue = $"{string.Join(", ",filteredCategories)}" },
                  new ReportProperties() { PropertyName = "Filter_Zones", PropertyValue = $"{string.Join(", ",filteredZones.Select(x=> x.Code+"-"+x.Title))}" },
                     new ReportProperties() { PropertyName = "AutoPageNumber", PropertyValue = $"{AutoPageNumber}" },
                     new ReportProperties() { PropertyName = "TotalPages", PropertyValue = $"{TotalPages}" },
                        new ReportProperties() { PropertyName = "LastPageNumber", PropertyValue = $"{LastPageNumber}" },
            };
        var sharedService = new SharedService(db);
        var _reportContainerUrl = await sharedService.GetPresetValue(McvConstant.RDLC_REPORT_CONTAINER_URL);
        var DEVMODE = Convert.ToBoolean((await sharedService.GetPresetValue(McvConstant.DEVMODE)));

        var _reportPath = $"MeasurementSheet-{reportSize}.rdlc";

        var _reportDef = new ReportDefinition()
        {
            ReportName = "Measurement Sheet",
            ReportPath = DEVMODE ? $"{_reportContainerUrl}DEV/{_reportPath}" : $"{_reportContainerUrl}{_reportPath}",
            ReportDataSet = DataTools.ToDataTable(_data

            ),
            ReportProperties = _reportProperties,
            Filename = $"{filename}-{project.Code}-{project.Title}-{ClockTools.GetISTNow().ToString("yyMMddHHmm")}",
            RenderType = renderType
        };

        var reportServiceApi = await sharedService.GetPresetValue(McvConstant.RDLC_PROCESSOR_API);
        return await ReportClient.GenerateReport(_reportDef, reportServiceApi);

    }

    public async Task<ReportDefinition> GetElementBOQ(Guid projectUID, string reportSize = "a4", IEnumerable<QueryFilter> filters = null, string renderType = "PDF", bool AutoPageNumber = true,
               int TotalPages = 0,
   int LastPageNumber = 0, string reportTitle = "BILL OF QUANTITIES [E]", string filename = "EBOQ")
    {


        var projectService = new ProjectService(db);
        var project = await projectService.Get()
                                           .Where(x => x.UID == projectUID)
                                           .SingleOrDefaultAsync();

        if (project == null) throw new EntityServiceException("Project not found!");

        var entities = await Get()
               .Where(x => x.ProjectID == project.ID)
                 .Include(x => x.ItemMaps).ThenInclude(c => c.DesignScriptItem)
                   .Include(x => x.ItemMaps).ThenInclude(c => c.MeasurementGroups)
               .ToListAsync();

        var _masterPhase = entities
                         .Where(x => x.ProjectID == project.ID && x.TypeFlag == McvConstant.DESIGN_SCRIPT_ENTITY_TYPEFLAG_ZONE
                         && x.isMasterPhase).SingleOrDefault();

        if (_masterPhase == null) throw new EntityServiceException("Master phase not found!");
        var filteredZones = entities
         .Where(x => x.ParentID == null && x.TypeFlag == McvConstant.DESIGN_SCRIPT_ENTITY_TYPEFLAG_ZONE)
         .ToList();


        var filteredCategories = await db.DesignScriptDataCards.AsNoTracking()
            
                                            .Where(x => !x.IsHidden)
                                            .Where(x => x.Category != null && x.Category != string.Empty)
                                            .Where(x => x.TypeFlag == McvConstant.DESIGN_SCRIPT_DATA_CARD_TYPEFLAG_MATERIAL)
                                            .Select(x => x.Category)
                                            .Distinct()
                                            .ToListAsync();
        var showRateAmount = false;
        var showElements = false;

        if (filters != null)
        {
            if (filters.Where(x => x.Key.Equals("zone", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var _zoneUIDs = filters.Where(x => x.Key.Equals("zone", StringComparison.OrdinalIgnoreCase)).Select(x => Guid.Parse(x.Value.Trim()));

                filteredZones = filteredZones.Where(x => _zoneUIDs.Any(z => z == x.UID)).ToList();
            }

            if (filters.Where(x => x.Key.Equals("category", StringComparison.OrdinalIgnoreCase)).Any())
            {
                filteredCategories = filters.Where(x => x.Key.Equals("category", StringComparison.OrdinalIgnoreCase)).Select(x => x.Value.Trim()).ToList();
            }

            if (filters.Where(x => x.Key.Equals("showRateAmount", StringComparison.OrdinalIgnoreCase)).Any())
            {
                showRateAmount = Convert.ToBoolean(filters.FirstOrDefault(x => x.Key.Equals("showRateAmount", StringComparison.OrdinalIgnoreCase)).Value);
            }
            if (filters.Where(x => x.Key.Equals("showElements", StringComparison.OrdinalIgnoreCase)).Any())
            {
                showElements = Convert.ToBoolean(filters.FirstOrDefault(x => x.Key.Equals("showElements", StringComparison.OrdinalIgnoreCase)).Value);
            }
        }

        var _entityIDs = filteredZones.Select(x => x.ID);
        var _designScriptEntityIds = _entityIDs.ToList();
        while (_entityIDs.Any())
        {
            _entityIDs = GetRecursiveChildrenIDs(entities, _entityIDs);
            _designScriptEntityIds = _designScriptEntityIds.Concat(_entityIDs).ToList();
        }

        if (_designScriptEntityIds != null && _designScriptEntityIds.Any())
        {
            entities = entities.Where(x => _designScriptEntityIds.Any(c => c == x.ID)).ToList();
        }





        var dataCards = await db.DesignScriptDataCards.AsNoTracking()
            
                 .Where(x => !x.IsHidden && filteredCategories.Any(c => c == x.Category) && x.ProjectID == project.ID)
                 .Select(x => new
                 {
                     x.ID,
                     x.Title,
                     x.Subtitle,
                     x.GFCTag,
                     x.Description,
                     Attachments = x.Attachments.Where(a => !a.IsDeleted && a.Url != null).Select(a => new
                     {
                         Url = a.ThumbUrl != null ? a.ThumbUrl : a.Url
                     }).Where(a => a.Url != null).ToList(),
                     Attributes = x.Attributes.Where(a => !a.IsDeleted && !a.IsHidden && a.AttributeValue != null).Select(a => new
                     {
                         a.AttributeKey,
                         a.AttributeValue
                     }).Where(a => a.AttributeValue != null).ToList()
                 })
                 .ToListAsync();

        var _data = entities
            .Where(x => _designScriptEntityIds.Any(i => i == x.ID)) //Filtered ZONES

                    .Join(entities, zone => zone.ID, space => space.ParentID, (a, b) => new { zone = a, space = b }) //SPACE

                    .Join(entities, ps => ps.space.ID, e => e.ParentID, (ps, e) => new { ps.zone, ps.space, element = e }) //ELEMENT

      .SelectMany(join => join.element.ItemMaps.Where(x => !x.IsDeleted && filteredCategories.Any(c => c == x.DesignScriptItem.Category)),
           (a, b) => new {
               zone = a.zone,
               space = a.space,
               element = a.element,
               item = b.DesignScriptItem,
               remark = b.CostingRemark,
               measurements = b.MeasurementGroups
              .Where(m => !m.IsDeleted)
              .Select(m => new
              {
                  m.DesignScriptEntityItemMapID,
                  m.DesignScriptDataCardID,
                  m.Rate,
                  m.Amount,
                  GroupQty = m.Total
              })
           })
         .OrderBy(x => x.zone.OrderFlag)
         .ThenBy(x => x.space.OrderFlag)
         .ThenBy(x => x.element.OrderFlag)
         .SelectMany(join => join.measurements,
         (a, b) => new
         {
             a.zone,
             a.space,
             a.element,
             a.item,
             a.remark,
             mmt = b
         })
        .Select(x => new
        {
            Phase = x.zone.Code + "-" + x.zone.Title.ToUpper(),
            Space = x.space.Code + "-" + x.space.Title.ToUpper(),
            Element = x.element.Code + "-" + x.element.Title.ToUpper(),
            ItemCategory = x.item.Category.ToUpper(),
            ItemSubCategory = x.item.SubCategory,
            ItemGroup = x.item.ItemGroup,
            ItemCategoryOrder = x.item.CategoryOrderFlag,
            ItemSubCategoryOrder = x.item.SubCategoryOrderFlag,
            ItemOrder = x.item.OrderFlag,
            ItemTitle = x.item.Title,
            ItemSpecification = x.item.Specification,
            ItemDSR = x.item.DSR,
            ItemDSRNumber = x.item.DSRNumber,
            ItemCode = x.item.Code,
            ItemReference = x.item.Reference,
            Remark = x.remark,
            MUnit = x.item.Units,
            MGroupQty = x.mmt != null ? x.mmt.GroupQty : 0,
            MRate = x.mmt != null ? x.mmt.Rate : 0,
            MAmount = x.mmt != null ? x.mmt.Amount : 0,
            DesignScriptDataCardID = x.mmt != null ? x.mmt.DesignScriptDataCardID : null
        })
         .GroupJoin(dataCards,
                        x => x.DesignScriptDataCardID,
                        y => y.ID,
                        (x, y) => new
                        {

                            mmt = x,
                            dc = y.DefaultIfEmpty()
                        })
                         .SelectMany(join => join.dc.DefaultIfEmpty(), (a, b) => new
                         {

                             mmt = a.mmt,
                             dc = b
                         });



        var reportData = _data
             .Select(x => new
             {
                 x.mmt.Phase,
                 x.mmt.Space,
                 x.mmt.Element,
                 x.mmt.ItemCategory,
                 x.mmt.ItemSubCategory,
                 x.mmt.ItemGroup,
                 x.mmt.ItemTitle,
                 x.mmt.ItemSpecification,
                 x.mmt.ItemDSR,
                 x.mmt.ItemDSRNumber,
                 x.mmt.ItemCode,
                 x.mmt.ItemCategoryOrder,
                 x.mmt.ItemSubCategoryOrder,
                 x.mmt.ItemOrder,
                 x.mmt.ItemReference,
                 ItemCategoryPrefix = McvConstant.DESIGN_SCRIPT_CATEGORY_ABBREIVIATIONS.FirstOrDefault(c => c.Key.ToUpper() == x.mmt.ItemCategory.ToUpper()).Value,
                 x.mmt.Remark,
                 x.mmt.MUnit,
                 x.mmt.MGroupQty,
                 MRate = showRateAmount ? x.mmt.MRate : 0,
                 MAmount = showRateAmount ? x.mmt.MAmount : 0,

                 MDTitle = x.dc != null ? x.dc.Title : null,
                 MDSubtitle = x.dc != null ? x.dc.Subtitle : null,
                 MDDescription = x.dc != null ? x.dc.Description : null,
                 MDGFCTag = x.dc != null ? x.dc.GFCTag : null,
                 MDUrl = x.dc != null && x.dc.Attachments.Any() ? x.dc.Attachments.FirstOrDefault().Url : null,
                 MDAttributes = x.dc != null && x.dc.Attributes.Any() ? string.Join(",\n", x.dc.Attributes.Select(a => a.AttributeKey + ": " + a.AttributeValue)) : null
             })
               .OrderBy(x => x.ItemCategoryOrder)
                .ThenBy(x => x.ItemSubCategoryOrder)
                .ThenBy(x => x.ItemOrder)
             .ToList();

        var _reportProperties = new List<ReportProperties>
                {
                 new ReportProperties() { PropertyName = "ReportTitle", PropertyValue = $"{reportTitle}" },
                    new ReportProperties() { PropertyName = "Title", PropertyValue = $"{project.Code}-{project.Title}" },
                    new ReportProperties() { PropertyName = "Filter_Categories", PropertyValue = $"{string.Join(", ",filteredCategories)}" },
                      new ReportProperties() { PropertyName = "Filter_Zones", PropertyValue = $"{string.Join(", ",filteredZones.Select(x=> x.Code+"-"+x.Title))}" },
                         new ReportProperties() { PropertyName = "AutoPageNumber", PropertyValue = $"{AutoPageNumber}" },
                         new ReportProperties() { PropertyName = "TotalPages", PropertyValue = $"{TotalPages}" },
                            new ReportProperties() { PropertyName = "LastPageNumber", PropertyValue = $"{LastPageNumber}" },
                             new ReportProperties() { PropertyName = "Contingency", PropertyValue = $"{_masterPhase.EstimateContingency}" },
                    new ReportProperties() { PropertyName = "GST", PropertyValue = $"{_masterPhase.EstimateGST}" },
                     new ReportProperties() { PropertyName = "Notes", PropertyValue = $"{_masterPhase.EstimateRemark}" },
                    new ReportProperties() { PropertyName = "Message", PropertyValue = $"{(showRateAmount ? "All prices shown are in Indian Rupee (₹). This is a bill of quantities, not a quote. The rates mentioned are subject to change." : "")}"},
                      new ReportProperties() { PropertyName = "HideRate", PropertyValue = $"{!showRateAmount}" },
                      new ReportProperties() { PropertyName = "ShowElements", PropertyValue = $"{showElements}" },
                };
        var sharedService = new SharedService(db); ;
        var _reportContainerUrl = await sharedService.GetPresetValue(McvConstant.RDLC_REPORT_CONTAINER_URL);
        var DEVMODE = Convert.ToBoolean((await sharedService.GetPresetValue(McvConstant.DEVMODE)));

        var _reportPath = $"BOQE-{reportSize}.rdlc";

        var _reportDef = new ReportDefinition()
        {
            ReportName = $"BOQE",
            ReportPath = DEVMODE ? $"{_reportContainerUrl}DEV/{_reportPath}" : $"{_reportContainerUrl}{_reportPath}",
            ReportDataSet = DataTools.ToDataTable(reportData),
            ReportProperties = _reportProperties,
            Filename = $"{filename}-{project.Code}-{project.Title}-{ClockTools.GetISTNow().ToString("yyMMddHHmm")}",
            RenderType = renderType
        };

        var reportServiceApi = await sharedService.GetPresetValue(McvConstant.RDLC_PROCESSOR_API);
        return await ReportClient.GenerateReport(_reportDef, reportServiceApi);

    }
    public async Task<ReportDefinition> GetZoneBOQ(Guid projectUID, string reportSize = "a4", IEnumerable<QueryFilter> filters = null, string renderType = "PDF", bool AutoPageNumber = true,
                   int TotalPages = 0,
       int LastPageNumber = 0, string reportTitle = "BILL OF QUANTITIES [Z]", string filename = "ZBOQ")
    {


        var projectService = new ProjectService(db);
        var project = await projectService.Get()
                                           .Where(x => x.UID == projectUID)
                                           .SingleOrDefaultAsync();

        if (project == null) throw new EntityServiceException("Project not found!");

        var entities = await Get()
               .Where(x => x.ProjectID == project.ID)
                 .Include(x => x.ItemMaps).ThenInclude(c => c.DesignScriptItem)
                   .Include(x => x.ItemMaps).ThenInclude(c => c.MeasurementGroups)
                   .Where(x => x.TypeFlag != McvConstant.DESIGN_SCRIPT_ENTITY_TYPEFLAG_ELEMENT)
               .ToListAsync();

        var _masterPhase = entities
                         .Where(x => x.ProjectID == project.ID && x.TypeFlag == McvConstant.DESIGN_SCRIPT_ENTITY_TYPEFLAG_ZONE
                         && x.isMasterPhase).SingleOrDefault();

        if (_masterPhase == null) throw new EntityServiceException("Master phase not found!");
        var filteredZones = entities
         .Where(x => x.ParentID == null && x.TypeFlag == McvConstant.DESIGN_SCRIPT_ENTITY_TYPEFLAG_ZONE)
         .ToList();


        var filteredCategories = await db.DesignScriptDataCards.AsNoTracking()
            
                                            .Where(x => !x.IsHidden)
                                            .Where(x => x.Category != null && x.Category != string.Empty)
                                            .Where(x => x.TypeFlag == McvConstant.DESIGN_SCRIPT_DATA_CARD_TYPEFLAG_MATERIAL)
                                            .Select(x => x.Category)
                                            .Distinct()
                                            .ToListAsync();
        var showRateAmount = false;
        var showElements = false;

        if (filters != null)
        {
            if (filters.Where(x => x.Key.Equals("zone", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var _zoneUIDs = filters.Where(x => x.Key.Equals("zone", StringComparison.OrdinalIgnoreCase)).Select(x => Guid.Parse(x.Value.Trim()));

                filteredZones = filteredZones.Where(x => _zoneUIDs.Any(z => z == x.UID)).ToList();
            }

            if (filters.Where(x => x.Key.Equals("category", StringComparison.OrdinalIgnoreCase)).Any())
            {
                filteredCategories = filters.Where(x => x.Key.Equals("category", StringComparison.OrdinalIgnoreCase)).Select(x => x.Value.Trim()).ToList();
            }

            if (filters.Where(x => x.Key.Equals("showRateAmount", StringComparison.OrdinalIgnoreCase)).Any())
            {
                showRateAmount = Convert.ToBoolean(filters.FirstOrDefault(x => x.Key.Equals("showRateAmount", StringComparison.OrdinalIgnoreCase)).Value);
            }
            if (filters.Where(x => x.Key.Equals("showElements", StringComparison.OrdinalIgnoreCase)).Any())
            {
                showElements = Convert.ToBoolean(filters.FirstOrDefault(x => x.Key.Equals("showElements", StringComparison.OrdinalIgnoreCase)).Value);
            }
        }

        var _entityIDs = filteredZones.Select(x => x.ID);
        var _designScriptEntityIds = _entityIDs.ToList();
        while (_entityIDs.Any())
        {
            _entityIDs = GetRecursiveChildrenIDs(entities, _entityIDs);
            _designScriptEntityIds = _designScriptEntityIds.Concat(_entityIDs).ToList();
        }

        if (_designScriptEntityIds != null && _designScriptEntityIds.Any())
        {
            entities = entities.Where(x => _designScriptEntityIds.Any(c => c == x.ID)).ToList();
        }





        var dataCards = await db.DesignScriptDataCards.AsNoTracking()
            
                 .Where(x => !x.IsHidden && filteredCategories.Any(c => c == x.Category) && x.ProjectID == project.ID)
                 .Select(x => new
                 {
                     x.ID,
                     x.Title,
                     x.Subtitle,
                     x.GFCTag,
                     x.Description,
                     Attachments = x.Attachments.Where(a => !a.IsDeleted && a.Url != null).Select(a => new
                     {
                         Url = a.ThumbUrl != null ? a.ThumbUrl : a.Url
                     }).Where(a => a.Url != null).ToList(),
                     Attributes = x.Attributes.Where(a => !a.IsDeleted && !a.IsHidden && a.AttributeValue != null).Select(a => new
                     {
                         a.AttributeKey,
                         a.AttributeValue
                     }).Where(a => a.AttributeValue != null).ToList()
                 })
                 .ToListAsync();

        var _data = entities
            .Where(x => _designScriptEntityIds.Any(i => i == x.ID))
      .SelectMany(join => join.ItemMaps.Where(x => !x.IsDeleted && filteredCategories.Any(c => c == x.DesignScriptItem.Category)),
           (a, b) => new
           {
               zone = a,
               space = "",
               element = "",
               item = b.DesignScriptItem,
               remark = b.CostingRemark,
               measurements = b.MeasurementGroups
              .Where(m => !m.IsDeleted)
              .Select(m => new
              {
                  m.DesignScriptEntityItemMapID,
                  m.DesignScriptDataCardID,
                  m.Rate,
                  m.Amount,
                  GroupQty = m.Total
              })
           })
         .OrderBy(x => x.zone.OrderFlag)
         .SelectMany(join => join.measurements,
         (a, b) => new
         {
             a.zone,
             a.space,
             a.element,
             a.item,
             a.remark,
             mmt = b
         })
        .Select(x => new
        {
            Zone = x.zone.Code + "-" + x.zone.Title.ToUpper(),
            Space = "",
            Element = "",
            ItemCategory = x.item.Category.ToUpper(),
            ItemCategoryOrder = x.item.CategoryOrderFlag,
            ItemSubCategory = x.item.SubCategory,
            ItemSubCategoryOrder = x.item.SubCategoryOrderFlag,
            ItemOrder = x.item.OrderFlag,
            ItemGroup = x.item.ItemGroup,
            ItemTitle = x.item.Title,
            ItemSpecification = x.item.Specification,
            ItemDSR = x.item.DSR,
            ItemDSRNumber = x.item.DSRNumber,
            ItemCode = x.item.Code,
            ItemReference = x.item.Reference,
            Remark = x.remark,
            MUnit = x.item.Units,
            MGroupQty = x.mmt != null ? x.mmt.GroupQty : 0,
            MRate = x.mmt != null ? x.mmt.Rate : 0,
            MAmount = x.mmt != null ? x.mmt.Amount : 0,
            DesignScriptDataCardID = x.mmt != null ? x.mmt.DesignScriptDataCardID : null
        })
         .GroupJoin(dataCards,
                        x => x.DesignScriptDataCardID,
                        y => y.ID,
                        (x, y) => new
                        {

                            mmt = x,
                            dc = y.DefaultIfEmpty()
                        })
                         .SelectMany(join => join.dc.DefaultIfEmpty(), (a, b) => new
                         {

                             mmt = a.mmt,
                             dc = b
                         });



        var reportData = _data
             .Select(x => new
             {
                 x.mmt.Zone,
                 x.mmt.Space,
                 x.mmt.Element,
                 x.mmt.ItemCategory,
                 x.mmt.ItemSubCategory,
                 x.mmt.ItemGroup,
                 x.mmt.ItemTitle,
                 x.mmt.ItemSpecification,
                 x.mmt.ItemDSR,
                 x.mmt.ItemDSRNumber,
                 x.mmt.ItemCode,
                 x.mmt.ItemCategoryOrder,
                 x.mmt.ItemSubCategoryOrder,
                 x.mmt.ItemOrder,
                 x.mmt.ItemReference,
                 ItemCategoryPrefix = McvConstant.DESIGN_SCRIPT_CATEGORY_ABBREIVIATIONS.FirstOrDefault(c => c.Key.ToUpper() == x.mmt.ItemCategory.ToUpper()).Value,
                 x.mmt.Remark,
                 x.mmt.MUnit,
                 x.mmt.MGroupQty,
                 MRate = showRateAmount ? x.mmt.MRate : 0,
                 MAmount = showRateAmount ? x.mmt.MAmount : 0,
                 MDTitle = x.dc != null ? x.dc.Title : null,
                 MDSubtitle = x.dc != null ? x.dc.Subtitle : null,
                 MDDescription = x.dc != null ? x.dc.Description : null,
                 MDGFCTag = x.dc != null ? x.dc.GFCTag : null,
                 MDUrl = x.dc != null && x.dc.Attachments.Any() ? x.dc.Attachments.FirstOrDefault().Url : null,
                 MDAttributes = x.dc != null && x.dc.Attributes.Any() ? string.Join(",\n", x.dc.Attributes.Select(a => a.AttributeKey + ": " + a.AttributeValue)) : null,
             })
 .OrderBy(x => x.ItemCategoryOrder)
                .ThenBy(x => x.ItemSubCategoryOrder)
                .ThenBy(x => x.ItemOrder)
             .ToList();

        var _reportProperties = new List<ReportProperties>
            { new ReportProperties() { PropertyName = "ReportTitle", PropertyValue = $"{reportTitle}" },
                new ReportProperties() { PropertyName = "Title", PropertyValue = $"{project.Code}-{project.Title}" },
                new ReportProperties() { PropertyName = "Filter_Categories", PropertyValue = $"{string.Join(", ",filteredCategories)}" },
                  new ReportProperties() { PropertyName = "Filter_Zones", PropertyValue = $"{string.Join(", ",filteredZones.Select(x=> x.Code+"-"+x.Title))}" },
                     new ReportProperties() { PropertyName = "AutoPageNumber", PropertyValue = $"{AutoPageNumber}" },
                     new ReportProperties() { PropertyName = "TotalPages", PropertyValue = $"{TotalPages}" },
                        new ReportProperties() { PropertyName = "LastPageNumber", PropertyValue = $"{LastPageNumber}" },
                         new ReportProperties() { PropertyName = "Contingency", PropertyValue = $"{_masterPhase.EstimateContingency}" },
                new ReportProperties() { PropertyName = "GST", PropertyValue = $"{_masterPhase.EstimateGST}" },
                              new ReportProperties() { PropertyName = "Message", PropertyValue = $"{(!showRateAmount ? "All prices shown are in Indian Rupee (₹). This is a bill of quantities, not a quote. The rates mentioned are subject to change." : "")}"},
                  new ReportProperties() { PropertyName = "Estimated", PropertyValue = $"{showRateAmount}" },
                  new ReportProperties() { PropertyName = "ShowElements", PropertyValue = $"{showElements}" },
            };
        var sharedService = new SharedService(db); ;
        var _reportContainerUrl = await sharedService.GetPresetValue(McvConstant.RDLC_REPORT_CONTAINER_URL);
        var DEVMODE = Convert.ToBoolean((await sharedService.GetPresetValue(McvConstant.DEVMODE)));

        var _reportPath = $"BOQZ-{reportSize}.rdlc";

        var _reportDef = new ReportDefinition()
        {
            ReportName = $"BOQZ",
            ReportPath = DEVMODE ? $"{_reportContainerUrl}DEV/{_reportPath}" : $"{_reportContainerUrl}{_reportPath}",
            ReportDataSet = DataTools.ToDataTable(reportData),
            ReportProperties = _reportProperties,
            Filename = $"{filename}-{project.Code}-{project.Title}-{ClockTools.GetISTNow().ToString("yyMMddHHmm")}",
            RenderType = renderType
        };

        var reportServiceApi = await sharedService.GetPresetValue(McvConstant.RDLC_PROCESSOR_API);
        return await ReportClient.GenerateReport(_reportDef, reportServiceApi);

    }

    public async Task<ReportDefinition> GetElementBOQSummary(Guid projectUID, string reportSize = "a4", IEnumerable<QueryFilter> filters = null, string renderType = "PDF", bool AutoPageNumber = true,
                  int TotalPages = 0,
      int LastPageNumber = 0)
    {

        var projectService = new ProjectService(db);
        var project = await projectService.Get()
                                           .Where(x => x.UID == projectUID)
                                           .SingleOrDefaultAsync();

        if (project == null) throw new EntityServiceException("Project not found!");

        var entities = await Get()
               .Where(x => x.ProjectID == project.ID)
                 .Include(x => x.ItemMaps).ThenInclude(c => c.DesignScriptItem)
                   .Include(x => x.ItemMaps).ThenInclude(c => c.MeasurementGroups)
               .ToListAsync();

        var _masterPhase = entities
                         .Where(x => x.ProjectID == project.ID && x.TypeFlag == McvConstant.DESIGN_SCRIPT_ENTITY_TYPEFLAG_ZONE
                         && x.isMasterPhase).SingleOrDefault();

        if (_masterPhase == null) throw new EntityServiceException("Master phase not found!");
        var filteredZones = entities
         .Where(x => x.ParentID == null && x.TypeFlag == McvConstant.DESIGN_SCRIPT_ENTITY_TYPEFLAG_ZONE)
         .ToList();


        var filteredCategories = await db.DesignScriptDataCards.AsNoTracking()
            
                                            .Where(x => !x.IsHidden)
                                            .Where(x => x.Category != null && x.Category != string.Empty)
                                            .Where(x => x.TypeFlag == McvConstant.DESIGN_SCRIPT_DATA_CARD_TYPEFLAG_MATERIAL)
                                            .Select(x => x.Category)
                                            .Distinct()
                                            .ToListAsync();
        var hideRate = false;
        var showElements = false;

        if (filters != null)
        {
            if (filters.Where(x => x.Key.Equals("zone", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var _zoneUIDs = filters.Where(x => x.Key.Equals("zone", StringComparison.OrdinalIgnoreCase)).Select(x => Guid.Parse(x.Value.Trim()));

                filteredZones = filteredZones.Where(x => _zoneUIDs.Any(z => z == x.UID)).ToList();
            }

            if (filters.Where(x => x.Key.Equals("category", StringComparison.OrdinalIgnoreCase)).Any())
            {
                filteredCategories = filters.Where(x => x.Key.Equals("category", StringComparison.OrdinalIgnoreCase)).Select(x => x.Value.Trim()).ToList();
            }

            if (filters.Where(x => x.Key.Equals("hideRate", StringComparison.OrdinalIgnoreCase)).Any())
            {
                hideRate = Convert.ToBoolean(filters.FirstOrDefault(x => x.Key.Equals("hideRate", StringComparison.OrdinalIgnoreCase)).Value);
            }
            if (filters.Where(x => x.Key.Equals("showElements", StringComparison.OrdinalIgnoreCase)).Any())
            {
                showElements = Convert.ToBoolean(filters.FirstOrDefault(x => x.Key.Equals("showElements", StringComparison.OrdinalIgnoreCase)).Value);
            }
        }

        var _entityIDs = filteredZones.Select(x => x.ID);
        var _designScriptEntityIds = _entityIDs.ToList();
        while (_entityIDs.Any())
        {
            _entityIDs = GetRecursiveChildrenIDs(entities, _entityIDs);
            _designScriptEntityIds = _designScriptEntityIds.Concat(_entityIDs).ToList();
        }

        if (_designScriptEntityIds != null && _designScriptEntityIds.Any())
        {
            entities = entities.Where(x => _designScriptEntityIds.Any(c => c == x.ID)).ToList();
        }





        var dataCards = await db.DesignScriptDataCards.AsNoTracking()
            
                 .Where(x => !x.IsHidden && filteredCategories.Any(c => c == x.Category) && x.ProjectID == project.ID)
                 .ToListAsync();

        var _data = entities
            .Where(x => _designScriptEntityIds.Any(i => i == x.ID)) //Filtered ZONES

                    .Join(entities, zone => zone.ID, space => space.ParentID, (a, b) => new { zone = a, space = b }) //SPACE

                    .Join(entities, ps => ps.space.ID, e => e.ParentID, (ps, e) => new { ps.zone, ps.space, element = e }) //ELEMENT

      .SelectMany(join => join.element.ItemMaps.Where(x => !x.IsDeleted && filteredCategories.Any(c => c == x.DesignScriptItem.Category)),
           (a, b) => new
           {
               zone = a.zone,
               space = a.space,
               element = a.element,
               item = b.DesignScriptItem,
               remark = b.CostingRemark,
               measurements = b.MeasurementGroups
              .Where(m => !m.IsDeleted)
              .Select(m => new
              {
                  m.DesignScriptEntityItemMapID,
                  m.DesignScriptDataCardID,
                  m.Rate,
                  m.Amount,
                  GroupQty = m.Total
              })
           })
         .OrderBy(x => x.zone.OrderFlag)
         .ThenBy(x => x.space.OrderFlag)
         .ThenBy(x => x.element.OrderFlag)
         .SelectMany(join => join.measurements,
         (a, b) => new
         {
             a.zone,
             a.space,
             a.element,
             a.item,
             a.remark,
             mmt = b
         })
        .Select(x => new
        {
            Zone = x.zone.Code + "-" + x.zone.Title.ToUpper(),
            Space = x.space.Code + "-" + x.space.Title.ToUpper(),
            Element = x.element.Code + "-" + x.element.Title.ToUpper(),
            ItemCategory = x.item.Category.ToUpper(),
            ItemSubCategory = x.item.SubCategory,
            ItemGroup = x.item.ItemGroup,
            ItemTitle = x.item.Title,
            ItemSpecification = x.item.Specification,
            ItemDSR = x.item.DSR,
            ItemDSRNumber = x.item.DSRNumber,
            ItemCode = x.item.Code,
            ItemCategoryOrder = x.item.CategoryOrderFlag,
            ItemSubCategoryOrder = x.item.SubCategoryOrderFlag,
            ItemOrder = x.item.OrderFlag,
            Remark = x.remark,
            MUnit = x.item.Units,
            MGroupQty = x.mmt != null ? x.mmt.GroupQty : 0,
            MRate = x.mmt != null && !hideRate ? x.mmt.Rate : 0,
            MAmount = x.mmt != null && !hideRate ? x.mmt.Amount : 0,
            DesignScriptDataCardID = x.mmt != null ? x.mmt.DesignScriptDataCardID : null
        })
         .GroupJoin(dataCards,
                        x => x.DesignScriptDataCardID,
                        y => y.ID,
                        (x, y) => new
                        {

                            mmt = x,
                            dc = y.DefaultIfEmpty()
                        })
                         .SelectMany(join => join.dc.DefaultIfEmpty(), (a, b) => new
                         {

                             mmt = a.mmt,
                             dc = b
                         });



        var reportData = _data
             .Select(x => new
             {
                 x.mmt.Zone,
                 x.mmt.Space,
                 x.mmt.Element,
                 x.mmt.ItemCategory,
                 x.mmt.ItemSubCategory,
                 x.mmt.ItemGroup,
                 x.mmt.ItemTitle,
                 x.mmt.ItemSpecification,
                 x.mmt.ItemDSR,
                 x.mmt.ItemDSRNumber,
                 x.mmt.ItemCode,
                 x.mmt.ItemCategoryOrder,
                 x.mmt.ItemSubCategoryOrder,
                 x.mmt.ItemOrder,
                 ItemCategoryPrefix = McvConstant.DESIGN_SCRIPT_CATEGORY_ABBREIVIATIONS.FirstOrDefault(c => c.Key.ToUpper() == x.mmt.ItemCategory.ToUpper()).Value,
                 x.mmt.Remark,
                 x.mmt.MUnit,
                 x.mmt.MGroupQty,
                 x.mmt.MRate,
                 x.mmt.MAmount,
                 MDTitle = x.dc != null ? x.dc.Title : null,
                 MDSubtitle = x.dc != null ? x.dc.Subtitle : null,
                 MDDescription = x.dc != null ? x.dc.Description : null,
                 MDGFCTag = x.dc != null ? x.dc.GFCTag : null,
                 MDUrl = x.dc != null && x.dc.Attachments.Where(a => !a.IsDeleted).Where(a => a.ThumbUrl != null).Any() ? x.dc.Attachments.Where(a => !a.IsDeleted).Where(a => a.ThumbUrl != null).FirstOrDefault().ThumbUrl : null,
                 MDAttributes = x.dc != null && x.dc.Attributes.Where(a => !a.IsDeleted && !a.IsHidden && a.AttributeValue != null).Any() ? string.Join(",\n", x.dc.Attributes.Where(a => !a.IsDeleted && !a.IsHidden && a.AttributeValue != null).Select(a => a.AttributeKey + ": " + a.AttributeValue)) : null,
             })
               .OrderBy(x => x.ItemCategoryOrder)
                .ThenBy(x => x.ItemSubCategoryOrder)
                .ThenBy(x => x.ItemOrder)
             .ToList();

        var _reportProperties = new List<ReportProperties>
            {
                new ReportProperties() { PropertyName = "Title", PropertyValue = $"{project.Code}-{project.Title}" },
                new ReportProperties() { PropertyName = "Filter_Categories", PropertyValue = $"{string.Join(", ",filteredCategories)}" },
                  new ReportProperties() { PropertyName = "Filter_Zones", PropertyValue = $"{string.Join(", ",filteredZones.Select(x=> x.Code+"-"+x.Title))}" },
                     new ReportProperties() { PropertyName = "AutoPageNumber", PropertyValue = $"{AutoPageNumber}" },
                     new ReportProperties() { PropertyName = "TotalPages", PropertyValue = $"{TotalPages}" },
                        new ReportProperties() { PropertyName = "LastPageNumber", PropertyValue = $"{LastPageNumber}" },
                         new ReportProperties() { PropertyName = "Contingency", PropertyValue = $"{_masterPhase.EstimateContingency}" },
                new ReportProperties() { PropertyName = "GST", PropertyValue = $"{_masterPhase.EstimateGST}" },
            new ReportProperties() { PropertyName = "Message", PropertyValue = $"{(!hideRate ? "All prices shown are in Indian Rupee (₹). This is a bill of quantities, not a quote. The rates mentioned are subject to change." : "")}"},
                  new ReportProperties() { PropertyName = "HideRate", PropertyValue = $"{hideRate}" },
                  new ReportProperties() { PropertyName = "ShowElements", PropertyValue = $"{showElements}" },
            };
        var sharedService = new SharedService(db); ;
        var _reportContainerUrl = await sharedService.GetPresetValue(McvConstant.RDLC_REPORT_CONTAINER_URL);
        var DEVMODE = Convert.ToBoolean((await sharedService.GetPresetValue(McvConstant.DEVMODE)));

        var _reportPath = $"BOQSummary-{reportSize}.rdlc";

        var _reportDef = new ReportDefinition()
        {
            ReportName = $"BOQSummary",
            ReportPath = DEVMODE ? $"{_reportContainerUrl}DEV/{_reportPath}" : $"{_reportContainerUrl}{_reportPath}",
            ReportDataSet = DataTools.ToDataTable(reportData),
            ReportProperties = _reportProperties,
            Filename = $"BOQSummary-{project.Code}-{project.Title}-{ClockTools.GetISTNow().ToString("yyMMddHHmm")}",
            RenderType = renderType
        };

        var reportServiceApi = await sharedService.GetPresetValue(McvConstant.RDLC_PROCESSOR_API);
        return await ReportClient.GenerateReport(_reportDef, reportServiceApi);

    }

    public async Task<ReportDefinition> GetZoneBOQSummary(Guid projectUID, string reportSize = "a4", IEnumerable<QueryFilter> filters = null, string renderType = "PDF", bool AutoPageNumber = true,
          int TotalPages = 0,
int LastPageNumber = 0)
    {

        var projectService = new ProjectService(db);
        var project = await projectService.Get()
                                           .Where(x => x.UID == projectUID)
                                           .SingleOrDefaultAsync();

        if (project == null) throw new EntityServiceException("Project not found!");

        var entities = await Get()
                .Where(x => x.ProjectID == project.ID && x.TypeFlag == McvConstant.DESIGN_SCRIPT_ENTITY_TYPEFLAG_ZONE)
                 .Include(x => x.ItemMaps).ThenInclude(c => c.DesignScriptItem)
                   .Include(x => x.ItemMaps).ThenInclude(c => c.MeasurementGroups)
               .ToListAsync();

        var _masterPhase = entities
                         .Where(x => x.ProjectID == project.ID && x.TypeFlag == McvConstant.DESIGN_SCRIPT_ENTITY_TYPEFLAG_ZONE
                         && x.isMasterPhase).SingleOrDefault();

        if (_masterPhase == null) throw new EntityServiceException("Master phase not found!");
        var filteredZones = entities
         .Where(x => x.ParentID == null && x.TypeFlag == McvConstant.DESIGN_SCRIPT_ENTITY_TYPEFLAG_ZONE)
         .ToList();


        var filteredCategories = await db.DesignScriptDataCards.AsNoTracking()
            
                                            .Where(x => !x.IsHidden)
                                            .Where(x => x.Category != null && x.Category != string.Empty)
                                            .Where(x => x.TypeFlag == McvConstant.DESIGN_SCRIPT_DATA_CARD_TYPEFLAG_MATERIAL)
                                            .Select(x => x.Category)
                                            .Distinct()
                                            .ToListAsync();
        var hideRate = false;
        var showElements = false;

        if (filters != null)
        {
            if (filters.Where(x => x.Key.Equals("zone", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var _zoneUIDs = filters.Where(x => x.Key.Equals("zone", StringComparison.OrdinalIgnoreCase)).Select(x => Guid.Parse(x.Value.Trim()));

                filteredZones = filteredZones.Where(x => _zoneUIDs.Any(z => z == x.UID)).ToList();
            }

            if (filters.Where(x => x.Key.Equals("category", StringComparison.OrdinalIgnoreCase)).Any())
            {
                filteredCategories = filters.Where(x => x.Key.Equals("category", StringComparison.OrdinalIgnoreCase)).Select(x => x.Value.Trim()).ToList();
            }

            if (filters.Where(x => x.Key.Equals("hideRate", StringComparison.OrdinalIgnoreCase)).Any())
            {
                hideRate = Convert.ToBoolean(filters.FirstOrDefault(x => x.Key.Equals("hideRate", StringComparison.OrdinalIgnoreCase)).Value);
            }
            if (filters.Where(x => x.Key.Equals("showElements", StringComparison.OrdinalIgnoreCase)).Any())
            {
                showElements = Convert.ToBoolean(filters.FirstOrDefault(x => x.Key.Equals("showElements", StringComparison.OrdinalIgnoreCase)).Value);
            }
        }

        var _entityIDs = filteredZones.Select(x => x.ID);
        var _designScriptEntityIds = _entityIDs.ToList();
        //while (_entityIDs.Any())
        //{
        //    _entityIDs = GetRecursiveChildrenIDs(entities, _entityIDs);
        //    _designScriptEntityIds = _designScriptEntityIds.Concat(_entityIDs).ToList();
        //}

        if (_designScriptEntityIds != null && _designScriptEntityIds.Any())
        {
            entities = entities.Where(x => _designScriptEntityIds.Any(c => c == x.ID)).ToList();
        }





        var dataCards = await db.DesignScriptDataCards.AsNoTracking()
            
                 .Where(x => !x.IsHidden && filteredCategories.Any(c => c == x.Category) && x.ProjectID == project.ID)
                 .ToListAsync();

        var _data = entities
            .Where(x => _designScriptEntityIds.Any(i => i == x.ID)) //Filtered ZONES

      //.Join(entities, zone => zone.ID, space => space.ParentID, (a, b) => new { zone = a, space = b }) //SPACE

      //.Join(entities, ps => ps.space.ID, e => e.ParentID, (ps, e) => new { ps.zone, ps.space, element = e }) //ELEMENT

      .SelectMany(join => join.ItemMaps.Where(x => !x.IsDeleted && filteredCategories.Any(c => c == x.DesignScriptItem.Category)),
           (a, b) => new
           {
               zone = a,
               //space = a.space,
               //element = a.element,
               item = b.DesignScriptItem,
               remark = b.CostingRemark,
               measurements = b.MeasurementGroups
              .Where(m => !m.IsDeleted)
              .Select(m => new
              {
                  m.DesignScriptEntityItemMapID,
                  m.DesignScriptDataCardID,
                  m.Rate,
                  m.Amount,
                  GroupQty = m.Total
              })
           })
         .OrderBy(x => x.zone.OrderFlag)
         //.ThenBy(x => x.space.OrderFlag)
         //.ThenBy(x => x.element.OrderFlag)
         .SelectMany(join => join.measurements,
         (a, b) => new
         {
             a.zone,
             //a.space,
             //a.element,
             a.item,
             a.remark,
             mmt = b
         })
        .Select(x => new
        {
            Zone = x.zone.Code + "-" + x.zone.Title.ToUpper(),
            //Space = x.space.Code + "-" + x.space.Title.ToUpper(),
            //Element = x.element.Code + "-" + x.element.Title.ToUpper(),
            ItemCategory = x.item.Category.ToUpper(),
            ItemSubCategory = x.item.SubCategory,
            ItemGroup = x.item.ItemGroup,
            ItemTitle = x.item.Title,
            ItemSpecification = x.item.Specification,
            ItemDSR = x.item.DSR,
            ItemDSRNumber = x.item.DSRNumber,
            ItemCode = x.item.Code,
            ItemCategoryOrder = x.item.CategoryOrderFlag,
            ItemSubCategoryOrder = x.item.SubCategoryOrderFlag,
            ItemOrder = x.item.OrderFlag,
            Remark = x.remark,
            MUnit = x.item.Units,
            MGroupQty = x.mmt != null ? x.mmt.GroupQty : 0,
            MRate = x.mmt != null && !hideRate ? x.mmt.Rate : 0,
            MAmount = x.mmt != null && !hideRate ? x.mmt.Amount : 0,
            DesignScriptDataCardID = x.mmt != null ? x.mmt.DesignScriptDataCardID : null
        })
         .GroupJoin(dataCards,
                        x => x.DesignScriptDataCardID,
                        y => y.ID,
                        (x, y) => new
                        {

                            mmt = x,
                            dc = y.DefaultIfEmpty()
                        })
                         .SelectMany(join => join.dc.DefaultIfEmpty(), (a, b) => new
                         {

                             mmt = a.mmt,
                             dc = b
                         });



        var reportData = _data
             .Select(x => new
             {
                 x.mmt.Zone,
                 //x.mmt.Space,
                 //x.mmt.Element,
                 x.mmt.ItemCategory,
                 x.mmt.ItemSubCategory,
                 x.mmt.ItemGroup,
                 x.mmt.ItemTitle,
                 x.mmt.ItemSpecification,
                 x.mmt.ItemDSR,
                 x.mmt.ItemDSRNumber,
                 x.mmt.ItemCode,
                 x.mmt.ItemCategoryOrder,
                 x.mmt.ItemSubCategoryOrder,
                 x.mmt.ItemOrder,
                 ItemCategoryPrefix = McvConstant.DESIGN_SCRIPT_CATEGORY_ABBREIVIATIONS.FirstOrDefault(c => c.Key.ToUpper() == x.mmt.ItemCategory.ToUpper()).Value,
                 x.mmt.Remark,
                 x.mmt.MUnit,
                 x.mmt.MGroupQty,
                 x.mmt.MRate,
                 x.mmt.MAmount,
                 MDTitle = x.dc != null ? x.dc.Title : null,
                 MDSubtitle = x.dc != null ? x.dc.Subtitle : null,
                 MDDescription = x.dc != null ? x.dc.Description : null,
                 MDGFCTag = x.dc != null ? x.dc.GFCTag : null,
                 MDUrl = x.dc != null && x.dc.Attachments.Where(a => !a.IsDeleted).Where(a => a.ThumbUrl != null).Any() ? x.dc.Attachments.Where(a => !a.IsDeleted).Where(a => a.ThumbUrl != null).FirstOrDefault().ThumbUrl : null,
                 MDAttributes = x.dc != null && x.dc.Attributes.Where(a => !a.IsDeleted && !a.IsHidden && a.AttributeValue != null).Any() ? string.Join(",\n", x.dc.Attributes.Where(a => !a.IsDeleted && !a.IsHidden && a.AttributeValue != null).Select(a => a.AttributeKey + ": " + a.AttributeValue)) : null,
             })
                        .OrderBy(x => x.ItemCategoryOrder)
                .ThenBy(x => x.ItemSubCategoryOrder)
                .ThenBy(x => x.ItemOrder)
             .ToList();

        var _reportProperties = new List<ReportProperties>
            {
                new ReportProperties() { PropertyName = "Title", PropertyValue = $"{project.Code}-{project.Title}" },
                new ReportProperties() { PropertyName = "Filter_Categories", PropertyValue = $"{string.Join(", ",filteredCategories)}" },
                  new ReportProperties() { PropertyName = "Filter_Zones", PropertyValue = $"{string.Join(", ",filteredZones.Select(x=> x.Code+"-"+x.Title))}" },
                     new ReportProperties() { PropertyName = "AutoPageNumber", PropertyValue = $"{AutoPageNumber}" },
                     new ReportProperties() { PropertyName = "TotalPages", PropertyValue = $"{TotalPages}" },
                        new ReportProperties() { PropertyName = "LastPageNumber", PropertyValue = $"{LastPageNumber}" },
                         new ReportProperties() { PropertyName = "Contingency", PropertyValue = $"{_masterPhase.EstimateContingency}" },
                new ReportProperties() { PropertyName = "GST", PropertyValue = $"{_masterPhase.EstimateGST}" },
                new ReportProperties() { PropertyName = "Message", PropertyValue = "All prices shown are in Indian Rupee (₹). This is a bill of quantities, not a quote. The rates mentioned are subject to change." },
                  new ReportProperties() { PropertyName = "HideRate", PropertyValue = $"{hideRate}" },
                  new ReportProperties() { PropertyName = "ShowElements", PropertyValue = $"{showElements}" },
            };
        var sharedService = new SharedService(db); ;
        var _reportContainerUrl = await sharedService.GetPresetValue(McvConstant.RDLC_REPORT_CONTAINER_URL);
        var DEVMODE = Convert.ToBoolean((await sharedService.GetPresetValue(McvConstant.DEVMODE)));

        var _reportPath = $"BOQSummary-{reportSize}.rdlc";

        var _reportDef = new ReportDefinition()
        {
            ReportName = $"BOQSummary",
            ReportPath = DEVMODE ? $"{_reportContainerUrl}DEV/{_reportPath}" : $"{_reportContainerUrl}{_reportPath}",
            ReportDataSet = DataTools.ToDataTable(reportData),
            ReportProperties = _reportProperties,
            Filename = $"BOQSummary-{project.Code}-{project.Title}-{ClockTools.GetISTNow().ToString("yyMMddHHmm")}",
            RenderType = renderType
        };

        var reportServiceApi = await sharedService.GetPresetValue(McvConstant.RDLC_PROCESSOR_API);
        return await ReportClient.GenerateReport(_reportDef, reportServiceApi);

    }

    public async Task<ReportDefinition> GetElementEstimatedBOQ(Guid projectUID, string reportSize = "a4", IEnumerable<QueryFilter> filters = null, string renderType = "PDF", bool AutoPageNumber = true,
           int TotalPages = 0,
int LastPageNumber = 0)
    {
        var _filters = filters != null ? filters.ToList() : new List<QueryFilter>();

        _filters.Add(new QueryFilter { Key = "showRateAmount", Value = "true" });


        var _reportDef = await GetElementBOQ(projectUID, reportSize, _filters, renderType, AutoPageNumber, TotalPages, LastPageNumber, "ESTIMATED BILL OF QUANTITIES [E]", "ESTIMATED-EBOQ");

        //_reportDef.Filename = "Estimated BOQ-Element Level-" + _reportDef.Filename;

        return _reportDef;

    }

    public async Task<ReportDefinition> GetZoneEstimatedBOQ(Guid projectUID, string reportSize = "a4", IEnumerable<QueryFilter> filters = null, string renderType = "PDF", bool AutoPageNumber = true,
          int TotalPages = 0,
int LastPageNumber = 0)
    {
        var _filters = filters != null ? filters.ToList() : new List<QueryFilter>();

        _filters.Add(new QueryFilter { Key = "showRateAmount", Value = "true" });


        var _reportDef = await GetZoneBOQ(projectUID, reportSize, _filters, renderType, AutoPageNumber, TotalPages, LastPageNumber, "ESTIMATED BILL OF QUANTITIES [Z]", "ESTIMATED-ZBOQ");

        //_reportDef.Filename = "Estimated BOQ-Zone Level-" + _reportDef.Filename;

        return _reportDef;

    }

    public async Task<byte[]> GetItemListExcel(
      Guid ProjectUID,
      IEnumerable<QueryFilter> filters = null)
    {
        var projectService = new ProjectService(db);
        var project = await projectService.Get()
              .Where(x => x.UID == ProjectUID)
              .SingleOrDefaultAsync();

        if (project == null) throw new EntityServiceException("Project not found!");



        IEnumerable<Guid> _phaseUIDs = null;
        IEnumerable<string> _categories = null;
        var showAttributes = false;

        if (filters != null)
        {
            if (filters.Where(x => x.Key.Equals("category", StringComparison.OrdinalIgnoreCase)).Any())
            {
                _categories = filters.Where(x => x.Key.Equals("category", StringComparison.OrdinalIgnoreCase)).Select(x => x.Value.Trim());
            }


            //if (filters.Where(x => x.Key.Equals("showAttributes", StringComparison.OrdinalIgnoreCase)).Any())
            //{
            //    showAttributes = filters.Where(x => x.Key.Equals("showAttributes", StringComparison.OrdinalIgnoreCase)).Select(x => Convert.ToBoolean(x.Value.Trim())).FirstOrDefault();
            //}

            if (filters.Where(x => x.Key.Equals("phase", StringComparison.OrdinalIgnoreCase)).Any())
            {
                _phaseUIDs = filters.Where(x => x.Key.Equals("phase", StringComparison.OrdinalIgnoreCase)).Select(x => Guid.Parse(x.Value.Trim()));
            }
        }

        if (_phaseUIDs == null)
        {
            _phaseUIDs = await Get()
            .Where(x => x.ProjectID == project.ID)
            .Where(x => x.ParentID == null && x.TypeFlag == McvConstant.DESIGN_SCRIPT_ENTITY_TYPEFLAG_ZONE)
            .Select(x => x.UID)
            .ToListAsync();
        }

        var designScriptEntities = Get()
                    .Where(x => x.ProjectID == project.ID);

        var _data = await designScriptEntities
        .Where(p => _phaseUIDs.Any(i => i == p.UID))
        .Join(designScriptEntities, p => p.ID, s => s.ParentID, (p, s) => new { p, s })
        .Join(designScriptEntities, ps => ps.s.ID, e => e.ParentID, (ps, e) => new { ps.p, ps.s, e })
        .Join(db.DesignScriptEntityItemMaps.AsNoTracking()
               .Include(x => x.DesignScriptItem),
             ese => ese.e.ID, qty => qty.DesignScriptEntityID, (ese, qty) => new { ese.p, ese.s, ese.e, qty })
           .OrderBy(ese => ese.p.OrderFlag)
        .ThenBy(ese => ese.s.OrderFlag)
        .ThenBy(ese => ese.e.OrderFlag)
        // If you need a left outer join, you can use the GroupJoin and SelectMany
        .GroupJoin(
                   db.DesignScriptMeasurementGroups.AsNoTracking()
                     
                 .Include(x => x.Measurements)
              //.SelectMany(join => join.Measurements,
              //(a, b) => new
              //{
              //    a.DesignScriptEntityItemMapID,
              //    a.DesignScriptDataCardID,
              //    b.Area,
              //    b.Number,
              //    b.Length,
              //    b.Breadth,
              //    b.Height,
              //    b.Quantity,
              //    b.Tag,
              //    b.Total,
              //    b.Percentage,
              //    b.CenterToCenter,
              //    b.TypeFlag,
              //    b.Unit
              //})
              ,
                   x => x.qty.ID,
                   y => y.DesignScriptEntityItemMapID,
                   (x, y) => new
                   {
                       p = x.p,
                       s = x.s,
                       e = x.e,
                       qty = x.qty,
                       mmt = y.DefaultIfEmpty()
                   })
                    .SelectMany(join => join.mmt.DefaultIfEmpty(), (a, b) => new
                    {
                        p = a.p,
                        s = a.s,
                        e = a.e,
                        qty = a.qty,
                        mmt = b
                    })
                    .GroupJoin(db.DesignScriptDataCards.AsNoTracking()
                   
                   .Where(x => !x.IsHidden)
                   .Include(x => x.Attachments),
                   x => x.mmt.DesignScriptDataCardID,
                   y => y.ID,
                   (x, y) => new
                   {
                       p = x.p,
                       s = x.s,
                       e = x.e,
                       qty = x.qty,
                       mmt = x.mmt,
                       dc = y.DefaultIfEmpty()
                   })
                    .SelectMany(join => join.dc.DefaultIfEmpty(), (a, b) => new
                    {
                        p = a.p,
                        s = a.s,
                        e = a.e,
                        qty = a.qty,
                        mmt = a.mmt,
                        dc = b
                    })
                        .Select(x => new
                        {
                            Zone = x.p.Code + "-" + x.p.Title.ToUpper(),
                            Space = x.s.Code + "-" + x.s.Title.ToUpper(),
                            Element = x.e.Code + "-" + x.e.Title.ToUpper(),
                            ItemCategory = x.qty != null ? x.qty.DesignScriptItem.Category.ToUpper() : null,
                            ItemGroup = x.qty != null ? x.qty.DesignScriptItem.ItemGroup : null,
                            ItemTitle = x.qty != null ? x.qty.DesignScriptItem.Title : null,
                            ItemSpecification = x.qty != null ? x.qty.DesignScriptItem.Specification : null,
                            ItemDSR = x.qty != null ? x.qty.DesignScriptItem.DSR : null,
                            ItemDSRNumber = x.qty != null ? x.qty.DesignScriptItem.DSRNumber : null,
                            ItemCode = x.qty != null ? x.qty.DesignScriptItem.Code : null,
                            ItemOrderFlag = x.qty != null ? x.qty.DesignScriptItem.OrderFlag : 0,
                            Quantity = x.mmt != null ? x.mmt.Total : 0,
                            Unit = x.mmt != null ? x.qty.DesignScriptItem.Units : "UNITS",
                            Rate = x.mmt != null ? x.mmt.Rate : 0,
                            Amount = x.mmt != null ? x.mmt.Amount : 0,
                            //Quantity = x.qty != null ? x.qty.CostingQuantity : 0m,
                            //Unit = x.qty != null ? x.qty.CostingUnit : "UNIT",
                            //Rate = x.qty != null ? x.qty.CostingRate : 0m,
                            //Amount = x.qty != null ? x.qty.CostingAmount : 0m,
                            //Remark = x.qty != null ? x.qty.CostingRemark : null,
                            //Measurements = x.qty != null ? x.qty.Measurements : null,
                            //MArea = x.mmt != null ? x.mmt.Area : (decimal?)null,
                            //MLength = x.mmt != null ? x.mmt.Length : (decimal?)null,
                            //MHeight = x.mmt != null ? x.mmt.Height : (decimal?)null,
                            //MBreadth = x.mmt != null ? x.mmt.Breadth : (decimal?)null,
                            //MCenterToCenter = x.mmt != null ? x.mmt.CenterToCenter : (decimal?)null,
                            //MPercentage = x.mmt != null ? x.mmt.Percentage : (decimal?)null,
                            //MNumber = x.mmt != null ? x.mmt.Number : (decimal?)null,
                            //MTag = x.mmt != null ? x.mmt.Tag : null,
                            //MType = x.mmt != null ? x.mmt.TypeFlag : 1,
                            //MTotal = x.mmt != null ? x.mmt.Total : 0,
                            //MQuantity = x.mmt != null ? x.mmt.Quantity : 0,
                            //MUnit=x.mmt!=null ? x.mmt.Unit : "",

                            MDTitle = x.dc != null ? x.dc.Title : null,
                            MDSubtitle = x.dc != null ? x.dc.Subtitle : null,
                            MDDescription = x.dc != null ? x.dc.Description : null,
                            MDGFCTag = x.dc != null ? x.dc.GFCTag : null,
                            MDUrl = x.dc != null && x.dc.Attachments.Where(a => !a.IsDeleted).Where(a => a.ThumbUrl != null).Any() ? x.dc.Attachments.Where(a => !a.IsDeleted).Where(a => a.ThumbUrl != null).FirstOrDefault().ThumbUrl : null,


                        }).OrderBy(x => x.Zone).ThenBy(x => x.Space).ThenBy(x => x.Element)
                        .ThenBy(x => x.ItemCode)
                   .ToListAsync();

        var _dataSet = new DataSet();
        _dataSet.Tables.Add(DataTools.ToDataTable(_data));

        return ExcelUtility.ExportExcel(_dataSet);

    }

    public async Task<byte[]> GetScheduleExcel(Guid projectUID, IEnumerable<QueryFilter> filters = null)
    {

        var projectService = new ProjectService(db);

        var project = await projectService.Get()
                                           .Where(x => x.UID == projectUID)
                                           .SingleOrDefaultAsync();

        if (project == null) throw new EntityServiceException("Project not found!");

        var projectEntities = await Get()
                .Where(x => x.ProjectID == project.ID)
                         .Include(x => x.DataCardMaps) // linked datacards
                         .Include(x => x.ItemMaps) //linked items
                .ToListAsync();

        var filteredZones = projectEntities
                .Where(x => x.ParentID == null && x.TypeFlag == McvConstant.DESIGN_SCRIPT_ENTITY_TYPEFLAG_ZONE)
                .ToList();


        var filteredCategories = await db.DesignScriptDataCards.AsNoTracking()
            
                                            .Where(x => !x.IsHidden)
                                            .Where(x => x.Category != null && x.Category != string.Empty)
                                            .Where(x => x.TypeFlag == McvConstant.DESIGN_SCRIPT_DATA_CARD_TYPEFLAG_MATERIAL)
                                            .Select(x => x.Category)
                                            .Distinct()
                                            .ToListAsync();

        if (filters != null)
        {

            if (filters.Where(x => x.Key.Equals("zone", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var _zoneUIDs = filters.Where(x => x.Key.Equals("zone", StringComparison.OrdinalIgnoreCase)).Select(x => Guid.Parse(x.Value.Trim()));

                filteredZones = filteredZones.Where(x => _zoneUIDs.Any(z => z == x.UID)).ToList();
            }

            if (filters.Where(x => x.Key.Equals("category", StringComparison.OrdinalIgnoreCase)).Any())
            {
                filteredCategories = filters.Where(x => x.Key.Equals("category", StringComparison.OrdinalIgnoreCase)).Select(x => x.Value.Trim()).ToList();
            }


        }

        var _entityIDs = filteredZones.Select(x => x.ID);
        var _designScriptEntityIds = _entityIDs.ToList();

        while (_entityIDs.Any())
        {
            _entityIDs = await GetRecursiveChildrenIDsAsync(_entityIDs);
            _designScriptEntityIds = _designScriptEntityIds.Concat(_entityIDs).ToList();
        }

        var filteredEntities = projectEntities
            .Where(x => _designScriptEntityIds.Any(c => c == x.ID));

        var projectDataCards = await db.DesignScriptDataCards.AsNoTracking()
            
            .Where(x => !x.IsHidden)
                                  .Where(x => x.TypeFlag == McvConstant.DESIGN_SCRIPT_DATA_CARD_TYPEFLAG_MATERIAL)
                                  .Where(x => x.ProjectID == project.ID)
                                  .Where(x => x.Category != null && x.Category != string.Empty)
                                    .Where(x => x.Title != null && x.Title != string.Empty)
                                    .Where(x => filteredCategories.Any(c => c == x.Category))
                                    .Select(x => new
                                    {
                                        x.ID,
                                        x.Title,
                                        x.Subtitle,
                                        x.GFCTag
                                    })
                  .ToListAsync();

        var entityDataCardMaps = filteredEntities
            .SelectMany(join => join.DataCardMaps, (a, b) => b.DesignScriptDataCardID)
            .Distinct();

        var products = projectDataCards
                .Where(x => entityDataCardMaps.Any(m => m == x.ID))
                .Select(x => new
                {
                    GFCTag = x.GFCTag,
                    Title = x.Title,
                    Subtitle = x.Subtitle,
                })
                .OrderBy(x => x.GFCTag);

        var projectItems = await db.DesignScriptItems.AsNoTracking()
            
                                 .Where(x => x.ProjectID == project.ID)
                                   .Where(x => filteredCategories.Any(c => c == x.Category))
                                   .OrderBy(x => x.OrderFlag)
                                   .Select(x => new
                                   {
                                       x.ID,
                                       x.Code,
                                       x.OrderFlag,
                                       x.Category,
                                       x.ItemGroup,
                                       x.Title,
                                       x.Specification,
                                       x.DrawingSpecification
                                   })
                 .ToListAsync();

        var entityItemMaps = filteredEntities
            .SelectMany(join => join.ItemMaps, (a, b) => b.DesignScriptItemID)
            .Distinct();

        var items = projectItems
                .Where(x => entityItemMaps.Any(m => m == x.ID))
                .OrderBy(x => x.OrderFlag)
                .Select(x => new
                {
                    Code = x.Code,
                    BOQ = $"{x.Code} | {x.ItemGroup} | {x.Title} | {x.Specification}",
                    Drawing = $"{x.Code} | {x.ItemGroup} | {x.Title} | {x.DrawingSpecification}"
                });

        var _dataSet = new DataSet();
        _dataSet.Tables.Add(DataTools.ToDataTable(products));
        _dataSet.Tables.Add(DataTools.ToDataTable(items));
        var sheets = new string[]
        {
            "Products",
            "Items"
        };
        return ExcelUtility.ExportExcel(_dataSet, sheets);

    }
}


public class ScriptItem
{
    public Guid ID { get; set; }
    public string Code { get; set; }
    public string Title { get; set; }
    public string Description { get; set; }
    public decimal? OrderFlag { get; set; }
    public decimal? TypeFlag { get; set; }
    public int? ParentID { get; set; }
    public string TypeValue { get; set; }
    public string AttachmentUrl { get; set; }
    public string AttachmentDescription { get; set; }
    public string AttachmentAttributes { get; set; }
    public string AttachmentCategories { get; set; }
    public decimal? AttachmentOrderFlag { get; set; }
}

public class ReportDesignScriptEntity
{
    public int ID { get; set; }
    public string Code { get; set; }
    public string Title { get; set; }
    public string Description { get; set; }
    public decimal? OrderFlag { get; set; }
    public decimal? TypeFlag { get; set; }
    public int? ParentID { get; set; }
    public string TypeValue { get; set; }
}

public class DesignIntentReportData
{
    public int ID { get; set; }
    public string Code { get; set; }
    public string Title { get; set; }
    public string Description { get; set; }
    public decimal? OrderFlag { get; set; }
    public int TypeFlag { get; set; }
    public int? ParentID { get; set; }
    public string TypeValue { get; set; }
}
public class ReportDataCardCategory
{
    public string Category { get; set; }
}
    public class ReportDataCard
{
    public int ID { get; set; }
    public string Category { get; set; }
    public string Title { get; set; }
    public string Subtitle { get; set; }
    public string Description { get; set; }
    public string Tags { get; set; }

    public int? EntityID { get; set; }

    public string GFCTag { get; set; }

    public bool HideImages { get; set; }
    public bool HideAttributes { get; set; }
    public bool HideEntities { get; set; }
}

public class ReportDataCardImage
{
    public int DataCardID { get; set; }
    public string Url { get; set; }
}
public class ReportDataCardAttachment
{
    public int DataCardID { get; set; }
    public string Url { get; set; }

    public string Filename { get; set; }
}
public class ReportDataCardAttribute
{
    public int DataCardID { get; set; }
    public string AttributeKey { get; set; }
    public string AttributeValue { get; set; }
}

public class ReportDataCardEntity
{
    public int DataCardID { get; set; }
    public string EntityCode { get; set; }
    public string EntityTitle { get; set; }
}

public class ReportMaterialListDataCard
{
    public int ID { get; set; }
    public string Category { get; set; }
    public string Title { get; set; }
    public string Subtitle { get; set; }
    public string Description { get; set; }
    public string Tags { get; set; }

    public string GFCTag { get; set; }
    public int OrderFlag { get; set; }
}

public class PackageCoverItem
{
    public int Page { get; set; }

    public string Title { get; set; }
}