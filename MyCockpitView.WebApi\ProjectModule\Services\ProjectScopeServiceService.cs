﻿using Microsoft.EntityFrameworkCore;
using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.ProjectModule.Entities;
using MyCockpitView.WebApi.Services;


using System.Data;


using System.Text;


namespace MyCockpitView.WebApi.ProjectModule.Services;

public class ProjectScopeServiceService : BaseEntityService<ProjectScopeService>, IProjectScopeServiceService
{
    public ProjectScopeServiceService(EntitiesContext db) : base(db) { }

    public IQueryable<ProjectScopeService> Get(IEnumerable<QueryFilter>? Filters = null, string? Search = null, string? Sort = null)
    {

        IQueryable<ProjectScopeService> _query = base.Get(Filters);

        //Apply filters
        if (Filters != null)
        {
            if (Filters.Where(x => x.Key.Equals("statusFlag", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<ProjectScopeService>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("statusFlag", StringComparison.OrdinalIgnoreCase)))
                {
                    var isNumeric = Convert.ToInt32(_item.Value);

                    predicate = predicate.Or(x => x.StatusFlag == isNumeric);
                }
                _query = _query.Where(predicate);
            }

            if (Filters.Where(x => x.Key.Equals("typeFlag", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<ProjectScopeService>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("typeFlag", StringComparison.OrdinalIgnoreCase)))
                {
                    var isNumeric = Convert.ToInt32(_item.Value);

                    predicate = predicate.Or(x => x.TypeFlag == isNumeric);
                }
                _query = _query.Where(predicate);
            }
            if (Filters.Where(x => x.Key.Equals("projectID", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<ProjectScopeService>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("projectID", StringComparison.OrdinalIgnoreCase)))
                {
                    var isNumeric = Convert.ToInt32(_item.Value);

                    predicate = predicate.Or(x => x.ProjectID == isNumeric);
                }
                _query = _query.Where(predicate);
            }
            if (Filters.Where(x => x.Key.Equals("ProjectScopeID", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<ProjectScopeService>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("ProjectScopeID", StringComparison.OrdinalIgnoreCase)))
                {
                    var isNumeric = Convert.ToInt32(_item.Value);

                    predicate = predicate.Or(x => x.ProjectScopeID == isNumeric);
                }
                _query = _query.Where(predicate);
            }

        }

        if (Search != null && Search != String.Empty)
        {
            Search = Search.ToLower();
            _query = _query
                 .Where(x => x.Title.ToLower().Contains(Search.ToLower())
                                           );

        }

        if (Sort != null && Sort != String.Empty)
        {
            switch (Sort.ToLower())
            {
                case "createddate":
                    return _query
                            .OrderBy(x => x.Created);

                case "modifieddate":
                    return _query
                            .OrderBy(x => x.Modified);

                case "createddate desc":
                    return _query
                            .OrderByDescending(x => x.Created);

                case "modifieddate desc":
                    return _query
                            .OrderByDescending(x => x.Modified);

                case "amount":
                    return _query
                            .OrderBy(x => x.Amount);

                case "amount desc":
                    return _query
                            .OrderByDescending(x => x.Amount);

                case "sharePercentage":
                    return _query
                            .OrderBy(x => x.SharePercentage);

                case "sharePercentage desc":
                    return _query
                            .OrderByDescending(x => x.SharePercentage);
            }
        }

        return _query.OrderBy(x => x.OrderFlag);

    }


    public async Task<decimal> GetNextOrder(int ProjectID, int ScopeID)
    {

        var _last = await db.ProjectScopeServices.AsNoTracking()
            .Where(x => x.ProjectID == ProjectID && x.ProjectScopeID == ScopeID)
            .OrderByDescending(x => x.OrderFlag).FirstOrDefaultAsync();
        if (_last == null) return 1;
        else return _last.OrderFlag + 1.0m;

    }

    private string GetAbbreviation(string title)
    {
        if (string.IsNullOrEmpty(title))
        {
            return "";
        }

        StringBuilder abbreviation = new StringBuilder();

        // Split the title into words
        string[] words = title.Split(' ');

        // Take the first letter of each word and append to the abbreviation
        foreach (string word in words)
        {
            if (!string.IsNullOrEmpty(word))
            {
                abbreviation.Append(word[0]);
            }
        }

        return abbreviation.ToString().ToUpper(); // You can convert to uppercase if needed
    }

}