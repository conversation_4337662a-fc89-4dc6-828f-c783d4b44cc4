using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.WebApi.AuthModule.Services;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;

namespace MyCockpitView.WebApi.AuthModule.Middleware;

public class SessionValidationMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<SessionValidationMiddleware> _logger;

    public SessionValidationMiddleware(RequestDelegate next, ILogger<SessionValidationMiddleware> logger)
    {
        _next = next;
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context, ILoginSessionService loginSessionService)
    {
        // Skip validation for non-authenticated endpoints
        var endpoint = context.GetEndpoint();
        var authorizeAttribute = endpoint?.Metadata?.GetMetadata<AuthorizeAttribute>();
        
        if (authorizeAttribute == null)
        {
            await _next(context);
            return;
        }

        // Skip validation for auth endpoints
        if (context.Request.Path.StartsWithSegments("/Auth"))
        {
            await _next(context);
            return;
        }

        // Get the authorization header
        var authHeader = context.Request.Headers["Authorization"].FirstOrDefault();
        if (authHeader != null && authHeader.StartsWith("Bearer "))
        {
            var token = authHeader.Substring("Bearer ".Length).Trim();
            
            try
            {
                // Parse the JWT token to get claims
                var handler = new JwtSecurityTokenHandler();
                var jsonToken = handler.ReadJwtToken(token);
                
                var usernameClaim = jsonToken.Claims.FirstOrDefault(x => x.Type == ClaimTypes.Name);
                if (usernameClaim != null)
                {
                    var username = usernameClaim.Value;
                    
                    // Validate session in database
                    var activeSession = await loginSessionService.Get()
                        .Where(x => x.Username == username && x.IsActive)
                        .OrderByDescending(x => x.Created)
                        .FirstOrDefaultAsync();

                    if (activeSession == null)
                    {
                        _logger.LogWarning("No active session found for user: {Username}", username);
                        context.Response.StatusCode = 401;
                        await context.Response.WriteAsync("Session expired. Please login again.");
                        return;
                    }

                    // Check if session requires OTP verification
                    if (activeSession.IsOTPRequired && !activeSession.IsOTPVerified)
                    {
                        _logger.LogWarning("OTP verification required for user: {Username}", username);
                        context.Response.StatusCode = 401;
                        await context.Response.WriteAsync("OTP verification required.");
                        return;
                    }

                    // Validate token matches session
                    if (!string.IsNullOrEmpty(activeSession.Token) && activeSession.Token != token)
                    {
                        _logger.LogWarning("Token mismatch for user: {Username}", username);
                        context.Response.StatusCode = 401;
                        await context.Response.WriteAsync("Invalid session token.");
                        return;
                    }

                    // Update session activity
                    activeSession.Modified = DateTime.UtcNow;
                    await loginSessionService.Update(activeSession);

                    // Set user context for downstream services
                    var claims = new List<Claim>
                    {
                        new Claim(ClaimTypes.Name, username),
                        new Claim(ClaimTypes.NameIdentifier, activeSession.ContactID.ToString()),
                        new Claim("SessionId", activeSession.UID.ToString()),
                        new Claim("ContactId", activeSession.ContactID.ToString())
                    };

                    var identity = new ClaimsIdentity(claims, "SessionValidation");
                    context.User = new ClaimsPrincipal(identity);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating session for token");
                context.Response.StatusCode = 401;
                await context.Response.WriteAsync("Invalid token.");
                return;
            }
        }

        await _next(context);
    }
}

public static class SessionValidationMiddlewareExtensions
{
    public static IApplicationBuilder UseSessionValidation(this IApplicationBuilder builder)
    {
        return builder.UseMiddleware<SessionValidationMiddleware>();
    }
}
