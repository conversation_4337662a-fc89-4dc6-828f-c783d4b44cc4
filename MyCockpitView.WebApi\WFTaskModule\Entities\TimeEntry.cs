
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.WebApi.ContactModule.Entities;
using MyCockpitView.CoreModule;
using System.ComponentModel.DataAnnotations;


namespace MyCockpitView.WebApi.WFTaskModule.Entities;
public class TimeEntry : BaseEntity
{

    public int ContactID { get; set; }
    public virtual Contact? Contact { get; set; }
    [StringLength(255)]
    public string? TaskTitle { get; set; }
    
    [Precision(18,2)] public decimal ManHours { get; set; }


    public DateTime StartDate { get; set; }


    public DateTime? EndDate { get; set; }

    public int? WFTaskID { get; set; }
    public virtual WFTask? WFTask { get; set; }

    public bool IsPaused { get; set; }

    public int? ProjectID { get; set; }
    public int? CompanyID { get; set; }
    
    //[Precision(18,2)] public decimal ManValue { get; set; }
    
    //[Precision(18,2)] public decimal ValueHourRate { get; set; } = 0;
    [StringLength(255)]
    public string? Entity { get; set; }
    public int? EntityID { get; set; }
    [StringLength(255)]
    public string? EntityTitle { get; set; }

}

public class TimeEntryConfiguration : BaseEntityConfiguration<TimeEntry>, IEntityTypeConfiguration<TimeEntry>
{
    public void Configure(EntityTypeBuilder<TimeEntry> builder)
    {
        base.Configure(builder);
    }
}