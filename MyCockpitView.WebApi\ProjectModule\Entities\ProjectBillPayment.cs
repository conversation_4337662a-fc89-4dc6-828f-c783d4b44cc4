﻿using MyCockpitView.CoreModule;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;


namespace MyCockpitView.WebApi.ProjectModule.Entities;

public class ProjectBillPayment : BaseEntity
{

    [Required]
    public int ProjectBillID { get; set; }

    public string? Mode { get; set; }

    public decimal Amount { get; set; }

    public decimal TDS { get; set; }

    public decimal TDSRate { get; set; }

    public string? Comment { get; set; }


    [StringLength(50)]
    public string? TransactionNo { get; set; }

    [Required]
    [Column(TypeName = "datetime2")]
    public DateTime TransactionDate { get; set; }

    public string? BankDetail { get; set; }

    public string? RefUrl { get; set; }

    public string? RefGuid { get; set; }

    public virtual ProjectBill? ProjectBill { get; set; }

    public decimal BillAmountReceived { get; set; }
    public decimal GstAmountReceived { get; set; }
    public virtual ICollection<ProjectBillPaymentAttachment> Attachments { get; set; } = new HashSet<ProjectBillPaymentAttachment>();

}

public class ProjectBillPaymentConfiguration : BaseEntityConfiguration<ProjectBillPayment>, IEntityTypeConfiguration<ProjectBillPayment>
{
    public void Configure(EntityTypeBuilder<ProjectBillPayment> builder)
    {
        base.Configure(builder);
    }

}