﻿using MyCockpitView.CoreModule;
using MyCockpitView.Utility.RDLCClient;
using MyCockpitView.WebApi.ProjectModule.Entities;
using MyCockpitView.WebApi.Services;

namespace MyCockpitView.WebApi.ProjectModule.Services;

public interface IProjectService: IBaseEntityService<Project>
{
    Task ActivateProjectsByAgenda();
    Task<int> Create(Project Entity, IEnumerable<ProjectArea>? Areas = null);
    Task<bool> Exist(string title);
    Task<IEnumerable<ProjectSummary>> GetCashflowData(IEnumerable<QueryFilter>? Filters = null, string? Search = null, string? Sort = null, bool ISTDates = false);
    Task<byte[]> GetCashflowExcel(IEnumerable<QueryFilter>? Filters = null, string? Search = null, string? Sort = null);
    Task<string> GetCode(int ID);
    Task<IEnumerable<ProjectSummary>> GetCRMData(IEnumerable<QueryFilter>? Filters = null, string? Search = null, string? Sort = null);
    Task<byte[]> GetCRMExcel(IEnumerable<QueryFilter>? Filters = null, string? Search = null, string? Sort = null);
    Task<IEnumerable<ProjectSummary>> GetInspectionVHrData(IEnumerable<QueryFilter>? Filters = null, string? Search = null, string? Sort = null);
    Task<IEnumerable<ProjectLastBite>> GetLastBiteData(IEnumerable<QueryFilter>? Filters = null, string? Search = null, string? Sort = null, bool ISTDates = false);
    Task<byte[]> GetLastBiteExcel(IEnumerable<QueryFilter>? Filters = null, string? Search = null, string? Sort = null);
    Task<IEnumerable<ProjectSummary>> GetMeetingVHrData(IEnumerable<QueryFilter>? Filters = null, string? Search = null, string? Sort = null);
    Task<int> GetNewCodeOrder();
    Task<IEnumerable<ProjectSummary>> GetPackageVHrData(IEnumerable<QueryFilter>? Filters = null, string? Search = null, string? Sort = null);
    Task<IEnumerable<ProjectActivity>> GetProjectActivityData(int ProjectID, IEnumerable<QueryFilter>? Filters = null);
    Task<ReportDefinition> GetProjectActivityReport(int ProjectID);
    Task<IEnumerable<ProjectActivity>> GetProjectActivityWeeklyData(IEnumerable<QueryFilter>? Filters = null, bool ISTDates = false);
    Task<byte[]> GetProjectActivityWeeklyExcel(IEnumerable<QueryFilter>? Filters = null);
    Task<ReportDefinition> GetProjectActivityWeeklyReport(IEnumerable<QueryFilter>? Filters = null);
    Task<IEnumerable<ProjectAnalysis>> GetProjectAnalysisData(IEnumerable<QueryFilter>? Filters = null, string? Search = null, string? Sort = null);
    Task<byte[]> GetProjectAnalysisExcel(IEnumerable<QueryFilter>? Filters = null, string? Search = null, string? Sort = null);
    Task<object> GetProjectConsumedVHr(int ProjectID, DateTime From, DateTime To);
    Task<IEnumerable<ProjectEstimation>> GetProjectEstimationData(IEnumerable<QueryFilter>? Filters = null, string? Search = null, string? Sort = null, bool ISTDates = false);
    Task<byte[]> GetProjectEstimationExcel(IEnumerable<QueryFilter>? Filters = null, string? Search = null, string? Sort = null);
    Task<dynamic> GetTaskByStage(string Entity, int EntityID, string StageCode, string StageTitle, decimal StageDuration, DateTime? FollowUpDate = null);
    Task<IEnumerable<ProjectSummary>> GetTodoVHrData(IEnumerable<QueryFilter>? Filters = null, string? Search = null, string? Sort = null);
    Task LockProjects();
    Task RecordHistory(int ProjectID);
    Task ResumeTaskFlow();
    Task TaskAction(int EntityID, string StageCode, string? taskComment = null);

    Task ScaffoldAgendaTasks(int projectID, int statusFlag);
}