﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.CompanyModule.Entities;
using MyCockpitView.WebApi.ContactModule.Entities;
using System.ComponentModel.DataAnnotations;

namespace MyCockpitView.WebApi.ContactModule.Dtos
{
    public class ContactAppointmentDto : BaseEntityDto
    {
        public string? Designation { get; set; }
        public Company? Company { get; set; }
        public string? Code { get; set; }
        public decimal ExpectedVhr { get; set; }
        public decimal ExpectedRemuneration { get; set; }
        public DateTime JoiningDate { get; set; }
        public DateTime? ResignationDate { get; set; }
        public decimal ManValue { get; set; }
        public int ContactID { get; set; }
        public int CompanyID { get; set; }
        public int? ManagerContactID { get; set; }
        public virtual ICollection<ContactAppointmentAttachmentDto> Attachments { get; set; } = new HashSet<ContactAppointmentAttachmentDto>();
        public ContactListDto? ManagerContact  { get; set; }
        public decimal BasicPayPercentage { get; set; }
        public decimal HRAPercentage { get; set; }
        public decimal SpecialAllowancePercentage { get; set; }
        public decimal BasicPayAmount { get; set; }
        public decimal HRAAmount { get; set; }
        public decimal SpecialAllowanceAmount { get; set; }

        [StringLength(255)]
        public string? BankAccountNo { get; set; }
        public string? BankName { get; set; }
        public string? BankBranch { get; set; }
        [StringLength(255)]
        public string? BankIFSCCode { get; set; }
        public string? Location { get; set; }

        public bool IsFixedRemuneration { get; set; }
        public bool IsVariableRemuneration { get; set; }
        public bool IsLastBiteRemuneration { get; set; }
        [Precision(18, 2)] public decimal AnnualCTC { get; set; }
        [Precision(18, 2)] public decimal MonthlyCTC { get; set; }
        public bool IsIncomeTaxDeduction { get; set; }
        public bool IsTDSDeduction { get; set; }
        public bool IsProfessionTaxDeduction { get; set; }
        public bool IsStudioMember { get; set; }
        public string? Certifications { get; set; }
        public string? SoftwareProficiency { get; set; }
        public string? KeySkills { get; set; }
        public string? YearsOfExperiance { get; set; }
        public string? Qualification { get; set; }
    }

    public class AppointmentMapperProfile : Profile
    {
        public AppointmentMapperProfile()
        {

            CreateMap<ContactAppointment, ContactAppointmentDto>()
             .ReverseMap()
             .ForMember(dest => dest.Contact, opt => opt.Ignore())
             .ForMember(dest => dest.ManagerContact, opt => opt.Ignore())
                          .ForMember(dest => dest.Attachments, opt => opt.Ignore())
              .ForMember(dest => dest.Company, opt => opt.Ignore());

            CreateMap<ContactAppointment, ContactAppointmentDto>();

        }
    }
}
