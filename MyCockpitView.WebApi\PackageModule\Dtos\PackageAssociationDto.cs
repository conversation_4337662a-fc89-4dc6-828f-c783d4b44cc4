﻿using AutoMapper;
using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.ContactModule.Dtos;
using MyCockpitView.WebApi.PackageModule.Entities;

namespace MyCockpitView.WebApi.PackageModule.Dtos;

public class PackageAssociationDto : BaseEntityDto
{
    public string?  Title { get; set; }
    public int PackageID { get; set; }
    public int ContactID { get; set; }
    public virtual ContactListDto? Contact { get; set; }
    public decimal ValueHours { get; set; } = 0;
    public decimal ValueHourRate { get; set; } = 0;
    public decimal ShareValue { get; set; } = 0;
}

public class PackageAssociationDtoMapperProfile : Profile
{
    public PackageAssociationDtoMapperProfile()
    {

        CreateMap<PackageAssociation, PackageAssociationDto>()
         .ReverseMap()
         .ForMember(dest => dest.Contact, opt => opt.Ignore())
      .ForMember(dest => dest.Package, opt => opt.Ignore());

    }
}