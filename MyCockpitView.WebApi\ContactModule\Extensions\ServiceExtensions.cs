﻿
using MyCockpitView.WebApi.ContactModule.Services;

namespace MyCockpitView.WebApi.ContactModule.Extensions;

public static class ServiceExtensions
{

    public static IServiceCollection RegisterContactServices(
     this IServiceCollection services)
    {
        services.AddScoped<IContactService,ContactService>();
        services.AddScoped<IContactAttachmentService,ContactAttachmentService>();
        services.AddScoped<IContactAssociationService,ContactAssociationService>();
        services.AddScoped<IContactAppointmentAttachmentService,ContactAppointmentAttachmentService>();
        services.AddScoped<IContactAppointmentService,ContactAppointmentService>();
        services.AddScoped<IContactGroupService,ContactGroupService>();
        services.AddScoped<IContactGroupMemberService,ContactGroupMemberService>();
        services.AddScoped<IContactAppointmentBurnOutLogService, ContactAppointmentBurnOutLogService>();

        return services;
    }
}
