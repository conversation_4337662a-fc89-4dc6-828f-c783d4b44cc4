﻿using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.WebApi.ContactModule.Entities;
using MyCockpitView.CoreModule;
using MyCockpitView.Utility.Common;
using MyCockpitView.WebApi.MeetingModule.Entities;
using System.ComponentModel.DataAnnotations.Schema;



namespace MyCockpitView.WebApi.TodoModule.Entities;

public class Todo : BaseEntity
{
    public string? Title { get; set; }

    public string? Subtitle { get; set; }

    public DateTime DueDate { get; set; }


    public DateTime StartDate { get; set; }

    public string? Comment { get; set; }

    public int AssigneeContactID { get; set; }

    public int AssignerContactID { get; set; }

    public virtual Contact? AssigneeContact { get; set; }

    public virtual Contact? AssignerContact { get; set; }

    public virtual ICollection<TodoAttachment> Attachments { get; set; } = new List<TodoAttachment>();

    public virtual ICollection<TodoAgenda> Agendas { get; set; } = new List<TodoAgenda>();
    
    [Precision(18,2)] public decimal MHrAssigned { get; set; }
    
    [Precision(18,2)] public decimal MHrConsumed { get; set; }
    public int? ProjectID { get; set; }
    public int? FunctionID { get; set; }
    [Column("MeetingAgendas")]
    public string? _meetingAgendaJson { get; set; }


    [NotMapped]
    public ICollection<MeetingAgenda> MeetingAgendas
    {
        get
        {
            return _meetingAgendaJson != null && _meetingAgendaJson != string.Empty ?
                DataTools.GetObjectFromJsonString<ICollection<MeetingAgenda>>(_meetingAgendaJson) :
                new List<MeetingAgenda>();
        }
        set
        {
            _meetingAgendaJson = DataTools.GetJsonStringFromObject(value);
        }
    }
}


public class TodoConfiguration : BaseEntityConfiguration<Todo>, IEntityTypeConfiguration<Todo>
{
    public void Configure(EntityTypeBuilder<Todo> builder)
    {
        base.Configure(builder);

        builder.Property(t => t.ProjectID);

        // Relationships
        builder.HasOne(t => t.AssigneeContact)
            .WithMany()
            .HasForeignKey(t => t.AssigneeContactID)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasOne(t => t.AssignerContact)
            .WithMany()
            .HasForeignKey(t => t.AssignerContactID)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);


        builder.HasIndex(e => e.DueDate);
    }
}
