﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using MyCockpitView.CoreModule;
using System.ComponentModel.DataAnnotations;

namespace MyCockpitView.WebApi.ProjectModule.Entities;

public class ProjectScopeService : BaseEntity
{

    [Required]
    public int ProjectID { get; set; }

    [Required]
    public int ProjectScopeID { get; set; }

    public virtual ProjectScope? ProjectScope { get; set; }

    [StringLength(50)]
    [Required]
    public string? Title { get; set; }

    [StringLength(10)]
    public string? Abbreviation { get; set; }
    [Precision(14, 2)]
    public decimal SharePercentage { get; set; } = 0;
    [Precision(14, 2)]
    public decimal Amount { get; set; } = 0;
}

public class ProjectScopeServiceConfiguration : BaseEntityConfiguration<ProjectScopeService>, IEntityTypeConfiguration<ProjectScopeService>
{
    public void Configure(EntityTypeBuilder<ProjectScopeService> builder)
    {
        base.Configure(builder);
        // Properties

        builder.HasIndex(e => e.Title);
        builder.HasIndex(e => e.Abbreviation);
        builder.HasIndex(e => e.SharePercentage);
        builder.HasIndex(e => e.Amount);

    }
}