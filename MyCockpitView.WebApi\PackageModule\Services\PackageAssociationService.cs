﻿using MyCockpitView.WebApi.PackageModule.Entities;
using MyCockpitView.WebApi.Services;
using MyCockpitView.WebApi.WFTaskModule.Services;
using MyCockpitView.CoreModule;
using Microsoft.EntityFrameworkCore;

namespace MyCockpitView.WebApi.PackageModule.Services;

public class PackageAssociationService : BaseEntityService<PackageAssociation>, IPackageAssociationService
{
    public PackageAssociationService(EntitiesContext db) : base(db) { }

    public IQueryable<PackageAssociation> Get(IEnumerable<QueryFilter>? Filters = null, string? Search = null, string? Sort = null)
    {

        IQueryable<PackageAssociation> _query = base.Get(Filters);

        //Apply filters
        if (Filters != null)
        {

            if (Filters.Where(x => x.Key.Equals("contactID", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<PackageAssociation>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("contactID", StringComparison.OrdinalIgnoreCase)))
                {
                    var isNumeric = Convert.ToInt32(_item.Value);

                    predicate = predicate.Or(x => x.ContactID == isNumeric);
                }
                _query = _query.Where(predicate);
            }
            if (Filters.Where(x => x.Key.Equals("PackageID", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<PackageAssociation>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("packageID", StringComparison.OrdinalIgnoreCase)))
                {
                    var isNumeric = Convert.ToInt32(_item.Value);

                    predicate = predicate.Or(x => x.PackageID == isNumeric);
                }
                _query = _query.Where(predicate);
            }

        }

        if (Search != null && Search != "")
        {
            _query = _query.Include(x => x.Contact);

            _query = _query.Where(s => (s.Contact.FirstName + " " + s.Contact.LastName).ToLower().Contains(Search.ToLower())

                          );

        }

        return _query;

    }

    public async Task<PackageAssociation?> GetById(int Id)
    {

        return await db.PackageAssociations
               .AsNoTracking()
               .Include(x => x.Contact)
                .SingleOrDefaultAsync(i => i.ID == Id);

    }

    public async Task<int> Create(PackageAssociation Entity)
    {

        //    var _exist = await db.PackageAssociations.AsNoTracking()
        //        .Where(x => x.PackageID == Entity.PackageID
        //    && x.ContactID == Entity.ContactID
        //    && x.TypeFlag == Entity.TypeFlag).FirstOrDefaultAsync();

        //if (_exist != null) return _exist.ID;

        var _flagValue = await db.TypeMasters.AsNoTracking().Where(x => x.Entity==nameof(PackageAssociation))
                .Where(x => x.Value == Entity.TypeFlag).FirstOrDefaultAsync();
        if (_flagValue != null)
        {
            Entity.Title = _flagValue.Title;
        }


        await base.Create(Entity);

        if (Entity.TypeFlag == McvConstant.PACKAGE_ASSOCIATION_TYPEFLAG_ASSOCIATE || Entity.TypeFlag == McvConstant.PACKAGE_ASSOCIATION_TYPEFLAG_INVITEE || Entity.TypeFlag == McvConstant.PACKAGE_ASSOCIATION_TYPEFLAG_PARTNER)
        {
            var _contact = await db.Contacts.AsNoTracking().Where(x => x.ID == Entity.ContactID).SingleOrDefaultAsync();


            var _activeTask = await db.WFTasks.AsNoTracking()
                .Where(x => x.Entity == nameof(Package)
                && x.EntityID == Entity.PackageID
                && x.StatusFlag != McvConstant.WFTASK_STATUSFLAG_COMPLETED
                && x.StageIndex != 3) //Not StudioWork
                .ToListAsync();

            if (_activeTask.Any(x => x.ContactID != Entity.ContactID))
            {
                var _stageIndex = 1;
                if (_activeTask.FirstOrDefault() != null)
                {
                    _stageIndex = _activeTask.FirstOrDefault().StageIndex.Value;
                }
                var packageService = new PackageService(db);
                await packageService.AssignPackageTask(Entity.PackageID, _stageIndex);
            }
            //        var _tasks = await db.WFTasks.AsNoTracking()
            //     .Where(x => x.Entity==nameof(Package) 
            //     && x.EntityID == Entity.PackageID 

            //     && x.StageIndex != 3) //Not StudioWork
            //     .Select(x=> new {x.ContactID, x.StageIndex,x.StatusFlag,x.Created,x.CompletedDate})
            //     .ToListAsync();

            //if (!_tasks.Where(x => x.StatusFlag == McvConstant.WFTASK_STATUSFLAG_PENDING || x.StatusFlag == McvConstant.WFTASK_STATUSFLAG_STARTED || x.StatusFlag == McvConstant.WFTASK_STATUSFLAG_PAUSED).Any())
            //{
            //    var lastCompleted = _tasks.OrderByDescending(x => x.CompletedDate).FirstOrDefault();
            //    if (lastCompleted != null)
            //    {

            //        var packageService = new PackageService(db);
            //        await packageService.AssignPackageTask(Entity.PackageID, lastCompleted.StageIndex.Value);
            //    }
            //    else
            //    {
            //        var packageService = new PackageService(db);
            //        await packageService.AssignPackageTask(Entity.PackageID, 1);
            //    }
            //}
            //else if (_tasks.Where(x => x.StatusFlag == McvConstant.WFTASK_STATUSFLAG_PENDING || x.StatusFlag == McvConstant.WFTASK_STATUSFLAG_STARTED || x.StatusFlag == McvConstant.WFTASK_STATUSFLAG_PAUSED).Any(x => x.ContactID != Entity.ContactID))
            //{
            //    var lastActive = _tasks.Where(x => x.StatusFlag == McvConstant.WFTASK_STATUSFLAG_PENDING || x.StatusFlag == McvConstant.WFTASK_STATUSFLAG_STARTED || x.StatusFlag == McvConstant.WFTASK_STATUSFLAG_PAUSED).OrderByDescending(x => x.Created).FirstOrDefault();
            //    if (lastActive != null)
            //    {
            //        var packageService = new PackageService(db);
            //        await packageService.AssignPackageTask(Entity.PackageID, lastActive.StageIndex.Value);
            //    }
            //    else
            //    {
            //        var packageService = new PackageService(db);
            //        await packageService.AssignPackageTask(Entity.PackageID, 1);
            //    }
            //}
            //else
            //{
            //    var packageService = new PackageService(db);
            //    await packageService.AssignPackageTask(Entity.PackageID, 1);
            //}
        }


        return Entity.ID;

    }


    public async Task Delete(int Id)
    {

        PackageAssociation Entity = await Get().SingleOrDefaultAsync(i => i.ID == Id);

        if (Entity == null)
        {
            return;
        }

        await base.Delete(Id);
        var sharedService = new SharedService(db); ;

        var pendingTasks = await db.WFTasks.AsNoTracking().Where(x => x.StatusFlag == McvConstant.WFTASK_STATUSFLAG_PENDING && x.ContactID == Entity.ContactID)
            .Where(x => x.Entity == nameof(Package) && x.EntityID == Entity.PackageID
            && x.StageIndex != 3) //not studiowork

            .ToListAsync();
        var _taskService = new WFTaskService(db);
        foreach (var task in pendingTasks)
        {
            await _taskService.Delete(task.ID);
        }
    }


}