﻿

using System.Data;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.ProjectModule.Entities;
using MyCockpitView.WebApi.Services;

namespace MyCockpitView.WebApi.ProjectModule.Services;

public class ProjectScope_Service : BaseEntityService<ProjectScope>, IProjectScope_Service
{
    public ProjectScope_Service(EntitiesContext db) : base(db) { }

    public IQueryable<ProjectScope> Get(IEnumerable<QueryFilter>? Filters = null, string? Search = null, string? Sort = null)
    {

        IQueryable<ProjectScope> _query = base.Get(Filters);

        //Apply filters
        if (Filters != null)
        {

            if (Filters.Where(x => x.Key.Equals("projectID", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<ProjectScope>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("projectID", StringComparison.OrdinalIgnoreCase)))
                {
                    var isNumeric = Convert.ToInt32(_item.Value);

                    predicate = predicate.Or(x => x.ProjectID == isNumeric);
                }
                _query = _query.Where(predicate);
            }


        }

        if (Search != null && Search != String.Empty)
        {
            Search = Search.ToLower();
            _query = _query
                 .Where(x => x.Title.ToLower().Contains(Search.ToLower())
                                           );

        }

        if (Sort != null && Sort != String.Empty)
        {
            switch (Sort.ToLower())
            {
                case "createddate":
                    return _query
                            .OrderBy(x => x.Created);

                case "modifieddate":
                    return _query
                            .OrderBy(x => x.Modified);

                case "createddate desc":
                    return _query
                            .OrderByDescending(x => x.Created);

                case "modifieddate desc":
                    return _query
                            .OrderByDescending(x => x.Modified);

                case "amount":
                    return _query
                            .OrderBy(x => x.Amount);

                case "amount desc":
                    return _query
                            .OrderByDescending(x => x.Amount);

                case "sharePercentage":
                    return _query
                            .OrderBy(x => x.SharePercentage);

                case "sharePercentage desc":
                    return _query
                            .OrderByDescending(x => x.SharePercentage);
            }
        }

        return _query.OrderBy(x => x.OrderFlag);

    }

    public async Task<ProjectScope?> GetById(int Id)
    {

        return await db.ProjectScopes.AsNoTracking()
              .Include(x => x.Services)
                 .SingleOrDefaultAsync(i => i.ID == Id);

    }

    public async Task<int> GetNextOrder(int ProjectID)
    {

        var _last = await db.ProjectScopes.AsNoTracking()
            .Where(x => x.ProjectID == ProjectID)
            .OrderByDescending(x => x.OrderFlag).FirstOrDefaultAsync();
        if (_last == null) return 1;
        else return _last.OrderFlag + 1;

    }

    public async Task<int> Create(ProjectScope Entity, ICollection<ProjectScopeService>? Services = null)
    {

        Entity.OrderFlag = await GetNextOrder(Entity.ProjectID);

        if (Services != null)
        {
            Entity.Services = Services;
        }

        return await base.Create(Entity);

    }


}