﻿




using Microsoft.EntityFrameworkCore;
using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.Services;
using MyCockpitView.WebApi.WFStageModule.Entities;

namespace MyCockpitView.WebApi.WFStageModule.Services;

public class WFStageService : BaseEntityService<WFStage>, IWFStageService
{
    public WFStageService(EntitiesContext db) : base(db) { }

    public IQueryable<WFStage> Get(IEnumerable<QueryFilter>? Filters = null, string? Search = null, string? Sort = null)
    {
       
            IQueryable<WFStage> _query = base.Get(Filters, Search, Sort)
               .Include(x => x.Actions);

            //Apply filters
            if (Filters != null)
            {

                if (Filters.Where(x => x.Key.Equals("code", StringComparison.OrdinalIgnoreCase)).Any())
                {
                    var predicate = PredicateBuilder.False<WFStage>();
                    foreach (var _item in Filters.Where(x => x.Key.Equals("code", StringComparison.OrdinalIgnoreCase)))
                    {

                        predicate = predicate.Or(x => x.Code == _item.Value);
                    }
                    _query = _query.Where(predicate);
                }
            }

            return _query;
    }

    public async Task<WFStage?> GetById(int Id)
    {
     
            return await db.WFStages
                .AsNoTracking()
                .Include(x => x.Actions)
                 .SingleOrDefaultAsync(i => i.ID == Id);
    }

}