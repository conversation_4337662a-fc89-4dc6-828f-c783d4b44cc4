﻿
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.WebApi.Responses;
using MyCockpitView.WebApi.ContactModule.Entities;
using MyCockpitView.WebApi.ContactModule.Services;
using MyCockpitView.Utility.Common;
using MyCockpitView.CoreModule;
using MyCockpitView.Utility.Excel;

namespace MyCockpitView.WebApi.ContactModule.Controllers;

[Authorize]
[ApiController]
[Route("[controller]")]
public class ContactAppointmentBurnOutLogController : ControllerBase
{
    private readonly IContactAppointmentBurnOutLogService service;
    private readonly EntitiesContext db;

    public ContactAppointmentBurnOutLogController(EntitiesContext db, IContactAppointmentBurnOutLogService service   )
    {
        this.db = db;
        this.service = service;
    }

    [HttpGet]
    public async Task<ActionResult<IEnumerable<ContactAppointmentBurnOutLog>>> Get(
        [FromQuery] string? filters = null,
        [FromQuery] string? search = null,
        [FromQuery] string? sort = null)
    {
        var query = service.Get(filters != null ? DataTools.GetObjectFromJsonString<APIFilter>(filters).Filters : null, search, sort);
        var results = await query.ToListAsync();

        return Ok(results);
    }

    [HttpGet("Pages")]
    public async Task<ActionResult<PagedResponse<ContactAppointmentBurnOutLog>>> GetPages(
        [FromQuery] int page = 0,
        [FromQuery] int pageSize = 50,
        [FromQuery] string? filters = null,
        [FromQuery] string? search = null,
        [FromQuery] string? sort = null)
    {
        var query = service.Get(filters != null ? DataTools.GetObjectFromJsonString<APIFilter>(filters).Filters : null, search, sort);

        var totalCount = await query.CountAsync();
        var totalPages = (int)Math.Ceiling((decimal)totalCount / pageSize);
        var results = await query
            .Skip(pageSize * page)
            .Take(pageSize)
            .ToListAsync();


        return Ok(new PagedResponse<ContactAppointmentBurnOutLog>(results, totalCount, totalPages));
    }


    [AllowAnonymous]
    [HttpGet("excel")]
    public async Task<IActionResult> GetExcel(
        [FromQuery] string? filters = null,
        [FromQuery] string? search = null,
        [FromQuery] string? sort = null)
    {
        var query = service.Get(filters != null ? DataTools.GetObjectFromJsonString<APIFilter>(filters).Filters : null, search, sort);
        var results = await query.ToListAsync();
        var fileName = $"BurnOut_{DateTime.Now:yyyyMMddHHmmss}.xlsx";
        var stream = ExcelUtility.ExportExcel(results);
        return File(stream, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileName);
    }
}