﻿using AutoMapper;
using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.PackageModule.Dtos;
using MyCockpitView.WebApi.PackageModule.Entities;
using MyCockpitView.WebApi.ProcessLibraryModule.Entities;

namespace MyCockpitView.WebApi.ProcessLibraryModule.Dtos;

public class ProcessLibraryEntityAttachmentDto : BaseBlobEntityDto
{
    public int ProcessLibraryEntityID { get; set; }

    public string[] Categories { get; set; }
}

public class ProcessLibraryEntityAttachmentDtoMapperProfile : Profile
{
    public ProcessLibraryEntityAttachmentDtoMapperProfile()
    {
        CreateMap<ProcessLibraryEntityAttachment, ProcessLibraryEntityAttachmentDto>()
                          .ReverseMap()
                         .ForMember(dest => dest.ProcessLibraryEntity, opt => opt.Ignore());

    }
}