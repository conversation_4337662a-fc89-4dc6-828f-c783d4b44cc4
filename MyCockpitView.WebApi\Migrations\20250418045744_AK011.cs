﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace MyCockpitView.WebApi.Migrations
{
    /// <inheritdoc />
    public partial class AK011 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "PackageFeedbackAttachments",
                columns: table => new
                {
                    ID = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    PackageFeedbackID = table.Column<int>(type: "int", nullable: false),
                    IsReadOnly = table.Column<bool>(type: "bit", nullable: false),
                    Created = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatedBy = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: true),
                    CreatedByContactID = table.Column<int>(type: "int", nullable: true),
                    Description = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false, defaultValue: false),
                    Modified = table.Column<DateTime>(type: "datetime2", nullable: false),
                    ModifiedBy = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: true),
                    ModifiedByContactID = table.Column<int>(type: "int", nullable: true),
                    OrderFlag = table.Column<int>(type: "int", nullable: false, defaultValue: 0),
                    SearchTags = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    StatusFlag = table.Column<int>(type: "int", nullable: false, defaultValue: 0),
                    TypeFlag = table.Column<int>(type: "int", nullable: false, defaultValue: 0),
                    UID = table.Column<Guid>(type: "uniqueidentifier", nullable: false, defaultValueSql: "NEWID()"),
                    IsFolder = table.Column<bool>(type: "bit", nullable: false),
                    ParentID = table.Column<int>(type: "int", nullable: true),
                    FolderPath = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    BlobPath = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Container = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ContentType = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: true),
                    Filename = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    Guidname = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    OriginalUrl = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Size = table.Column<int>(type: "int", nullable: false),
                    ThumbFilename = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ThumbUrl = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Url = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    IsProcessed = table.Column<bool>(type: "bit", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PackageFeedbackAttachments", x => x.ID);
                    table.ForeignKey(
                        name: "FK_PackageFeedbackAttachments_PackageFeedbacks_PackageFeedbackID",
                        column: x => x.PackageFeedbackID,
                        principalTable: "PackageFeedbacks",
                        principalColumn: "ID",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_PackageFeedbackAttachments_Created",
                table: "PackageFeedbackAttachments",
                column: "Created");

            migrationBuilder.CreateIndex(
                name: "IX_PackageFeedbackAttachments_CreatedByContactID",
                table: "PackageFeedbackAttachments",
                column: "CreatedByContactID");

            migrationBuilder.CreateIndex(
                name: "IX_PackageFeedbackAttachments_Filename",
                table: "PackageFeedbackAttachments",
                column: "Filename");

            migrationBuilder.CreateIndex(
                name: "IX_PackageFeedbackAttachments_IsDeleted",
                table: "PackageFeedbackAttachments",
                column: "IsDeleted");

            migrationBuilder.CreateIndex(
                name: "IX_PackageFeedbackAttachments_IsFolder",
                table: "PackageFeedbackAttachments",
                column: "IsFolder");

            migrationBuilder.CreateIndex(
                name: "IX_PackageFeedbackAttachments_IsReadOnly",
                table: "PackageFeedbackAttachments",
                column: "IsReadOnly");

            migrationBuilder.CreateIndex(
                name: "IX_PackageFeedbackAttachments_Modified",
                table: "PackageFeedbackAttachments",
                column: "Modified");

            migrationBuilder.CreateIndex(
                name: "IX_PackageFeedbackAttachments_ModifiedByContactID",
                table: "PackageFeedbackAttachments",
                column: "ModifiedByContactID");

            migrationBuilder.CreateIndex(
                name: "IX_PackageFeedbackAttachments_OrderFlag",
                table: "PackageFeedbackAttachments",
                column: "OrderFlag");

            migrationBuilder.CreateIndex(
                name: "IX_PackageFeedbackAttachments_PackageFeedbackID",
                table: "PackageFeedbackAttachments",
                column: "PackageFeedbackID");

            migrationBuilder.CreateIndex(
                name: "IX_PackageFeedbackAttachments_StatusFlag",
                table: "PackageFeedbackAttachments",
                column: "StatusFlag");

            migrationBuilder.CreateIndex(
                name: "IX_PackageFeedbackAttachments_TypeFlag",
                table: "PackageFeedbackAttachments",
                column: "TypeFlag");

            migrationBuilder.CreateIndex(
                name: "IX_PackageFeedbackAttachments_UID",
                table: "PackageFeedbackAttachments",
                column: "UID");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "PackageFeedbackAttachments");
        }
    }
}
