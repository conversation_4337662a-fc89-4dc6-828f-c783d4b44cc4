﻿using AutoMapper;
using MyCockpitView.WebApi.ProjectModule.Entities;

namespace MyCockpitView.WebApi.ProjectModule.Dtos;

public class ProjectScriptDto
{
    public int ID { get; set; }
    public Guid UID { get; set; }
    public string? Code { get; set; }
    public string? Title { get; set; }
    public string? ImageUrl { get; set; }
    public string? StatusValue { get; set; }
    public int StatusFlag { get; set; }
}

public class ProjectScriptDtoMapperProfile : Profile
{
    public ProjectScriptDtoMapperProfile()
    {
        CreateMap<Project, ProjectScriptDto>();

    }
}
