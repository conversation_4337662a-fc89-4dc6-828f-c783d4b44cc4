﻿using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.CoreModule;


namespace MyCockpitView.WebApi.WFTaskModule.Entities;

public class WFTaskAttachment : BaseBlobEntity
{
    public int WFTaskID { get; set; }

    public virtual WFTask? WFTask { get; set; }
}


public class WFTaskAttachmentConfiguration : BaseBlobEntityConfiguration<WFTaskAttachment>, IEntityTypeConfiguration<WFTaskAttachment>
{
    public void Configure(EntityTypeBuilder<WFTaskAttachment> builder)
    {
        base.Configure(builder);

    }
}