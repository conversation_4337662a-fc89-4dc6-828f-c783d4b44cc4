﻿using Microsoft.EntityFrameworkCore;
using MyCockpitView.Utility.Common;
using System.Text;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using MyCockpitView.Utility.ZeptoMailClient;
using MyCockpitView.WebApi.Models;
using MyCockpitView.WebApi.ContactModule.Entities;
using Azure.Core;
using MyCockpitView.Utility.SendGrid;
using MyCockpitView.WebApi.PackageModule.Services;

namespace MyCockpitView.WebApi.Services;

public interface ISharedService
{
    Task<string> GenerateQR(QRRequest request);
    Task<int> GetBusinessEndMinutesIST();
    Task<int> GetBusinessStartMinutesIST();
    Task<List<int>> GetContactIDByRoleAsync(string roleName);
    Task<ContactAppointment?> GetLastAppointment(int ContactID, int? CompanyID = null);
    Task<string?> GetPresetValue(string Key);
    Task PushNotification(string username, string title, string body, string entity, string entityID);
    Task SendMail(string subject, string senderName, string senderEmail, string mailBody, List<(string name, string email)> toAddresses, List<(string name, string email)>? ccAddresses = null, List<(string name, string email)>? bccAddresses = null, string? replyAddress = null, string? replyName = null, List<(string filename, string content)>? attachments = null);
}

public class SharedService : ISharedService
{
    private readonly EntitiesContext db;


    public SharedService(EntitiesContext db)
    {
        this.db = db;
    }

    public async Task<ContactAppointment?> GetLastAppointment(int ContactID, int? CompanyID = null)
    {

        var _contact = await db.Contacts.AsNoTracking()
           .Include(x => x.Appointments).ThenInclude(c => c.Company)

           .Where(x => x.ID == ContactID)
           .SingleOrDefaultAsync();

        if (_contact != null && _contact.Appointments.Where(x => x.StatusFlag == McvConstant.APPOINTMENT_STATUSFLAG_APPOINTED).Any())
        {
            if (CompanyID != null)
            {
                return _contact.Appointments
                    .Where(x => x.CompanyID == CompanyID)
                    .Where(x => x.StatusFlag == McvConstant.APPOINTMENT_STATUSFLAG_APPOINTED)
                    .OrderByDescending(x => x.JoiningDate).FirstOrDefault();

            }
            else
            {
                return _contact.Appointments
                        .Where(x => x.StatusFlag == McvConstant.APPOINTMENT_STATUSFLAG_APPOINTED)
                        .OrderByDescending(x => x.JoiningDate).FirstOrDefault();
            }


        }

        return null;

    }

    public async Task<List<int>> GetContactIDByRoleAsync(string roleName)
    {
        return await db.Users
        .Join(db.UserRoles, user => user.Id, userRole => userRole.UserId, (user, userRole) => new { user, userRole })
        .Join(db.Roles, ur => ur.userRole.RoleId, role => role.Id, (ur, role) => new { ur.user, role })
                .Join(db.Contacts, ur => ur.user.UserName, contact => contact.Username, (ur, contact) => new { ur.user, ur.role, contact })
        .Where(ur => ur.role.Name == roleName)
        .Select(ur => ur.contact.ID)
                      .ToListAsync();
    }

    public async Task<string?> GetPresetValue(string Key)
    {

        var _query = await db.AppSettingMasters
            .AsNoTracking()
            .Where(x => x.PresetKey.Trim().ToLower() == Key.Trim().ToLower())
            .SingleOrDefaultAsync();

        if (_query == null) throw new Exception(Key + " not found in Presets!");
        return _query.PresetValue?.Trim();

    }

    public async Task<int> GetBusinessEndMinutesIST()
    {

        return ClockTools.ConvertTimestringToMinutes(await GetPresetValue(McvConstant.OFFICE_CLOSE_TIME));

    }

    public async Task<int> GetBusinessStartMinutesIST()
    {

        return ClockTools.ConvertTimestringToMinutes(await GetPresetValue(McvConstant.OFFICE_OPEN_TIME));

    }


    #region Email

    /// <summary>
    /// Parse a JSON string into a list of name and email tuples
    /// </summary>
    /// <param name="jsonString">JSON string containing an array of objects with name and email properties</param>
    /// <returns>List of name and email tuples</returns>
    private static List<(string name, string email)> ParseEmailJson(string? jsonString)
    {
        if (string.IsNullOrEmpty(jsonString))
        {
            return [("User", "<EMAIL>")];
        }

        try
        {
            // Try to parse as an array of objects with name and email properties
            var emailList = new List<(string name, string email)>();

            // Parse the JSON array
            var jsonArray = JArray.Parse(jsonString);

            foreach (var item in jsonArray)
            {
                // Extract name and email from each object
                string name = item["name"]?.ToString() ?? "User";
                string? email = item["email"]?.ToString();

                // Only add if email is not null or empty
                if (!string.IsNullOrEmpty(email))
                {
                    emailList.Add((name, email));
                }
            }

            return emailList;
        }
        catch (Exception)
        {
            // If parsing fails, return a list with a single entry using the string as the email
            return [("User", jsonString.Trim())];
        }
    }

    public async Task SendMail(
        string subject,
        string senderName,
        string senderEmail,
        string mailBody,
        List<(string name, string email)> toAddresses,
        List<(string name, string email)>? ccAddresses = null,
        List<(string name, string email)>? bccAddresses = null,
        string? replyAddress = null,
        string? replyName = null,
        List<(string filename, string content)>? attachments = null)
    {
        var sendgridEnabledStr = await GetPresetValue(McvConstant.ENABLE_SENDGRID);
        var devModeStr = await GetPresetValue(McvConstant.DEVMODE);

        var isSendgridEnabled = Convert.ToBoolean(sendgridEnabledStr);
        var isDevMode = Convert.ToBoolean(devModeStr);

        // Get the devmode email recipients and parse as JSON array
        var devmodeEmailToJson = await GetPresetValue(McvConstant.DEVMODE_EMAIL_TO);
        var devmodeEmailList = ParseEmailJson(devmodeEmailToJson);

        // Ensure we have at least one recipient in dev mode
        if (devmodeEmailList.Count == 0)
        {
            devmodeEmailList.Add(("Developer", "<EMAIL>"));
        }

        if (isSendgridEnabled)
        {
            var sendgridKey = await GetPresetValue(McvConstant.SENDGRID_KEY) ?? "";
            var emailService = new SendGridEmailUtility(sendgridKey);

            await emailService.SendEmail(
                subject,
                senderName,
                senderEmail,
                mailBody,
                isDevMode ? devmodeEmailList : toAddresses,
                isDevMode ? null : ccAddresses,
                isDevMode ? null : bccAddresses,
                replyAddress, replyName, attachments);
        }
        else
        {
            var zohoMailKey = await GetPresetValue(McvConstant.ZOHO_MAIL_API_KEY) ?? "";
            var zohoMailApi = await GetPresetValue(McvConstant.ZOHO_MAIL_API) ?? "";

            ZeptoEmailUtility.SendEmail(
                zohoMailApi,
                zohoMailKey,
                subject,
                senderName,
                senderEmail,
                mailBody,
                isDevMode ? devmodeEmailList : toAddresses,
                isDevMode ? null : ccAddresses,
                isDevMode ? null : bccAddresses,
                replyAddress, replyName,
                attachments != null && attachments.Count > 0 ?
                    attachments.Select(x => ZeptoEmailAttachment.FromBase64(x.filename, x.content)).ToList() :
                    null);
        }
    }



    #endregion

    public async Task PushNotification(string username, string title, string body, string entity, string entityID)
    {
        //var contact = await db.Contacts.AsNoTracking().FirstOrDefaultAsync(x => x.ID == ContactID);
        //if (contact == null || contact.Username == null) return;

        var subscriptions = await db.WebPushSubscriptions.AsNoTracking()
            .Where(x => x.Username == username)
            .Select(x => x.Subscription)
            .ToListAsync();

        if (subscriptions.Count == 0) return;

        var logoUrl = await GetPresetValue(McvConstant.COMPANY_LOGO_URL) ?? "";
        var api = await GetPresetValue(McvConstant.PUSH_NOTIFICATION_API) ?? "";
        var pub = await GetPresetValue(McvConstant.PUSH_NOTIFICATION_PUBLIC_KEY) ?? "";
        var prv = await GetPresetValue(McvConstant.PUSH_NOTIFICATION_PRIVATE_KEY) ?? "";

        foreach (var subscription in subscriptions)
        {
            if (string.IsNullOrEmpty(subscription)) continue;

            try
            {
                // Deserialize the subscription string into dynamic JObject
                var subscriptionObject = JsonConvert.DeserializeObject<JObject>(subscription)
                    ?? throw new InvalidOperationException("Failed to deserialize subscription");

                var requestPayload = new
                {
                    subscription = subscriptionObject,
                    notificationPayload = new
                    {
                        notification = new
                        {
                            title,
                            body,
                            icon = logoUrl,
                            vibrate = new[] { 100, 50, 100 },
                            tag = $"{entity}-{entityID}",
                            data = new
                            {
                                entity,
                                entityID
                            },
                            //actions = new[]
                            //{
                            //    new { action = "open", title = "Open In App" }
                            //}
                        }
                    },
                    vapidPrivateKey = prv,
                    vapidPublicKey = pub
                };

                using var httpClient = new HttpClient();
                // Set the base URL of the Azure Function endpoint
                httpClient.BaseAddress = new Uri(api);

                // Convert notification payload to JSON
                string jsonPayload = JsonConvert.SerializeObject(requestPayload);

                // Convert JSON string to HttpContent
                var content = new StringContent(jsonPayload, Encoding.UTF8, "application/json");

                // Send the HTTP POST request to the Azure Function endpoint
                var response = await httpClient.PostAsync("", content);

                // Check if the response is successful
                if (!response.IsSuccessStatusCode)
                    throw new Exception($"HTTP POST request failed with status code {response.StatusCode}");
            }
            catch (Exception)
            {
                // Log the exception or handle it as needed
                // Currently silently ignoring errors
            }
        }
    }

    public async Task<string> GenerateQR(QRRequest request)
    {
        // Create an HttpClient instance
        using var httpClient = new HttpClient();

        // Set the base URL of the Azure Function endpoint
        var api = await GetPresetValue(McvConstant.GENERATE_QR_API) ?? "";
        httpClient.BaseAddress = new Uri(api);

        string jsonPayload = JsonConvert.SerializeObject(request);

        // Convert JSON string to HttpContent
        var content = new StringContent(jsonPayload, System.Text.Encoding.UTF8, "application/json");

        // Send the HTTP POST request to the Azure Function endpoint
        var response = await httpClient.PostAsync("", content);

        // Check if the response is successful
        if (response.IsSuccessStatusCode)
        {
            // Read the response body
            var jsonResponse = await response.Content.ReadAsStringAsync();
            var jObject = JObject.Parse(jsonResponse);

            // Get the QR code from the response
            var responseBytes = jObject["qrCode"]?.ToString() ??
                throw new InvalidOperationException("QR code not found in response");

            return responseBytes;
        }
        else
        {
            // Read the response body
            var jsonResponse = await response.Content.ReadAsStringAsync();
            throw new Exception($"HTTP POST request failed with status code {response.StatusCode}. {jsonResponse}");
        }
    }
}
public class QRRequest
{
    public string Url { get; set; }
    public bool UploadToBlob { get; set; }
    public string BlobConnectionString { get; set; }
    public string BlobUrl { get; set; }
}