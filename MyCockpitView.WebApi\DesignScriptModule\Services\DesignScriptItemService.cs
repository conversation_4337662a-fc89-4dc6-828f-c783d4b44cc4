﻿

using System.Data;




using MyCockpitView.WebApi.Exceptions;
using MyCockpitView.WebApi.Services;
using MyCockpitView.WebApi.DesignScriptModule.Entities;
using MyCockpitView.CoreModule;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.Utility.Common;

namespace MyCockpitView.WebApi.DesignScriptModule.Services;

public class DesignScriptItemService :BaseEntityService<DesignScriptItem>, IDesignScriptItemService
{
    public DesignScriptItemService(EntitiesContext db) : base(db) { }

    public IQueryable<DesignScriptItem> Get( IEnumerable<QueryFilter>? Filters = null, string? Search = null, string? Sort = null)
    {
       
            IQueryable<DesignScriptItem> _query = base.Get(Filters);

            //Apply filters
            if (Filters != null)
            {
                if (Filters.Where(x => x.Key.Equals("deleted", StringComparison.OrdinalIgnoreCase)).Any())
                {

                    _query = db.DesignScriptItems
                            .AsNoTracking();
                }


                if (Filters.Where(x => x.Key.Equals("projectid", StringComparison.OrdinalIgnoreCase)).Any())
                {
                    var predicate = PredicateBuilder.False<DesignScriptItem>();
                    foreach (var _item in Filters.Where(x => x.Key.Equals("projectid", StringComparison.OrdinalIgnoreCase)))
                    {
                        var isNumeric = Convert.ToInt32(_item.Value);

                        predicate = predicate.Or(x => x.ProjectID == isNumeric);
                    }
                    _query = _query.Where(predicate);
                }

            if (Filters.Where(x => x.Key.Equals("projectCode", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<DesignScriptItem>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("projectCode", StringComparison.OrdinalIgnoreCase)))
                {

                    predicate = predicate.Or(x => x.ProjectCode == _item.Value);
                }
                _query = _query.Where(predicate);
            }

            if (Filters.Where(x => x.Key.Equals("code", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<DesignScriptItem>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("code", StringComparison.OrdinalIgnoreCase)))
                {
                    predicate = predicate.Or(x => x.Code == _item.Value);
                }
                _query = _query.Where(predicate);
            }


        }

            if (Search != null && Search != String.Empty)
            {
                var _keywords = Search.Split(' ');

                foreach (var _key in _keywords)
                {
                    _query = _query
                         .Where(x => x.Title.ToLower().Contains(_key.ToLower())
                         || x.Code.ToLower().Contains(_key.ToLower())
                         || x.Category.ToLower().Contains(_key.ToLower())
                         || x.ItemGroup.ToLower().Contains(_key.ToLower())
                         || x._searchTags.ToLower().Contains(_key.ToLower()));
                }
            }

            if (Sort != null && Sort != String.Empty)
            {
                switch (Sort.ToLower())
                {
                    case "createddate":
                        return _query
                                .OrderBy(x => x.Created);

                    case "modifieddate":
                        return _query
                                .OrderBy(x => x.Modified);

                    case "createddate desc":
                        return _query
                                .OrderByDescending(x => x.Created);

                    case "modifieddate desc":
                        return _query
                                .OrderByDescending(x => x.Modified);


                }
            }
            return _query.OrderByDescending(x => x.Category).ThenBy(x=>x.ItemGroup).ThenBy(x=>x.Title);
      
    }

    public async Task<int> Create(DesignScriptItem Entity)
    {
        
            if (Entity.Title == null || Entity.Title == string.Empty)
                throw new EntityServiceException("Title is required. Please enter proper title!");

            var _project = await db.Projects.AsNoTracking().SingleOrDefaultAsync(x => x.ID == Entity.ProjectID);
            if (_project == null) throw new EntityServiceException("Project not found!");


            var _existing=await db.DesignScriptItems.AsNoTracking().FirstOrDefaultAsync(x=>x.ProjectID == Entity.ProjectID && x.Title==Entity.Title
            && x.Category==Entity.Category && x.SubCategory == Entity.SubCategory && x.ItemGroup==Entity.ItemGroup && x.MasterID==Entity.MasterID);

        if (_existing != null) throw new EntityServiceException("This Item already exists in the Project!");

        Entity.ProjectUID=_project.UID; 
        Entity.ProjectCode=_project.Code;

        if (Entity.MasterID == null)
        {
            var projectItems = await db.DesignScriptItems.AsNoTracking()
                .Where(x => x.ProjectID == Entity.ProjectID)
                .Select(x=> new
                {
                    x.ProjectID,
                    x.Code,
                })
                .ToListAsync();

            // Start the code flag at 1 and increment until we find a unique code.
            int codeFlag = 1;
            string categoryKey = McvConstant.DESIGN_SCRIPT_CATEGORY_ABBREIVIATIONS
                .FirstOrDefault(x => x.Key.ToUpper() == Entity.Category.ToUpper()).Value;

            string generatedCode;

            do
            {
                generatedCode = $"{Entity.DSR.Substring(0, 1).ToUpper()}{categoryKey}{DataTools.GetAbbreviation(Entity.ItemGroup.ToUpper(), 2, 2)}{codeFlag.ToString("D2")}";
                codeFlag++;
            }
            while (projectItems.Any(x => x.ProjectID == Entity.ProjectID && x.Code == generatedCode));

            Entity.CodeFlag = codeFlag - 1; // Adjust the code flag after exiting loop
            Entity.Code = generatedCode;
        }

        return await base.Create(Entity);
    }

    public async Task Update(DesignScriptItem Entity)
    {
        var existing=await db.DesignScriptItems.AsNoTracking()
            .FirstOrDefaultAsync(x=>x.ID==Entity.ID);

        
        if (existing.MasterID!=null && Entity.MasterID == null)
        {
            var projectItems = await db.DesignScriptItems.AsNoTracking()
                .Where(x => x.ProjectID == Entity.ProjectID)
                .Select(x => new
                {
                    x.ProjectID,
                    x.Code,
                })
                .ToListAsync();

            // Start the code flag at 1 and increment until we find a unique code.
            int codeFlag = 1;
            string categoryKey = McvConstant.DESIGN_SCRIPT_CATEGORY_ABBREIVIATIONS
                .FirstOrDefault(x => x.Key.ToUpper() == Entity.Category.ToUpper()).Value;

            string generatedCode;

            do
            {
                generatedCode = $"{Entity.DSR.Substring(0, 1).ToUpper()}{categoryKey}{DataTools.GetAbbreviation(Entity.ItemGroup.ToUpper(), 2, 2)}{codeFlag.ToString("D2")}";
                codeFlag++;
            }
            while (projectItems.Any(x => x.ProjectID == Entity.ProjectID && x.Code == generatedCode));

            Entity.CodeFlag = codeFlag - 1; // Adjust the code flag after exiting loop
            Entity.Code = generatedCode;
        }

        await base.Update(Entity);
    }
}