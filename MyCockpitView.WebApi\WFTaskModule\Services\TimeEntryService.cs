﻿


using System.Data;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.CoreModule;
using MyCockpitView.Utility.Common;
using MyCockpitView.Utility.Excel;
using MyCockpitView.WebApi.Exceptions;
using MyCockpitView.WebApi.Services;
using MyCockpitView.WebApi.WFTaskModule.Entities;

namespace MyCockpitView.WebApi.WFTaskModule.Services;

public class TimeEntryService : BaseEntityService<TimeEntry>, ITimeEntryService
{
    public TimeEntryService(EntitiesContext db) : base(db) { }

    public IQueryable<TimeEntry> Get(IEnumerable<QueryFilter>? Filters = null, string? Search = null, string? Sort = null)
    {

        var _query = base.Get(Filters);

        if (Search != null && Search != String.Empty)
        {
            _query = _query.Include(x => x.Contact);
            _query = _query.Where(x => x.TaskTitle.ToLower().Contains(Search.ToLower())
                                         || x.Entity.ToLower().Contains(Search.ToLower())
                                         || x.EntityTitle.ToLower().Contains(Search.ToLower())
                                                || (x.Contact.FirstName + " " + x.Contact.LastName).ToLower().Contains(Search.ToLower())
                                            );

        }

        //Apply filters
        if (Filters != null)
        {


            if (Filters.Where(x => x.Key.Equals("entity", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<TimeEntry>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("entity", StringComparison.OrdinalIgnoreCase)))
                {
                    predicate = predicate.Or(x => x.Entity != null && x.Entity == _item.Value);
                }
                _query = _query.Where(predicate);
            }

            if (Filters.Where(x => x.Key.Equals("entityID", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<TimeEntry>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("entityID", StringComparison.OrdinalIgnoreCase)))
                {
                    predicate = predicate.Or(x => x.EntityID != null && x.EntityID.ToString() == _item.Value);
                }
                _query = _query.Where(predicate);
            }

            if (Filters.Where(x => x.Key.Equals("contactID", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<TimeEntry>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("contactID", StringComparison.OrdinalIgnoreCase)))
                {
                    var isNumeric = Convert.ToInt32(_item.Value);

                    predicate = predicate.Or(x => x.ContactID == isNumeric);
                }
                _query = _query.Where(predicate);
            }

            if (Filters.Where(x => x.Key.Equals("WftaskID", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<TimeEntry>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("WFtaskID", StringComparison.OrdinalIgnoreCase)))
                {
                    var isNumeric = Convert.ToInt32(_item.Value);

                    predicate = predicate.Or(x => x.WFTaskID != null && x.WFTaskID == isNumeric);
                }
                _query = _query.Where(predicate);
            }

            if (Filters.Where(x => x.Key.Equals("paused", StringComparison.OrdinalIgnoreCase)).Any())
            {
                _query = _query.Where(x => x.IsPaused == true);
            }

            if (Filters.Where(x => x.Key.Equals("startDate", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var _item = Filters.Where(x => x.Key.Equals("startDate", StringComparison.OrdinalIgnoreCase)).First();

                DateTime result = Convert.ToDateTime(_item.Value);
                _query = _query.Where(x => x.StartDate != null
                            && x.StartDate.Date == result.Date);
            }
            if (Filters.Where(x => x.Key.Equals("endDate", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var _item = Filters.Where(x => x.Key.Equals("endDate", StringComparison.OrdinalIgnoreCase)).First();

                DateTime result = Convert.ToDateTime(_item.Value);
                _query = _query.Where(x => x.EndDate != null
                            && x.EndDate.Value.Date == result.Date);
            }

            if (Filters.Where(x => x.Key.Equals("rangestart", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var _item = Filters.First(x => x.Key.Equals("rangestart", StringComparison.OrdinalIgnoreCase));
                DateTime result = Convert.ToDateTime(_item.Value);
                _query = _query.Where(x => x.StartDate >= result || (x.EndDate != null && x.EndDate >= result));
            }

            if (Filters.Where(x => x.Key.Equals("rangeend", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var _item = Filters.First(x => x.Key.Equals("rangeend", StringComparison.OrdinalIgnoreCase));

                DateTime result = Convert.ToDateTime(_item.Value);
                var end = result.AddDays(1);
                _query = _query.Where(x => x.StartDate < end || (x.EndDate != null && x.EndDate < end));

            }
        }

        if (Sort != null && Sort != String.Empty)
        {
            var _orderedQuery = _query.OrderBy(l => 0);
            var keywords = Sort.Replace("asc", "").Split(',');

            foreach (var key in keywords)
            {
                if (key.Trim().Equals("created", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenBy(x => x.Created);

                else if (key.Trim().Equals("created desc", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenByDescending(x => x.Created);

                else if (key.Trim().Equals("modified", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenBy(x => x.Modified);

                else if (key.Trim().Equals("modified desc", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenByDescending(x => x.Modified);

                else if (key.Trim().Equals("startdate", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenBy(x => x.StartDate);

                else if (key.Trim().Equals("startdate desc", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenByDescending(x => x.StartDate);

                else if (key.Trim().Equals("enddate", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenBy(x => x.EndDate);

                else if (key.Trim().Equals("enddate desc", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenByDescending(x => x.EndDate);


            }

            return _orderedQuery;
        }

        return _query
          .OrderByDescending(x => x.StartDate);

    }

    public async Task<TimeEntry?> GetById(int Id)
    {
        return
            await db.TimeEntries.AsNoTracking()
            .Include(x => x.Contact)
            .SingleOrDefaultAsync(x => x.ID == Id);

    }



    public async Task<int> Create(TimeEntry Entity, bool AllowOverlapp = false)
    {

        //Validate Time & Overlapping
        await ValidateTime(Entity, false, AllowOverlapp);


        if (Entity.WFTaskID != null)
        {
            var _task = await db.WFTasks.AsNoTracking()
               .Where(x => x.ID == Entity.WFTaskID)
               .SingleOrDefaultAsync();

            if (_task != null)
            {
                Entity.Entity = _task.Entity;
                Entity.EntityID = _task.EntityID;
                Entity.EntityTitle = _task.Subtitle;
                Entity.TaskTitle = (_task.Title + " " + _task.Subtitle).Trim();
            }
            else
            {
                Entity.TaskTitle = "MANUAL TIME LOG";
            }
        }

        if (Entity.EndDate != null)
        {
            Entity.StatusFlag = 1;
        }

        Entity.StartDate = new DateTime(Entity.StartDate.Year, Entity.StartDate.Month, Entity.StartDate.Day, Entity.StartDate.Hour, Entity.StartDate.Minute, 0);

        if (Entity.EndDate != null)
            Entity.EndDate = new DateTime(Entity.EndDate.Value.Year, Entity.EndDate.Value.Month, Entity.EndDate.Value.Day, Entity.EndDate.Value.Hour, Entity.EndDate.Value.Minute, 0);

        Entity.ManHours = Convert.ToDecimal(Entity.EndDate != null && Entity.EndDate > Entity.StartDate ? (Entity.EndDate.Value - Entity.StartDate).TotalHours : 0);

        return await base.Create(Entity);

    }


    public async Task Update(TimeEntry Entity)
    {


        await ValidateTime(Entity, true, true);
        if (Entity.EndDate != null) Entity.StatusFlag = 1;

        Entity.StartDate = new DateTime(Entity.StartDate.Year, Entity.StartDate.Month, Entity.StartDate.Day, Entity.StartDate.Hour, Entity.StartDate.Minute, 0);

        if (Entity.EndDate != null)
            Entity.EndDate = new DateTime(Entity.EndDate.Value.Year, Entity.EndDate.Value.Month, Entity.EndDate.Value.Day, Entity.EndDate.Value.Hour, Entity.EndDate.Value.Minute, 0);

        Entity.ManHours = Convert.ToDecimal(Entity.EndDate != null && Entity.EndDate > Entity.StartDate ? (Entity.EndDate.Value - Entity.StartDate).TotalHours : 0);

        await base.Update(Entity);

    }


    public IQueryable<TimeEntryAnalysis> GetAnalysisData(IQueryable<TimeEntry> TimeEntries, bool ISTDates = false)
    {

        var query = TimeEntries;


        return query
            .Include(x => x.Contact)
            .Include(x => x.WFTask.Assessments)
              .Select(x => new TimeEntryAnalysis
              {
                  StartDate = ISTDates ? ClockTools.GetIST(x.StartDate) : x.StartDate,
                  EndDate = ISTDates ? ClockTools.GetIST(x.EndDate != null ? x.EndDate.Value : DateTime.UtcNow) : x.EndDate != null ? x.EndDate.Value : DateTime.UtcNow,
                  ID = x.ID,
                  ContactID = x.ContactID,
                  Entity = x.Entity,
                  EntityTitle = x.EntityTitle,
                  Person = (x.Contact.FirstName + " " + x.Contact.LastName).Trim(),
                  TaskStage = x.TaskTitle,
                  ManHours = x.ManHours,
                  Status = x.IsPaused ? "PAUSED" : "COMPLETED",
                  //  AssessmentPoints = x.WFTask != null && x.WFTask.Assessments.Any() ? x.WFTask.Assessments.Sum(a => a.ScoredPoints) : (decimal?)null,
                  //ManValue = x.ManValue,
                  //  ValueHourRate=x.ValueHourRate,
              });


    }

    public async Task<TimeEntryAnalysis> GetAnalysisDataTotal(IQueryable<TimeEntry> TimeEntries)
    {
        var _query = await GetAnalysisData(TimeEntries)
            .Select(x => new
            {
                x.ManHours,
            }).ToListAsync();


        return new TimeEntryAnalysis
        {

            ManHours = _query.Select(x => x.ManHours).Sum(),

        };

    }

    public async Task<byte[]> GetAnalysisExcel(IEnumerable<QueryFilter>? Filters = null, string? Search = null, string? Sort = null)
    {

        var _dataSet = new DataSet();
        var _query = Get(Filters, Search, Sort).Where(x => x.EndDate != null);
        var _data = GetAnalysisData(_query, true);
        _dataSet.Tables.Add(DataTools.ToDataTable(await _data.ToListAsync()));

        return ExcelUtility.ExportExcel(_dataSet);

    }


    public async Task EndTimeLog(int WFTaskID, bool IsPaused = false)
    {

        var Entity = await db.TimeEntries.AsNoTracking()
            .Where(x => x.WFTaskID != null && x.WFTaskID == WFTaskID)
                 .Where(x => x.StatusFlag == 0 && x.EndDate == null)
                 .SingleOrDefaultAsync();

        if (Entity == null)
        {
            return;
        }

        Entity.EndDate = DateTime.UtcNow;
        Entity.IsPaused = IsPaused;
        Entity.StatusFlag = 1;

        await ValidateTime(Entity, true, true);
        if (Entity.EndDate != null) Entity.StatusFlag = 1;

        Entity.StartDate = new DateTime(Entity.StartDate.Year, Entity.StartDate.Month, Entity.StartDate.Day, Entity.StartDate.Hour, Entity.StartDate.Minute, 0);

        if (Entity.EndDate != null)
            Entity.EndDate = new DateTime(Entity.EndDate.Value.Year, Entity.EndDate.Value.Month, Entity.EndDate.Value.Day, Entity.EndDate.Value.Hour, Entity.EndDate.Value.Minute, 0);

        Entity.ManHours = Convert.ToDecimal(Entity.EndDate != null && Entity.EndDate > Entity.StartDate ? (Entity.EndDate.Value - Entity.StartDate).TotalHours : 0);


        db.Entry(Entity).State = EntityState.Modified;

        await db.SaveChangesAsync();

    }
    public async Task StartTimeLog(int WFTaskID)
    {

        var Entity = new TimeEntry()
        {
            WFTaskID = WFTaskID,
            StartDate = DateTime.UtcNow,
            TypeFlag = 1,
        };

        if (Entity.WFTaskID != null)
        {
            var _task = await db.WFTasks.AsNoTracking()
               .Where(x => x.ID == Entity.WFTaskID)
               .SingleOrDefaultAsync();

            if (_task != null)
            {
                Entity.Entity = _task.Entity;
                Entity.EntityID = _task.EntityID;
                Entity.EntityTitle = _task.Subtitle;
                Entity.ContactID = _task.ContactID;

                Entity.TaskTitle = (_task.Title + " " + _task.Subtitle).Trim();
            }
            else
            {
                Entity.TaskTitle = "MANUAL TIME LOG";
            }
        }



        var _query = db.TimeEntries.AsNoTracking()
               .Where(x => x.ContactID == Entity.ContactID);

        if (Entity.WFTaskID != null)
            _query = _query.Where(x => x.WFTaskID != null && x.WFTaskID == Entity.WFTaskID);

        if (await _query.Where(x => x.EndDate == null).AnyAsync()) return;

        //Validate Time & Overlapping
        await ValidateTime(Entity);


        if (Entity.EndDate != null)
        {
            Entity.StatusFlag = 1;
        }

        Entity.StartDate = new DateTime(Entity.StartDate.Year, Entity.StartDate.Month, Entity.StartDate.Day, Entity.StartDate.Hour, Entity.StartDate.Minute, 0);

        if (Entity.EndDate != null)
            Entity.EndDate = new DateTime(Entity.EndDate.Value.Year, Entity.EndDate.Value.Month, Entity.EndDate.Value.Day, Entity.EndDate.Value.Hour, Entity.EndDate.Value.Minute, 0);

        Entity.ManHours = Convert.ToDecimal(Entity.EndDate != null && Entity.EndDate > Entity.StartDate ? (Entity.EndDate.Value - Entity.StartDate).TotalHours : 0);

        db.TimeEntries.Add(Entity);
        await db.SaveChangesAsync();
    }
    public async Task StartTimeLog(TimeEntry Entity)
    {

        var _query = db.TimeEntries.AsNoTracking()
                .Where(x => x.ContactID == Entity.ContactID);

        if (Entity.WFTaskID != null)
            _query = _query.Where(x => x.WFTaskID != null && x.WFTaskID == Entity.WFTaskID);

        if (await _query.Where(x => x.EndDate == null).AnyAsync()) return;

        Entity.StartDate = DateTime.UtcNow;
        Entity.TypeFlag = 1;
        //Validate Time & Overlapping
        await ValidateTime(Entity);


        if (Entity.WFTaskID != null)
        {
            var _task = await db.WFTasks.AsNoTracking()
               .Where(x => x.ID == Entity.WFTaskID)
               .SingleOrDefaultAsync();

            if (_task != null)
            {
                Entity.Entity = _task.Entity;
                Entity.EntityID = _task.EntityID;
                Entity.EntityTitle = _task.Subtitle;
                Entity.ContactID = _task.ContactID;

                Entity.TaskTitle = (_task.Title + " " + _task.Subtitle).Trim();
            }
            else
            {
                Entity.TaskTitle = "MANUAL TIME LOG";
            }
        }

        if (Entity.EndDate != null)
        {
            Entity.StatusFlag = 1;
        }

        Entity.StartDate = new DateTime(Entity.StartDate.Year, Entity.StartDate.Month, Entity.StartDate.Day, Entity.StartDate.Hour, Entity.StartDate.Minute, 0);

        if (Entity.EndDate != null)
            Entity.EndDate = new DateTime(Entity.EndDate.Value.Year, Entity.EndDate.Value.Month, Entity.EndDate.Value.Day, Entity.EndDate.Value.Hour, Entity.EndDate.Value.Minute, 0);

        Entity.ManHours = Convert.ToDecimal(Entity.EndDate != null && Entity.EndDate > Entity.StartDate ? (Entity.EndDate.Value - Entity.StartDate).TotalHours : 0);

        db.TimeEntries.Add(Entity);
        await db.SaveChangesAsync();

    }
    public async Task ValidateTime(TimeEntry Entity, bool isUpdate = false, bool allowOverlapp = false)
    {
        if (Entity.StartDate > Entity.EndDate) throw new EntityServiceException("Invalid time slot. Start-time is greater than end-time.");

        if (Entity.StartDate > DateTime.UtcNow)
            throw new EntityServiceException("Start-time is beyond current time " + ClockTools.GetISTNow().ToString("HH:mm") + "!");

        if (Entity.EndDate != null && Entity.EndDate > DateTime.UtcNow)
            throw new EntityServiceException("End-time is beyond current time " + ClockTools.GetISTNow().ToString("HH:mm") + "!");

        var _start = Entity.StartDate.AddSeconds(1);
        var _end = (Entity.EndDate != null ? Entity.EndDate.Value : DateTime.UtcNow).AddSeconds(-1);

        var _query = db.TimeEntries.AsNoTracking()
                    .Where(x => x.ContactID == Entity.ContactID);

        if (isUpdate)
            _query = _query.Where(x => x.ID != Entity.ID);

        TimeEntry _overlapp = null;

        _overlapp = await _query
                    .Where(x => (x.StartDate == _start) //equal_start
                     ).FirstOrDefaultAsync();
        if (_overlapp == null)
            _overlapp = await _query
                       .Where(x => (x.EndDate == _end) //equal_end

                       ).FirstOrDefaultAsync();

        if (_overlapp == null)
            _overlapp = await _query
                       .Where(x => (x.StartDate < _start && x.EndDate > _end) //inside

                        ).FirstOrDefaultAsync();

        if (_overlapp == null)
            _overlapp = await _query
                       .Where(x => (x.StartDate > _start && x.EndDate < _end) //outside

                         ).FirstOrDefaultAsync();

        if (_overlapp == null)
            _overlapp = await _query
                       .Where(x => (x.StartDate < _start && x.EndDate > _start) //start_overlapp

                      ).FirstOrDefaultAsync();

        if (_overlapp == null)
            _overlapp = await _query
                       .Where(x => (x.StartDate > _start && x.StartDate < _end) //end_overlapp
                       ).FirstOrDefaultAsync();


        if (_overlapp != null && !allowOverlapp)
        {
            var _existEnd = _overlapp.EndDate != null ? _overlapp.EndDate.Value : DateTime.UtcNow;

            throw new EntityServiceException("Time entry already exists! \n "
                + _overlapp.TaskTitle + " " + ClockTools.GetIST(_overlapp.StartDate).ToString("dd MMM yyyy HH:mm")
                + " - " + ClockTools.GetIST(_existEnd).ToString("HH:mm"));
        }

    }


}

public class TimeEntryAnalysis
{
    public string? Person { get; set; }
    public string? TaskStage { get; set; }
    public string? Entity { get; set; }
    public string? EntityTitle { get; set; }
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }

    public decimal ManHours { get; set; }
    public string? Status { get; set; }
    public int ID { get; set; }

    public int ContactID { get; set; }


}