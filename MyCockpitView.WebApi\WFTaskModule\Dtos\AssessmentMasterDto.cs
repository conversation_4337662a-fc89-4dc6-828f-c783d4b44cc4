﻿using MyCockpitView.CoreModule;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using AutoMapper;
using MyCockpitView.WebApi.WFTaskModule.Entities;

namespace MyCockpitView.WebApi.WFTaskModule.Dtos;

public class AssessmentMasterDto : BaseEntityDto
{


    [StringLength(255)]

    public string? Category { get; set; }


    [Precision(18, 2)] public decimal Points { get; set; }
}

public class AssessmentMasterDtoMapperProfile : Profile
{
    public AssessmentMasterDtoMapperProfile()
    {


        CreateMap<AssessmentMaster, AssessmentMasterDto>()
                    .ReverseMap();
    }
}