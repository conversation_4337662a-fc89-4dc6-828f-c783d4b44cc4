﻿
using MyCockpitView.WebApi.LibraryModule.Services;

namespace MyCockpitView.WebApi.LibraryModule.Extensions;

public static class ServiceExtensions
{

    public static IServiceCollection RegisterLibraryServices(
     this IServiceCollection services)
    {
        services.AddScoped<ILibraryAttributeMasterService,LibraryAttributeMasterService>();
        services.AddScoped<ILibraryEntityAttachmentService, LibraryEntityAttachmentService>();
        services.AddScoped<ILibraryEntityService, LibraryEntityService>();
        services.AddScoped<ILibraryEntityVendorService, LibraryEntityVendorService>();
        services.AddScoped<ILibraryEntityAttributeService, LibraryEntityAttributeService>();
        services.AddScoped<ILibraryTitleMasterService, LibraryTitleMasterService>();
        return services;
    }
}
