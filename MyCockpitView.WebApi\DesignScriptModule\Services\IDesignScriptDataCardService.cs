﻿using MyCockpitView.WebApi.DesignScriptModule.Entities;
using MyCockpitView.WebApi.Services;

namespace MyCockpitView.WebApi.DesignScriptModule.Services;

public interface IDesignScriptDataCardService : IBaseEntityService<DesignScriptDataCard>
{
    Task ConnectWithEntity(int designScriptEntityID, int designScriptDataCardID);
    Task<int> Copy(int designScriptEntityID, DesignScriptDataCard dataCard, IEnumerable<DesignScriptDataCardAttachment>? attachments = null);
    Task DisconnectFromEntity(int designScriptEntityID, int designScriptDataCardID);
}