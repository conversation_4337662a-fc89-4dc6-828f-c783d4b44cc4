﻿
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.CoreModule;

namespace MyCockpitView.WebApi.ProjectModule.Entities;
public class ProjectNote : BaseEntity
    {
        public string Notes { get; set; }
        public int ProjectID { get; set; }
        public virtual Project? Project { get; set; }

    }
public class ProjectNoteConfiguration : BaseEntityConfiguration<ProjectNote>, IEntityTypeConfiguration<ProjectNote>
{
    public void Configure(EntityTypeBuilder<ProjectNote> builder)
    {
        base.Configure(builder);
    }
}
