﻿

using System.Data;

using MyCockpitView.WebApi.Exceptions;
using MyCockpitView.WebApi.Services;
using MyCockpitView.WebApi.DesignScriptModule.Entities;
using MyCockpitView.CoreModule;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.Utility.Common;
using MyCockpitView.Utility.RDLCClient;
using MyCockpitView.WebApi.AzureBlobsModule;

namespace MyCockpitView.WebApi.DesignScriptModule.Services;

public class DesignScriptItemMasterService : BaseEntityService<DesignScriptItemMaster>, IDesignScriptItemMasterService
{
    public DesignScriptItemMasterService(EntitiesContext db) : base(db) { }

    public IQueryable<DesignScriptItemMaster> Get(IEnumerable<QueryFilter>? Filters = null, string? Search = null, string? Sort = null)
    {

        IQueryable<DesignScriptItemMaster> _query = base.Get(Filters);

        //Apply filters
        if (Filters != null)
        {
            if (Filters.Where(x => x.Key.Equals("dsr", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<DesignScriptItemMaster>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("dsr", StringComparison.OrdinalIgnoreCase)))
                {
                    predicate = predicate.Or(x => x.DSR == _item.Value);
                }
                _query = _query.Where(predicate);
            }
            if (Filters.Where(x => x.Key.Equals("category", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<DesignScriptItemMaster>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("category", StringComparison.OrdinalIgnoreCase)))
                {
                    predicate = predicate.Or(x => x.Category == _item.Value);
                }
                _query = _query.Where(predicate);
            }

            if (Filters.Where(x => x.Key.Equals("itemgroup", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<DesignScriptItemMaster>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("itemgroup", StringComparison.OrdinalIgnoreCase)))
                {
                    predicate = predicate.Or(x => x.ItemGroup == _item.Value);
                }
                _query = _query.Where(predicate);
            }

        }

        if (Search != null && Search != String.Empty)
        {
            var _keywords = Search.Split(' ');

            foreach (var _key in _keywords)
            {
                _query = _query
                     .Where(x => x.Title.ToLower().Contains(_key.ToLower())
                     || x.Code.ToLower().Contains(_key.ToLower())
                     || x.Category.ToLower().Contains(_key.ToLower())
                     || x.ItemGroup.ToLower().Contains(_key.ToLower())
                     || x._searchTags.ToLower().Contains(_key.ToLower()));
            }
        }

        return _query.OrderByDescending(x => x.Category).ThenBy(x => x.ItemGroup).ThenBy(x => x.Title);

    }


    public async Task<int> Create(DesignScriptItemMaster Entity)
    {

        if (Entity.Title == null || Entity.Title == string.Empty)
            throw new EntityServiceException("Title is required. Please enter proper title!");

        var masters = await db.DesignScriptItemMasters.AsNoTracking()
        .Select(x => new
        {
            x.Code
        })
        .ToListAsync();

        // Start the code flag at 1 and increment until we find a unique code.
        int codeFlag = 1;
        string categoryKey = McvConstant.DESIGN_SCRIPT_CATEGORY_ABBREIVIATIONS
            .FirstOrDefault(x => x.Key.ToUpper() == Entity.Category.ToUpper()).Value;

        string generatedCode;

        do
        {
            generatedCode = $"{Entity.DSR.Substring(0, 1).ToUpper()}{categoryKey}{DataTools.GetAbbreviation(Entity.ItemGroup.ToUpper(), 2, 2)}{codeFlag.ToString("D2")}";
            codeFlag++;
        }
        while (masters.Any(x => x.Code == generatedCode));

        Entity.CodeFlag = codeFlag - 1; // Adjust the code flag after exiting loop
        Entity.Code = generatedCode;

        return await base.Create(Entity);

    }


    public async Task<string> GetListReport(IEnumerable<QueryFilter> filters = null, string Search = null, string Sort = null, string RenderType = "PDF")
    {

        var items = await Get(filters, Search, Sort)
                .OrderBy(x => x.Code)
                .Select(x => new
                {
                    x.ID,
                    x.DSR,
                    x.DSRNumber,
                    x.Category,
                    x.ItemGroup,
                    x.Title,
                    x.Specification,
                    x.DrawingSpecification,
                    x.Code,
                })
            .ToListAsync();

        var dsrFilters = new List<string>();
        var categoryFilters = new List<string>();

        if (filters != null)
        {

            if (filters.Where(x => x.Key.Equals("DSR", StringComparison.OrdinalIgnoreCase)).Any())
            {
                dsrFilters = filters.Where(x => x.Key.Equals("DSR", StringComparison.OrdinalIgnoreCase)).Select(x => x.Value.Trim()).ToList();

            }

            if (filters.Where(x => x.Key.Equals("category", StringComparison.OrdinalIgnoreCase)).Any())
            {
                categoryFilters = filters.Where(x => x.Key.Equals("category", StringComparison.OrdinalIgnoreCase)).Select(x => x.Value.Trim()).ToList();
            }

        }

        var sharedService = new SharedService(db); ;
        var dateTimeFormat = await sharedService.GetPresetValue(McvConstant.DATE_FORMAT_WITH_TIME);

        var _reportProperties = new List<ReportProperties>
            {
            new ReportProperties() { PropertyName = "Filter_Categories", PropertyValue = $"{string.Join(", ",categoryFilters)}" },
             new ReportProperties() { PropertyName = "Filter_DSR", PropertyValue = $"{string.Join(", ",dsrFilters)}" },
        };

        var _reportContainerUrl = await sharedService.GetPresetValue(McvConstant.RDLC_REPORT_CONTAINER_URL);

        var DEVMODE = Convert.ToBoolean((await sharedService.GetPresetValue(McvConstant.DEVMODE)));

        var _reportPath = $"ItemMaster.rdlc";

        var _reportDef = new ReportDefinition()
        {
            ReportName = "ItemMaster",
            ReportPath = DEVMODE ? $"{_reportContainerUrl}DEV/{_reportPath}" : $"{_reportContainerUrl}{_reportPath}",
            ReportDataSet = DataTools.ToDataTable(items),
            ReportProperties = _reportProperties,
            Filename = $"ItemMaster-{ClockTools.GetISTNow().ToString("yyMMddHHmm")}",
            RenderType = RenderType
        };

        var reportServiceApi = await sharedService.GetPresetValue(McvConstant.RDLC_PROCESSOR_API);
        _reportDef = await ReportClient.GenerateReport(_reportDef, reportServiceApi);

        var blobName = $"{nameof(DesignScriptItem)}/{_reportDef.Filename}{_reportDef.FileExtension}";

        var container = await sharedService.GetPresetValue(McvConstant.BLOB_CONTAINER_ATTACHMENTS);
        var connetionString = await sharedService.GetPresetValue(McvConstant.AZURE_STORAGE_KEY);
        var azureBlobService = new AzureBlobService();
        return await azureBlobService.UploadAsync(connetionString, container, blobName, new MemoryStream(_reportDef.FileContent));
    }

}