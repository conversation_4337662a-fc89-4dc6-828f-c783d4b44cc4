﻿using System.ComponentModel.DataAnnotations;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.CoreModule;


namespace MyCockpitView.WebApi.ContactModule.Entities;

public class ContactAttachment : BaseBlobEntity
{
    [Required]
    public int ContactID { get; set; }

    public virtual Contact? Contact { get; set; }

    [StringLength(255)]
    public string? Title { get; set; }
}

public class ContactAttachmentConfiguration : BaseBlobEntityConfiguration<ContactAttachment>, IEntityTypeConfiguration<ContactAttachment>
{
    public void Configure(EntityTypeBuilder<ContactAttachment> builder)
    {
        base.Configure(builder);

        builder
            .HasOne(u => u.Contact)
            .WithMany(c => c.Attachments)
            .HasForeignKey(x => x.ContactID).IsRequired();
    }
}
