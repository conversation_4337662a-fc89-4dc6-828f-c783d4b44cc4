﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using MyCockpitView.CoreModule;
using System.ComponentModel.DataAnnotations;

namespace MyCockpitView.WebApi.ProjectModule.Entities
{
    public class ProjectScopeServiceMaster : BaseEntity
    {
        [StringLength(50)]
        public string?  Title { get; set; }

        [StringLength(10)]
        public string?  Abbreviation { get; set; }
    }

    public class ProjectScopeServiceMastertConfiguration : BaseEntityConfiguration<ProjectScopeServiceMaster>, IEntityTypeConfiguration<ProjectScopeServiceMaster>
    {
        public void Configure(EntityTypeBuilder<ProjectScopeServiceMaster> builder)
        {
            base.Configure(builder);

            builder.HasIndex(x => x.Title);
            builder.HasIndex(x => x.Abbreviation);
        }
    }

       
}
