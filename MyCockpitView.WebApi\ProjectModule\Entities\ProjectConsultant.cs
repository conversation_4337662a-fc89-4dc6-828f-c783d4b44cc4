﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using MyCockpitView.WebApi.ContactModule.Entities;
using MyCockpitView.CoreModule;
using System.ComponentModel.DataAnnotations;

namespace MyCockpitView.WebApi.ProjectModule.Entities;

public class ProjectConsultant : BaseEntity
{

    [StringLength(255)]
    public string? Title { get; set; }
    public int ProjectID { get; set; }
    public virtual Project? Project { get; set; }
    public int ContactID { get; set; }
    public virtual Contact? Contact { get; set; }
    [Precision(14, 2)]
    public decimal Fee { get; set; } = 0;
}

public class ProjectConsultantConfiguration : BaseEntityConfiguration<ProjectConsultant>, IEntityTypeConfiguration<ProjectConsultant>
{
    public void Configure(EntityTypeBuilder<ProjectConsultant> builder)
    {
        base.Configure(builder);
        // Properties
        builder.Property(p => p.Title)
            .HasMaxLength(255);

        builder.HasIndex(e => e.Title);


        builder.HasOne(pc => pc.Contact)
            .WithMany()
            .HasForeignKey(pc => pc.ContactID)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);
    }
}