﻿using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using MyCockpitView.CoreModule;

namespace MyCockpitView.WebApi.ContactModule.Entities
{
    public class ContactAppointmentAttachment : BaseBlobEntity
    {
        [Required]
        public int ContactAppointmentID { get; set; }

        public virtual ContactAppointment? ContactAppointment { get; set; }

        [StringLength(255)]
        public string? Title { get; set; }
    }

    public class ContactAppointmentAttachmentConfiguration : BaseEntityConfiguration<ContactAppointmentAttachment>, IEntityTypeConfiguration<ContactAppointmentAttachment>
    {
        public void Configure(EntityTypeBuilder<ContactAppointmentAttachment> builder)
        {
            base.Configure(builder);
        }
    }
}
