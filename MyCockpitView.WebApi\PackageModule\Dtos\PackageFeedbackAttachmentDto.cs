﻿using AutoMapper;
using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.PackageModule.Entities;

namespace MyCockpitView.WebApi.PackageModule.Dtos;

public class PackageFeedbackAttachmentDto : BaseBlobEntityDto
{
    public int PackageFeedbackID { get; set; }
}
public class PackageFeedbackAttachmentDtoMapperProfile : Profile
{
    public PackageFeedbackAttachmentDtoMapperProfile()
    {
        CreateMap<PackageFeedbackAttachment, PackageFeedbackAttachmentDto>()
                          .ReverseMap()
                         .ForMember(dest => dest.PackageFeedback, opt => opt.Ignore());

    }
}