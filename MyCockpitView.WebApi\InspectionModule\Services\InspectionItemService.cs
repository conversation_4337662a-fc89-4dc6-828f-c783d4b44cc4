﻿







using MyCockpitView.WebApi.Exceptions;
using MyCockpitView.WebApi.InspectionModule.Entities;
using MyCockpitView.WebApi.Services;
using MyCockpitView.WebApi.ProjectModule.Entities;
using MyCockpitView.WebApi.WFTaskModule.Services;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.CoreModule;
using MyCockpitView.Utility.Common;

namespace MyCockpitView.WebApi.InspectionModule.Services;

public class InspectionItemService : BaseEntityService<InspectionItem>, IInspectionItemService
{
    public InspectionItemService(EntitiesContext db) : base(db)
    {
    }

    public async Task GenerateTasks()
    {
        var count = 0;
        var _userContacts = await db.Contacts.AsNoTracking()
            .Where(x => x.Username != null)
               .Select(x => x.ID)
               .ToListAsync();

        var _activeProjects = await db.Projects.AsNoTracking()
                                          .Where(x =>
               x.StatusFlag == 0 //Inquiry
            || x.StatusFlag == 1 //pre-proposal
            || x.StatusFlag == 2 //inprogress
                                 || x.StatusFlag == 6 //locked
            ).Select(x => x.ID).ToListAsync();

        var items = await db.InspectionItems.AsNoTracking()
            .Include(x => x.Inspection)
            .Where(x => x.Inspection.StatusFlag == McvConstant.INSPECTION_STATUSFLAG_SENT)
            .Where(x => x.StatusFlag == 0 && x.ActionByContactID != null && !x.IsForwarded && !x.IsVersion
            && x.PackageID == null && x.TodoID == null)
            .Where(x => _userContacts.Any(c => c == x.ActionByContactID))
            .Where(x => x.ProjectID == null || _activeProjects.Any(c => c == x.ProjectID))
            .OrderByDescending(x => x.Created)
            .ToListAsync();

        foreach (var item in items)
        {
            try
            {
                //Console.WriteLine($"Inspection:{item.InspectionTitle}");
                //Console.WriteLine($"Item:{item.Title}|{item.Subtitle}");
                //Check if item task is pending
                var _currentItemTasks = await db.WFTasks.Where(x => x.Entity == nameof(InspectionItem)
         && x.EntityID == item.ID && x.StatusFlag == 0).ToListAsync();

                if (!_currentItemTasks.Any())
                {
                    await AssignItemTasks(item.ID);

                    count++;
                }
            }
            catch (Exception e)
            {
                Console.WriteLine(e.Message);
            }
            //Console.WriteLine(count + " of " + items.Count() + " processed");
            //Console.WriteLine("***************************************************");
        }
    }

    public IQueryable<InspectionItem> Get(IEnumerable<QueryFilter>? Filters = null, string? Search = null, string? Sort = null)
    {

        IQueryable<InspectionItem> _query = base.Get(Filters)
                               ;

        //Apply filters
        if (Filters != null)
        {


            if (Filters.Where(x => x.Key.Equals("isForwarded", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var _item = Filters.First(x => x.Key.Equals("isForwarded", StringComparison.OrdinalIgnoreCase));

                var isBoolean = Boolean.TryParse(_item.Value, out bool result);

                if (isBoolean && result)
                    _query = _query.Where(x => x.IsForwarded == true);
                else
                    _query = _query.Where(x => x.IsForwarded == false);
            }

            if (Filters.Where(x => x.Key.Equals("IsInspection", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var _item = Filters.First(x => x.Key.Equals("IsInspection", StringComparison.OrdinalIgnoreCase));

                var isBoolean = Boolean.TryParse(_item.Value, out bool result);

                if (isBoolean && result)
                    _query = _query.Where(x => x.IsInspection == true);
                else
                    _query = _query.Where(x => x.IsInspection == false);
            }

            if (Filters.Where(x => x.Key.Equals("IsVersion", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var _item = Filters.First(x => x.Key.Equals("IsVersion", StringComparison.OrdinalIgnoreCase));

                var isBoolean = Boolean.TryParse(_item.Value, out bool result);

                if (isBoolean && result)
                    _query = _query.Where(x => x.IsVersion == true);
                else
                    _query = _query.Where(x => x.IsVersion == false);
            }


            if (Filters.Where(x => x.Key.Equals("isDelayed", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var _today = ClockTools.GetISTNow();
                var _item = Convert.ToBoolean(Filters.First(x => x.Key.Equals("isDelayed", StringComparison.OrdinalIgnoreCase)).Value);

                var predicate = PredicateBuilder.False<InspectionItem>();

                if (_item)
                {
                    predicate = predicate.Or(x => x.DueDate != null && x.DueDate.Value.Date <= _today);
                }
                else
                {
                    predicate = predicate.Or(x => x.DueDate != null && x.DueDate.Value.Date > _today);
                }

                _query = _query.Where(x => !x.IsForwarded).Where(predicate);
            }

            if (Filters.Where(x => x.Key.Equals("entity", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<InspectionItem>();
                if (Filters.Where(x => x.Key.Equals("Entity", StringComparison.OrdinalIgnoreCase)
                    && x.Value.Equals(nameof(Project), StringComparison.OrdinalIgnoreCase)).Any())
                {
                    var _activeProjects = db.Projects.AsNoTracking()
                                          .Where(x =>
               x.StatusFlag == McvConstant.PROJECT_STATUSFLAG_INQUIRY //Inquiry
            || x.StatusFlag == McvConstant.PROJECT_STATUSFLAG_PREPOPOSAL //pre-proposal
            || x.StatusFlag == McvConstant.PROJECT_STATUSFLAG_INPROGRESS //inprogress
                                 || x.StatusFlag == McvConstant.PROJECT_STATUSFLAG_LOCKED //locked
            );

                    if (Filters.Where(x => x.Key.Equals("projectPartnerContactID", StringComparison.OrdinalIgnoreCase)).Any())
                    {
                        var predicate2 = PredicateBuilder.False<Project>();
                        foreach (var _item in Filters.Where(x => x.Key.Equals("projectPartnerContactID", StringComparison.OrdinalIgnoreCase)))
                        {
                            int _value = Convert.ToInt32(_item.Value);
                            predicate2 = predicate2.Or(x => x.Associations
                            .Where(a => a.TypeFlag == 0
                            && a.ContactID == _value)
                            .Any()
                            );
                        }
                        _activeProjects = _activeProjects.Include(x => x.Associations).Where(predicate2);
                    }
                    if (Filters.Where(x => x.Key.Equals("projectAssociateContactID", StringComparison.OrdinalIgnoreCase)).Any())
                    {
                        var predicate2 = PredicateBuilder.False<Project>();
                        foreach (var _item in Filters.Where(x => x.Key.Equals("projectAssociateContactID", StringComparison.OrdinalIgnoreCase)))
                        {
                            int _value = Convert.ToInt32(_item.Value);
                            predicate2 = predicate2.Or(x => x.Associations
                            .Where(a => a.TypeFlag == 1
                            && a.ContactID == _value)
                            .Any()
                            );
                        }
                        _activeProjects = _activeProjects.Include(x => x.Associations).Where(predicate2);
                    }
                    if (Filters.Where(x => x.Key.Equals("PartnerOrAssociateContactID", StringComparison.OrdinalIgnoreCase)).Any())
                    {
                        var predicate2 = PredicateBuilder.False<Project>();
                        foreach (var _item in Filters.Where(x => x.Key.Equals("PartnerOrAssociateContactID", StringComparison.OrdinalIgnoreCase)))
                        {
                            int _value = Convert.ToInt32(_item.Value);
                            predicate2 = predicate2.Or(x => x.Associations
                            .Where(a => a.TypeFlag == McvConstant.PROJECT_ASSOCIATION_TYPEFLAG_PARTNER || a.TypeFlag == McvConstant.PROJECT_ASSOCIATION_TYPEFLAG_ASSOCIATE)
                            .Where(a => a.ContactID == _value)
                            .Any()
                            );
                        }
                        _activeProjects = _activeProjects.Include(x => x.Associations).Where(predicate2);
                    }

                    predicate = predicate.Or(x => x.ProjectID != null
                        && _activeProjects.Select(p => p.ID).Where(p => p == x.ProjectID.Value).Any());
                }

                if (Filters.Where(x => x.Key.Equals("Entity", StringComparison.OrdinalIgnoreCase)
                    && x.Value.Equals("NO-ENTITY", StringComparison.OrdinalIgnoreCase)).Any())
                {
                    predicate = predicate.Or(x => x.ProjectID == null);
                }
                _query = _query.Where(predicate);
            }

            if (Filters.Where(x => x.Key.Equals("ActionBy", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<InspectionItem>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("ActionBy", StringComparison.OrdinalIgnoreCase)))
                {
                    predicate = predicate.Or(x => x.ActionBy != null && x.ActionBy == _item.Value);
                }
                _query = _query.Where(predicate);
            }

            if (Filters.Where(x => x.Key.Equals("actionByContactID", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<InspectionItem>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("actionByContactID", StringComparison.OrdinalIgnoreCase)))
                {
                    var isNumeric = Convert.ToInt32(_item.Value);

                    predicate = predicate.Or(x => x.ActionByContactID != null && x.ActionByContactID == isNumeric);
                }
                _query = _query.Where(predicate);
            }

            if (Filters.Where(x => x.Key.Equals("ProjectID", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<InspectionItem>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("ProjectID", StringComparison.OrdinalIgnoreCase)))
                {
                    var isNumeric = Convert.ToInt32(_item.Value);

                    predicate = predicate.Or(x => x.ProjectID != null && x.ProjectID == isNumeric);
                }
                _query = _query.Where(predicate);
            }

            if (Filters.Where(x => x.Key.Equals("meetingContactID", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<InspectionItem>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("meetingContactID", StringComparison.OrdinalIgnoreCase)))
                {
                    var isNumeric = Convert.ToInt32(_item.Value);

                    predicate = predicate.Or(x => x.Inspection.ContactID == isNumeric);
                }
                _query = _query.Include(x => x.Inspection).Where(predicate);
            }

            if (Filters.Where(x => x.Key.Equals("meetingstatusFlag", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<InspectionItem>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("meetingstatusFlag", StringComparison.OrdinalIgnoreCase)))
                {
                    var isNumeric = Convert.ToInt32(_item.Value);

                    predicate = predicate.Or(x => x.Inspection.StatusFlag == isNumeric);
                }
                _query = _query.Include(x => x.Inspection).Where(predicate);
            }


            //if (Filters.Where(x => x.Key.Equals("contactID", StringComparison.OrdinalIgnoreCase)).Any())
            //{
            //    var _associatedProjectsForContact = db.Projects.AsNoTracking()
            //          .Where(x => x.StatusFlag != 4 //completed
            //          && x.StatusFlag != -1); //discarded

            //    var predicate2 = PredicateBuilder.False<Project>();
            //    foreach (var _item in Filters.Where(x => x.Key.Equals("contactID", StringComparison.OrdinalIgnoreCase)))
            //    {
            //        int _value = Convert.ToInt32(_item.Value);
            //        predicate2 = predicate2.Or(x => x.Associations
            //        .Where(a => a.ContactID == _value).Any());
            //    }

            //    _associatedProjectsForContact = _associatedProjectsForContact.Include(x => x.Associations).Where(predicate2);

            //    var predicate = PredicateBuilder.False<InspectionItem>();

            //    foreach (var _item in Filters.Where(x => x.Key.Equals("contactID", StringComparison.OrdinalIgnoreCase)))
            //    {
            //        var isNumeric = Convert.ToInt32(_item.Value);

            //        predicate = predicate.Or(x => (x.ActionByContactID != null && x.ActionByContactID == isNumeric)
            //         || (x.Inspection.ContactID == isNumeric)
            //         || (x.ProjectID != null
            //                && _associatedProjectsForContact.Where(p => p.ID == x.ProjectID.Value).Any())
            //        );
            //    }
            //    _query = _query.Include(x => x.Inspection).Where(predicate);
            //}

            if (Filters.Where(x => x.Key.Equals("PackageID", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<InspectionItem>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("PackageID", StringComparison.OrdinalIgnoreCase)))
                {
                    var isNumeric = Convert.ToInt32(_item.Value);

                    predicate = predicate.Or(x => x.PackageID != null && x.PackageID == isNumeric);
                }
                _query = _query.Where(predicate);
            }

            if (Filters.Where(x => x.Key.Equals("DesignScriptEntityID", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<InspectionItem>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("DesignScriptEntityID", StringComparison.OrdinalIgnoreCase)))
                {
                    var isNumeric = Convert.ToInt32(_item.Value);

                    predicate = predicate.Or(x => x.DesignScriptEntityID != null && x.DesignScriptEntityID == isNumeric);
                }
                _query = _query.Where(predicate);
            }

            if (Filters.Where(x => x.Key.Equals("DesignScriptItemID", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<InspectionItem>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("DesignScriptItemID", StringComparison.OrdinalIgnoreCase)))
                {
                    var isNumeric = Convert.ToInt32(_item.Value);

                    predicate = predicate.Or(x => x.DesignScriptItemID != null && x.DesignScriptItemID == isNumeric);
                }
                _query = _query.Where(predicate);
            }

            if (Filters.Where(x => x.Key.Equals("dueDate", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var _item = Filters.First(x => x.Key.Equals("dueDate", StringComparison.OrdinalIgnoreCase));
                DateTime result = Convert.ToDateTime(_item.Value).Date;
                _query = _query.Where(x => x.DueDate != null && x.DueDate.Value.Date == result);
            }

            if (Filters.Where(x => x.Key.Equals("rangestart", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var _item = Filters.First(x => x.Key.Equals("rangestart", StringComparison.OrdinalIgnoreCase));

                DateTime result = Convert.ToDateTime(_item.Value);
                _query = _query.Where(x => x.DueDate >= result);
            }

            if (Filters.Where(x => x.Key.Equals("rangeend", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var _item = Filters.First(x => x.Key.Equals("rangeend", StringComparison.OrdinalIgnoreCase));

                DateTime result = Convert.ToDateTime(_item.Value);
                var end = result.AddDays(1);
                _query = _query.Where(x => x.DueDate < end);
            }

            if (Filters.Where(x => x.Key.Equals("subject", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<InspectionItem>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("subject", StringComparison.OrdinalIgnoreCase)))
                {
                    predicate = predicate.Or(x => x.Inspection.Title == _item.Value);
                }
                _query = _query.Include(x => x.Inspection).Where(predicate);
            }
            if (Filters.Where(x => x.Key.Equals("meetingstatusFlag", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<InspectionItem>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("meetingstatusFlag", StringComparison.OrdinalIgnoreCase)))
                {
                    var isNumeric = Convert.ToInt32(_item.Value);

                    predicate = predicate.Or(x => x.Inspection.StatusFlag == isNumeric);
                }
                _query = _query.Include(x => x.Inspection).Where(predicate);
            }

            if (Filters.Where(x => x.Key.Equals("usersOnly", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var _activeUserContactIDs = db.Contacts.AsNoTracking()
                   .Where(x => x.Username != null)
                    .Select(x => x.ID).ToList();

                var predicate = PredicateBuilder.False<InspectionItem>();
                predicate = predicate.Or(x => (x.ActionByContactID != null
                && _activeUserContactIDs.Any(c => c == x.ActionByContactID))
                //|| (_activeUserContactIDs.Any(c => c == x.Inspection.ContactID))
                );

                _query = _query.Include(x => x.Inspection).Where(predicate);
            }
        }

        if (Search != null && Search != string.Empty)
        {
            var _keywords = Search.Split(' ');

            foreach (var _key in _keywords)
            {
                _query = _query
                         .Where(x => x.Title.ToLower().Contains(_key.ToLower())
                         || x.Subtitle.ToLower().Contains(_key.ToLower())
                         || x.InspectionTitle.ToLower().Contains(_key.ToLower())
                         || x.ActionBy.ToLower().Contains(_key.ToLower())
                         || x.CreatedBy.ToLower().Contains(_key.ToLower()));
            }
        }

        if (Sort != null && Sort != String.Empty)
        {
            var _orderedQuery = _query.Include(x => x.Inspection).OrderBy(l => 0);
            var keywords = Sort.Split(',');

            foreach (var key in keywords)
            {
                if (key.Equals("dueDate", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenBy(x => x.DueDate);
                else if (key.Equals("dueDate-desc", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenByDescending(x => x.DueDate);
                else if (key.Equals("actionby", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenBy(x => x.ActionBy);
                else if (key.Equals("subject", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenBy(x => x.Inspection.Title);
            }

            return _orderedQuery;
        }

        return _query
          .OrderBy(x => x.DueDate).ThenBy(x => x.ActionBy);

    }

    public async Task<InspectionItem?> GetById(int Id)
    {

        return await db.InspectionItems
            .AsNoTracking()

            .Include(x => x.Attachments)
             .SingleOrDefaultAsync(i => i.ID == Id);

    }

    public async Task<InspectionItem?> GetById(Guid Id)
    {

        return await db.InspectionItems
            .AsNoTracking()
            .Include(x => x.Attachments)
            .SingleOrDefaultAsync(x => x.UID == Id);

    }

    public IQueryable<InspectionItem> GetByInspection(int InspectionID)
    {

        return db.InspectionItems
            .AsNoTracking()
            .Include(x => x.Attachments)
                //.Include(x => x.Links)
                .Where(x => x.InspectionID == InspectionID)
                .OrderBy(x => x.OrderFlag);

    }

    public IQueryable<InspectionItemGroup> GetGroups(IQueryable<InspectionItem> ItemQuery, string[] GroupBy = null)
    {

        if (GroupBy != null
            && GroupBy.Where(x => x.Equals("DueDate", StringComparison.OrdinalIgnoreCase)).Any()
            && GroupBy.Where(x => x.Equals("ActionBy", StringComparison.OrdinalIgnoreCase)).Any()
            && GroupBy.Where(x => x.Equals("Subject", StringComparison.OrdinalIgnoreCase)).Any())
        {
            return ItemQuery
                    .Include(x => x.Inspection)
                    .GroupBy(x => new { x.DueDate, x.ActionBy, Subject = x.Inspection.Title })

                    .Select(x => new InspectionItemGroup
                    {
                        Guid = Guid.NewGuid().ToString(),
                        DueDate = x.Key.DueDate,
                        Subject = x.Key.Subject,
                        ActionBy = x.Key.ActionBy,
                        ItemCount = x.Count()
                    })
                    .OrderBy(x => x.DueDate)
                    .ThenBy(x => x.ActionBy)
                    .ThenBy(x => x.Subject);
        }
        else if (GroupBy != null && GroupBy.Where(x => x.Equals("DueDate", StringComparison.OrdinalIgnoreCase)).Any()
           && GroupBy.Where(x => x.Equals("ActionBy", StringComparison.OrdinalIgnoreCase)).Any())
        {
            return ItemQuery
                    .GroupBy(x => new { x.DueDate, x.ActionBy })

                    .Select(x => new InspectionItemGroup
                    {
                        Guid = Guid.NewGuid().ToString(),
                        DueDate = x.Key.DueDate,
                        ActionBy = x.Key.ActionBy,
                        ItemCount = x.Count()
                    })
                    .OrderBy(x => x.DueDate)
                    .ThenBy(x => x.ActionBy);
        }
        else if (GroupBy != null && GroupBy.Where(x => x.Equals("DueDate", StringComparison.OrdinalIgnoreCase)).Any()
           && GroupBy.Where(x => x.Equals("Subject", StringComparison.OrdinalIgnoreCase)).Any())
        {
            return ItemQuery
                    .Include(x => x.Inspection)
                    .GroupBy(x => new { x.DueDate, Subject = x.Inspection.Title })

                    .Select(x => new InspectionItemGroup
                    {
                        Guid = Guid.NewGuid().ToString(),
                        DueDate = x.Key.DueDate,
                        Subject = x.Key.Subject,
                        ItemCount = x.Count()
                    })
                    .OrderBy(x => x.DueDate)
                    .ThenBy(x => x.Subject);
        }
        else if (GroupBy != null && GroupBy.Where(x => x.Equals("ActionBy", StringComparison.OrdinalIgnoreCase)).Any()
           && GroupBy.Where(x => x.Equals("Subject", StringComparison.OrdinalIgnoreCase)).Any())
        {
            return ItemQuery
                    .Include(x => x.Inspection)
                    .GroupBy(x => new { x.ActionBy, Subject = x.Inspection.Title })

                    .Select(x => new InspectionItemGroup
                    {
                        Guid = Guid.NewGuid().ToString(),
                        Subject = x.Key.Subject,
                        ActionBy = x.Key.ActionBy,
                        ItemCount = x.Count()
                    })
                    .OrderBy(x => x.ActionBy)
                    .ThenBy(x => x.Subject);
        }
        else if (GroupBy != null && GroupBy.Where(x => x.Equals("ActionBy", StringComparison.OrdinalIgnoreCase)).Any())
        {
            return ItemQuery
                    .GroupBy(x => new { x.ActionBy })

                    .Select(x => new InspectionItemGroup
                    {
                        Guid = Guid.NewGuid().ToString(),
                        ActionBy = x.Key.ActionBy,
                        ItemCount = x.Count()
                    })
                    .OrderBy(x => x.ActionBy);
        }
        else if (GroupBy != null && GroupBy.Where(x => x.Equals("Subject", StringComparison.OrdinalIgnoreCase)).Any())
        {
            return ItemQuery
                    .Include(x => x.Inspection)
                    .GroupBy(x => new { Subject = x.Inspection.Title })

                    .Select(x => new InspectionItemGroup
                    {
                        Guid = Guid.NewGuid().ToString(),
                        Subject = x.Key.Subject,
                        ItemCount = x.Count()
                    })
                    .OrderBy(x => x.Subject);
        }
        else //if (GroupBy == null || GroupBy.Where(x => x.Equals("DueDate", StringComparison.OrdinalIgnoreCase)).Any())
        {
            return ItemQuery
                    .GroupBy(x => new { x.DueDate })

                    .Select(x => new InspectionItemGroup
                    {
                        Guid = Guid.NewGuid().ToString(),
                        DueDate = x.Key.DueDate,
                        ItemCount = x.Count()
                    })
                    .OrderBy(x => x.DueDate);
        }

    }

    public async Task<IEnumerable<InspectionItem>> GetPreviousItem(int ItemID)
    {

        var _history = new List<InspectionItem>();

        var _item = await db.InspectionItems.AsNoTracking()
            .SingleOrDefaultAsync(x => x.ID == ItemID);

        if (_item != null && _item.PreviousItemID != null)
        {
            var _previousItemID = _item.PreviousItemID;
            do
            {
                var _previousItem = await db.InspectionItems.AsNoTracking()

                    .Include(x => x.Attachments)
                    .Where(x => !x.IsVersion)
                .SingleOrDefaultAsync(x => x.ID == _item.PreviousItemID);
                _history.Add(_previousItem);
                _previousItemID = _previousItem.PreviousItemID;
            } while (_previousItemID != null);
        }

        return _history.OrderByDescending(x => x.Modified);

    }

    public async Task Update(InspectionItem UpdatedEntity)
    {

        if (UpdatedEntity.ActionByContactID == 0)
        {
            UpdatedEntity.ActionByContactID = null;
        }
        var _originalEntity = await db.InspectionItems.AsNoTracking().SingleOrDefaultAsync(x => x.ID == UpdatedEntity.ID);

        if (_originalEntity == null) throw new EntityServiceException("Inspection item not found!");

        if (UpdatedEntity.Title == null || UpdatedEntity.Title == String.Empty) throw new EntityServiceException("Item title cannot be empty!");

        var inspection = await db.Inspections.AsNoTracking().Where(x => x.ID == UpdatedEntity.InspectionID).SingleOrDefaultAsync();
        if (inspection == null) throw new EntityServiceException("Inspection not found for this item!");
        UpdatedEntity.ProjectID = inspection.ProjectID;
        UpdatedEntity.InspectionDate = inspection.StartDate;
        UpdatedEntity.InspectionTitle = inspection.Title;

        UpdatedEntity.NotDiscussed = false;


        if (UpdatedEntity.DueDate != null)
        {
            var sharedService = new SharedService(db);
            UpdatedEntity.DueDate = ClockTools.GetUTC(ClockTools.GetIST(UpdatedEntity.DueDate.Value).Date.AddMinutes(await sharedService.GetBusinessEndMinutesIST()));
        }

        if (UpdatedEntity.ActionByContactID == null)
        {
            UpdatedEntity.ActionBy = null;
        }



        if (inspection.StatusFlag >= McvConstant.INSPECTION_STATUSFLAG_SENT) //after Sent
        {
            if (UpdatedEntity.Comment != _originalEntity.Comment
              || UpdatedEntity.StatusFlag != _originalEntity.StatusFlag
              || UpdatedEntity.ActionBy != _originalEntity.ActionBy
              || UpdatedEntity.DueDate != _originalEntity.DueDate
              || UpdatedEntity.Progress != _originalEntity.Progress
              )  //Any change after Minutes sent
            {
                UpdatedEntity.ReminderCount = 0;
                UpdatedEntity.UpdateFrom = "POST-INSPECTION";
                if (!UpdatedEntity.SendUpdate)
                {
                    UpdatedEntity.PreviousActionBy = _originalEntity.ActionBy;
                    UpdatedEntity.PreviousDueDate = _originalEntity.DueDate;
                    UpdatedEntity.PreviousComment = _originalEntity.Comment;
                    UpdatedEntity.PreviousProgress = _originalEntity.PreviousProgress;

                    var meetingService = new InspectionService(db);
                    UpdatedEntity.PreviousHistory = await meetingService.GetInspectionItemHistoryString(UpdatedEntity.ID);
                }

                //UpdatedEntity.SendUpdate = true;
            }
        }


        await base.Update(UpdatedEntity);

    }

    public async Task<int> Create(InspectionItem Entity)
    {
        if (Entity.ActionByContactID == 0)
        {
            Entity.ActionByContactID = null;
        }
        if (Entity.Title == null
                || Entity.Title == String.Empty) throw new EntityServiceException("Item title cannot be empty!");


        if (Entity.ActionByContactID == null) Entity.ActionBy = null;

        if (Entity.DueDate != null)
        {
            var sharedService = new SharedService(db); ;
            Entity.DueDate = ClockTools.GetUTC(ClockTools.GetIST(Entity.DueDate.Value).Date.AddMinutes(await sharedService.GetBusinessEndMinutesIST()));
        }


        var inepection = await db.Inspections.AsNoTracking().Where(x => x.ID == Entity.InspectionID).SingleOrDefaultAsync();
        if (inepection == null) throw new EntityServiceException("Inspection not found!");

        Entity.ProjectID = inepection.ProjectID;
        Entity.InspectionDate = inepection.StartDate;
        Entity.InspectionTitle = inepection.Title;
        Entity.NotDiscussed = false;

        Entity.TypeFlag = inepection.TypeFlag;


        var _lastItem = await db.InspectionItems
            .Where(x => !x.IsVersion)
            .Where(x => x.DesignScriptEntityID == Entity.DesignScriptEntityID)
            .OrderByDescending(x => x.Created)
            .FirstOrDefaultAsync();

        if (_lastItem != null)
        {
            Entity.PreviousItemID = _lastItem.ID;
            Entity.PreviousActionBy = _lastItem.ActionBy;
            Entity.PreviousProgress = _lastItem.Progress;
            Entity.PreviousDueDate = _lastItem.DueDate;
            Entity.PreviousComment = _lastItem.Comment;
            var meetingService = new InspectionService(db);
            Entity.PreviousHistory = await meetingService.GetInspectionItemHistoryString(_lastItem.ID);
            _lastItem.IsForwarded = true;

        }


        if (Entity.StatusFlag == 1)
        {
            Entity.DueDate = null;
            Entity.ActionBy = null;
            Entity.ActionByContactID = null;
        }

        //if (Entity.PreviousItemID != null)
        //{
        //    //Check if previous item task is pending
        //    await CompleteItemTasks(Entity.PreviousItemID.Value);
        //}

        if (Entity.DesignScriptEntityID != null)
        {
            var dsEntity = await db.DesignScriptEntities.AsNoTracking()
                .Include(x => x.Parent)
                .SingleOrDefaultAsync(x => x.ID == Entity.DesignScriptEntityID);
            if (dsEntity != null)
            {
                if (dsEntity.TypeFlag == McvConstant.DESIGN_SCRIPT_ENTITY_TYPEFLAG_ZONE)
                {
                    Entity.Zone = $"{dsEntity.Code}-{dsEntity.Title}";
                }
                else if (dsEntity.TypeFlag == McvConstant.DESIGN_SCRIPT_ENTITY_TYPEFLAG_SPACE)
                {
                    Entity.Zone = $"{dsEntity.Parent.Code}-{dsEntity.Parent.Title}";
                    Entity.Space = $"{dsEntity.Code}-{dsEntity.Title}";
                }
                else
                {
                    var zone = await db.DesignScriptEntities.AsNoTracking()
                .SingleOrDefaultAsync(x => x.ID == dsEntity.Parent.ParentID);
                    if (zone != null)
                        Entity.Zone = $"{zone.Code}-{zone.Title}";

                    Entity.Space = $"{dsEntity.Parent.Code}-{dsEntity.Parent.Title}";
                    Entity.Element = $"{dsEntity.Code}-{dsEntity.Title}";
                }
            }
        }


        return await base.Create(Entity);

    }

    public async Task Delete(int Id)
    {

        var Entity = await Get()
        .Include(x => x.Attachments)
             .SingleOrDefaultAsync(i => i.ID == Id);

        if (Entity == null) throw new EntityServiceException($"{nameof(InspectionRecipient)} not found!");

        if (Entity.PreviousItemID != null)
        {
            var _previousItem = await Get()
                .Where(x => !x.IsVersion)
                .Where(x => x.ID == Entity.PreviousItemID.Value).SingleOrDefaultAsync();

            if (_previousItem != null)
            {

                _previousItem.IsForwarded = false;
                _previousItem.PackageID = Entity.PackageID;
                _previousItem.TodoID = Entity.TodoID;
                db.Entry(_previousItem).State = EntityState.Modified;
                await db.SaveChangesAsync();

                if (_previousItem.StatusFlag == 0 && _previousItem.PackageID == null && _previousItem.TodoID == null)
                    await AssignItemTasks(_previousItem.ID);

            }

        }

        var attachmentService = new BaseAttachmentService<InspectionItemAttachment>(db);
        foreach (var x in Entity.Attachments)
        {
            await attachmentService.Delete(x.ID);
        }



        var taskService = new WFTaskService(db);
        var tasks = await taskService.Get()
            .Where(x => x.Entity == nameof(InspectionItem) && x.EntityID == Id)
            .Select(x => x.ID)
            .ToListAsync();

        foreach (var x in tasks)
        {
            await taskService.Delete(x);
        }

        await base.Delete(Id);

    }
    public async Task AssignItemTasks(int itemID)
    {

        var _item = await db.InspectionItems
            .SingleOrDefaultAsync(x => x.ID == itemID);
        if (_item == null || _item.IsVersion) return;

        var _activeProjectIDs = await db.Projects.AsNoTracking()
           .Where(x =>
              x.StatusFlag == 0 //Inquiry
           || x.StatusFlag == 1 //pre-proposal
           || x.StatusFlag == 2 //inprogress
                                //|| x.StatusFlag == 6 //locked
           )
           .Select(x => x.ID).ToListAsync();

        if (_item.StatusFlag != 0
           || _item.ActionByContactID == null
           || _item.IsForwarded
           || _item.TodoID != null
           || (_item.ProjectID != null && !_activeProjectIDs.Any(p => p == _item.ProjectID))
           )
        {
            await CompleteItemTasks(_item.ID);
            return;
        }

        if (_item.PackageID != null)
        {
            var _package = await db.Packages.AsNoTracking().Where(x => x.ID == _item.PackageID).FirstOrDefaultAsync();

            if (_package != null && _package.StatusFlag == 0)
            {
                await CompleteItemTasks(_item.ID);
                return;
            }

        }


        if (!await db.Contacts
            .Where(c => c.Username != null)
            .Select(c => c.ID).AnyAsync(c => c == _item.ActionByContactID))
        {
            await CompleteItemTasks(_item.ID); return;
        }
        //Check if current item task is pending


        if (_item.PreviousItemID != null)
        {
            var _previousItem = await db.InspectionItems.AsNoTracking()
                .Where(x => !x.IsVersion)
            .SingleOrDefaultAsync(x => x.ID == _item.PreviousItemID);
            if (_previousItem != null)
            {
                //Check if previous item task is pending
                await CompleteItemTasks(_previousItem.ID);
            }
        }


        _item.PackageID = null;
        _item.TodoID = null;
        _item.IsForwarded = false;

        var taskService = new WFTaskService(db);
        await taskService.StartFlow(nameof(InspectionItem), _item.TypeFlag, _item.ID);

    }
    public async Task CompleteItemTasks(int itemID)
    {

        var _currentItemTasks = await db.WFTasks.Where(x => x.Entity == nameof(InspectionItem)
        && x.EntityID == itemID
        && x.StatusFlag != 1).ToListAsync();

        if (_currentItemTasks.Any())
        {

            foreach (var task in _currentItemTasks)
            {
                task.StatusFlag = 1;
                task.Comment = "Item Updated";
                task.CompletedDate = DateTime.UtcNow;


                await db.SaveChangesAsync();
            }
        }

    }

    public async Task<IEnumerable<int>> GetAssigneeContactIDs(int entityID, string stageCode, string propertyName)
    {
        var Entity = await Get()
            .Where(x => x.ID == entityID).SingleOrDefaultAsync();

        if (Entity == null) throw new EntityServiceException($"{nameof(Inspection)} not found!");

        var _list = new List<int>();
        if (stageCode.Equals("AGENDA_ACTION", StringComparison.OrdinalIgnoreCase))//Log Travel time
        {

            if (await db.Contacts
            .Where(c => c.Username != null)
            .Select(c => c.ID).AnyAsync(x => x == Entity.ActionByContactID))
                _list.Add(Entity.ActionByContactID.Value);

            if (Entity.ProjectID != null)
            {
                var _projectAssociates = await db.ProjectAssociations.AsNoTracking()
                    .Where(x => x.ProjectID == Entity.ProjectID)
                    .Where(x => x.TypeFlag == McvConstant.PROJECT_ASSOCIATION_TYPEFLAG_PARTNER || x.TypeFlag == McvConstant.PROJECT_ASSOCIATION_TYPEFLAG_ASSOCIATE)
                    .Select(x => x.ContactID)
                    .ToListAsync();

                _list = _list.Concat(_projectAssociates).ToList();
            }
        }
        else
        {
            throw new EntityServiceException($"{nameof(InspectionItem)} Task assignee not found for stage {stageCode}!");
        }
        return _list;
    }

    public async Task<dynamic> GetTaskByStage(string Entity, int EntityID, string StageCode, string StageTitle, decimal StageDuration, DateTime? FollowUpDate = null)
    {


        var sharedService = new SharedService(db); ;

        var _entity = await Get()
            .Include(x => x.Inspection)
            .SingleOrDefaultAsync(x => x.ID == EntityID);

        if (_entity == null) throw new EntityServiceException($"{nameof(InspectionItem)} not found!");

        var _startTimeSpan = TimeSpan.Parse(await sharedService.GetPresetValue(McvConstant.OFFICE_OPEN_TIME));
        var _endTimeSpan = TimeSpan.Parse(await sharedService.GetPresetValue(McvConstant.OFFICE_CLOSE_TIME));

        var _nextDue = DateTime.UtcNow.AddDays(Decimal.ToDouble(StageDuration)).Date;

        if (FollowUpDate != null)
            _nextDue = FollowUpDate.Value.Date;

        if (StageCode == "AGENDA_ACTION")
        {
            _nextDue = _entity.Inspection.EndDate.AddDays(1);
        }

        return new
        {
            Title = StageTitle,
            Entity = Entity,
            EntityID = EntityID,
            Subtitle = $"{_entity.InspectionTitle}-{_entity.Title}-{_entity.Subtitle}",
            WFStageCode = StageCode,
            StartDate = ClockTools.GetUTC(ClockTools.GetISTNow().Date
              .AddMinutes(_startTimeSpan.TotalMinutes)),
            DueDate = ClockTools.GetUTC(ClockTools.GetIST(_nextDue).Date
              .AddMinutes(_endTimeSpan.TotalMinutes)),
            MHrAssigned = 0,
            IsPreAssignedTimeTask = false
        };
    }
}

public class InspectionItemGroup
{
    public string Guid { get; set; }
    public DateTime? DueDate { get; set; }
    public string ActionBy { get; set; }
    public int? ActionByContactID { get; set; }
    public string Subject { get; set; }
    public bool IsDelayed { get; set; }
    public int ItemCount { get; set; }
}