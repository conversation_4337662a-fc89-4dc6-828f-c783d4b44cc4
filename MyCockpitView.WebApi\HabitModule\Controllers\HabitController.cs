﻿
using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.WebApi.Responses;
using MyCockpitView.Utility.Common;
using MyCockpitView.WebApi.ActivityModule;
using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.ContactModule.Services;
using MyCockpitView.WebApi.HabitModule.Services;
using MyCockpitView.WebApi.HabitModule.Dtos;
using MyCockpitView.WebApi.HabitModule.Entities;
using MyCockpitView.WebApi.WFTaskModule.Services;

namespace MyCockpitView.WebApi.HabitModule.Controllers;

[Authorize]
[ApiController]
[Route("[controller]")]
public class HabitController : ControllerBase
{
    private readonly IHabitService service;
    private readonly EntitiesContext db;
    private readonly IActivityService activityService;
    private readonly IMapper mapper;
    private readonly IContactService contactService;
    private readonly IWFTaskService taskService;

    public HabitController(EntitiesContext db, IHabitService service, IMapper mapper, IActivityService activityService, IContactService contactService, IWFTaskService taskService   )
    {
        this.db = db;
        this.service = service;
        this.activityService = activityService;
        this.mapper = mapper;
        this.contactService = contactService;
        this.taskService = taskService;
    }

    [HttpGet]
    public async Task<ActionResult<IEnumerable<HabitDto>>> Get(
        [FromQuery] string? filters = null,
        [FromQuery] string? search = null,
        [FromQuery] string? sort = null)
    {
        var query = service.Get(filters != null ? DataTools.GetObjectFromJsonString<APIFilter>(filters).Filters : null, search, sort).Include(x=>x.AssignerContact).Include(x=>x.AssigneeContact);
        var results = mapper.Map<IEnumerable<HabitDto>>(await query.ToListAsync());

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(Habit))
            .ToListAsync();

        foreach (var obj in results)
        {
            obj.Type = typeMasters.FirstOrDefault(x => x.Value == obj.TypeFlag)?.Title ?? "";
        }

        var statusMasters = await db.StatusMasters
          .AsNoTracking()
          .Where(x => x.Entity == nameof(Habit))
          .ToListAsync();

        foreach (var obj in results)
        {
            obj.Status = statusMasters.FirstOrDefault(x => x.Value == obj.StatusFlag)?.Title ?? "";
        }

        return Ok(results);
    }

    [HttpGet("Pages")]
    public async Task<ActionResult<PagedResponse<HabitDto>>> GetPages(
        [FromQuery] int page = 0,
        [FromQuery] int pageSize = 50,
        [FromQuery] string? filters = null,
        [FromQuery] string? search = null,
        [FromQuery] string? sort = null)
    {
        var query = service.Get(filters != null ? DataTools.GetObjectFromJsonString<APIFilter>(filters).Filters : null, search, sort).Include(x => x.AssignerContact).Include(x => x.AssigneeContact);

        var totalCount = await query.CountAsync();
        var totalPages = (int)Math.Ceiling((decimal)totalCount / pageSize);
        var results = mapper.Map<IEnumerable<HabitDto>>(await query
            .Skip(pageSize * page)
            .Take(pageSize)
            .ToListAsync());

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(Habit))
            .ToListAsync();

        foreach (var obj in results)
        {
            obj.Type = typeMasters.FirstOrDefault(x => x.Value == obj.TypeFlag)?.Title ?? "";
        }

        var statusMasters = await db.StatusMasters
           .AsNoTracking()
           .Where(x => x.Entity == nameof(Habit))
           .ToListAsync();

        foreach (var obj in results)
        {
            obj.Status = statusMasters.FirstOrDefault(x => x.Value == obj.StatusFlag)?.Title ?? "";
        }

        return Ok(new PagedResponse<HabitDto>(results, totalCount, totalPages));
    }

    [HttpGet("{id:int}")]
    public async Task<ActionResult<HabitDto>> GetByID(int id)
    {
        var responseDto = mapper.Map<HabitDto>(await service.GetById(id));
        if (responseDto == null)
        {
            return NotFound();
        }

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(Habit))
            .ToListAsync();

        responseDto.Type = typeMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        var statusMasters = await db.StatusMasters
          .AsNoTracking()
          .Where(x => x.Entity == nameof(Habit))
          .ToListAsync();

        responseDto.Status = statusMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        return Ok(responseDto);
    }

    [HttpPost]
    public async Task<ActionResult<HabitDto>> Post(HabitDto dto)
    {
        var id = await service.Create(mapper.Map<Habit>(dto), mapper.Map<ICollection<HabitResponse>>(dto.Responses));
        var responseDto = mapper.Map<HabitDto>(await service.GetById(id));

        if (responseDto == null)
            return BadRequest("Not Created");

            if (responseDto.StatusFlag == 0)
            {
                await taskService.StartFlow(nameof(Habit), responseDto.TypeFlag, responseDto.ID);
            }

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(Habit))
            .ToListAsync();

        responseDto.Type = typeMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        var statusMasters = await db.StatusMasters
  .AsNoTracking()
  .Where(x => x.Entity == nameof(Habit))
  .ToListAsync();

        responseDto.Status = statusMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        //if (!string.IsNullOrEmpty(User.Identity?.Name))
        //{
        //    var currentContact = await contactService.Get().FirstOrDefaultAsync(x => x.Username == User.Identity.Name);
        //    if (currentContact != null)
        //    {
        //        var parent = await contactService.Get().SingleOrDefaultAsync(x => x.ID == responseDto.ContactID);
        //        if (parent != null)
        //        {
        //            await activityService.LogUserActivity(currentContact, nameof(parent), parent.ID, $"{parent.Name}", $"{nameof(Habit).Replace(nameof(parent),"")} | {responseDto.Company.Title} | {responseDto.Designation} | {responseDto.ManValue}mV", "Created");
        //        }
        //    }
        //}

        return CreatedAtAction(nameof(GetByID), new { id = responseDto.ID }, responseDto);
    }

    [HttpPut("{id:int}")]
    public async Task<ActionResult<HabitDto>> Put(int id, HabitDto dto)
    {
        if (id != dto.ID)
        {
            return BadRequest();
        }

        await service.Update(mapper.Map<Habit>(dto), mapper.Map<ICollection<HabitResponse>>(dto.Responses));
        var responseDto = mapper.Map<HabitDto>(await service.GetById(id));

        if (responseDto == null)
            return BadRequest("Not Updated");

            if (responseDto.StatusFlag == 0)
            {
                await taskService.UpdateTaskDue(nameof(Habit), responseDto.ID);

            }

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(Habit))
            .ToListAsync();

        responseDto.Type = typeMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        var statusMasters = await db.StatusMasters
  .AsNoTracking()
  .Where(x => x.Entity == nameof(Habit))
  .ToListAsync();

        responseDto.Status = statusMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        //if (!string.IsNullOrEmpty(User.Identity?.Name))
        //{
        //    var currentContact = await contactService.Get().FirstOrDefaultAsync(x => x.Username == User.Identity.Name);
        //    if (currentContact != null)
        //    {
        //        var parent = await contactService.Get().SingleOrDefaultAsync(x => x.ID == responseDto.ContactID);
        //        if (parent != null)
        //        {
        //            await activityService.LogUserActivity(currentContact, nameof(parent), parent.ID, $"{parent.Name}", $"{nameof(Habit).Replace(nameof(parent), "")} | {responseDto.Company.Title} | {responseDto.Designation} | {responseDto.ManValue}mV", "Updated");
        //        }
        //    }
        //}

        return Ok(responseDto);
    }

    [HttpDelete("{id:int}")]
    public async Task<IActionResult> Delete(int id)
    {
        var responseDto = mapper.Map<HabitDto>(await service.GetById(id));
        if (responseDto == null) return BadRequest($"{nameof(Habit)} not found!");

        await service.Delete(id);

        await taskService.PurgePendingTasks(nameof(Habit), id);


        return NoContent();
    }


    [HttpGet("uid/{id:guid}")]
    [AllowAnonymous]
    public async Task<ActionResult<HabitDto>> GetByGUID(Guid id)
    {
        var responseDto = mapper.Map<HabitDto>(await service.GetById(id));
        if (responseDto == null)
        {
            return NotFound();
        }

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(Habit))
            .ToListAsync();

        responseDto.Type = typeMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        var statusMasters = await db.StatusMasters
                        .AsNoTracking()
                        .Where(x => x.Entity == nameof(Habit))
                        .ToListAsync();

        responseDto.Status = statusMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        return Ok(responseDto);
    }


    [HttpGet("SearchTagOptions")]
    public async Task<IActionResult> GetSearchTagOptions()
    {
        var results = await service.GetSearchTagOptions();
        return Ok(results);
    }


    [HttpGet("FieldOptions")]
    public async Task<IActionResult> GetFieldOptions(string field)
    {
        return Ok(await service.GetFieldOptions(field));
    }


    [HttpGet("startflow/{id}")]
    public async Task<IActionResult> StartFlow(int ID)
    {
        var Entity = await service.GetById(ID);
        if (Entity == null) return NotFound();

        await taskService.StartFlow(nameof(Habit), Entity.TypeFlag, ID);
        return Ok();

    }


    [HttpGet("SubjectOptions")]
    public async Task<IActionResult> GetSubjectOptions()
    {

        var query = service.Get().Where(x => x.Entity == null).Select(x => x.Title).Distinct();


        return Ok(await query.ToListAsync());

    }
}