﻿using AutoMapper;
using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.ProjectModule.Entities;
using System.ComponentModel.DataAnnotations;

namespace MyCockpitView.WebApi.ProjectModule.Dtos;

public class ProjectScopeVersionDto : BaseEntityDto
{

    [Required]
    public int ProjectID { get; set; }
    public string? Scopes { get; set; }
}

public class ProjectScopeVersionDtoMapperProfile : Profile
{
    public ProjectScopeVersionDtoMapperProfile()
    {
        CreateMap<ProjectScopeVersion, ProjectScopeVersionDto>()
            .ReverseMap();

    }
}
