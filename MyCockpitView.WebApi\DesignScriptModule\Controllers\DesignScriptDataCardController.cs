﻿
using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.WebApi.Responses;
using MyCockpitView.Utility.Common;
using MyCockpitView.WebApi.ActivityModule;
using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.DesignScriptModule.Services;
using MyCockpitView.WebApi.ContactModule.Services;
using MyCockpitView.WebApi.DesignScriptModule.Dtos;
using MyCockpitView.WebApi.DesignScriptModule.Entities;

namespace MyCockpitView.WebApi.DesignScriptModule.Controllers;

[Authorize]
[ApiController]
[Route("[controller]")]
public class DesignScriptDataCardController : ControllerBase
{
    private readonly IDesignScriptDataCardService service;
    private readonly EntitiesContext db;
    private readonly IActivityService activityService;
    private readonly IMapper mapper;
    private readonly IContactService contactService;

    public DesignScriptDataCardController(EntitiesContext db, IDesignScriptDataCardService service, IMapper mapper, IActivityService activityService, IContactService contactService   )
    {
        this.db = db;
        this.service = service;
        this.activityService = activityService;
        this.mapper = mapper;
        this.contactService = contactService;
    }

    [HttpGet]
    public async Task<ActionResult<IEnumerable<DesignScriptDataCardDto>>> Get(
        [FromQuery] string? filters = null,
        [FromQuery] string? search = null,
        [FromQuery] string? sort = null)
    {
        var query = service.Get(filters != null ? DataTools.GetObjectFromJsonString<APIFilter>(filters).Filters : null, search, sort)
              .Include(x => x.Attachments)
                    .Include(x => x.Attributes)
                    .Include(x => x.Maps);


        var results = mapper.Map<IEnumerable<DesignScriptDataCardDto>>(await query.ToListAsync());

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(DesignScriptDataCard))
            .ToListAsync();

        foreach (var obj in results)
        {
            obj.Type = typeMasters.FirstOrDefault(x => x.Value == obj.TypeFlag)?.Title ?? "";
        }

        var statusMasters = await db.StatusMasters
          .AsNoTracking()
          .Where(x => x.Entity == nameof(DesignScriptDataCard))
          .ToListAsync();

        foreach (var obj in results)
        {
            obj.Status = statusMasters.FirstOrDefault(x => x.Value == obj.StatusFlag)?.Title ?? "";
        }

        return Ok(results);
    }

    [HttpGet("Pages")]
    public async Task<ActionResult<PagedResponse<DesignScriptDataCardDto>>> GetPages(
        [FromQuery] int page = 0,
        [FromQuery] int pageSize = 50,
        [FromQuery] string? filters = null,
        [FromQuery] string? search = null,
        [FromQuery] string? sort = null)
    {
        var query = service.Get(filters != null ? DataTools.GetObjectFromJsonString<APIFilter>(filters).Filters : null, search, sort).Include(x => x.Attachments)
                    .Include(x => x.Attributes)
                    .Include(x => x.Maps); ;

        var totalCount = await query.CountAsync();
        var totalPages = (int)Math.Ceiling((decimal)totalCount / pageSize);
        var results = mapper.Map<IEnumerable<DesignScriptDataCardDto>>(await query
            .Skip(pageSize * page)
            .Take(pageSize)
            .ToListAsync());

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(DesignScriptDataCard))
            .ToListAsync();

        foreach (var obj in results)
        {
            obj.Type = typeMasters.FirstOrDefault(x => x.Value == obj.TypeFlag)?.Title ?? "";
        }

        var statusMasters = await db.StatusMasters
           .AsNoTracking()
           .Where(x => x.Entity == nameof(DesignScriptDataCard))
           .ToListAsync();

        foreach (var obj in results)
        {
            obj.Status = statusMasters.FirstOrDefault(x => x.Value == obj.StatusFlag)?.Title ?? "";
        }

        return Ok(new PagedResponse<DesignScriptDataCardDto>(results, totalCount, totalPages));
    }

    [HttpGet("{id:int}")]
    public async Task<ActionResult<DesignScriptDataCardDto>> GetByID(int id)
    {
        var responseDto = mapper.Map<DesignScriptDataCardDto>(await service.GetById(id));
        if (responseDto == null)
        {
            return NotFound();
        }

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(DesignScriptDataCard))
            .ToListAsync();

        responseDto.Type = typeMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        var statusMasters = await db.StatusMasters
          .AsNoTracking()
          .Where(x => x.Entity == nameof(DesignScriptDataCard))
          .ToListAsync();

        responseDto.Status = statusMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        return Ok(responseDto);
    }

    [HttpPost]
    public async Task<ActionResult<DesignScriptDataCardDto>> Post(DesignScriptDataCardDto dto)
    {
        var id = await service.Create(mapper.Map<DesignScriptDataCard>(dto));
        var responseDto = mapper.Map<DesignScriptDataCardDto>(await service.GetById(id));

        if (responseDto == null)
            return BadRequest("Not Created");

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(DesignScriptDataCard))
            .ToListAsync();

        responseDto.Type = typeMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        var statusMasters = await db.StatusMasters
  .AsNoTracking()
  .Where(x => x.Entity == nameof(DesignScriptDataCard))
  .ToListAsync();

        responseDto.Status = statusMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        //if (!string.IsNullOrEmpty(User.Identity?.Name))
        //{
        //    var currentContact = await contactService.Get().FirstOrDefaultAsync(x => x.Username == User.Identity.Name);
        //    if (currentContact != null)
        //    {
        //        var parent = await contactService.Get().SingleOrDefaultAsync(x => x.ID == responseDto.ContactID);
        //        if (parent != null)
        //        {
        //            await activityService.LogUserActivity(currentContact, nameof(parent), parent.ID, $"{parent.Name}", $"{nameof(DesignScriptDataCard).Replace(nameof(parent),"")} | {responseDto.Company.Title} | {responseDto.Designation} | {responseDto.ManValue}mV", "Created");
        //        }
        //    }
        //}

        return CreatedAtAction(nameof(GetByID), new { id = responseDto.ID }, responseDto);
    }

    [HttpPut("{id:int}")]
    public async Task<ActionResult<DesignScriptDataCardDto>> Put(int id, DesignScriptDataCardDto dto)
    {
        if (id != dto.ID)
        {
            return BadRequest();
        }

        await service.Update(mapper.Map<DesignScriptDataCard>(dto));
        var responseDto = mapper.Map<DesignScriptDataCardDto>(await service.GetById(id));

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(DesignScriptDataCard))
            .ToListAsync();

        responseDto.Type = typeMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        var statusMasters = await db.StatusMasters
  .AsNoTracking()
  .Where(x => x.Entity == nameof(DesignScriptDataCard))
  .ToListAsync();

        responseDto.Status = statusMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        //if (!string.IsNullOrEmpty(User.Identity?.Name))
        //{
        //    var currentContact = await contactService.Get().FirstOrDefaultAsync(x => x.Username == User.Identity.Name);
        //    if (currentContact != null)
        //    {
        //        var parent = await contactService.Get().SingleOrDefaultAsync(x => x.ID == responseDto.ContactID);
        //        if (parent != null)
        //        {
        //            await activityService.LogUserActivity(currentContact, nameof(parent), parent.ID, $"{parent.Name}", $"{nameof(DesignScriptDataCard).Replace(nameof(parent), "")} | {responseDto.Company.Title} | {responseDto.Designation} | {responseDto.ManValue}mV", "Updated");
        //        }
        //    }
        //}

        return Ok(responseDto);
    }

    [HttpDelete("{id:int}")]
    public async Task<IActionResult> Delete(int id)
    {
        var responseDto = mapper.Map<DesignScriptDataCardDto>(await service.GetById(id));
        if (responseDto == null) return BadRequest($"{nameof(DesignScriptDataCard)} not found!");

        await service.Delete(id);

        //if (!string.IsNullOrEmpty(User.Identity?.Name))
        //{
        //    var currentContact = await contactService.Get().FirstOrDefaultAsync(x => x.Username == User.Identity.Name);
        //    if (currentContact != null)
        //    {
        //        var parent = await contactService.Get().SingleOrDefaultAsync(x => x.ID == responseDto.ContactID);
        //        if (parent != null)
        //        {
        //            await activityService.LogUserActivity(currentContact, nameof(parent), parent.ID, $"{parent.Name}", $"{nameof(DesignScriptDataCard).Replace(nameof(parent), "")} | {responseDto.Company.Title} | {responseDto.Designation} | {responseDto.ManValue}mV", "Deleted");
        //        }
        //    }
        //}

        return NoContent();
    }


    [HttpGet("uid/{id:guid}")]
    [AllowAnonymous]
    public async Task<ActionResult<DesignScriptDataCardDto>> GetByGUID(Guid id)
    {
        var responseDto = mapper.Map<DesignScriptDataCardDto>(await service.GetById(id));
        if (responseDto == null)
        {
            return NotFound();
        }

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(DesignScriptDataCard))
            .ToListAsync();

        responseDto.Type = typeMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        var statusMasters = await db.StatusMasters
                        .AsNoTracking()
                        .Where(x => x.Entity == nameof(DesignScriptDataCard))
                        .ToListAsync();

        responseDto.Status = statusMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        return Ok(responseDto);
    }

    [HttpGet("SearchTagOptions")]
    public async Task<IActionResult> GetSearchTagOptions()
    {
        var results = await service.GetSearchTagOptions();
        return Ok(results);
    }


    [HttpGet("FieldOptions")]
    public async Task<IActionResult> GetFieldOptions(string field)
    {
        return Ok(await service.GetFieldOptions(field));
    }


    [HttpPost("Copy/{id}")]
    public async Task<IActionResult> Copy(int id, [FromBody] DesignScriptDataCardDto Dto)
    {
        var entity = mapper.Map<DesignScriptDataCard>(Dto);

            var resultID = await service.Copy(id, entity, mapper.Map<IEnumerable<DesignScriptDataCardAttachment>>(Dto.Attachments));

            var responseDto = mapper.Map<DesignScriptDataCardDto>(await service.GetById(resultID));

            return Ok(responseDto);
        

    }

    [HttpPut("LinkToEntity/{id}")]
    public async Task<IActionResult> LinkToEntity(int id, [FromBody] DesignScriptDataCardDto Dto)
    {
            await service.ConnectWithEntity(id, Dto.ID);
        
            return Ok(Dto);
        

    }

    [HttpPut("UnLinkFromEntity/{id}")]
    public async Task<IActionResult> UnLinkFromEntity(int id, [FromBody] DesignScriptDataCardDto Dto)
    {
            await service.DisconnectFromEntity(id, Dto.ID);
            
            return Ok();
        

    }

}