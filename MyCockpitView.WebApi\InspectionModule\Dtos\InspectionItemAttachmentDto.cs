﻿using AutoMapper;
using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.InspectionModule.Entities;

namespace MyCockpitView.WebApi.InspectionModule.Dtos;

public class InspectionItemAttachmentDto : BaseBlobEntityDto
{
    public int InspectionItemID { get; set; }
}

public class InspectionItemAttachmentDtoMapperProfile : Profile
{
    public InspectionItemAttachmentDtoMapperProfile()
    {


        CreateMap<InspectionItemAttachment, InspectionItemAttachmentDto>()
             .ReverseMap()
                      .ForMember(dest => dest.InspectionItem, opt => opt.Ignore());

    }
}
