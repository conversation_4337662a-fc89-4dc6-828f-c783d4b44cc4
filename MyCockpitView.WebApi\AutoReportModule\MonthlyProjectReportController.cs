﻿
using DocumentFormat.OpenXml.Bibliography;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.Utility.Common;
using MyCockpitView.Utility.Excel;
using MyCockpitView.WebApi.ContactModule.Services;
using MyCockpitView.WebApi.InspectionModule.Services;
using MyCockpitView.WebApi.MeetingModule.Services;
using MyCockpitView.WebApi.PackageModule.Services;
using MyCockpitView.WebApi.ProjectModule.Entities;
using MyCockpitView.WebApi.ProjectModule.Services;
using MyCockpitView.WebApi.Services;
using System.Diagnostics;
using System.Text;

namespace MyCockpitView.WebApi.AutoReportModule;

[ApiController]
[Route("[controller]")]
public class MonthlyProjectReportController : ControllerBase
{
    private readonly EntitiesContext db;
    private readonly IProjectService projectService;
    private readonly IContactService contactService;
    private readonly IPackageService packageService;
    private readonly ISharedService sharedService;
    private readonly IMeetingService meetingService;
    private readonly IProjectBillService billService;
    private readonly IInspectionService inspectionService;

    public MonthlyProjectReportController(EntitiesContext db,    IProjectService projectService,
    IContactService contactService,
    IPackageService packageService,
    ISharedService sharedService,
    IMeetingService meetingService,
    IProjectBillService billService,
    IInspectionService inspectionService)
    {
        this.db = db;
        this.projectService = projectService;
        this.contactService = contactService;
        this.packageService = packageService;
        this.sharedService = sharedService;
        this.meetingService = meetingService;
        this.billService = billService;
        this.inspectionService = inspectionService;
    }

    [HttpGet]
    public async Task<IActionResult> Get()
    {
        
            //"0 0/35 * ? * * *"
            var todayIST = DateTime.UtcNow.Date;
            var currentMonthStart = new DateTime(todayIST.Year, todayIST.Month, 1);
            var previousMonthStart=currentMonthStart.AddMonths(-1);

            var company=await db.Companies.FirstOrDefaultAsync(x=>x.Initials=="NAL");

            Debug.WriteLine($"Date:{todayIST.ToString(McvConstant.DATE_FORMAT)}");

            var projects =  await projectService.Get()
                .Where(x=>x.CompanyID==company.ID)
                .Include(x=>x.Associations).ThenInclude(c=>c.Contact)
                .Where(x => x.StatusFlag== McvConstant.PROJECT_STATUSFLAG_INPROGRESS &&
                x.Associations.Any(a => !a.IsDeleted && a.TypeFlag == McvConstant.PROJECT_ASSOCIATION_TYPEFLAG_CLIENT_REPORT_GROUP))
                .Select(x=> new{
                    x.ID,
                    x.Code,
                    x.Title,
                   Clients= x.Associations.Where(a=>!a.IsDeleted && a.TypeFlag==McvConstant.PROJECT_ASSOCIATION_TYPEFLAG_CLIENT_REPORT_GROUP)
                   .Select(c=> new
                   {
                       ID=c.ContactID,
                       Name=c.Contact.FullName,
                       Email=c.Contact.Email1
                   }),
                    Partners = x.Associations.Where(a => !a.IsDeleted && a.TypeFlag == McvConstant.PROJECT_ASSOCIATION_TYPEFLAG_PARTNER).Select(c => new
                    {
                        ID = c.ContactID,
                        Name = c.Contact.FullName,
                        Email = c.Contact.Email1
                    }),
                    Associates = x.Associations.Where(a => !a.IsDeleted && a.TypeFlag == McvConstant.PROJECT_ASSOCIATION_TYPEFLAG_ASSOCIATE).Select(c => new 
                    {
                        ID = c.ContactID,
                        Name = c.Contact.FullName, 
                        Email = c.Contact.Email1 
                    }),

                })
            .ToListAsync();

            var packages = await packageService.Get()
              .Where(x => x.TypeFlag == McvConstant.PACKAGE_TYPEFLAG_ACTIVE && x.StatusFlag == McvConstant.PACKAGE_STATUSFLAG_SENT && x.SubmissionDate >= previousMonthStart && x.SubmissionDate < currentMonthStart)
              .Select(x => new
              {
                  x.ID,
                  x.ProjectID,
                 Title= x.Title,
                 Date= x.SubmissionDate.Value,
                  x.UID
          })
              .ToListAsync();

            var meetingVersions = meetingService.Get().Where(x => x.StatusFlag == McvConstant.MEETING_STATUSFLAG_SENT && x.TypeFlag == McvConstant.MEETING_TYPEFLAG_MEETING && x.IsVersion);

            var meetings=await meetingService.Get()
                .Where(x=>x.StatusFlag==McvConstant.MEETING_STATUSFLAG_SENT && !x.IsVersion && x.StartDate>=previousMonthStart && x.StartDate < currentMonthStart
                )
                .Include(x => x.Contact)
                .Include(x=>x.Attendees)
                .Select(x=> new
                {
                    x.ID,
                    x.ProjectID,
                    Creator= x.Contact.FullName,
                  Date= x.StartDate,
                    Attendees=x.Attendees.Where(a=>!a.IsDeleted && a.TypeFlag==McvConstant.MEETING_ATTENDEE_TYPEFLAG_TO && a.ContactID!=x.ContactID).Select(a=> new
                    {
                        a.Name,
                        a.Company,
                        a.Email,
                    }),
                    Code=x.Code,
                    Type= x.TypeFlag == McvConstant.MEETING_TYPEFLAG_MEETING ? "MEETING": x.TypeFlag == McvConstant.MEETING_TYPEFLAG_CNOTE ? "COMMUNICATION NOTE":"INSPECTION",
                    UID= meetingVersions.Where(v => v.ParentID == x.ID).OrderByDescending(v => v.Created).Any() ? meetingVersions.Where(v=>v.ParentID==x.ID).OrderByDescending(v=>v.Created).FirstOrDefault().UID : x.UID,
                }).ToListAsync();

            var inspectionVersions = inspectionService.Get().Where(x => x.StatusFlag == McvConstant.MEETING_STATUSFLAG_SENT && x.TypeFlag == McvConstant.MEETING_TYPEFLAG_MEETING && x.IsVersion);

            var inspections = await inspectionService.Get()
                .Where(x => x.StatusFlag == McvConstant.MEETING_STATUSFLAG_SENT && x.TypeFlag == McvConstant.MEETING_TYPEFLAG_MEETING && !x.IsVersion && x.StartDate >= previousMonthStart && x.StartDate < currentMonthStart
                )
                .Include(x => x.Contact)
                .Select(x => new
                {
                    x.ID,
                    x.ProjectID,
                    Date = x.StartDate,
                    Code = x.Code,
                    Visitor = x.Contact.FullName,
                    UID = inspectionVersions.Where(v => v.ParentID == x.ID).OrderByDescending(v => v.Created).Any() ? inspectionVersions.Where(v => v.ParentID == x.ID).OrderByDescending(v => v.Created).FirstOrDefault().UID : x.UID,
                }).ToListAsync();

            var pendingBills = await billService.Get()
                .Include(x => x.Payments)
                .Where(x => x.TypeFlag == McvConstant.PROJECT_BILL_TYPEFLAG_INVOICE && !x.Payments.Any(p => !p.IsDeleted))
                .Select(x=>new
                {
                    x.ID,
                    x.UID,
                    x.ProjectID,
                    Date=x.BillDate,
                    Title=x.BillNo,
                    Amount=x.Payable,
                    Url=x.BlobUrl,
                })
                .ToListAsync();

            var proforma = await billService.Get()
                .Where(x => x.TypeFlag == McvConstant.PROJECT_BILL_TYPEFLAG_PROFORMA )
                .Select(x => new
                {
                    x.ID,
                    x.UID,
                    x.ProjectID,
                    Date = x.BillDate,
                    Title = "PROFROMA",
                    Amount = x.Payable,
                    Url = x.BlobUrl,
                })
                .ToListAsync();

            var packageSubmissionURL = await sharedService.GetPresetValue(McvConstant.PACKAGE_SUBMISSION_URL_ROOT);
            var meetingMinutesURL = await sharedService.GetPresetValue(McvConstant.MEETING_MINUTES_URL_ROOT);
            var inspectionReportURL = await sharedService.GetPresetValue(McvConstant.INSPECTION_REPORT_URL_ROOT);

            var _senderEmail = await sharedService.GetPresetValue(McvConstant.LOCK_PROJECT_EMAIL_SENDER_ID);
            var _senderName = await sharedService.GetPresetValue(McvConstant.LOCK_PROJECT_EMAIL_SENDER_NAME);

            var count = 0;

            foreach (var project in projects)
            {
                Debug.WriteLine($"Project: {project.Title}");

                var projectPackages = packages.Where(x => x.ProjectID == project.ID).ToList();
                var projectMeetings = meetings.Where(x => x.ProjectID == project.ID).ToList();
                var projectInspections = inspections.Where(x => x.ProjectID == project.ID).ToList();
                var projectBills = pendingBills.Where(x => x.ProjectID == project.ID).ToList();
                projectBills = projectBills.Concat(proforma.Where(x => x.ProjectID == project.ID)).ToList();

                Debug.WriteLine($"Packages: {projectPackages.Count}");
                Debug.WriteLine($"Meetings: {projectMeetings.Count}");
                Debug.WriteLine($"Inspections: {projectInspections.Count}");
                Debug.WriteLine($"Bills: {projectBills.Count}");

                if (projectPackages.Count + projectMeetings.Count + projectInspections.Count + projectBills.Count == 0)
                {
                    Debug.WriteLine($"items are empty, then skip to next project....");
                    continue;
                }


                IEnumerable<ProjectReportData> activities = new List<ProjectReportData>();

                if (projectPackages.Any())
                {
                    activities = activities.Concat(projectPackages.Select(x => new ProjectReportData
                    {
                        Date = ClockTools.GetIST(x.Date).ToString("dd MMM yyyy"),
                        Type = "SUBMISSION",
                        Title = x.Title,
                        Url = $"{packageSubmissionURL}{x.UID}"
                    }));
                }

                if (projectMeetings.Any())
                {
                    activities = activities.Concat(projectMeetings.Select(x => new ProjectReportData
                    {
                        Date = ClockTools.GetIST(x.Date).ToString("dd MMM yyyy"),
                        Type = x.Type,
                        Title = x.Code,
                        Url = $"{meetingMinutesURL}{x.UID}"
                    }));
                }

                if (projectInspections.Any())
                {
                    activities = activities.Concat(projectInspections.Select(x => new ProjectReportData
                    {
                        Date = ClockTools.GetIST(x.Date).ToString("dd MMM yyyy"),
                        Type = "INSPECTION",
                        Title = x.Code,
                        Url = $"{inspectionReportURL}{x.UID}"
                    }));
                }

                if (projectBills.Any())
                {
                    activities = activities.Concat(projectBills.Select(x => new ProjectReportData
                    {
                        Date = ClockTools.GetIST(x.Date).ToString("dd MMM yyyy"),
                        Type = "Bill/PROFORMA",
                        Title = x.Title,
                        Url = x.Url
                    }));
                }

                Debug.WriteLine($"Project Activities: {activities.Count()}");

                var _reportTitle = $"Client Report | {project.Code}-{project.Title} | {ClockTools.GetIST(previousMonthStart).ToString("MMM yyyy")}";

                //Debug.WriteLine(_reportTitle);

                var toList = new List<EmailContact>();
                foreach (var obj in project.Clients)
                {
                    toList.Add(new EmailContact { ID=obj.ID, Name = obj.Name, Email = obj.Email });
                }


                var ccList = new List<EmailContact>() {
                //new EmailContact { Name = "Core | Newarch", Email = "<EMAIL>"},
                new EmailContact { Name = "Backup | Newarch",Email = "<EMAIL>" }
                                                };
                foreach (var obj in project.Partners)
                {
                    ccList.Add(new EmailContact { ID=obj.ID, Name = obj.Name, Email = obj.Email });
                }
                foreach (var obj in project.Associates)
                {
                    ccList.Add(new EmailContact { ID=obj.ID, Name = obj.Name, Email = obj.Email });
                }


                StringBuilder sb = new StringBuilder();
                sb.AppendLine("<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Transitional//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd\">");
                sb.AppendLine("<html xmlns=\"http://www.w3.org/1999/xhtml\">");
                sb.AppendLine("<head>");
                sb.AppendLine("    <meta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\" />");
                sb.AppendLine("    <title>Email Design</title>");
                sb.AppendLine("    <meta name=\"viewport\" content=\"width=device-width; initial-scale=1.0;\" />");
                sb.AppendLine("    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=9; IE=8; IE=7; IE=EDGE\" />");
                sb.AppendLine("    <meta name=\"format-detection\" content=\"telephone=no\" />");
                sb.AppendLine("    <!--[if gte mso 9]><xml>");
                sb.AppendLine("    <o:OfficeDocumentSettings>");
                sb.AppendLine("    <o:AllowPNG />");
                sb.AppendLine("    <o:PixelsPerInch>96</o:PixelsPerInch>");
                sb.AppendLine("    </o:OfficeDocumentSettings>");
                sb.AppendLine("    </xml><![endif]-->");
                sb.AppendLine("    <style type=\"text/css\">");
                sb.AppendLine("        /* Some resets and issue fixes */");
                sb.AppendLine("        #outlook a {");
                sb.AppendLine("            padding: 0;");
                sb.AppendLine("        }");
                sb.AppendLine("");
                sb.AppendLine("        body {");
                sb.AppendLine("            width: 100% !important;margin:0;");
                sb.AppendLine("            -webkit-text-size-adjust: 100%;");
                sb.AppendLine("            -ms-text-size-adjust: 100%;");
                sb.AppendLine("        }");

                sb.AppendLine("        table{");
                sb.AppendLine("            mso-table-lspace: 0px;");
                sb.AppendLine("            mso-table-rspace: 0px;");
                sb.AppendLine("        }");
                sb.AppendLine("");
                sb.AppendLine("        table td {");
                sb.AppendLine("            border-collapse: collapse;");
                sb.AppendLine("        }");
                sb.AppendLine("");
                sb.AppendLine("        .ExternalClass * {");
                sb.AppendLine("            line-height: 115%;");
                sb.AppendLine("        }");
                sb.AppendLine("        /* End reset */");

                sb.AppendLine("    </style>");
                sb.AppendLine("</head>");
                sb.AppendLine("");
                sb.AppendLine("<body>");
                sb.AppendLine("");

                sb.AppendLine("");
                sb.AppendLine("    <div style=\"margin: 0 auto;font-family:Calibri;font-size:14px;line-height:1.8;padding-left:5px;padding-right:5px; max-width:500px;\">");
                sb.AppendLine("");
                sb.AppendLine("        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; border-collapse: collapse;\">");
                sb.AppendLine("            <tr>");
                sb.AppendLine("                <td style=\"font-size: 16px; font-weight: bold; background-color: #005ba4; color: #fff; padding: 10px; text-align: center;\">");
                sb.AppendLine(_reportTitle.ToUpper());
                sb.AppendLine("                </td>");
                sb.AppendLine("            </tr>");
                sb.AppendLine("        </table>");
                sb.AppendLine("");

                sb.AppendLine("        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width: 100%; border-collapse: collapse\">");
                sb.AppendLine("            <tr>");
                sb.AppendLine("                <td valign=\"top\"  style=\"font-weight:bold;\">");
                sb.AppendLine("                    To:");
                sb.AppendLine("                </td>");
                sb.AppendLine("                <td valign=\"top\" style=\"padding-bottom: 5px;\">");
                sb.AppendLine("                    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width: 100%; border-collapse: collapse\">");
                foreach (var obj in toList)
                {
                    sb.AppendLine("                        <tr>");
                    sb.AppendLine("                            <td>");
                    sb.AppendLine(obj.Name + " <i> (" + obj.Email + ")</i>");
                    sb.AppendLine("                            </td>");
                    sb.AppendLine("                        </tr>");
                }
                sb.AppendLine("                    </table>");
                sb.AppendLine("                </td>");
                sb.AppendLine("            </tr>");
                sb.AppendLine("            <tr>");
                sb.AppendLine("                <td valign=\"top\"  style=\"font-weight:bold;\">");
                sb.AppendLine("                    CC:");
                sb.AppendLine("                </td>");
                sb.AppendLine("                <td valign=\"top\" style=\"padding-bottom: 5px;\">");
                sb.AppendLine("                    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width: 100%; border-collapse: collapse\">");
                foreach (var obj in ccList)
                {
                    sb.AppendLine("                        <tr>");
                    sb.AppendLine("                            <td>");
                    sb.AppendLine(obj.Name + " <i> (" + obj.Email + ")</i>");
                    sb.AppendLine("                            </td>");
                    sb.AppendLine("                        </tr>");
                }
                sb.AppendLine("                    </table>");
                sb.AppendLine("                </td>");
                sb.AppendLine("            </tr>");

                sb.AppendLine("        </table>");

                sb.AppendLine("<div style=\"border-bottom: 1px solid #cccccc; margin-top: 15px; margin-bottom: 15px; \"></div>");

                sb.AppendLine("");
                sb.AppendLine("        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width: 100%; border-collapse: collapse\">");

                sb.AppendLine("            <tr>");
                sb.AppendLine("                <td valign=\"top\"  style=\"font-weight:bold;\">");
                sb.AppendLine("                   First Contact:");
                sb.AppendLine("                </td>");
                sb.AppendLine("                <td valign=\"top\" style=\"padding-bottom: 5px;\">");
                sb.AppendLine("                    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width: 100%; border-collapse: collapse\">");
                foreach (var obj in project.Associates)
                {
                    sb.AppendLine("                        <tr>");
                    sb.AppendLine("                            <td>");
                    sb.AppendLine(obj.Name + " <i> (" + obj.Email + ")</i>");
                    sb.AppendLine("                            </td>");
                    sb.AppendLine("                        </tr>");
                }
                sb.AppendLine("                    </table>");
                sb.AppendLine("                </td>");
                sb.AppendLine("            </tr>");

                sb.AppendLine("            <tr>");
                sb.AppendLine("                <td valign=\"top\"  style=\"font-weight:bold;\">");
                sb.AppendLine("                    Second Contact:");
                sb.AppendLine("                </td>");
                sb.AppendLine("                <td valign=\"top\" style=\"padding-bottom: 5px;\">");
                sb.AppendLine("                    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width: 100%; border-collapse: collapse\">");
                foreach (var obj in project.Partners)
                {
                    sb.AppendLine("                        <tr>");
                    sb.AppendLine("                            <td>");
                    sb.AppendLine(obj.Name + " <i> (" + obj.Email + ")</i>");
                    sb.AppendLine("                            </td>");
                    sb.AppendLine("                        </tr>");
                }
                sb.AppendLine("                    </table>");
                sb.AppendLine("                </td>");
                sb.AppendLine("            </tr>");


                sb.AppendLine("        </table>");


                sb.AppendLine("<div style=\"border-bottom: 1px solid #cccccc; margin-top: 15px; margin-bottom: 15px; \"></div>");

                sb.AppendLine("        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width: 100%; border-collapse: collapse;\">");

                foreach (var obj in activities.OrderBy(x => x.Date))
                {

                    sb.AppendLine("<tr style=\"border-bottom: 1px Solid #e5e5e5;\">");

                    sb.AppendLine("                            <td >");
                    sb.AppendLine($"<strong style=\"padding-right:5px;margin:0;\">{obj.Date}</strong>");
                    sb.AppendLine($"<div><span >{obj.Type}</span><div>");
                    sb.AppendLine("                            </td>");
                    sb.AppendLine("                            <td style=\"text-align:end;\">");
                    sb.AppendLine($"<strong>{obj.Title}</strong>");
                    sb.AppendLine($"<div><a href=\"{obj.Url}\" target=\"_blank\" style=\"text-decoration:none;font-size:12px;margin:0;\">VIEW</a><div>");

                    sb.AppendLine("                            </td>");

                    sb.AppendLine("                        </tr>");

                }

                sb.AppendLine("        </table>");

                //FOOTER
                sb.AppendLine("<div style=\"border-bottom: 1px solid #cccccc; margin-top: 15px; margin-bottom: 15px; \"></div>");
                sb.AppendLine("        <table align=\"center\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; border-collapse: collapse;font-size:11px;\">");

                sb.AppendLine("");
                sb.AppendLine("            <tr>");
                sb.AppendLine("");
                sb.AppendLine("                <td align=\"center\" >");
                sb.AppendLine("This is a <b>MyCockpitView<sup>&copy;</sup></b> & <b>DesignScript<sup>&copy;</sup></b> generated e-mail for your information and necessary action.");
                sb.AppendLine("</td>");
                sb.AppendLine("            </tr>");
                sb.AppendLine("");
                sb.AppendLine("            <tr>");
                sb.AppendLine("");
                sb.AppendLine("                <td align=\"center\" >");
                sb.AppendLine("");
                sb.AppendLine("                    Powered by <b>Newarch<sup>&reg;</sup> Infotech LLP</b>");
                sb.AppendLine("");
                sb.AppendLine("                </td>");
                sb.AppendLine("            </tr>");
                sb.AppendLine("        </table>");
                sb.AppendLine("    </div>");

                sb.AppendLine("</body>");
                sb.AppendLine("");
                sb.AppendLine("</html>");

                var _emailBody = sb.ToString();
            var emailTo = new List<(string name, string email)>();
            foreach (var obj in toList)
                emailTo.Add((obj.Name, obj.Email));

            var emailCC = new List<(string name, string email)>();
            foreach (var obj in ccList)
                emailCC.Add((obj.Name, obj.Email));

            await sharedService.SendMail(_reportTitle, _senderName, _senderEmail, _emailBody, emailTo, emailCC);

                //Debug.WriteLine($"Sent TO");
                //foreach (var i in toList)
                //{
                //    Debug.WriteLine($"{i.Name}:{i.Email}");
                //}

                //Debug.WriteLine($"Sent CC");
                //foreach (var i in ccList)
                //{
                //    Debug.WriteLine($"{i.Name}:{i.Email}");
                //}

                count++;

                Debug.WriteLine($"{count} of {projects.Count} processed");
                Debug.WriteLine("=========================================");

                db.ProjectClientReportLogs.Add(new ProjectClientReportLog
                {
                    ProjectID=project.ID,
                    SentDate=DateTime.UtcNow,
                    Title=_reportTitle,
                    EmailTo=toList,
                    EmailCC=ccList,
                    HTMLContent=_emailBody,

                });
                await db.SaveChangesAsync();

            }
            return Ok($"{count} of {projects.Count} processed");
      
    }


    [HttpGet("Excel/project/{id}")]
    public async Task<IActionResult> GetExcelByProjectID(int id)
    {

        var project = await projectService.Get()
          
         .SingleOrDefaultAsync(x => x.ID == id);

        if (project == null) return BadRequest("Project not found!");

        var packages = await packageService.Get()
          .Where(x => x.TypeFlag == McvConstant.PACKAGE_TYPEFLAG_ACTIVE && x.StatusFlag == McvConstant.PACKAGE_STATUSFLAG_SENT)
          .Select(x => new
          {
              x.ID,
              x.ProjectID,
              Title = x.Title,
              Date = x.SubmissionDate.Value,
              x.UID
          })
          .ToListAsync();

        var meetingVersions = meetingService.Get().Where(x => x.StatusFlag == McvConstant.MEETING_STATUSFLAG_SENT && x.TypeFlag == McvConstant.MEETING_TYPEFLAG_MEETING && x.IsVersion);

        var meetings = await meetingService.Get()
            .Where(x => x.StatusFlag == McvConstant.MEETING_STATUSFLAG_SENT && !x.IsVersion )
            .Include(x => x.Contact)
            .Include(x => x.Attendees)
            .Select(x => new
            {
                x.ID,
                x.ProjectID,
                Creator = x.Contact.FullName,
                Date = x.StartDate,
                Attendees = x.Attendees.Where(a => !a.IsDeleted && a.TypeFlag == McvConstant.MEETING_ATTENDEE_TYPEFLAG_TO && a.ContactID != x.ContactID).Select(a => new
                {
                    a.Name,
                    a.Company,
                    a.Email,
                }),
                Code = x.Code,
                Type = x.TypeFlag == McvConstant.MEETING_TYPEFLAG_MEETING ? "MEETING" : x.TypeFlag == McvConstant.MEETING_TYPEFLAG_CNOTE ? "COMMUNICATION NOTE" : "INSPECTION",
                UID = meetingVersions.Where(v => v.ParentID == x.ID).OrderByDescending(v => v.Created).Any() ? meetingVersions.Where(v => v.ParentID == x.ID).OrderByDescending(v => v.Created).FirstOrDefault().UID : x.UID,
            }).ToListAsync();

        var inspectionVersions = inspectionService.Get().Where(x => x.StatusFlag == McvConstant.MEETING_STATUSFLAG_SENT && x.TypeFlag == McvConstant.MEETING_TYPEFLAG_MEETING && x.IsVersion);

        var inspections = await inspectionService.Get()
            .Where(x => x.StatusFlag == McvConstant.MEETING_STATUSFLAG_SENT && x.TypeFlag == McvConstant.MEETING_TYPEFLAG_MEETING && !x.IsVersion )
            .Include(x => x.Contact)
            .Select(x => new
            {
                x.ID,
                x.ProjectID,
                Date = x.StartDate,
                Code = x.Code,
                Visitor = x.Contact.FullName,
                UID = inspectionVersions.Where(v => v.ParentID == x.ID).OrderByDescending(v => v.Created).Any() ? inspectionVersions.Where(v => v.ParentID == x.ID).OrderByDescending(v => v.Created).FirstOrDefault().UID : x.UID,
            }).ToListAsync();

        var pendingBills = await billService.Get()
            .Include(x => x.Payments)
            .Where(x => x.TypeFlag == McvConstant.PROJECT_BILL_TYPEFLAG_INVOICE && !x.Payments.Any(p => !p.IsDeleted))
            .Select(x => new
            {
                x.ID,
                x.UID,
                x.ProjectID,
                Date = x.BillDate,
                Title = x.BillNo,
                Amount = x.Payable,
                Url = x.BlobUrl,
            })
            .ToListAsync();

        var proforma = await billService.Get()
            .Where(x => x.TypeFlag == McvConstant.PROJECT_BILL_TYPEFLAG_PROFORMA)
            .Select(x => new
            {
                x.ID,
                x.UID,
                x.ProjectID,
                Date = x.BillDate,
                Title = "PROFROMA",
                Amount = x.Payable,
                Url = x.BlobUrl,
            })
            .ToListAsync();

        var packageSubmissionURL = await sharedService.GetPresetValue(McvConstant.PACKAGE_SUBMISSION_URL_ROOT);
        var meetingMinutesURL = await sharedService.GetPresetValue(McvConstant.MEETING_MINUTES_URL_ROOT);
        var inspectionReportURL = await sharedService.GetPresetValue(McvConstant.INSPECTION_REPORT_URL_ROOT);

    

            Debug.WriteLine($"Project: {project.Title}");

            var projectPackages = packages.Where(x => x.ProjectID == project.ID).ToList();
            var projectMeetings = meetings.Where(x => x.ProjectID == project.ID).ToList();
            var projectInspections = inspections.Where(x => x.ProjectID == project.ID).ToList();
            var projectBills = pendingBills.Where(x => x.ProjectID == project.ID).ToList();
            projectBills = projectBills.Concat(proforma.Where(x => x.ProjectID == project.ID)).ToList();

            Debug.WriteLine($"Packages: {projectPackages.Count}");
            Debug.WriteLine($"Meetings: {projectMeetings.Count}");
            Debug.WriteLine($"Inspections: {projectInspections.Count}");
            Debug.WriteLine($"Bills: {projectBills.Count}");

            if (projectPackages.Count + projectMeetings.Count + projectInspections.Count + projectBills.Count == 0)
            {
                Debug.WriteLine($"items are empty, then skip to next project....");
                    return BadRequest("No activity found for this Project");
            }


            IEnumerable<ProjectReportData> activities = new List<ProjectReportData>();

            if (projectPackages.Any())
            {
                activities = activities.Concat(projectPackages.Select(x => new ProjectReportData
                {
                    Date = ClockTools.GetIST(x.Date).ToString("dd MMM yyyy"),
                    Type = "SUBMISSION",
                    Title = x.Title,
                    Url = $"{packageSubmissionURL}{x.UID}",
                    UtcDate = x.Date
                }));
            }

            if (projectMeetings.Any())
            {
                activities = activities.Concat(projectMeetings.Select(x => new ProjectReportData
                {
                    Date = ClockTools.GetIST(x.Date).ToString("dd MMM yyyy"),
                    Type = x.Type,
                    Title = x.Code,
                    Url = $"{meetingMinutesURL}{x.UID}",
                    UtcDate = x.Date
                }));
            }

            if (projectInspections.Any())
            {
                activities = activities.Concat(projectInspections.Select(x => new ProjectReportData
                {
                    Date = ClockTools.GetIST(x.Date).ToString("dd MMM yyyy"),
                    Type = "INSPECTION",
                    Title = x.Code,
                    Url = $"{inspectionReportURL}{x.UID}",
                    UtcDate = x.Date
                }));
            }

            if (projectBills.Any())
            {
                activities = activities.Concat(projectBills.Select(x => new ProjectReportData
                {
                    Date = ClockTools.GetIST(x.Date).ToString("dd MMM yyyy"),
                    Type = "Bill/PROFORMA",
                    Title = x.Title,
                    Url = x.Url,
                    UtcDate=x.Date
                }));
            }

            Debug.WriteLine($"Project Activities: {activities.Count()}");

            var _reportTitle = $"Client Report | {project.Code}-{project.Title} | {ClockTools.GetIST(DateTime.UtcNow).ToString("MMM yyyy")}";

           
            var _report = ExcelUtility.ExportExcel(activities.OrderBy(x=>x.UtcDate).Select(x=> new
            {
                x.Date,
                x.Type,
                x.Title,
                x.Url
            }));
        if (_report == null) return BadRequest("Excel cannot be generated");

        return File(
        fileContents: _report,
           contentType: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        fileDownloadName: $"{_reportTitle}.xlsx",
           enableRangeProcessing: true);

    }

    class ProjectReportData
    {
        public string? Date { get; set; }
        public string? Type { get; set; }
        public string? Title { get; set; }
        public string? Url { get; set; }
        public DateTime UtcDate { get; set; }
    }
}