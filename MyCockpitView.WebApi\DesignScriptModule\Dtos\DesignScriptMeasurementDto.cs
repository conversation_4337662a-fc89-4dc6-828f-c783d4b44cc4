﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.DesignScriptModule.Entities;
using System.ComponentModel.DataAnnotations;

namespace MyCockpitView.WebApi.DesignScriptModule.Dtos;

public class DesignScriptMeasurementDto : BaseEntityDto
{
    public int DesignScriptMeasurementGroupID { get; set; }

    [StringLength(50)]
    public string? Tag { get; set; }
    public decimal? Length { get; set; }
    public decimal? Breadth { get; set; }
    public decimal? Height { get; set; }
    public decimal? Number { get; set; }
    public decimal? Area { get; set; }
    public decimal? Total { get; set; }
    public decimal? CenterToCenter { get; set; }
    public decimal? Percentage { get; set; }
    public decimal? Quantity { get; set; }
    public string? Unit { get; set; }
}

public class DesignScriptMeasurementGroupDto : BaseEntityDto
{
    public int DesignScriptEntityItemMapID { get; set; }
    [Precision(14, 2)]
    public decimal Total { get; set; } = 0;
    public string? Unit { get; set; }
    [Precision(14, 2)]
    public decimal Rate { get; set; } = 0;
    [Precision(14, 2)]
    public decimal Amount { get; set; } = 0;
    public int? ProjectID { get; set; }
    public int? DesignScriptEntityID { get; set; }
    public int? DesignScriptItemID { get; set; }
    public int? DesignScriptDataCardID { get; set; }

    public virtual ICollection<DesignScriptMeasurementDto> Measurements { get; set; } = new List<DesignScriptMeasurementDto>();
}

public class DesignScriptMeasurementGroupDtoMapperProfile : Profile
{
    public DesignScriptMeasurementGroupDtoMapperProfile()
    {


        CreateMap<DesignScriptMeasurementGroup, DesignScriptMeasurementGroupDto>()
             .ForMember(dest => dest.Measurements, opt => opt.MapFrom(src => src.Measurements))
       .ReverseMap()
       .ForMember(dest => dest.DesignScriptEntityItemMap, opt => opt.Ignore());

        CreateMap<DesignScriptMeasurement, DesignScriptMeasurementDto>()
      .ReverseMap()
      .ForMember(dest => dest.DesignScriptMeasurementGroup, opt => opt.Ignore());

    }
}