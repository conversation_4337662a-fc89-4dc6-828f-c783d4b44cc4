﻿using AutoMapper;
using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.WFTaskModule.Dtos;

namespace MyCockpitView.WebApi.ActivityModule;

public class ActivityDto : BaseEntityDto
{
    public int ContactID { get; set; }

    public string? Action { get; set; }

    public int? WFTaskID { get; set; }

    public string? Comments { get; set; }

    public string? Status { get; set; }

    public Guid ContactUID { get; set; }

    public string? ContactName { get; set; }
    public string? ContactPhotoUrl { get; set; }

    public ICollection<WFTaskAttachmentDto> Attachments { get; set; } = new List<WFTaskAttachmentDto>();

    public string? Entity { get; set; }
    public int? EntityID { get; set; }
    public string? EntityTitle { get; set; }
}

public class ActivityDtoMapperProfile : Profile
{
    public ActivityDtoMapperProfile()
    {
        CreateMap<Activity, ActivityDto>()
             .ReverseMap();
    }
}