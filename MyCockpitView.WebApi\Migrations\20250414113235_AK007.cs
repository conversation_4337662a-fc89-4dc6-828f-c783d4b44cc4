﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace MyCockpitView.WebApi.Migrations
{
    /// <inheritdoc />
    public partial class AK007 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<string>(
                name: "ClientName",
                table: "ProjectBills",
                type: "nvarchar(255)",
                maxLength: 255,
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "ClientGSTStateCode",
                table: "ProjectBills",
                type: "nvarchar(255)",
                maxLength: 255,
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "ClientGS<PERSON><PERSON>",
                table: "ProjectBills",
                type: "nvarchar(255)",
                maxLength: 255,
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldNullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "BillPercentage",
                table: "ProjectBills",
                type: "decimal(18,2)",
                precision: 18,
                scale: 2,
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<string>(
                name: "ClientPAN",
                table: "ProjectBills",
                type: "nvarchar(255)",
                maxLength: 255,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ClientTAN",
                table: "ProjectBills",
                type: "nvarchar(255)",
                maxLength: 255,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "CompanyAddress",
                table: "ProjectBills",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "CompanyBank",
                table: "ProjectBills",
                type: "nvarchar(255)",
                maxLength: 255,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "CompanyBankAccount",
                table: "ProjectBills",
                type: "nvarchar(255)",
                maxLength: 255,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "CompanyBankBranch",
                table: "ProjectBills",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "CompanyBankIFSCCode",
                table: "ProjectBills",
                type: "nvarchar(255)",
                maxLength: 255,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "CompanyGSTIN",
                table: "ProjectBills",
                type: "nvarchar(255)",
                maxLength: 255,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "CompanyGSTStateCode",
                table: "ProjectBills",
                type: "nvarchar(255)",
                maxLength: 255,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<int>(
                name: "CompanyID",
                table: "ProjectBills",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "CompanyLogoUrl",
                table: "ProjectBills",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "CompanyName",
                table: "ProjectBills",
                type: "nvarchar(255)",
                maxLength: 255,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "CompanyPAN",
                table: "ProjectBills",
                type: "nvarchar(255)",
                maxLength: 255,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "CompanySignStampUrl",
                table: "ProjectBills",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "CompanySwiftCode",
                table: "ProjectBills",
                type: "nvarchar(255)",
                maxLength: 255,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "CompanyTAN",
                table: "ProjectBills",
                type: "nvarchar(255)",
                maxLength: 255,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "CompanyUDHYAM",
                table: "ProjectBills",
                type: "nvarchar(255)",
                maxLength: 255,
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "DueAmount",
                table: "ProjectBills",
                type: "decimal(18,2)",
                precision: 18,
                scale: 2,
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<string>(
                name: "HSN",
                table: "ProjectBills",
                type: "nvarchar(255)",
                maxLength: 255,
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsIGSTApplicable",
                table: "ProjectBills",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "IsLumpSump",
                table: "ProjectBills",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "IsPreDated",
                table: "ProjectBills",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<decimal>(
                name: "PayableAmount",
                table: "ProjectBills",
                type: "decimal(18,2)",
                precision: 18,
                scale: 2,
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<DateTime>(
                name: "ProformaDate",
                table: "ProjectBills",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ProformaInvoiceNo",
                table: "ProjectBills",
                type: "nvarchar(255)",
                maxLength: 255,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ProformaInvoiceUrl",
                table: "ProjectBills",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ProjectCode",
                table: "ProjectBills",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "ProjectFee",
                table: "ProjectBills",
                type: "decimal(18,2)",
                precision: 18,
                scale: 2,
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<string>(
                name: "ProjectLocation",
                table: "ProjectBills",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ProjectTitle",
                table: "ProjectBills",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "ProjectWorkOrderID",
                table: "ProjectBills",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "TaxInvoiceNo",
                table: "ProjectBills",
                type: "nvarchar(255)",
                maxLength: 255,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "TaxInvoiceUrl",
                table: "ProjectBills",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "WorkOrderDate",
                table: "ProjectBills",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "WorkOrderNo",
                table: "ProjectBills",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "WorkPercentage",
                table: "ProjectBills",
                type: "decimal(18,2)",
                precision: 18,
                scale: 2,
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<string>(
                name: "_stages",
                table: "ProjectBills",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_ProjectBills_BillPercentage",
                table: "ProjectBills",
                column: "BillPercentage");

            migrationBuilder.CreateIndex(
                name: "IX_ProjectBills_ProformaInvoiceNo",
                table: "ProjectBills",
                column: "ProformaInvoiceNo");

            migrationBuilder.CreateIndex(
                name: "IX_ProjectBills_TaxInvoiceNo",
                table: "ProjectBills",
                column: "TaxInvoiceNo");

            migrationBuilder.Sql("update ProjectBills set companyID=1 where companyID is null");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_ProjectBills_BillPercentage",
                table: "ProjectBills");

            migrationBuilder.DropIndex(
                name: "IX_ProjectBills_ProformaInvoiceNo",
                table: "ProjectBills");

            migrationBuilder.DropIndex(
                name: "IX_ProjectBills_TaxInvoiceNo",
                table: "ProjectBills");

            migrationBuilder.DropColumn(
                name: "BillPercentage",
                table: "ProjectBills");

            migrationBuilder.DropColumn(
                name: "ClientPAN",
                table: "ProjectBills");

            migrationBuilder.DropColumn(
                name: "ClientTAN",
                table: "ProjectBills");

            migrationBuilder.DropColumn(
                name: "CompanyAddress",
                table: "ProjectBills");

            migrationBuilder.DropColumn(
                name: "CompanyBank",
                table: "ProjectBills");

            migrationBuilder.DropColumn(
                name: "CompanyBankAccount",
                table: "ProjectBills");

            migrationBuilder.DropColumn(
                name: "CompanyBankBranch",
                table: "ProjectBills");

            migrationBuilder.DropColumn(
                name: "CompanyBankIFSCCode",
                table: "ProjectBills");

            migrationBuilder.DropColumn(
                name: "CompanyGSTIN",
                table: "ProjectBills");

            migrationBuilder.DropColumn(
                name: "CompanyGSTStateCode",
                table: "ProjectBills");

            migrationBuilder.DropColumn(
                name: "CompanyID",
                table: "ProjectBills");

            migrationBuilder.DropColumn(
                name: "CompanyLogoUrl",
                table: "ProjectBills");

            migrationBuilder.DropColumn(
                name: "CompanyName",
                table: "ProjectBills");

            migrationBuilder.DropColumn(
                name: "CompanyPAN",
                table: "ProjectBills");

            migrationBuilder.DropColumn(
                name: "CompanySignStampUrl",
                table: "ProjectBills");

            migrationBuilder.DropColumn(
                name: "CompanySwiftCode",
                table: "ProjectBills");

            migrationBuilder.DropColumn(
                name: "CompanyTAN",
                table: "ProjectBills");

            migrationBuilder.DropColumn(
                name: "CompanyUDHYAM",
                table: "ProjectBills");

            migrationBuilder.DropColumn(
                name: "DueAmount",
                table: "ProjectBills");

            migrationBuilder.DropColumn(
                name: "HSN",
                table: "ProjectBills");

            migrationBuilder.DropColumn(
                name: "IsIGSTApplicable",
                table: "ProjectBills");

            migrationBuilder.DropColumn(
                name: "IsLumpSump",
                table: "ProjectBills");

            migrationBuilder.DropColumn(
                name: "IsPreDated",
                table: "ProjectBills");

            migrationBuilder.DropColumn(
                name: "PayableAmount",
                table: "ProjectBills");

            migrationBuilder.DropColumn(
                name: "ProformaDate",
                table: "ProjectBills");

            migrationBuilder.DropColumn(
                name: "ProformaInvoiceNo",
                table: "ProjectBills");

            migrationBuilder.DropColumn(
                name: "ProformaInvoiceUrl",
                table: "ProjectBills");

            migrationBuilder.DropColumn(
                name: "ProjectCode",
                table: "ProjectBills");

            migrationBuilder.DropColumn(
                name: "ProjectFee",
                table: "ProjectBills");

            migrationBuilder.DropColumn(
                name: "ProjectLocation",
                table: "ProjectBills");

            migrationBuilder.DropColumn(
                name: "ProjectTitle",
                table: "ProjectBills");

            migrationBuilder.DropColumn(
                name: "ProjectWorkOrderID",
                table: "ProjectBills");

            migrationBuilder.DropColumn(
                name: "TaxInvoiceNo",
                table: "ProjectBills");

            migrationBuilder.DropColumn(
                name: "TaxInvoiceUrl",
                table: "ProjectBills");

            migrationBuilder.DropColumn(
                name: "WorkOrderDate",
                table: "ProjectBills");

            migrationBuilder.DropColumn(
                name: "WorkOrderNo",
                table: "ProjectBills");

            migrationBuilder.DropColumn(
                name: "WorkPercentage",
                table: "ProjectBills");

            migrationBuilder.DropColumn(
                name: "_stages",
                table: "ProjectBills");

            migrationBuilder.AlterColumn<string>(
                name: "ClientName",
                table: "ProjectBills",
                type: "nvarchar(max)",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(255)",
                oldMaxLength: 255);

            migrationBuilder.AlterColumn<string>(
                name: "ClientGSTStateCode",
                table: "ProjectBills",
                type: "nvarchar(max)",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(255)",
                oldMaxLength: 255);

            migrationBuilder.AlterColumn<string>(
                name: "ClientGSTIN",
                table: "ProjectBills",
                type: "nvarchar(max)",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(255)",
                oldMaxLength: 255);
        }
    }
}
