﻿
using MyCockpitView.WebApi.ProcessLibraryModule.Services;

namespace MyCockpitView.WebApi.ProcessLibraryModule.Extensions;

public static class ServiceExtensions
{

    public static IServiceCollection RegisterProcessLibraryServices(
     this IServiceCollection services)
    {
        services.AddScoped<IProcessLibraryEntityService, ProcessLibraryEntityService>();
        services.AddScoped<IProcessLibraryEntityAttachmentService, ProcessLibraryEntityAttachmentService>();
        return services;
    }
}
