﻿using Microsoft.EntityFrameworkCore;
using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.DesignScriptModule.Entities;
using MyCockpitView.WebApi.Services;


using System.Data;




namespace MyCockpitView.WebApi.DesignScriptModule.Services
{
    public class DesignScriptEntityItemMapService : BaseEntityService<DesignScriptEntityItemMap>, IDesignScriptEntityItemMapService
    {
        public DesignScriptEntityItemMapService(EntitiesContext db) : base(db) { }

        public IQueryable<DesignScriptEntityItemMap> Get(IEnumerable<QueryFilter>? Filters = null, string? Search = null, string? Sort = null)
        {

            IQueryable<DesignScriptEntityItemMap> _query = base.Get(Filters);

            //Apply filters
            if (Filters != null)
            {

                if (Filters.Where(x => x.Key.Equals("ProjectID", StringComparison.OrdinalIgnoreCase)).Any())
                {
                    var predicate = PredicateBuilder.False<DesignScriptEntityItemMap>();
                    foreach (var _item in Filters.Where(x => x.Key.Equals("ProjectID", StringComparison.OrdinalIgnoreCase)))
                    {
                        var isNumeric = Convert.ToInt32(_item.Value);

                        predicate = predicate.Or(x => x.ProjectID == isNumeric);
                    }
                    _query = _query.Where(predicate);
                }

                if (Filters.Where(x => x.Key.Equals("designscriptentityid", StringComparison.OrdinalIgnoreCase)).Any())
                {
                    var predicate = PredicateBuilder.False<DesignScriptEntityItemMap>();
                    foreach (var _item in Filters.Where(x => x.Key.Equals("designscriptentityid", StringComparison.OrdinalIgnoreCase)))
                    {
                        var isNumeric = Convert.ToInt32(_item.Value);

                        predicate = predicate.Or(x => x.DesignScriptEntityID == isNumeric);
                    }
                    _query = _query.Where(predicate);
                }

                if (Filters.Where(x => x.Key.Equals("designscriptitemid", StringComparison.OrdinalIgnoreCase)).Any())
                {
                    var predicate = PredicateBuilder.False<DesignScriptEntityItemMap>();
                    foreach (var _item in Filters.Where(x => x.Key.Equals("designscriptitemid", StringComparison.OrdinalIgnoreCase)))
                    {
                        var isNumeric = Convert.ToInt32(_item.Value);

                        predicate = predicate.Or(x => x.DesignScriptItemID == isNumeric);
                    }
                    _query = _query.Where(predicate);
                }
            }

            if (Search != null && Search != String.Empty)
            {
                var _keywords = Search.Split(' ');

                foreach (var _key in _keywords)
                {
                    _query = _query.Include(x => x.DesignScriptItem)
                         .Where(x => x.DesignScriptItem.Title.ToLower().Contains(_key.ToLower())
                         || x.DesignScriptItem.Code.ToLower().Contains(_key.ToLower())
                         || x.DesignScriptItem.Category.ToLower().Contains(_key.ToLower())
                         || x.DesignScriptItem.ItemGroup.ToLower().Contains(_key.ToLower())
                         || x._searchTags.ToLower().Contains(_key.ToLower()));
                }
            }

            return _query.OrderByDescending(x => x.Created);

        }

        public async Task<DesignScriptEntityItemMap?> GetById(int Id)
        {

            return await db.DesignScriptEntityItemMaps.AsNoTracking()
                                 .Include(x => x.DesignScriptEntity)
                 .Include(x => x.DesignScriptItem)
                      .Include(x => x.MeasurementGroups).ThenInclude(c => c.Measurements)
                 .SingleOrDefaultAsync(i => i.ID == Id);

        }

        public async Task<DesignScriptEntityItemMap?> GetByID(Guid Id)
        {

            return await db.DesignScriptEntityItemMaps.AsNoTracking()
                  .Include(x => x.DesignScriptEntity)
                   .Include(x => x.DesignScriptItem)
                     .Include(x => x.MeasurementGroups).ThenInclude(c => c.Measurements)
                   .SingleOrDefaultAsync(i => i.UID == Id);
        }


        public async Task SyncFromElementToZone(int projectID)
        {
            // Fetch all DesignScriptEntities for the given project, including their ItemMaps and related entities (DesignScriptItem and MeasurementGroups)
            var entities = await db.DesignScriptEntities
                 // Filter out deleted entities
                .Include(x => x.ItemMaps).ThenInclude(c => c.DesignScriptItem) // Include related DesignScriptItem in ItemMaps
                .Include(x => x.ItemMaps).ThenInclude(c => c.MeasurementGroups).ThenInclude(m => m.Measurements) // Include MeasurementGroups and their Measurements
                .Where(x => x.ProjectID == projectID) // Filter entities by ProjectID
                .ToListAsync();

            // Build a hierarchy of entities starting from those without a parent (root-level entities)
            var hierarchy = entities
                            .Where(e => e.ParentID == null) // Start with root-level entities (no parent)
                            .Select(e => BuildHierarchy(e, entities)) // Recursively build the hierarchy for each root entity
                            .ToList();

            // Iterate over each zone (root-level entity)
            foreach (var zone in hierarchy)
            {
                // Create a collection to hold unique ItemMaps from the zone's child elements
                var consolidatedItemMaps = new HashSet<DesignScriptEntityItemMap>();

                // Traverse through each child of the zone (spaces and their child elements)
                foreach (var space in zone.Children)
                {
                    foreach (var element in space.Children)
                    {
                        // Iterate over each ItemMap in the element
                        foreach (var itemMap in element.ItemMaps)
                        {
                            // Check if the itemMap already exists in the consolidated set to avoid duplicates
                            var existingZoneMap = consolidatedItemMaps.SingleOrDefault(x => x.DesignScriptItemID == itemMap.DesignScriptItemID);
                            if (existingZoneMap == null)
                            {
                                // Create a new ItemMap object for the zone based on the element's itemMap
                                var zoneMap = new DesignScriptEntityItemMap();

                                // Copy necessary properties from the element's itemMap to the new zoneMap
                                zoneMap.ProjectID = itemMap.ProjectID;
                                zoneMap.DesignScriptEntityID = zone.ID;
                                zoneMap.DesignScriptItemID = itemMap.DesignScriptItemID;
                                zoneMap.CostingAmount = itemMap.CostingAmount;
                                zoneMap.Description = itemMap.Description;
                                zoneMap.CostingUnit = itemMap.CostingUnit;
                                zoneMap.OrderFlag = itemMap.OrderFlag;
                                zoneMap.StatusFlag = itemMap.StatusFlag;
                                zoneMap.TypeFlag = itemMap.TypeFlag;

                                // Iterate over each MeasurementGroup in the itemMap and copy it to the zoneMap
                                foreach (var mg in itemMap.MeasurementGroups)
                                {
                                    var zoneMG = new DesignScriptMeasurementGroup();
                                    zoneMG.DesignScriptItemID = mg.DesignScriptItemID;
                                    zoneMG.DesignScriptEntityID = zone.ID;
                                    zoneMG.DesignScriptDataCardID = mg.DesignScriptDataCardID;
                                    zoneMG.Description = mg.Description;
                                    zoneMG.ProjectID = mg.ProjectID;
                                    zoneMG.Unit = mg.Unit;
                                    zoneMG.StatusFlag = mg.StatusFlag;
                                    zoneMG.TypeFlag = mg.TypeFlag;
                                    zoneMap.MeasurementGroups.Add(zoneMG);
                                }

                                // Add the newly created zoneMap to the consolidated set
                                consolidatedItemMaps.Add(zoneMap);
                            }
                            else
                            {
                                // Iterate over each MeasurementGroup in the itemMap and copy it to the zoneMap
                                foreach (var mg in itemMap.MeasurementGroups)
                                {


                                    if (!existingZoneMap.MeasurementGroups.Any(x => x.DesignScriptItemID == mg.DesignScriptItemID && x.DesignScriptDataCardID == mg.DesignScriptDataCardID))
                                    {
                                        var zoneMG = new DesignScriptMeasurementGroup();
                                        zoneMG.DesignScriptItemID = mg.DesignScriptItemID;
                                        zoneMG.DesignScriptEntityID = zone.ID;
                                        zoneMG.DesignScriptDataCardID = mg.DesignScriptDataCardID;
                                        zoneMG.Description = mg.Description;
                                        zoneMG.ProjectID = mg.ProjectID;
                                        zoneMG.Unit = mg.Unit;
                                        zoneMG.StatusFlag = mg.StatusFlag;
                                        zoneMG.TypeFlag = mg.TypeFlag;
                                        existingZoneMap.MeasurementGroups.Add(zoneMG);
                                    }
                                }

                            }
                        }
                    }
                }

                // Remove ItemMaps from the zone that are no longer present in the consolidated set
                var unwantedItemMaps = zone.ItemMaps.Where(x => !consolidatedItemMaps.Any(m => m.DesignScriptItemID == x.DesignScriptItemID)).ToList();

                foreach (var unwanted in unwantedItemMaps)
                {
                    // Remove the unwanted ItemMap from both the zone's collection and the database
                    zone.ItemMaps.Remove(unwanted);
                    db.DesignScriptEntityItemMaps.Remove(unwanted);
                }

                // Update existing ItemMaps in the zone with the new data from the consolidated set
                foreach (var zoneMap in zone.ItemMaps)
                {
                    // Find the corresponding itemMap in the consolidated set
                    var itemMap = consolidatedItemMaps.FirstOrDefault(x => x.DesignScriptItemID == zoneMap.DesignScriptItemID);
                    if (itemMap != null)
                    {
                        // Remove unwanted MeasurementGroups from the existing zoneMap
                        foreach (var mg in zoneMap.MeasurementGroups.ToList())
                        {
                            if (mg.DesignScriptDataCardID != null && !itemMap.MeasurementGroups.Any(x => x.DesignScriptDataCardID == mg.DesignScriptDataCardID))
                            {
                                // Delete any Measurements associated with the unwanted MeasurementGroup
                                foreach (var measurement in mg.Measurements.ToList())
                                {
                                    db.Entry(measurement).State = EntityState.Deleted;
                                }

                                mg.Measurements.Clear();
                                zoneMap.MeasurementGroups.Remove(mg);
                                db.DesignScriptMeasurementGroups.Remove(mg);
                            }
                        }

                        // Add any missing MeasurementGroups from the consolidated itemMap to the zoneMap
                        foreach (var mg in itemMap.MeasurementGroups.Where(x => x.DesignScriptDataCardID != null))
                        {
                            var zoneMG = zoneMap.MeasurementGroups.FirstOrDefault(x => x.DesignScriptDataCardID == mg.DesignScriptDataCardID && x.DesignScriptItemID == mg.DesignScriptItemID);
                            if (zoneMG == null)
                            {
                                // Create a new MeasurementGroup if it doesn't already exist
                                zoneMG = new DesignScriptMeasurementGroup();
                                zoneMG.DesignScriptEntityItemMapID = zoneMap.ID;
                                zoneMG.ProjectID = mg.ProjectID;
                                zoneMG.DesignScriptItemID = mg.DesignScriptItemID;
                                zoneMG.DesignScriptEntityID = zone.ID;
                                zoneMG.DesignScriptDataCardID = mg.DesignScriptDataCardID;
                                zoneMG.Description = mg.Description;
                                zoneMG.Unit = mg.Unit;
                                zoneMG.StatusFlag = mg.StatusFlag;
                                zoneMG.TypeFlag = mg.TypeFlag;
                                zoneMap.MeasurementGroups.Add(zoneMG);
                                db.Entry(zoneMG).State = EntityState.Added;
                            }
                        }

                        // Remove the itemMap from the consolidated set as it's already processed
                        consolidatedItemMaps.Remove(itemMap);
                    }
                }

                // Add any remaining new itemMaps in the consolidated set to the zone
                foreach (var itemMap in consolidatedItemMaps)
                {
                    db.Entry(itemMap).State = EntityState.Added;
                    zone.ItemMaps.Add(itemMap);
                }
            }

            // Save all changes to the database
            await db.SaveChangesAsync();
        }


        private DesignScriptEntity BuildHierarchy(DesignScriptEntity entity, List<DesignScriptEntity> allEntities)
        {
            // Find the children of the current entity based on ParentID
            entity.Children = allEntities
                .Where(e => e.ParentID == entity.ID)
                .Select(e => BuildHierarchy(e, allEntities)) // Recursively build hierarchy for children
                .ToList();

            return entity;
        }
    }
}