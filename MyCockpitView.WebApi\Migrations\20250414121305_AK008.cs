﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace MyCockpitView.WebApi.Migrations
{
    /// <inheritdoc />
    public partial class AK008 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql("update projects set ExpectedMhr=ExpMHr where ExpectedMHr is not null");

            migrationBuilder.DropColumn(
                name: "ExpMHr",
                table: "Projects");

            migrationBuilder.DropColumn(
                name: "FacadeArea",
                table: "Projects");

            migrationBuilder.DropColumn(
                name: "InteriorArea",
                table: "Projects");

            migrationBuilder.DropColumn(
                name: "LandscapeArea",
                table: "Projects");

            migrationBuilder.AddColumn<string>(
                name: "HSNCode",
                table: "Projects",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.Sql("update projects set HSNCode='998328'");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "HSNCode",
                table: "Projects");

            migrationBuilder.AddColumn<int>(
                name: "ExpMHr",
                table: "Projects",
                type: "int",
                precision: 14,
                scale: 2,
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "FacadeArea",
                table: "Projects",
                type: "decimal(18,2)",
                precision: 18,
                scale: 2,
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "InteriorArea",
                table: "Projects",
                type: "decimal(18,2)",
                precision: 18,
                scale: 2,
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "LandscapeArea",
                table: "Projects",
                type: "decimal(18,2)",
                precision: 18,
                scale: 2,
                nullable: true);
        }
    }
}
