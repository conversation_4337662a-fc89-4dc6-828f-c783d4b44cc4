﻿





using MyCockpitView.WebApi.Services;
using MyCockpitView.WebApi.ContactModule.Entities;
using MyCockpitView.CoreModule;
using Microsoft.EntityFrameworkCore;

namespace MyCockpitView.WebApi.ContactModule.Services;

public class ContactAppointmentService : BaseEntityService<ContactAppointment>, IContactAppointmentService
{
    public ContactAppointmentService(EntitiesContext db) : base(db) { }

    public IQueryable<ContactAppointment> Get(IEnumerable<QueryFilter>? Filters = null, string? Search = null, string? Sort = null)
    {

        IQueryable<ContactAppointment> _query = base.Get(Filters);

        //Apply filters
        if (Filters != null)
        {


            if (Filters.Where(x => x.Key.Equals("companyID", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<ContactAppointment>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("companyID", StringComparison.OrdinalIgnoreCase)))
                {
                    var isNumeric = Convert.ToInt32(_item.Value);

                    predicate = predicate.Or(x => x.CompanyID == isNumeric);
                }
                _query = _query.Where(predicate);
            }

            if (Filters.Where(x => x.Key.Equals("contactID", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<ContactAppointment>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("contactID", StringComparison.OrdinalIgnoreCase)))
                {
                    var isNumeric = Convert.ToInt32(_item.Value);

                    predicate = predicate.Or(x => x.ContactID == isNumeric);
                }
                _query = _query.Where(predicate);
            }
        }

        if (Search != null && Search != String.Empty)
        {

            var _key = Search.Trim();
            _query = _query.Include(x => x.Company)
                 .Where(x => x.Code.ToLower().Contains(_key.ToLower())

                 || x._searchTags.ToLower().Contains(_key.ToLower())
                                            || x.Company.Title.ToLower().Contains(_key.ToLower())
                                            || x.Company.Initials.ToLower().Contains(_key.ToLower()));

        }

        if (Sort != null && Sort != String.Empty)
        {
            switch (Sort.ToLower())
            {
                case "createddate":
                    return _query
                            .OrderByDescending(x => x.Created);

                case "modifieddate":
                    return _query
                            .OrderByDescending(x => x.Modified);

                case "joiningdate":
                    return _query
                            .OrderByDescending(x => x.JoiningDate);
            }
        }

        return _query.OrderByDescending(x => x.JoiningDate);

    }


    public async Task<ContactAppointment?> GetById(int Id)
    {

        return await db.ContactAppointments.AsNoTracking()
            .Include(x => x.Company)
            .Include(x=>x.ManagerContact)
             .SingleOrDefaultAsync(i => i.ID == Id);
    }

    public async Task<ContactAppointment?> GetById(Guid Id)
    {

        return await db.ContactAppointments.AsNoTracking()
            .Include(x => x.Company)
            .Include(x => x.ManagerContact)
             .SingleOrDefaultAsync(i => i.UID == Id);

    }

    public async Task<int> Create(ContactAppointment Entity)
    {

        var appointedContact = await db.Contacts.AsNoTracking()
            .SingleOrDefaultAsync(i => i.ID == Entity.ContactID);

        if (appointedContact == null) throw new Exception($"{nameof(Contact)} not found!");

        Entity.OrderFlag = await GetNextOrder(Entity.CompanyID);

        var _type = await db.TypeMasters.AsNoTracking().Where(x => x.Entity==nameof(ContactAppointment)
          && x.Value == Entity.TypeFlag).Select(x => x.Title).SingleOrDefaultAsync();

        var _company = await db.Companies.AsNoTracking().Where(x => x.ID == Entity.CompanyID).SingleOrDefaultAsync();

        Entity.Code = _company.Initials + "-" + _type.Substring(0, 1).ToUpper() + "-" + Entity.OrderFlag.ToString("000");


        var _vhrRate = Convert.ToInt32((await db.AppSettingMasters.AsNoTracking().SingleOrDefaultAsync(x => x.PresetKey == McvConstant.COMPANY_VHR_COST)).PresetValue);
        var _expectedMHr = Convert.ToInt32((await db.AppSettingMasters.AsNoTracking().SingleOrDefaultAsync(x => x.PresetKey == McvConstant.TEAM_MONTHLY_EXPECTED_MHR)).PresetValue);

        Entity.ExpectedVhr = Entity.ManValue * _expectedMHr;
        Entity.ExpectedRemuneration = Entity.ExpectedVhr * _vhrRate;
        return await base.Create(Entity);

    }

    public async Task<int> GetNextOrder(int CompanyAccountID)
    {
        // Retrieve the maximum OrderFlag for the given CompanyID
        var lastOrderFlag = await Get()
            .Where(x => x.CompanyID == CompanyAccountID)
            .Select(x => x.OrderFlag).AnyAsync() ? await Get()
            .Where(x => x.CompanyID == CompanyAccountID)
            .Select(x => x.OrderFlag)
            .MaxAsync() : 0; // Use MaxAsync with defaultValue parameter in .NET 8

        // Increment the maximum OrderFlag by 1 and return it
        return lastOrderFlag + 1;
    }

    public async Task Update(ContactAppointment Entity)
    {

        var appointedContact = await db.Contacts.AsNoTracking()
            .SingleOrDefaultAsync(i => i.ID == Entity.ContactID);

        if (appointedContact == null) throw new Exception($"{nameof(Contact)} not found!");

        var _company = await db.Companies.AsNoTracking().Where(x => x.ID == Entity.CompanyID).SingleOrDefaultAsync();
        var _type = await db.TypeMasters.AsNoTracking().Where(x => x.Entity==nameof(ContactAppointment)
         && x.Value == Entity.TypeFlag).Select(x => x.Title).SingleOrDefaultAsync();


        var _vhrRate = Convert.ToInt32((await db.AppSettingMasters.AsNoTracking().SingleOrDefaultAsync(x => x.PresetKey == McvConstant.COMPANY_VHR_COST)).PresetValue);
        var _expectedMHr = Convert.ToInt32((await db.AppSettingMasters.AsNoTracking().SingleOrDefaultAsync(x => x.PresetKey == McvConstant.TEAM_MONTHLY_EXPECTED_MHR)).PresetValue);

        Entity.Code = _company.Initials + "-" + _type.Substring(0, 1).ToUpper() + "-" + Entity.OrderFlag.ToString("000");

        Entity.ExpectedVhr = Entity.ManValue * _expectedMHr;
        Entity.ExpectedRemuneration = Entity.ExpectedVhr * _vhrRate;


        await base.Update(Entity);

    }


}