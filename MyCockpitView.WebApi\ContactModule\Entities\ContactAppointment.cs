﻿using System.ComponentModel.DataAnnotations;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.CompanyModule.Entities;


namespace MyCockpitView.WebApi.ContactModule.Entities;

public class ContactAppointment : BaseEntity
{

    [StringLength(255)]
    public string? Designation { get; set; }

    [StringLength(255)]

    public string? Code { get; set; }
    public DateTime JoiningDate { get; set; }
    public DateTime? ResignationDate { get; set; }

     [Precision(18, 2)] 
    public decimal ManValue { get; set; } = 0;
     [Precision(18, 2)] 
    public decimal ExpectedVhr { get; set; } = 0;
     [Precision(18, 2)] 
    public decimal ExpectedRemuneration { get; set; } = 0;

    public int ContactID { get; set; }
    public virtual Contact? Contact { get; set; }
    public int CompanyID { get; set; }
    public virtual Company? Company { get; set; }

    public int? ManagerContactID { get; set; }
    public virtual Contact? ManagerContact { get; set; }

    public virtual ICollection<ContactAppointmentAttachment> Attachments { get; set; } = new HashSet<ContactAppointmentAttachment>();

    [Precision(18, 2)]
    public decimal BasicPayPercentage { get; set; } = 0;
    [Precision(18, 2)]
    public decimal HRAPercentage { get; set; } = 0;
    [Precision(18, 2)]
    public decimal SpecialAllowancePercentage { get; set; } = 0;
    [Precision(18, 2)]
    public decimal BasicPayAmount { get; set; } = 0;
    [Precision(18, 2)]
    public decimal HRAAmount { get; set; } = 0;
    [Precision(18, 2)]
    public decimal SpecialAllowanceAmount { get; set; } = 0;

    [StringLength(255)]
    public string? BankAccountNo { get; set; }
    public string? BankName { get; set; }
    public string? BankBranch { get; set; }
    [StringLength(255)]
    public string? BankIFSCCode { get; set; }
    public string? Location { get; set; }

    public bool IsFixedRemuneration { get; set; }
    public bool IsVariableRemuneration { get; set; }
    public bool IsLastBiteRemuneration { get; set; }
    [Precision(18, 2)]
    public decimal AnnualCTC { get; set; } = 0;
    [Precision(18, 2)]
    public decimal MonthlyCTC { get; set; } = 0;
    public bool IsIncomeTaxDeduction { get; set; }
    public bool IsTDSDeduction { get; set; }
    public bool IsProfessionTaxDeduction { get; set; }
    public bool IsStudioMember { get; set; }
    public string? Certifications { get; set; }
    public string? SoftwareProficiency { get; set; }
    public string? KeySkills { get; set; }
    public string? YearsOfExperiance { get; set; }
    public string? Qualification { get; set; }
}

public class ContactAppointmentConfiguration : BaseEntityConfiguration<ContactAppointment>, IEntityTypeConfiguration<ContactAppointment>
{
    public void Configure(EntityTypeBuilder<ContactAppointment> builder)
    {
        base.Configure(builder);

        builder
          .HasOne(u => u.Contact)
          .WithMany(x => x.Appointments)
          .HasForeignKey(x => x.ContactID).IsRequired()
                  .OnDelete(DeleteBehavior.Restrict); 


        builder
        .HasOne(u => u.ManagerContact)
        .WithMany()
        .HasForeignKey(x => x.ManagerContactID)
        .IsRequired(false)
        .OnDelete(DeleteBehavior.SetNull);


        builder.HasIndex(e => e.JoiningDate);

        builder.HasIndex(e => e.ResignationDate);
    }
}
