﻿using AutoMapper;
using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.DesignScriptModule.Entities;
using MyCockpitView.WebApi.ProjectModule.Dtos;

namespace MyCockpitView.WebApi.DesignScriptModule.Dtos;

public class DesignScriptEntityDto : BaseEntityDto
{

    public string? Title { get; set; }

    public int CodeFlag { get; set; }


    public string? Code { get; set; }


    public string? ProjectCode { get; set; }
    public int ProjectID { get; set; }
    public Guid ProjectUID { get; set; }
    public decimal SharePercentage { get; set; }
    public int? ProjectScopeID { get; set; }
    public virtual ProjectScopeDto? ProjectScope { get; set; }
    public virtual ICollection<DesignScriptEntityDto> Children { get; set; } = new List<DesignScriptEntityDto>();
    public int? ParentID { get; set; }
    //public virtual ICollection<DesignScriptEntityVersion> Versions { get; set; }
    public virtual ICollection<DesignScriptEntityItemMapDto> ItemMaps { get; set; } = new List<DesignScriptEntityItemMapDto>();
    public virtual ICollection<DesignScriptDataCardWithoutMapsDto> DataCards { get; set; } = new List<DesignScriptDataCardWithoutMapsDto>();


    public string? CostingUnit { get; set; } = "sqmt";
    public decimal CostingQuantity { get; set; } = 0;
    public decimal CostingRate { get; set; } = 0;
    public decimal CostingAmount { get; set; } = 0;
    public string? CostingRemark { get; set; }

    //FOR MASTER PHASE
    public bool isMasterPhase { get; set; }
    public decimal EstimateContingency { get; set; }
    public decimal EstimateGST { get; set; }
    public string? EstimateRemark { get; set; }
    //---------------------

    public bool IsDetail_CIVIL_NA { get; set; }
    public bool IsDetail_CIVIL_Material { get; set; }
    public bool IsDetail_CIVIL_Joinary { get; set; }
    public bool IsDetail_CIVIL_Ending { get; set; }
    public bool IsDetail_CIVIL_Size { get; set; }

    public bool IsDetail_FINISHING_NA { get; set; }
    public bool IsDetail_FINISHING_Material { get; set; }
    public bool IsDetail_FINISHING_Joinary { get; set; }
    public bool IsDetail_FINISHING_Ending { get; set; }
    public bool IsDetail_FINISHING_Size { get; set; }

    public bool IsDetail_FURNITURE_NA { get; set; }
    public bool IsDetail_FURNITURE_Material { get; set; }
    public bool IsDetail_FURNITURE_Joinary { get; set; }
    public bool IsDetail_FURNITURE_Ending { get; set; }
    public bool IsDetail_FURNITURE_Size { get; set; }

    public bool IsDetail_LIGHTING_NA { get; set; }
    public bool IsDetail_LIGHTING_Material { get; set; }
    public bool IsDetail_LIGHTING_Joinary { get; set; }
    public bool IsDetail_LIGHTING_Ending { get; set; }
    public bool IsDetail_LIGHTING_Size { get; set; }

    public bool IsDetail_PLANTING_NA { get; set; }
    public bool IsDetail_PLANTING_Material { get; set; }
    public bool IsDetail_PLANTING_Joinary { get; set; }
    public bool IsDetail_PLANTING_Ending { get; set; }
    public bool IsDetail_PLANTING_Size { get; set; }

    public bool IsDetail_SERVICES_NA { get; set; }
    public bool IsDetail_SERVICES_Material { get; set; }
    public bool IsDetail_SERVICES_Joinary { get; set; }
    public bool IsDetail_SERVICES_Ending { get; set; }
    public bool IsDetail_SERVICES_Size { get; set; }
}

public class DesignScriptEntityDtoMapperProfile : Profile
{
    public DesignScriptEntityDtoMapperProfile()
    {
        CreateMap<DesignScriptEntity, DesignScriptEntityDto>()
             .ForMember(dest => dest.Children, opt => opt.MapFrom(src => src.Children))
             .ForMember(dest => dest.ItemMaps, opt => opt.MapFrom(src => src.ItemMaps))
              .ForMember(dest => dest.DataCards, opt => opt.MapFrom(src => src.DataCardMaps.Select(c => c.DataCard)))
        .ReverseMap()
        .ForMember(dest => dest.Children, opt => opt.Ignore())
        //.ForMember(dest => dest.Project, opt => opt.Ignore())
         .ForMember(dest => dest.DataCardMaps, opt => opt.Ignore())
        .ForMember(dest => dest.ItemMaps, opt => opt.Ignore());


    }
}

