﻿using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.CoreModule;

namespace MyCockpitView.WebApi.PackageModule.Entities;

public class PackageFeedbackAttachment : BaseBlobEntity
{
    public int PackageFeedbackID { get; set; }

    public virtual PackageFeedback? PackageFeedback { get; set; }

}
public class PackageFeedbackAttachmentConfiguration : BaseBlobEntityConfiguration<PackageFeedbackAttachment>, IEntityTypeConfiguration<PackageFeedbackAttachment>
{
    public void Configure(EntityTypeBuilder<PackageFeedbackAttachment> builder)
    {

        base.Configure(builder);

    }
}
