﻿using AutoMapper;
using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.TodoModule.Entities;

namespace MyCockpitView.WebApi.TodoModule.Dtos;

public class TodoAgendaDto : BaseEntityDto
{

    public string? Title { get; set; }

    public int TodoID { get; set; }

}

public class TodoAgendaDtoMapperProfile : Profile
{
    public TodoAgendaDtoMapperProfile()
    {

        CreateMap<TodoAgenda, TodoAgendaDto>()
.ReverseMap().ForMember(dest => dest.Todo, opt => opt.Ignore());

    }
}