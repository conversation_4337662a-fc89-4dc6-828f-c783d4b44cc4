﻿using MyCockpitView.WebApi.ContactModule.Entities;
using MyCockpitView.WebApi.MeetingModule.Entities;
using MyCockpitView.WebApi.Services;

namespace MyCockpitView.WebApi.MeetingModule.Services;

public interface IMeetingAgendaService : IBaseEntityService<MeetingAgenda>
{
    Task AssignAgendaTasks(int agendaID);
    Task CompleteAgendaTasks(int agendaID);
    Task GenerateTasks();
    Task<IEnumerable<int>> GetAssigneeContactIDs(int entityID, string stageCode, string propertyName);
    IQueryable<MeetingAgenda> GetByMeeting(int MeetingID);
    IQueryable<MeetingAgendaGroup> GetGroups(IQueryable<MeetingAgenda> AgendaQuery, string[]? GroupBy = null);
    Task<IEnumerable<MeetingAgenda>> GetPreviousAgenda(int AgendaID);
    Task<dynamic> GetTaskByStage(string Entity, int EntityID, string StageCode, string StageTitle, decimal StageDuration, DateTime? FollowUpDate = null);
    Task ScaffoldPendingAgenda(int MeetingID, string Subject, int? ProjectID = null, int? typeFlag = null);
    Task SendAgendaFollowUpEmails();
    Task SendAsCNote(MeetingAgenda UpdatedAgenda, Contact? contact = null, IEnumerable<MeetingAttendee>? attendees = null);
}