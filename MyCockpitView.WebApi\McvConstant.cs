namespace MyCockpitView.WebApi;

public class McvConstant
{
    public static readonly string EMAIL_REGEX = @"(\w+([-+.']\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*)";
    public static readonly string PERMISSION_MASTER = "MASTER";
    public static readonly string PERMISSION_LEAVE_SPECIAL_EDIT = "LEAVE_SPECIAL_EDIT";
    public static readonly string VIDEO_FILE_EXTENSIONS = ".mp4,.webm,.avi,.mov,.mkv,.wmv,.flv";
    public static readonly string IMAGE_FILE_EXTENSIONS = ".png,.jpg,.jpeg,.webp,.jfif";
    //SETTINGS
    public static readonly string BLOB_CONTAINER_ATTACHMENTS = "BLOB_CONTAINER_ATTACHMENTS";
    public static readonly string AZURE_STORAGE_KEY = "AZURE_STORAGE_KEY";
    public static readonly string AZURE_STORAGE_ACCOUNT = "AZURE_STORAGE_ACCOUNT";
    public static readonly string COMPANY_ADDRESS = "COMPANY_ADDRESS";
    public static readonly string COMPANY_PAN = "COMPANY_PAN";
    public static readonly string COMPANY_SERVICETAX_NO = "COMPANY_SERVICETAX_NO";
    public static readonly string DATE_FORMAT = "DATE_FORMAT";
    public static readonly string DATE_FORMAT_WITH_TIME = "DATE_FORMAT_WITH_TIME";
    public static readonly string DEVMODE = "DEVMODE";
    public static readonly string DEVMODE_EMAIL_TO = "DEVMODE_EMAIL_TO";
    public static readonly string EVEN_SATURDAY_OFF = "EVEN_SATURDAY_OFF";
    public static readonly string LEAVE_APPLICATION_DURATION = "LEAVE_APPLICATION_DURATION";
    public static readonly string LEAVE_PERMISSIBLE_EMERGENCY = "LEAVE_PERMISSIBLE_EMERGENCY";
    public static readonly string LEAVE_PERMISSIBLE_TOTAL = "LEAVE_PERMISSIBLE_TOTAL";
    public static readonly string WFH_APPLICATION_DURATION_DAYS_PER_MONTH = "WFH_APPLICATION_DURATION_DAYS_PER_MONTH";
    public static readonly string WFH_APPLICATION_PRIOR_DAYS = "WFH_APPLICATION_PRIOR_DAYS";
    public static readonly string BREAK_APPLICATION_DURATION_HOURS_PER_MONTH = "BREAK_APPLICATION_DURATION_HOURS_PER_MONTH";
    public static readonly string BREAK_APPLICATION_ALLOWED_PER_MONTH = "BREAK_APPLICATION_ALLOWED_PER_MONTH";
    public static readonly string BREAK_APPLICATION_PRIOR_DAYS = "BREAK_APPLICATION_PRIOR_DAYS";
    public static readonly string HALFDAY_APPLICATION_DURATION_HOURS_PER_MONTH = "HALFDAY_APPLICATION_DURATION_HOURS_PER_MONTH";
    public static readonly string HALFDAY_APPLICATION_ALLOWED_PER_MONTH = "HALFDAY_APPLICATION_ALLOWED_PER_MONTH";
    public static readonly string HALFDAY_APPLICATION_PRIOR_DAYS = "HALFDAY_APPLICATION_PRIOR_DAYS";
    public static readonly string MEETING_EMAIL_CC = "MEETING_EMAIL_CC";
    public static readonly string MEETING_EMAIL_SENDER_ID = "MEETING_EMAIL_SENDER_ID";
    public static readonly string MEETING_EMAIL_SENDER_NAME = "MEETING_EMAIL_SENDER_NAME";
    public static readonly string MEETING_MINUTES_URL_ROOT = "MEETING_MINUTES_URL_ROOT";
    public static readonly string MEETING_UPDATE_ALLOW_DURATION = "MEETING_UPDATE_ALLOW_DURATION";
    public static readonly string INSPECTION_EMAIL_CC = "INSPECTION_EMAIL_CC";
    public static readonly string INSPECTION_EMAIL_SENDER_ID = "INSPECTION_EMAIL_SENDER_ID";
    public static readonly string INSPECTION_EMAIL_SENDER_NAME = "INSPECTION_EMAIL_SENDER_NAME";
    public static readonly string INSPECTION_REPORT_URL_ROOT = "INSPECTION_REPORT_URL_ROOT";
    public static readonly string INSPECTION_UPDATE_ALLOW_DURATION = "INSPECTION_UPDATE_ALLOW_DURATION";
    public static readonly string ODD_SATURDAY_OFF = "ODD_SATURDAY_OFF";
    public static readonly string OFFICE_CLOSE_TIME = "OFFICE_CLOSE_TIME";
    public static readonly string OFFICE_END_MINUTES_UTC = "OFFICE_END_MINUTES_UTC";
    public static readonly string OFFICE_OPEN_TIME = "OFFICE_OPEN_TIME";
    public static readonly string OFFICE_START_MINUTES_UTC = "OFFICE_START_MINUTES_UTC";
    public static readonly string PACKAGE_SUBMISSION_EMAIL_CC = "PACKAGE_SUBMISSION_EMAIL_CC";
    public static readonly string PACKAGE_SUBMISSION_EMAIL_SENDER_ID = "PACKAGE_SUBMISSION_EMAIL_SENDER_ID";
    public static readonly string PACKAGE_SUBMISSION_EMAIL_SENDER_NAME = "PACKAGE_SUBMISSION_EMAIL_SENDER_NAME";
    public static readonly string PACKAGE_SUBMISSION_URL_ROOT = "PACKAGE_SUBMISSION_URL_ROOT";
    public static readonly string PACKAGE_FEEDBACK_EMAIL_CC = "PACKAGE_FEEDBACK_EMAIL_CC";
    public static readonly string PACKAGE_FEEDBACK_EMAIL_SENDER_ID = "PACKAGE_FEEDBACK_EMAIL_SENDER_ID";
    public static readonly string PACKAGE_FEEDBACK_EMAIL_SENDER_NAME = "PACKAGE_FEEDBACK_EMAIL_SENDER_NAME";
    public static readonly string SENDGRID_KEY = "SENDGRID_KEY";
    public static readonly string STARTUP_MODE = "STARTUP_MODE";
    public static readonly string TAX_CGST = "TAX_CGST";
    public static readonly string TAX_GST = "TAX_GST";
    public static readonly string TAX_IGST = "TAX_IGST";
    public static readonly string TAX_PROFESSIONALTAX = "TAX_PROFESSIONALTAX";
    public static readonly string TAX_PROFESSIONALTAX_LASTMONTH = "TAX_PROFESSIONALTAX_LASTMONTH";
    public static readonly string TAX_SERVICETAX = "TAX_SERVICETAX";
    public static readonly string TAX_SGST = "TAX_SGST";
    public static readonly string TAX_TDS = "TAX_TDS";
    public static readonly string VERSION = "VERSION";
    public static readonly string WFTASK_DUEDATE_EXTEND_DURATION = "WFTASK_DUEDATE_EXTEND_DURATION";
    public static readonly string WFTASK_FOLLOWUP_DURATION = "WFTASK_FOLLOWUP_DURATION";
    public static readonly string RDLC_PROCESSOR_API = "RDLC_PROCESSOR_API";
    //public static readonly string VIDEO_ENCODE_FUNCTION_API = "VIDEO_ENCODE_FUNCTION_API";
    public static readonly string VIDEO_ENCODE_API = "VIDEO_ENCODE_API";
    public static readonly string SMTP_SERVER = "SMTP_SERVER";
    public static readonly string SMTP_PORT = "SMTP_PORT";
    public static readonly string SMTP_USERNAME = "SMTP_USERNAME";
    public static readonly string SMTP_PASSWORD = "SMTP_PASSWORD";
    public static readonly string MICRO_MAILKIT_API = "MICRO_MAILKIT_API";
    public static readonly string MICRO_SENDGRID_API = "MICRO_SENDGRID_API";
    public static readonly string MICRO_MAILKIT_API_KEY = "MICRO_MAILKIT_API_KEY";
    public static readonly string MICRO_SENDGRID_API_KEY = "MICRO_SENDGRID_API_KEY";
    public static readonly string ENABLE_SENDGRID = "ENABLE_SENDGRID";
    public static readonly string PACKAGE_END_MINUTES_UTC = "PACKAGE_END_MINUTES_UTC";
    public static readonly string COMPANY_MSME_NO = "COMPANY_MSME_NO";
    public static readonly string COMPANY_HSN_NO = "COMPANY_HSN_NO";
    public static readonly string COMPANY_GST_NO = "COMPANY_GST_NO";
    public static readonly string COMPANY_LOGO_URL = "COMPANY_LOGO_URL";
    public static readonly string COMPANY_VHR_COST = "COMPANY_VHR_COST";
    public static readonly string IMAGE_PROCESSOR_RESIZE_API = "IMAGE_PROCESSOR_RESIZE_API";
    public static readonly string PROJECT_REPORT_URL_ROOT = "PROJECT_REPORT_URL_ROOT";
    public static readonly string PROJECT_REPORT_MESSAGE = "PROJECT_REPORT_MESSAGE";
    public static readonly string PROJECT_REPORT_EMAIL_SENDER_ID = "PROJECT_REPORT_EMAIL_SENDER_ID";
    public static readonly string PROJECT_REPORT_EMAIL_SENDER_NAME = "PROJECT_REPORT_EMAIL_SENDER_NAME";
    public static readonly string AGENDA_REPORT_MESSAGE = "AGENDA_REPORT_MESSAGE";
    public static readonly string AGENDA_REPORT_EMAIL_SENDER_ID = "AGENDA_REPORT_EMAIL_SENDER_ID";
    public static readonly string AGENDA_REPORT_EMAIL_SENDER_NAME = "AGENDA_REPORT_EMAIL_SENDER_NAME";
    public static readonly string REQUEST_TICKET_EMAIL_CC = "REQUEST_TICKET_EMAIL_CC";
    public static readonly string REQUEST_TICKET_EMAIL_SENDER_ID = "REQUEST_TICKET_EMAIL_SENDER_ID";
    public static readonly string REQUEST_TICKET_EMAIL_SENDER_NAME = "REQUEST_TICKET_EMAIL_SENDER_NAME";
    public static readonly string AGENDA_FOLLOW_UP_MESSAGE = "AGENDA_FOLLOW_UP_MESSAGE";
    public static readonly string AGENDA_FOLLOW_UP_EMAIL_CC = "AGENDA_FOLLOW_UP_EMAIL_CC";
    public static readonly string AGENDA_FOLLOW_UP_EMAIL_SENDER_ID = "AGENDA_FOLLOW_UP_EMAIL_SENDER_ID";
    public static readonly string AGENDA_FOLLOW_UP_EMAIL_SENDER_NAME = "AGENDA_FOLLOW_UP_EMAIL_SENDER_NAME";
    public static readonly string AGENDA_MAX_REMINDER_COUNT_FOR_ESCALATION = "AGENDA_MAX_REMINDER_COUNT_FOR_ESCALATION";
    public static readonly string AGENDA_MAX_REMINDER_COUNT_FOR_LOCK = "AGENDA_MAX_REMINDER_COUNT_FOR_DISCARD";
    public static readonly string PROJECT_PARTNER_SHARE = "PROJECT_PARTNER_SHARE";
    public static readonly string PROJECT_PARTNER_SHARE_BEFORE_APR21 = "PROJECT_PARTNER_SHARE_BEFORE_APR21";
    public static readonly string XCOST_FACTOR = "XCOST_FACTOR";
    public static readonly string PROCESS_LIBRARY_MAX_LEVEL = "PROCESS_LIBRARY_MAX_LEVEL";
    public static readonly string RDLC_REPORT_CONTAINER_URL = "RDLC_REPORT_CONTAINER_URL";
    public static readonly string TEAM_MONTHLY_EXPECTED_MHR = "TEAM_MONTHLY_EXPECTED_MHR";
    public static readonly string MHR_PER_DAY = "MHR_PER_DAY";
    public static readonly string PROFESSIONAL_TAX_MALE_LIMIT_1 = "PROFESSIONAL_TAX_MALE_LIMIT_1";
    public static readonly string PROFESSIONAL_TAX_MALE_LIMIT_2 = "PROFESSIONAL_TAX_MALE_LIMIT_2";
    public static readonly string PROFESSIONAL_TAX_FEMALE_LIMIT_1 = "PROFESSIONAL_TAX_FEMALE_LIMIT_1";
    public static readonly string PROFESSIONAL_TAX_MALE_LIMIT_1_AMOUNT = "PROFESSIONAL_TAX_MALE_LIMIT_1_AMOUNT";
    public static readonly string PROFESSIONAL_TAX_MALE_LIMIT_2_AMOUNT = "PROFESSIONAL_TAX_MALE_LIMIT_2_AMOUNT";
    public static readonly string PROFESSIONAL_TAX_MALE_LIMIT_2_AMOUNT_FEB = "PROFESSIONAL_TAX_MALE_LIMIT_2_AMOUNT_FEB";
    public static readonly string PROFESSIONAL_TAX_FEMALE_LIMIT_1_AMOUNT = "PROFESSIONAL_TAX_FEMALE_LIMIT_1_AMOUNT";
    public static readonly string PROFESSIONAL_TAX_FEMALE_LIMIT_1_AMOUNT_FEB = "PROFESSIONAL_TAX_FEMALE_LIMIT_1_AMOUNT_FEB";
    public static readonly string PDF_COMBINE_API = "PDF_COMBINE_API";
    public static readonly string PDF_COMBINE_BLOB_API = "PDF_COMBINE_BLOB_API";
    public static readonly string DESIGN_SCRIPT_CATEGORY_OPTIONS = "DESIGN_SCRIPT_CATEGORY_OPTIONS";
    public static readonly string PAY_SLIP_EMAIL_CC = "PAY_SLIP_EMAIL_CC";
    public static readonly string PAY_SLIP_EMAIL_SENDER_ID = "PAY_SLIP_EMAIL_SENDER_ID";
    public static readonly string PAY_SLIP_EMAIL_SENDER_NAME = "PAY_SLIP_EMAIL_SENDER_NAME";
    public static readonly string PAY_SLIP_EMAIL_REPLY = "PAY_SLIP_EMAIL_REPLY";
    public static readonly string LOCK_PROJECT_EMAIL_SENDER_ID = "LOCK_PROJECT_EMAIL_SENDER_ID";
    public static readonly string LOCK_PROJECT_EMAIL_SENDER_NAME = "LOCK_PROJECT_EMAIL_SENDER_NAME";
    public static readonly string PUSH_NOTIFICATION_API = "PUSH_NOTIFICATION_API";
    public static readonly string PUSH_NOTIFICATION_PUBLIC_KEY = "PUSH_NOTIFICATION_PUBLIC_KEY";
    public static readonly string PUSH_NOTIFICATION_PRIVATE_KEY = "PUSH_NOTIFICATION_PRIVATE_KEY";
    public static readonly string VIDEO_THUMB_URL = "VIDEO_THUMB_URL";
    public static readonly string GENERATE_QR_API = "GENERATE_QR_API";
    public static readonly string ROOT_API = "ROOT_API";
    public static readonly string AZURE_STORAGE_BLOB_ORIGINAL_HOSTNAME = "AZURE_STORAGE_BLOB_ORIGINAL_HOSTNAME";
    public static readonly string AZURE_STORAGE_BLOB_CDN_HOSTNAME = "AZURE_STORAGE_BLOB_CDN_HOSTNAME";

    public static readonly int MEETING_TYPEFLAG_MEETING = 0;
    public static readonly int MEETING_TYPEFLAG_CNOTE = 1;
    public static readonly int MEETING_TYPEFLAG_INSPECTION = 2;

    public static readonly int MEETING_ATTENDEE_TYPEFLAG_TO = 0;
    public static readonly int MEETING_ATTENDEE_TYPEFLAG_CC = 1;

    public static readonly int MEETING_STATUSFLAG_SCHEDULED = 0;
    public static readonly int MEETING_STATUSFLAG_ATTENDED = 1;
    public static readonly int MEETING_STATUSFLAG_SENT = 2;

    public static readonly int MEETING_AGENDA_STATUSFLAG_PENDING = 0;
    public static readonly int MEETING_AGENDA_STATUSFLAG_RESOLVED = 1;

    public static readonly int MEETING_AGENDA_ATTACHMENT_TYPEFLAG_GENERAL = 0;
    public static readonly int MEETING_AGENDA_ATTACHMENT_TYPEFLAG_BOLKI = 1;

    public static readonly int INSPECTION_RECIPIENT_TYPEFLAG_TO = 0;
    public static readonly int INSPECTION_RECIPIENT_TYPEFLAG_CC = 1;


    public static readonly int INSPECTION_STATUSFLAG_SCHEDULED = 0;
    public static readonly int INSPECTION_STATUSFLAG_ATTENDED = 1;
    public static readonly int INSPECTION_STATUSFLAG_SENT = 2;

    public static readonly int PACKAGE_STATUSFLAG_ACTIVE = 0;
    public static readonly int PACKAGE_STATUSFLAG_SENT = 1;

    public static readonly int PACKAGE_TYPEFLAG_ACTIVE = 0;
    public static readonly int PACKAGE_TYPEFLAG_PROPOSED = 1;
    public static readonly int PACKAGE_TYPEFLAG_EMPTY = 2;

    public static readonly int PACKAGE_ASSOCIATION_TYPEFLAG_PARTNER = 0;
    public static readonly int PACKAGE_ASSOCIATION_TYPEFLAG_ASSOCIATE = 1;
    public static readonly int PACKAGE_ASSOCIATION_TYPEFLAG_INVITEE = 2;
    public static readonly int PACKAGE_ASSOCIATION_TYPEFLAG_AGENDA_ACTION_BY = 3;
    public static readonly int PACKAGE_ASSOCIATION_TYPEFLAG_TO = 4;
    public static readonly int PACKAGE_ASSOCIATION_TYPEFLAG_CC = 5;
    public static readonly int PACKAGE_ASSOCIATION_TYPEFLAG_BCC = 6;

    public static readonly int CONTACT_TYPEFLAG_GENERAL = 0;
    public static readonly int CONTACT_TYPEFLAG_APPOINTED = 1;

    public static readonly int APPOINTMENT_STATUSFLAG_APPOINTED = 0;
    public static readonly int APPOINTMENT_STATUSFLAG_RESIGNED = 1;
    public static readonly int APPOINTMENT_STATUSFLAG_ONBREAK = 2;

    public static readonly int APPOINTMENT_TYPEFLAG_EMPLOYEE = 0;
    public static readonly int APPOINTMENT_TYPEFLAG_ASSOCIATE = 1;
    public static readonly int APPOINTMENT_TYPEFLAG_PARTNER = 2;
    public static readonly int APPOINTMENT_TYPEFLAG_CANDIDATE = 3;
    public static readonly int APPOINTMENT_TYPEFLAG_GIG = 4;
    public static readonly int APPOINTMENT_TYPEFLAG_DESIGN_ASSOCIATE = 5;

    public static readonly int PAYROLL_TYPEFLAG_TDS = 1;
    public static readonly int PAYROLL_TYPEFLAG_PTAX = 0;

    public static readonly int LOAN_STATUS_FLAG_DUE = 0;
    public static readonly int LOAN_STATUS_FLAG_PAID = 1;

    public static readonly int EXPENSE_TYPEFLAG_GENERAL = 0;
    public static readonly int EXPENSE_TYPEFLAG_PETTYCASH = 1;
    public static readonly int EXPENSE_TYPEFLAG_REMUNERATION = 2;
    public static readonly int EXPENSE_TYPEFLAG_INVESTMENT = 3;
    public static readonly int EXPENSE_TYPEFLAG_TAXATION = 4;
    public static readonly int EXPENSE_TYPEFLAG_TRAVEL = 5;

    public static readonly int EXPENSE_STATUS_FLAG_PENDING = 0;
    public static readonly int EXPENSE_STATUS_FLAG_APPROVED = 1;
    public static readonly int EXPENSE_STATUS_FLAG_REJECTED = -1;
    public static readonly int EXPENSE_STATUS_FLAG_PAID = 2;

    public static readonly int LEAVE_STATUSFLAG_PENDING = 0;
    public static readonly int LEAVE_STATUSFLAG_APPROVED = 1;
    public static readonly int LEAVE_STATUSFLAG_REJECTED = -1;
    public static readonly int LEAVE_TYPEFLAG_APPROVED = 0;
    public static readonly int LEAVE_TYPEFLAG_EMERGENCY = 1;
    public static readonly int LEAVE_TYPEFLAG_LATE = 2;
    public static readonly int LEAVE_TYPEFLAG_PENELTY = 3;
    public static readonly int LEAVE_TYPEFLAG_APPROVED_BREAK = 4;
    public static readonly int LEAVE_TYPEFLAG_EMERGENCY_BREAK = 5;
    public static readonly int LEAVE_TYPEFLAG_APPROVED_WFH = 6;
    public static readonly int LEAVE_TYPEFLAG_EMERGENCY_WFH = 7;
    public static readonly int LEAVE_TYPEFLAG_APPROVED_HALFDAY = 8;
    public static readonly int LEAVE_TYPEFLAG_EMERGENCY_HALFDAY = 9;

    public static readonly int HOLIDAY_MASTER_TYPEFLAG_HOLIDAY = 0;
    public static readonly int HOLIDAY_MASTER_TYPEFLAG_WORKING = 1;

    public static readonly int WFTASK_STATUSFLAG_PENDING = 0;
    public static readonly int WFTASK_STATUSFLAG_COMPLETED = 1;
    public static readonly int WFTASK_STATUSFLAG_UNATTENDED = -1;
    public static readonly int WFTASK_STATUSFLAG_STARTED = 2;
    public static readonly int WFTASK_STATUSFLAG_PAUSED = 3;

    public static readonly int WFTASK_OUTCOME_START = 0;
    public static readonly int WFTASK_OUTCOME_PAUSE = 1;
    public static readonly int WFTASK_OUTCOME_STOP = 2;

    public static readonly int DESIGN_SCRIPT_ENTITY_TYPEFLAG_ZONE = 0;
    public static readonly int DESIGN_SCRIPT_ENTITY_TYPEFLAG_SPACE = 1;
    public static readonly int DESIGN_SCRIPT_ENTITY_TYPEFLAG_ELEMENT = 2;

    public static readonly int DESIGN_SCRIPT_DATA_CARD_TYPEFLAG_MATERIAL = 0;
    public static readonly int DESIGN_SCRIPT_DATA_CARD_TYPEFLAG_VISION = 1;
    public static readonly int DESIGN_SCRIPT_DATA_CARD_TYPEFLAG_GOAL = 2;
    public static readonly int DESIGN_SCRIPT_DATA_CARD_TYPEFLAG_STRATEGY = 3;

    public static readonly int PROCESS_LIBRARY_ENTITY_TYPEFLAG_FUNCTION = 0;
    public static readonly int PROCESS_LIBRARY_ENTITY_TYPEFLAG_KRA = 1;
    public static readonly int PROCESS_LIBRARY_ENTITY_TYPEFLAG_PROCESS = 2;
    public static readonly int PROCESS_LIBRARY_ENTITY_TYPEFLAG_TASK = 3;

    public static readonly int PROJECT_STATUSFLAG_LOST = -2;
    public static readonly int PROJECT_STATUSFLAG_DISCARD = -1;
    public static readonly int PROJECT_STATUSFLAG_COMPLETED = 4;
    public static readonly int PROJECT_STATUSFLAG_INQUIRY = 0;
    public static readonly int PROJECT_STATUSFLAG_PREPOPOSAL = 1;
    public static readonly int PROJECT_STATUSFLAG_INPROGRESS = 2;
    public static readonly int PROJECT_STATUSFLAG_ONHOLD = 3;
    public static readonly int PROJECT_STATUSFLAG_DUE = 5;
    public static readonly int PROJECT_STATUSFLAG_LOCKED = 6;

    public static readonly int PROJECT_BILL_TYPEFLAG_PROFORMA = 0;
    public static readonly int PROJECT_BILL_TYPEFLAG_INVOICE = 1;

    public static readonly int PROJECT_ASSOCIATION_TYPEFLAG_PARTNER = 0;
    public static readonly int PROJECT_ASSOCIATION_TYPEFLAG_ASSOCIATE = 1;
    public static readonly int PROJECT_ASSOCIATION_TYPEFLAG_CLIENT_REPORT_GROUP = 2;
    public static readonly int PROJECT_ASSOCIATION_TYPEFLAG_FIRST_CONTACT = 3;
    public static readonly int PROJECT_ASSOCIATION_TYPEFLAG_SECOND_CONTACT = 4;
    public static readonly int PROJECT_ASSOCIATION_TYPEFLAG_CLIENT_CONTACT = 5;
    public static readonly int PROJECT_ASSOCIATION_TYPEFLAG_DESIGN_SCRIPT_MANAGER = 6;


    public static readonly int REQUEST_TICKET_STATUSFLAG_ACTIVE = 0;
    public static readonly int REQUEST_TICKET_STATUSFLAG_CLOSED = 1;

    public static readonly int PACKAGE_ATTACHMENT_TYPEFLAG_EXTERNAL = 0;
    public static readonly int PACKAGE_ATTACHMENT_TYPEFLAG_INTERNAL = 1;

    public static readonly Dictionary<string, string> DESIGN_SCRIPT_CATEGORY_ABBREIVIATIONS = new Dictionary<string, string>
        {
            { "CIVIL","C" },
            { "FINISHING","F" },
            {"FURNITURE","U" },
            {"PLANTING","P" },
            {"LIGHTING","L" },
            {"SERVICES","S" },
        };

    public static readonly string PACKAGE_TASK_STAGE_1 = "Blue Audit";
    public static readonly string PACKAGE_TASK_STAGE_3 = "Studio Work";
    public static readonly string PACKAGE_TASK_STAGE_5 = "Green Workshop";
    public static readonly string PACKAGE_TASK_STAGE_6 = "Red Review";
    public static readonly string PACKAGE_TASK_STAGE_7 = "Submission";

   
    public static readonly string LOGIN_SESSION_EXPIRY_MINUTES = "LOGIN_SESSION_EXPIRY_MINUTES";
    public static readonly string LOGIN_OTP_EXPIRY_MINUTES = "LOGIN_OTP_EXPIRY_MINUTES";
    public static readonly string LOGIN_ALLOWED_COUNT = "LOGIN_ALLOWED_COUNT";
    public static readonly string LOGIN_OTP_ENABLED = "LOGIN_OTP_ENABLED";
    public static readonly string LOGIN_OTP_SENDER_ADDRESS = "LOGIN_OTP_SENDER_ADDRESS";
    public static readonly string LOGIN_OTP_SENDER_NAME = "LOGIN_OTP_SENDER_NAME";
    public static readonly string ZOHO_MAIL_API_KEY = "ZOHO_MAIL_API_KEY";
    public static readonly string ZOHO_MAIL_API = "ZOHO_MAIL_API";

    public static readonly string AGENDA_MAX_REMINDER_COUNT_FOR_DISCARD = "AGENDA_MAX_REMINDER_COUNT_FOR_DISCARD";

    public static readonly string REQUEST_TICKET_SEND_RESOLUTION_MESSAGE = "REQUEST_TICKET_SEND_RESOLUTION_MESSAGE";

    public static readonly int REQUEST_TICKET_STATUSFLAG_COMPLETED = 1;

    public static readonly int REQUEST_TICKET_ASSIGNEE_TO = 0;
    public static readonly int REQUEST_TICKET_ASSIGNEE_CC = 1;

    public static readonly int TODO_STATUSFLAG_PENDING = 0;
    public static readonly int TODO_STATUSFLAG_COMPLETED = 1;
    public static readonly int TODO_STATUSFLAG_DROPPED = -1;

    public static readonly int PROJECT_INWARD_TYPEFLAG_IN = 0;
    public static readonly int PROJECT_INWARD_TYPEFLAG_SITE_PHOTO = 1;

    public static readonly int PROJECT_WORK_ORDER_TYPEFLAG_RATE = 0;
    public static readonly int PROJECT_WORK_ORDER_TYPEFLAG_LUMPSUM = 1;


    public static readonly int PROJECT_STAGE_STATUSFLAG_PENDING = 0;
    public static readonly int PROJECT_STAGE_STATUSFLAG_COMPLETED = 1;

    public static readonly int PROJECT_STAGE_TYPEFLAG_WORK = 0;
    public static readonly int PROJECT_STAGE_TYPEFLAG_PAYMENT = 1;

    public static readonly int SPECIFICATION_ATTACHMENT_TYPEFLAG_GENERAL = 0;
    public static readonly int SPECIFICATION_ATTACHMENT_TYPEFLAG_VISUAL = 1;
    public static readonly int SPECIFICATION_ATTACHMENT_TYPEFLAG_ILLUSTRATIVE = 2;
    public static readonly int SPECIFICATION_ATTACHMENT_TYPEFLAG_PHOTOMETRICS = 3;

    public static readonly int SPECIFICATION_AREA_TYPEFLAG_AREA = 0;
    public static readonly int SPECIFICATION_AREA_TYPEFLAG_SUBAREA = 1;

    public static readonly int IMAGE_LIBRARY_ENTITY_TYPEFLAG_REFERENCE = 0;
    public static readonly int IMAGE_LIBRARY_ENTITY_TYPEFLAG_PHOTOSHOP = 1;

    public static readonly string ROLE_MASTER = "MASTER";

    public static readonly int PAYROLL_STATUSFLAG_PENDING = 0;
    public static readonly int PAYROLL_STATUSFLAG_PAID = 1;

}

