﻿


using System.Data;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.CoreModule;
using MyCockpitView.Utility.Common;
using MyCockpitView.Utility.Excel;
using MyCockpitView.WebApi.ContactModule.Entities;
using MyCockpitView.WebApi.Models;
using MyCockpitView.WebApi.Services;


namespace MyCockpitView.WebApi.ContactModule.Services;

    public class ContactService : BaseEntityService<Contact>, IContactService
    {
        public ContactService(EntitiesContext db) : base(db) { }

        public IQueryable<Contact> Get(IEnumerable<QueryFilter>? Filters = null, string? Search = null, string? Sort = null)
        {

            IQueryable<Contact> _query = base.Get(Filters);

            //Apply filters
            if (Filters != null)
            {


                if (Filters.Where(x => x.Key.Equals("category", StringComparison.OrdinalIgnoreCase)).Any())
                {
                    var predicate = PredicateBuilder.False<Contact>();
                    foreach (var _item in Filters.Where(x => x.Key.Equals("category", StringComparison.OrdinalIgnoreCase)))
                    {
                        predicate = predicate.Or(x => x._categories.Contains(_item.Value));
                    }
                    _query = _query.Where(predicate);
                }


                if (Filters.Where(x => x.Key.Equals("usersOnly", StringComparison.OrdinalIgnoreCase)).Any())
                {
                    _query = _query.Where(x => x.Username != null);
                }

                if (Filters.Where(x => x.Key.Equals("projectPartnersOnly", StringComparison.OrdinalIgnoreCase)).Any())
                {

                    var _activeProjectAssociates = db.ProjectAssociations.AsNoTracking()
                        .Include(x => x.Project)
                        .Where(x => x.Project.StatusFlag != McvConstant.PROJECT_STATUSFLAG_COMPLETED //completed
                                 && x.Project.StatusFlag != McvConstant.PROJECT_STATUSFLAG_DISCARD) //discarded
                        .Where(x => x.TypeFlag == McvConstant.PROJECT_ASSOCIATION_TYPEFLAG_PARTNER)
                        .Select(x => x.ContactID);

                    _query = _query.Where(x => _activeProjectAssociates.Where(a => a == x.ID).Any());

                }

                if (Filters.Where(x => x.Key.Equals("companyOnly", StringComparison.OrdinalIgnoreCase)).Any())
                {

                    _query = _query.Where(x => x.IsCompany);
                }

                if (Filters.Where(x => x.Key.Equals("appointmentstatusFlag", StringComparison.OrdinalIgnoreCase)).Any())
                {
                    var predicate = PredicateBuilder.False<Contact>();
                    foreach (var _item in Filters.Where(x => x.Key.Equals("appointmentstatusFlag", StringComparison.OrdinalIgnoreCase)))
                    {
                        var isNumeric = Convert.ToInt32(_item.Value);

                        predicate = predicate.Or(c => c.Appointments.Any(x => x.StatusFlag == isNumeric));
                    }
                    _query = _query.Include(x => x.Appointments).Where(predicate);
                }

                if (Filters.Where(x => x.Key.Equals("name", StringComparison.OrdinalIgnoreCase)).Any())
                {
                    var predicate = PredicateBuilder.False<Contact>();
                    foreach (var _item in Filters.Where(x => x.Key.Equals("name", StringComparison.OrdinalIgnoreCase)))
                    {

                        predicate = predicate.Or(c => c.Name == _item.Value);
                    }
                    _query = _query.Where(predicate);
                }

            if (Filters.Where(x => x.Key.Equals("Username", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<Contact>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("Username", StringComparison.OrdinalIgnoreCase)))
                {

                    predicate = predicate.Or(c => c.Username == _item.Value);
                }
                _query = _query.Where(predicate);
            }
        }

            if (Search != null && Search != String.Empty)
            {
                var _key = Search.Trim();

                _query = _query
                     .Where(x => (x.FirstName + " " + x.LastName).ToLower().Contains(_key.ToLower())

                     || x._searchTags.ToLower().Contains(_key.ToLower())
                                                || x.Email1.ToLower().Contains(_key.ToLower())
                                                || x.Email2.ToLower().Contains(_key.ToLower())
                                                //|| x.Website.ToLower().Contains(_key.ToLower())

                                                || x.Phone3.ToLower().Contains(_key.ToLower())
                                                || x.Phone1.ToLower().Contains(_key.ToLower())
                                                || x.Phone2.ToLower().Contains(_key.ToLower())
                                                  || x.Description.ToLower().Contains(_key.ToLower())
                                                || x.Notes.ToLower().Contains(_key.ToLower())
                                                 || x._categories.ToLower().Contains(_key.ToLower())
                                                             );

            }

            if (Sort != null && Sort != String.Empty)
            {
                switch (Sort.ToLower())
                {
                    case "createddate":
                        return _query
                                .OrderByDescending(x => x.Created);

                    case "modifieddate":
                        return _query
                                .OrderByDescending(x => x.Modified);

                    case "fullname":
                        return _query
                                .OrderBy(x => x.FirstName).ThenBy(x => x.LastName);
                }
            }

            return _query.OrderByDescending(x => x.FirstName).ThenBy(x => x.LastName);

        }

        public async Task<Contact?> GetById(int Id)
        {
            return await db.Contacts.AsNoTracking()
                     .Include(x => x.AssociatedCompanies).ThenInclude(c => c.Company)
                              .Include(x => x.AssociatedContacts).ThenInclude(c => c.Person)

                               .Include(x => x.Attachments)
                 .SingleOrDefaultAsync(i => i.ID == Id);


        }

    public async Task<Contact?> GetById(Guid Id)
    {
        return await db.Contacts.AsNoTracking()
                 .Include(x => x.AssociatedCompanies).ThenInclude(c => c.Company)
                          .Include(x => x.AssociatedContacts).ThenInclude(c => c.Person)

                           .Include(x => x.Attachments)
             .SingleOrDefaultAsync(i => i.UID == Id);


    }

    public async Task<int> Create(Contact Entity)
        {
            if (Entity.Birth != null && Entity.Birth.Value.Date == DateTime.UtcNow.Date)
                Entity.Birth = null;

            Entity.FullName = ($"{(Entity.Title != null ? Entity.Title : "")} {Entity.FirstName} {(Entity.LastName != null ? Entity.LastName : "")}").Trim();

            return await base.Create(Entity);
        }

        public async Task Update(Contact Entity)
        {
            Entity.FullName = ($"{(Entity.Title != null ? Entity.Title : "")} {Entity.FirstName} {(Entity.LastName != null ? Entity.LastName : "")}").Trim();

            await base.Update(Entity);

        }


        public async Task<IEnumerable<EmailContact>> GetEmailContacts(IEnumerable<QueryFilter>? Filters = null, string? Search = null, string? Sort = null)
        {

            var contacts = await Get(Filters, Search, Sort)
                                .Include(x => x.Appointments).ThenInclude(c => c.Company)
                                .Include(x => x.AssociatedCompanies).ThenInclude(c => c.Company)
                                .Where(x => (x.Email1 != null && x.Email1 != String.Empty)
                                 || (x.Email2 != null && x.Email2 != String.Empty))
                                .ToListAsync();

            var _results = new List<EmailContact>();

            foreach (var contact in contacts)
            {
                if (contact.Appointments.Any()) //Employee
                {
                    foreach (var appointment in contact.Appointments.Where(x => x.StatusFlag != 1))
                    {
                        if (contact.Email1 != null && contact.Email1 != string.Empty)
                        {
                            if (DataTools.IsEmailValid(contact.Email1))
                            {

                                _results.Add(new EmailContact
                                {
                                    Name = (contact.FullName).Trim(),
                                    Email = contact.Email1,
                                    Company = appointment.Company.Title,
                                    ID = contact.ID,
                                    UID = contact.UID,
                                    TypeFlag = contact.TypeFlag,
                                    PhotoUrl = contact.PhotoUrl,
                                });
                            }
                        }
                        if (contact.Email2 != null && contact.Email2 != string.Empty)
                        {
                            if (DataTools.IsEmailValid(contact.Email2))
                            {
                                _results.Add(new EmailContact
                                {
                                    Name = (contact.FullName).Trim(),
                                    Email = contact.Email2,
                                    Company = appointment.Company.Title,
                                    ID = contact.ID,
                                    UID = contact.UID,
                                    TypeFlag = contact.TypeFlag,
                                    PhotoUrl = contact.PhotoUrl,
                                });
                            }
                        }

                    }
                }
                else
                {
                    if (contact.AssociatedCompanies.Any())
                    {
                        foreach (var company in contact.AssociatedCompanies)
                        {
                            if (contact.Email1 != null && contact.Email1 != string.Empty)
                            {
                                if (DataTools.IsEmailValid(contact.Email1))
                                {
                                    _results.Add(new EmailContact
                                    {
                                        Name = (contact.FullName).Trim(),
                                        Email = contact.Email1,
                                        Company = company.Company.FullName,
                                        ID = contact.ID,
                                        UID = contact.UID,
                                        TypeFlag = contact.TypeFlag,
                                        PhotoUrl = contact.PhotoUrl,
                                    });
                                }
                            }
                            if (contact.Email2 != null && contact.Email2 != string.Empty)
                            {
                                if (DataTools.IsEmailValid(contact.Email2))
                                {
                                    _results.Add(new EmailContact
                                    {
                                        Name = (contact.FullName).Trim(),
                                        Email = contact.Email2,
                                        Company = company.Company.FullName,
                                        ID = contact.ID,
                                        UID = contact.UID,
                                        TypeFlag = contact.TypeFlag,
                                        PhotoUrl = contact.PhotoUrl,
                                    });
                                }
                            }

                        }
                    }
                    else
                    {
                        if (contact.Email1 != null && contact.Email1 != string.Empty)
                        {
                            if (DataTools.IsEmailValid(contact.Email1))
                            {
                                _results.Add(new EmailContact
                                {
                                    Name = (contact.FullName).Trim(),
                                    Email = contact.Email1,

                                    ID = contact.ID,
                                    UID = contact.UID,
                                    TypeFlag = contact.TypeFlag,
                                    PhotoUrl = contact.PhotoUrl,
                                });
                            }
                        }
                        if (contact.Email2 != null && contact.Email2 != string.Empty)
                        {
                            if (DataTools.IsEmailValid(contact.Email2))
                            {
                                _results.Add(new EmailContact
                                {
                                    Name = (contact.FullName).Trim(),
                                    Email = contact.Email2,

                                    ID = contact.ID,
                                    UID = contact.UID,
                                    TypeFlag = contact.TypeFlag,
                                    PhotoUrl = contact.PhotoUrl,
                                });
                            }
                        }

                    }
                }

            }

            return _results.OrderBy(x => x.Name).Distinct();

        }




        public async Task<byte[]> getExcel(IEnumerable<QueryFilter>? Filters = null, string? Search = null, string? Sort = null)
        {

            var _dataSet = new DataSet();
            var _data = await Get(Filters, Search, Sort)
                .ToListAsync();

            //var _typeMasters = await _db.TypeMasters.AsNoTracking().Where(x => x.Entity == nameof(Contact)).ToListAsync();
            var _result = _data.Select(x => new
            {
                //ContactType = _typeMasters.Any(t => t.Value == x.TypeFlag) ? _typeMasters.FirstOrDefault(t => t.Value == x.TypeFlag).Title : "",
                Categories = x._categories,
                Tags = x._searchTags,
                x.FullName,
                x.Email1,
                x.Email2,
                x.Phone1,
                x.Phone2,
                x.Phone3,
                x.Website,
                x.Address1,
                x.Address1City,
                x.Address1State,
                x.Address1Country,
                x.Address1PostalCode,
                x.Address2,
                x.Address2City,
                x.Address2State,
                x.Address2Country,
                x.Address2PostalCode,
                x.Birth,
                x.Anniversary,
                x.PAN,
                x.TAN,
                x.GSTIN,
                //Companies= x.Companies.Any() ? string.Join(",", x.Companies.Select(t => t.Contact.FullName)) : "",
            });

            _dataSet.Tables.Add(DataTools.ToDataTable(_result));

            return ExcelUtility.ExportExcel(_dataSet);

        }




    }


public class EmailContact
{
    //public int PendingAgendaCount { get; set; }

    //public string? Leaves { get; set; }

    //public int OrderFlag { get; set; }

    public int ID { get; set; }
    public Guid UID { get; set; }
    public string? Name { get; set; }
    public string? Email { get; set; }
    public string? Company { get; set; }
    public string? TypeValue { get; set; }
    public int TypeFlag { get; set; }
    public string? PhotoUrl { get; set; }
}