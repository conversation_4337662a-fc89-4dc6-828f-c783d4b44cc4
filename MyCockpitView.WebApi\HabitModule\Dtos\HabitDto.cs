﻿using AutoMapper;
using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.ContactModule.Dtos;
using MyCockpitView.WebApi.ExpenseModule.Dtos;
using MyCockpitView.WebApi.ExpenseModule.Entities;
using MyCockpitView.WebApi.HabitModule.Entities;

namespace MyCockpitView.WebApi.HabitModule.Dtos;

public class HabitListDto : BaseEntityDto
{

    public int? EntityID { get; set; }
    public string? Entity { get; set; }
    public string? EntityTitle { get; set; }
    public string? Code { get; set; }


    public string? Title { get; set; }
    public string? Subtitle { get; set; }



    public DateTime DueDate { get; set; }

    public ContactListDto? Assigner { get; set; }

    public ContactListDto? Assignee { get; set; }

    public DateTime NextRepeatDate { get; set; }
    public bool IsDelayed { get; set; }

}
public class HabitDto : HabitListDto
{

    public DateTime StartDate { get; set; }

    public string? Comment { get; set; }


    public int AssigneeContactID { get; set; }


    public int AssignerContactID { get; set; }

    public virtual ICollection<HabitAttachmentDto> Attachments { get; set; }=new List<HabitAttachmentDto>();

    public virtual ICollection<HabitResponseDto> Responses { get; set; } = new List<HabitResponseDto>();

    public decimal MHrAssigned { get; set; }
    public decimal MHrConsumed { get; set; }
    public decimal RepeatInterval { get; set; }
    public decimal TaskInterval { get; set; }
    public int RepeatCount { get; set; }
}

    public class HabitResponseDto : BaseEntityDto
    {
         public string? Value { get; set; }

        
        
         public string? Key { get; set; }

        
        
         public string? Label { get; set; }

        
         public string? PlaceHolder { get; set; }
         public string? Hint { get; set; }
        public bool Required { get; set; }
        public bool Email { get; set; }
        public int? MinLength { get; set; }
        public int? Min { get; set; }
        public int? MaxLength { get; set; }
        public int? Max { get; set; }
        public int Order { get; set; }

         public string? ControlType { get; set; }
        public string[] Options { get; set; }

        
        public int HabitID { get; set; }


    }

public class HabitDtoMapperProfile : Profile
{
    public HabitDtoMapperProfile()
    {

        CreateMap<Habit, HabitDto>().ForMember(dest => dest.IsDelayed, opt => opt.MapFrom(src => src.StatusFlag != 1
                                                                        && (src.DueDate < DateTime.UtcNow) ? true : false))
                     .ReverseMap()
                     .ForMember(dest => dest.Attachments, opt => opt.Ignore())
                     .ForMember(dest => dest.Responses, opt => opt.Ignore())
                     .ForMember(dest => dest.AssignerContact, opt => opt.Ignore())
                     .ForMember(dest => dest.AssigneeContact, opt => opt.Ignore());

        CreateMap<Habit, HabitListDto>().ForMember(dest => dest.IsDelayed, opt => opt.MapFrom(src => src.StatusFlag != 1
                                                               && (src.DueDate < DateTime.UtcNow) ? true : false));

        CreateMap<HabitResponse, HabitResponseDto>()
           .ReverseMap()
           .ForMember(dest => dest.Habit, opt => opt.Ignore());

        CreateMap<HabitAttachment, HabitAttachmentDto>()
            .ReverseMap();
    }
}
