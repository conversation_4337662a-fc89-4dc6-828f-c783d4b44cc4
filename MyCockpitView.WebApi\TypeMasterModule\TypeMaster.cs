﻿using System.ComponentModel.DataAnnotations;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.StatusMasterModule;

namespace MyCockpitView.WebApi.TypeMasterModule;

public class TypeMaster : BaseEntity
{

    [StringLength(255)]

    public string? Entity { get; set; }

    [StringLength(50)]
    public string? Title { get; set; }


    public int Value { get; set; }
}

public class TypeMasterConfiguration : BaseEntityConfiguration<TypeMaster>, IEntityTypeConfiguration<TypeMaster>
{
    public void Configure(EntityTypeBuilder<TypeMaster> builder)
    {
        base.Configure(builder);
    }
}