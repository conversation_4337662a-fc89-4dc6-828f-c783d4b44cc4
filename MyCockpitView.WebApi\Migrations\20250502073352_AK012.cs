﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace MyCockpitView.WebApi.Migrations
{
    /// <inheritdoc />
    public partial class AK012 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "Parent<PERSON>",
                table: "WFTasks",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "ParentID",
                table: "WFStages",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "ParentID",
                table: "WFStageActions",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "ParentID",
                table: "TypeMasters",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "Parent<PERSON>",
                table: "Todos",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "ParentID",
                table: "TodoAgendas",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "ParentID",
                table: "TimeEntries",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "ParentID",
                table: "TaskRequests",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "ParentID",
                table: "StatusMasters",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "ParentID",
                table: "RequestTicketAssignees",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "ParentID",
                table: "ProjectScopeVersions",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "ParentID",
                table: "ProjectScopeServices",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "ParentID",
                table: "ProjectScopeServiceMasters",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "ParentID",
                table: "ProjectScopes",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "ParentID",
                table: "Projects",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "ParentID",
                table: "ProjectOutwards",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "ParentID",
                table: "ProjectNotes",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "ParentID",
                table: "ProjectInwards",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "ParentID",
                table: "ProjectGigPoints",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "ParentID",
                table: "ProjectConsultants",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "ParentID",
                table: "ProjectBills",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "ParentID",
                table: "ProjectBillPayments",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "ParentID",
                table: "ProjectAssociations",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "ParentID",
                table: "ProjectAreas",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "ParentID",
                table: "Payrolls",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "ParentID",
                table: "Packages",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "ParentID",
                table: "PackageFeedbacks",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "ParentID",
                table: "PackageDeliverableTaskMaps",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "ParentID",
                table: "PackageDeliverables",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "ParentID",
                table: "PackageDeliverableMasters",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "ParentID",
                table: "PackageAssociations",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "ParentID",
                table: "MeetingAttendees",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "ParentID",
                table: "MeetingAgendas",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "ParentID",
                table: "LoginSessions",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "ParentID",
                table: "Loans",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "ParentID",
                table: "LibraryTitleMasters",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "ParentID",
                table: "LibraryEntityVendors",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "ParentID",
                table: "LibraryEntityAttributes",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "ParentID",
                table: "LibraryAttributeMasters",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "ParentID",
                table: "Leaves",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "ParentID",
                table: "InspectionRecipients",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "ParentID",
                table: "InspectionItems",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "ParentID",
                table: "HolidayMasters",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "ParentID",
                table: "Habits",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "ParentID",
                table: "HabitResponses",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "ParentID",
                table: "Expenses",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "ParentID",
                table: "DesignScriptMeasurements",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "ParentID",
                table: "DesignScriptMeasurementGroups",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "ParentID",
                table: "DesignScriptItems",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "ParentID",
                table: "DesignScriptItemMasters",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "ParentID",
                table: "DesignScriptEntityItemMaps",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "ParentID",
                table: "DesignScriptDataCards",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "ParentID",
                table: "DesignScriptDataCardEntityMaps",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "ParentID",
                table: "DesignScriptDataCardAttributes",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "ParentID",
                table: "ContactGroups",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "ParentID",
                table: "ContactGroupMembers",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "ParentID",
                table: "ContactAssociations",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "ParentID",
                table: "ContactAppointments",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "ParentID",
                table: "Companies",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "ParentID",
                table: "Assessments",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "ParentID",
                table: "AssessmentMasters",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "ParentID",
                table: "AppSettingMasters",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "ParentID",
                table: "Activities",
                type: "int",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "ParentID",
                table: "WFTasks");

            migrationBuilder.DropColumn(
                name: "ParentID",
                table: "WFStages");

            migrationBuilder.DropColumn(
                name: "ParentID",
                table: "WFStageActions");

            migrationBuilder.DropColumn(
                name: "ParentID",
                table: "TypeMasters");

            migrationBuilder.DropColumn(
                name: "ParentID",
                table: "Todos");

            migrationBuilder.DropColumn(
                name: "ParentID",
                table: "TodoAgendas");

            migrationBuilder.DropColumn(
                name: "ParentID",
                table: "TimeEntries");

            migrationBuilder.DropColumn(
                name: "ParentID",
                table: "TaskRequests");

            migrationBuilder.DropColumn(
                name: "ParentID",
                table: "StatusMasters");

            migrationBuilder.DropColumn(
                name: "ParentID",
                table: "RequestTicketAssignees");

            migrationBuilder.DropColumn(
                name: "ParentID",
                table: "ProjectScopeVersions");

            migrationBuilder.DropColumn(
                name: "ParentID",
                table: "ProjectScopeServices");

            migrationBuilder.DropColumn(
                name: "ParentID",
                table: "ProjectScopeServiceMasters");

            migrationBuilder.DropColumn(
                name: "ParentID",
                table: "ProjectScopes");

            migrationBuilder.DropColumn(
                name: "ParentID",
                table: "Projects");

            migrationBuilder.DropColumn(
                name: "ParentID",
                table: "ProjectOutwards");

            migrationBuilder.DropColumn(
                name: "ParentID",
                table: "ProjectNotes");

            migrationBuilder.DropColumn(
                name: "ParentID",
                table: "ProjectInwards");

            migrationBuilder.DropColumn(
                name: "ParentID",
                table: "ProjectGigPoints");

            migrationBuilder.DropColumn(
                name: "ParentID",
                table: "ProjectConsultants");

            migrationBuilder.DropColumn(
                name: "ParentID",
                table: "ProjectBills");

            migrationBuilder.DropColumn(
                name: "ParentID",
                table: "ProjectBillPayments");

            migrationBuilder.DropColumn(
                name: "ParentID",
                table: "ProjectAssociations");

            migrationBuilder.DropColumn(
                name: "ParentID",
                table: "ProjectAreas");

            migrationBuilder.DropColumn(
                name: "ParentID",
                table: "Payrolls");

            migrationBuilder.DropColumn(
                name: "ParentID",
                table: "Packages");

            migrationBuilder.DropColumn(
                name: "ParentID",
                table: "PackageFeedbacks");

            migrationBuilder.DropColumn(
                name: "ParentID",
                table: "PackageDeliverableTaskMaps");

            migrationBuilder.DropColumn(
                name: "ParentID",
                table: "PackageDeliverables");

            migrationBuilder.DropColumn(
                name: "ParentID",
                table: "PackageDeliverableMasters");

            migrationBuilder.DropColumn(
                name: "ParentID",
                table: "PackageAssociations");

            migrationBuilder.DropColumn(
                name: "ParentID",
                table: "MeetingAttendees");

            migrationBuilder.DropColumn(
                name: "ParentID",
                table: "MeetingAgendas");

            migrationBuilder.DropColumn(
                name: "ParentID",
                table: "LoginSessions");

            migrationBuilder.DropColumn(
                name: "ParentID",
                table: "Loans");

            migrationBuilder.DropColumn(
                name: "ParentID",
                table: "LibraryTitleMasters");

            migrationBuilder.DropColumn(
                name: "ParentID",
                table: "LibraryEntityVendors");

            migrationBuilder.DropColumn(
                name: "ParentID",
                table: "LibraryEntityAttributes");

            migrationBuilder.DropColumn(
                name: "ParentID",
                table: "LibraryAttributeMasters");

            migrationBuilder.DropColumn(
                name: "ParentID",
                table: "Leaves");

            migrationBuilder.DropColumn(
                name: "ParentID",
                table: "InspectionRecipients");

            migrationBuilder.DropColumn(
                name: "ParentID",
                table: "InspectionItems");

            migrationBuilder.DropColumn(
                name: "ParentID",
                table: "HolidayMasters");

            migrationBuilder.DropColumn(
                name: "ParentID",
                table: "Habits");

            migrationBuilder.DropColumn(
                name: "ParentID",
                table: "HabitResponses");

            migrationBuilder.DropColumn(
                name: "ParentID",
                table: "Expenses");

            migrationBuilder.DropColumn(
                name: "ParentID",
                table: "DesignScriptMeasurements");

            migrationBuilder.DropColumn(
                name: "ParentID",
                table: "DesignScriptMeasurementGroups");

            migrationBuilder.DropColumn(
                name: "ParentID",
                table: "DesignScriptItems");

            migrationBuilder.DropColumn(
                name: "ParentID",
                table: "DesignScriptItemMasters");

            migrationBuilder.DropColumn(
                name: "ParentID",
                table: "DesignScriptEntityItemMaps");

            migrationBuilder.DropColumn(
                name: "ParentID",
                table: "DesignScriptDataCards");

            migrationBuilder.DropColumn(
                name: "ParentID",
                table: "DesignScriptDataCardEntityMaps");

            migrationBuilder.DropColumn(
                name: "ParentID",
                table: "DesignScriptDataCardAttributes");

            migrationBuilder.DropColumn(
                name: "ParentID",
                table: "ContactGroups");

            migrationBuilder.DropColumn(
                name: "ParentID",
                table: "ContactGroupMembers");

            migrationBuilder.DropColumn(
                name: "ParentID",
                table: "ContactAssociations");

            migrationBuilder.DropColumn(
                name: "ParentID",
                table: "ContactAppointments");

            migrationBuilder.DropColumn(
                name: "ParentID",
                table: "Companies");

            migrationBuilder.DropColumn(
                name: "ParentID",
                table: "Assessments");

            migrationBuilder.DropColumn(
                name: "ParentID",
                table: "AssessmentMasters");

            migrationBuilder.DropColumn(
                name: "ParentID",
                table: "AppSettingMasters");

            migrationBuilder.DropColumn(
                name: "ParentID",
                table: "Activities");
        }
    }
}
