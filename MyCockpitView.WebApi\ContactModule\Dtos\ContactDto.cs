﻿using AutoMapper;
using MyCockpitView.WebApi.ContactModule.Entities;
using MyCockpitView.CoreModule;

namespace MyCockpitView.WebApi.ContactModule.Dtos;

public class ContactListDto : BaseEntityDto
{
    public bool IsCompany { get; set; }

    public string? FullName { get; set; }


    public string? Name { get; set; }


    public string? Email { get; set; }


    public string? Phone { get; set; }

    public string? PhotoUrl { get; set; }

    public string[] Categories { get; set; }


    public string? PAN { get; set; }


    public string? TAN { get; set; }


    public string? GSTIN { get; set; }


    public string? HSN { get; set; }


    public string? ARN { get; set; }

    public IEnumerable<ContactAppointmentDto> Appointments { get; set; }

    public string? Username { get; set; }
}
public class ContactDto : ContactListDto
{


    public string? Title { get; set; }



    public string? FirstName { get; set; }


    public string? MiddleName { get; set; }


    public string? LastName { get; set; }


    public string? Gender { get; set; }


    public string? Email1 { get; set; }


    public string? Email2 { get; set; }


    public string? Website { get; set; }


    public string? Phone1 { get; set; }


    public string? Phone2 { get; set; }


    public string? Phone3 { get; set; }


    public DateTime? Birth { get; set; }


    public DateTime? Anniversary { get; set; }


    public string? Address1 { get; set; }


    public string? Address1City { get; set; }


    public string? Address1State { get; set; }


    public string? Address1PostalCode { get; set; }


    public string? Address1Country { get; set; }


    public string? Address2 { get; set; }




    public string? Address2City { get; set; }


    public string? Address2State { get; set; }


    public string? Address2PostalCode { get; set; }


    public string? Address2Country { get; set; }


    public string? Address3Line1 { get; set; }


    public string? Address3Line2 { get; set; }


    public string? Address3Line3 { get; set; }


    public string? Address3City { get; set; }


    public string? Address3State { get; set; }


    public string? Address3PostalCode { get; set; }


    public string? Address3Country { get; set; }


    public string? PhotoFilename { get; set; }


    public virtual ICollection<ContactAssociationDto> AssociatedCompanies { get; set; }
    public virtual ICollection<ContactAssociationDto> AssociatedContacts { get; set; }

    public virtual ICollection<ContactAttachmentDto> Attachments { get; set; }

    public string? Notes { get; set; }


    public string? MaritalStatus { get; set; }


    public string? Adhaar { get; set; }


    public string? FamilyContactName { get; set; }


    public string? FamilyContactRelation { get; set; }


    public string? EmergencyContactName { get; set; }


    public string? EmergencyContactRelation { get; set; }

}


public class ContactDtoMapperProfile : Profile
{
    public ContactDtoMapperProfile()
    {
        CreateMap<Contact, ContactDto>()
           
            .ForMember(dest => dest.Appointments, opt => opt.MapFrom(src => src.Appointments))
            .ForMember(dest => dest.Attachments, opt => opt.MapFrom(src => src.Attachments))
            .ForMember(dest => dest.AssociatedContacts, opt => opt.MapFrom(src => src.AssociatedContacts))
            .ForMember(dest => dest.AssociatedCompanies, opt => opt.MapFrom(src => src.AssociatedCompanies))
                       //.ForMember(dest => dest.Children, opt => opt.MapFrom(src => src.Children))
            .ReverseMap()
               .ForMember(dest => dest.Children, opt => opt.Ignore())
            .ForMember(dest => dest.Attachments, opt => opt.Ignore())
            .ForMember(dest => dest.AssociatedContacts, opt => opt.Ignore())
            .ForMember(dest => dest.Appointments, opt => opt.Ignore())
            .ForMember(dest => dest.AssociatedCompanies, opt => opt.Ignore());

        CreateMap<Contact, ContactListDto>()
             .ForMember(dest => dest.Email, opt => opt.MapFrom(src => src.Email1))
                 .ForMember(dest => dest.Phone, opt => opt.MapFrom(src => src.Phone1));
            //.ForMember(dest => dest.Phone, opt => opt.MapFrom(src => src.Phones.Any(c => c.IsPrimary) ? src.Phones.FirstOrDefault(c=>c.IsPrimary).Phone:null))
            //.ForMember(dest => dest.Email, opt => opt.MapFrom(src => src.Emails.Any(c => c.IsPrimary) ? src.Emails.FirstOrDefault(c => c.IsPrimary).Email : null));
    }
}
