﻿

using System.Data;
using MyCockpitView.WebApi.Exceptions;
using MyCockpitView.WebApi.Services;
using MyCockpitView.WebApi.DesignScriptModule.Entities;
using MyCockpitView.CoreModule;
using Microsoft.EntityFrameworkCore;

namespace MyCockpitView.WebApi.DesignScriptModule.Services;



public class DesignScriptDataCardService : BaseEntityService<DesignScriptDataCard>, IDesignScriptDataCardService
{
    public DesignScriptDataCardService(EntitiesContext db) : base(db) { }

    public IQueryable<DesignScriptDataCard> Get(IEnumerable<QueryFilter>? Filters = null, string? Search = null, string? Sort = null)
    {

        IQueryable<DesignScriptDataCard> _query = base.Get(Filters);

        //Apply filters
        if (Filters != null)
        {
            if (Filters.Where(x => x.Key.Equals("deleted", StringComparison.OrdinalIgnoreCase)).Any())
            {

                _query = db.DesignScriptDataCards
                        .AsNoTracking();
            }



            if (Filters.Where(x => x.Key.Equals("projectid", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<DesignScriptDataCard>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("projectid", StringComparison.OrdinalIgnoreCase)))
                {
                    var isNumeric = Convert.ToInt32(_item.Value);

                    predicate = predicate.Or(x => x.ProjectID == isNumeric);
                }
                _query = _query.Where(predicate);
            }


        }

        if (Search != null && Search != String.Empty)
        {
            var _keywords = Search.Split(' ');

            foreach (var _key in _keywords)
            {
                _query = _query.Include(x => x.Attributes)
                     .Where(x => x.Attributes.Any(a => a.AttributeValue.ToLower().Contains(_key.ToLower()))
                     || x._searchTags.ToLower().Contains(_key.ToLower()));
            }
        }

        if (Sort != null && Sort != String.Empty)
        {
            switch (Sort.ToLower())
            {
                case "createddate":
                    return _query
                            .OrderBy(x => x.Created);

                case "modifieddate":
                    return _query
                            .OrderBy(x => x.Modified);

                case "createddate desc":
                    return _query
                            .OrderByDescending(x => x.Created);

                case "modifieddate desc":
                    return _query
                            .OrderByDescending(x => x.Modified);


            }
        }
        return _query.OrderByDescending(x => x.OrderFlag);

    }

    public async Task<DesignScriptDataCard?> GetById(int Id)
    {

       return await db.DesignScriptDataCards.AsNoTracking()
                        .Include(x => x.Attributes)
                        .Include(x => x.Attachments)
                        .Include(x => x.Maps)
             .SingleOrDefaultAsync(i => i.ID == Id);

    }

    public async Task<DesignScriptDataCard?> GetByUID(Guid Id)
    {

       return await db.DesignScriptDataCards.AsNoTracking()
             .Include(x => x.Attributes)
                        .Include(x => x.Attachments)
             .SingleOrDefaultAsync(i => i.UID == Id);

    }


    public async Task<int> Create(DesignScriptDataCard entity)
    {


        var entityAttributes = string.Join(",", entity.Attributes.OrderBy(a => a.AttributeKey).Select(a => a.AttributeKey + a.AttributeValue));
        var cards = await db.DesignScriptDataCards.AsNoTracking()
        .Include(x => x.Attributes)
        .Where(x => x.Category != null)
        .Where(x => x.ProjectID == entity.ProjectID)
        .Select(x => new
        {
            x.Category,
            x.Title,
            x.Subtitle,
            x.Attributes
        }).ToListAsync();


        if (cards.Select(x => new
        {
            x.Category,
            x.Title,
            x.Subtitle,
            Attributes = string.Join(",", x.Attributes.OrderBy(a => a.AttributeKey).Select(a => a.AttributeKey + a.AttributeValue))
        }).Any(x => x.Category == entity.Category && x.Title == entity.Title && x.Subtitle == entity.Subtitle && x.Attributes == entityAttributes))
            throw new EntityServiceException("DataCard already exists in Project Collection!");

        var _project = await db.Projects.AsNoTracking().SingleOrDefaultAsync(x => x.ID == entity.ProjectID);
        if (_project == null) throw new EntityServiceException("Project not found!");
        entity.ProjectCode = _project.Code;
        entity.ProjectUID = _project.UID;


        var _maxOrder = 0;
        if (await db.DesignScriptDataCards.AsNoTracking()
            .Where(x => x.ProjectID == _project.ID && x.Title == entity.Title && x.TypeFlag == entity.TypeFlag).AnyAsync())
        {
            _maxOrder = await db.DesignScriptDataCards.AsNoTracking()
                .Where(x => !x.IsDeleted)
                .Where(x => x.ProjectID == _project.ID && x.Title == entity.Title && x.TypeFlag == entity.TypeFlag)
                .MaxAsync(x => x.OrderFlag);
        }

        entity.OrderFlag = _maxOrder + 1;

        db.DesignScriptDataCards.Add(entity);
        await db.SaveChangesAsync();

        return entity.ID;

    }

    public async Task Update(DesignScriptDataCard UpdatedEntity)
    {



        if (UpdatedEntity == null) throw new EntityServiceException("Object is null!");

        var _entity = await db.DesignScriptDataCards.
            Include(x => x.Attributes)
            .SingleOrDefaultAsync(x => x.ID == UpdatedEntity.ID);

        if (_entity == null) throw new EntityServiceException("Object is null!");


        // Update or Add attributes
        foreach (var updatedAttr in UpdatedEntity.Attributes)
        {
            var existingAttr = _entity.Attributes.FirstOrDefault(attr =>
                attr.AttributeKey == updatedAttr.AttributeKey);

            if (existingAttr != null)
            {
                // Update the existing attribute
                //db.Entry(existingAttr).CurrentValues.SetValues(updatedAttr);
                existingAttr.AttributeValue = updatedAttr.AttributeValue;
                existingAttr.OrderFlag = updatedAttr.OrderFlag;
            }
            else
            {
                // Add new attribute
                _entity.Attributes.Add(updatedAttr);
            }
        }

        // Remove attributes not present in the updated entity
        foreach (var existingAttr in _entity.Attributes.ToList())
        {
            if (!UpdatedEntity.Attributes.Any(attr =>
                 attr.AttributeKey == existingAttr.AttributeKey))
            {
                _entity.Attributes.Remove(existingAttr);
                db.Entry(existingAttr).State = EntityState.Deleted;
            }
        }

        db.Entry(_entity).CurrentValues.SetValues(UpdatedEntity);

        await base.Update(_entity);


    }

    public async Task Delete(int Id)
    {
        var designScripMeasurementGroups = await db.DesignScriptMeasurementGroups.Where(x => x.DesignScriptDataCardID != null && x.DesignScriptDataCardID == Id)
            .ToListAsync();

        db.DesignScriptMeasurementGroups.RemoveRange(designScripMeasurementGroups);

        await base.Delete(Id);

    }

    public async Task<int> Copy(int designScriptEntityID, DesignScriptDataCard dataCard, IEnumerable<DesignScriptDataCardAttachment>? attachments = null)
    {


        var sourceEntity = await db.DesignScriptEntities.AsNoTracking()
             .SingleOrDefaultAsync(i => i.ID == designScriptEntityID);

        if (sourceEntity == null) throw new EntityServiceException("Destination entity not found!");

        var _project = await db.Projects.AsNoTracking().SingleOrDefaultAsync(x => x.ID == sourceEntity.ProjectID);
        if (_project == null) throw new EntityServiceException("Project not found!");

        dataCard.ProjectCode = _project.Code;
        dataCard.ProjectUID = _project.UID;
        dataCard.ProjectID = _project.ID;
        dataCard.IsReadOnly = true;

        var sharedService = new SharedService(db);
        if (attachments != null)
        {
            foreach (var attachment in attachments)
            {
                var newAttachment = new DesignScriptDataCardAttachment();
                // Copy all values except ID
                db.Entry(newAttachment).CurrentValues.SetValues(attachment);
                newAttachment.ID = 0;
                // Make sure the relationship is properly established
                newAttachment.DesignScriptDataCardID = dataCard.ID;  // This might be 0 until SaveChanges
                                                         // Add to both collection and context
                dataCard.Attachments.Add(newAttachment);
                db.DesignScriptDataCardAttachments.Add(newAttachment);
            }
        }
        db.DesignScriptDataCards.Add(dataCard);
        db.DesignScriptDataCardEntityMaps.Add(new DesignScriptDataCardEntityMap
        {
            DataCard = dataCard,
            DesignScriptEntityID = sourceEntity.ID
        });
        await db.SaveChangesAsync();
        return dataCard.ID;


    }

    public async Task ConnectWithEntity(int designScriptEntityID, int designScriptDataCardID)
    {

        var _designScriptEntityMap = await db.DesignScriptDataCardEntityMaps
             .FirstOrDefaultAsync(i => i.DesignScriptEntityID == designScriptEntityID && i.DesignScriptDataCardID == designScriptDataCardID);

        if (_designScriptEntityMap != null) throw new EntityServiceException("Mapping already exists!");

        db.DesignScriptDataCardEntityMaps.Add(new DesignScriptDataCardEntityMap
        {
            DesignScriptDataCardID = designScriptDataCardID,
            DesignScriptEntityID = designScriptEntityID
        });
        await db.SaveChangesAsync();
    }

    public async Task DisconnectFromEntity(int designScriptEntityID, int designScriptDataCardID)
    {

        var _designScriptEntityMap = await db.DesignScriptDataCardEntityMaps
                 .FirstOrDefaultAsync(i => i.DesignScriptEntityID == designScriptEntityID && i.DesignScriptDataCardID == designScriptDataCardID);

        if (_designScriptEntityMap == null) throw new EntityServiceException("Mapping doesnt exists!");

        db.DesignScriptDataCardEntityMaps.Remove(_designScriptEntityMap);
        await db.SaveChangesAsync();

    }

}