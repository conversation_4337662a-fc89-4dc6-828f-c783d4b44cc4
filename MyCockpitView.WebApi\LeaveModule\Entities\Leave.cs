﻿using Microsoft.EntityFrameworkCore;
using MyCockpitView.WebApi.ContactModule.Entities;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using MyCockpitView.CoreModule;

namespace MyCockpitView.WebApi.LeaveModule.Entities
{
    public class Leave : BaseEntity
    {
        [Required]
        public int ContactID { get; set; }


        [Required]
        [Column(TypeName = "datetime2")]
        public DateTime Start { get; set; }


        [Required]
        [Column(TypeName = "datetime2")]
        public DateTime End { get; set; }

        public bool AllDay { get; set; }
        [Precision(14, 2)]
        public decimal Total { get; set; } = 0;

        [Required]
        public string?  Reason { get; set; }

        public Contact? Contact { get; set; }
    }

    public class LeaveConfiguration : BaseEntityConfiguration<Leave>, IEntityTypeConfiguration<Leave>
    {
        public void Configure(EntityTypeBuilder<Leave> builder)
        {
          base.Configure(builder);

            builder.HasIndex(x => x.Start);
            builder.HasIndex(x => x.End);
            builder.HasIndex(x => x.AllDay);
        }
    }
}
