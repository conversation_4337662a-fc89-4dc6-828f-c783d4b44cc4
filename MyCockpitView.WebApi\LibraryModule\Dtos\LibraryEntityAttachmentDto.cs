﻿using AutoMapper;
using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.LibraryModule.Entities;

namespace MyCockpitView.WebApi.LibraryModule.Dtos;

public class LibraryEntityAttachmentDto : BaseBlobEntityDto
{
    public int LibraryEntityID { get; set; }
}

public class LibraryEntityAttachmentDtoMapperProfile : Profile
{
    public LibraryEntityAttachmentDtoMapperProfile()
    {
        CreateMap<LibraryEntityAttachment, LibraryEntityAttachmentDto>()
             .ReverseMap()
            .ForMember(dest => dest.LibraryEntity, opt => opt.Ignore());

    }
}