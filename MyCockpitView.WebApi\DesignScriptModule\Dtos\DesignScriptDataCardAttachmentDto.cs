﻿using AutoMapper;
using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.DesignScriptModule.Entities;

namespace MyCockpitView.WebApi.DesignScriptModule.Dtos;

public class DesignScriptDataCardAttachmentDto : BaseBlobEntityDto
{

    public int DesignScriptDataCardID { get; set; }
    public bool IsHidden { get; set; }
}


public class DesignScriptDataCardAttachmentDtoMapperProfile : Profile
{
    public DesignScriptDataCardAttachmentDtoMapperProfile()
    {


        CreateMap<DesignScriptDataCardAttachment, DesignScriptDataCardAttachmentDto>()
                    .ReverseMap()
                   .ForMember(dest => dest.DesignScriptDataCard, opt => opt.Ignore());
    }
}
