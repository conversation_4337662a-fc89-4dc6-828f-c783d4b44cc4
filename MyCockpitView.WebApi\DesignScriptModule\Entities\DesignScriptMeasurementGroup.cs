﻿using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.CoreModule;

namespace MyCockpitView.WebApi.DesignScriptModule.Entities;
public class DesignScriptMeasurementGroup : BaseEntity
{
    public int DesignScriptEntityItemMapID { get; set; }

    public virtual DesignScriptEntityItemMap? DesignScriptEntityItemMap { get; set; }
    [Precision(14, 2)]
    public decimal Total { get; set; } = 0;
    public string? Unit { get; set; }
    [Precision(14, 2)]
    public decimal Rate { get; set; } = 0;
    [Precision(14, 2)]
    public decimal Amount { get; set; } = 0;
    public int? ProjectID { get; set; }
    public int? DesignScriptEntityID { get; set; }
    public int? DesignScriptItemID { get; set; }
    public int? DesignScriptDataCardID { get; set; }

    public virtual ICollection<DesignScriptMeasurement> Measurements { get; set; } = new List<DesignScriptMeasurement>();

}

public class DesignScriptMeasurementGroupConfiguration : BaseEntityConfiguration<DesignScriptMeasurementGroup>, IEntityTypeConfiguration<DesignScriptMeasurementGroup>
{
    public void Configure(EntityTypeBuilder<DesignScriptMeasurementGroup> builder)
    {
        base.Configure(builder);
       
        builder.HasIndex(e => e.ProjectID);
        builder.HasIndex(e => e.DesignScriptEntityID);
        builder.HasIndex(e => e.DesignScriptItemID);
        builder.HasIndex(e => e.DesignScriptDataCardID);

    }
}