﻿
using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.WebApi.Responses;
using MyCockpitView.Utility.Common;
using MyCockpitView.WebApi.ActivityModule;
using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.ContactModule.Services;
using MyCockpitView.WebApi.ProjectModule.Services;
using MyCockpitView.WebApi.ProjectModule.Dtos;
using MyCockpitView.WebApi.ProjectModule.Entities;

namespace MyCockpitView.WebApi.ProjectModule.Controllers;

[Authorize]
[ApiController]
[Route("[controller]")]
public class ProjectBillController : ControllerBase
{
    private readonly IProjectBillService service;
    private readonly EntitiesContext db;
    private readonly IActivityService activityService;
    private readonly IMapper mapper;
    private readonly IContactService contactService;

    public ProjectBillController(EntitiesContext db, IProjectBillService service, IMapper mapper, IActivityService activityService, IContactService contactService   )
    {
        this.db = db;
        this.service = service;
        this.activityService = activityService;
        this.mapper = mapper;
        this.contactService = contactService;
    }

    [HttpGet]
    public async Task<ActionResult<IEnumerable<ProjectBillDto>>> Get(
        [FromQuery] string? filters = null,
        [FromQuery] string? search = null,
        [FromQuery] string? sort = null)
    {
        var query = service.Get(filters != null ? DataTools.GetObjectFromJsonString<APIFilter>(filters).Filters : null, search, sort).Include(x=>x.Payments).ThenInclude(x=>x.Attachments);
        var results = mapper.Map<IEnumerable<ProjectBillDto>>(await query.ToListAsync());

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(ProjectBill))
            .ToListAsync();

        foreach (var obj in results)
        {
            obj.Type = typeMasters.FirstOrDefault(x => x.Value == obj.TypeFlag)?.Title ?? "";
        }

        var statusMasters = await db.StatusMasters
          .AsNoTracking()
          .Where(x => x.Entity == nameof(ProjectBill))
          .ToListAsync();

        foreach (var obj in results)
        {
            obj.Status = statusMasters.FirstOrDefault(x => x.Value == obj.StatusFlag)?.Title ?? "";
        }

        foreach (var _obj in results)
        {
            if (_obj.TypeFlag == 0)
                _obj.BillNo = "PROFORMA";

            if (_obj.GSTFlag != 0)
            {
                _obj.TaxRate = _obj.GSTFlag == 1 ? _obj.GSTShare : _obj.IGSTShare;

                _obj.Tax = _obj.GSTFlag == 1 ? _obj.GSTAmount : _obj.IGSTAmount;
            }

            foreach (var p in _obj.Payments)
            {
                _obj.RecievedPayment += p.Amount;
            }

            _obj.PendingPayment = await service.GetPendingAmount(_obj.ID);
            _obj.ChequeAmount = await service.GetChequeAmount(_obj.ID);
        }

        return Ok(results);
    }

    [HttpGet("Pages")]
    public async Task<ActionResult<PagedResponse<ProjectBillDto>>> GetPages(
        [FromQuery] int page = 0,
        [FromQuery] int pageSize = 50,
        [FromQuery] string? filters = null,
        [FromQuery] string? search = null,
        [FromQuery] string? sort = null)
    {
        var query = service.Get(filters != null ? DataTools.GetObjectFromJsonString<APIFilter>(filters).Filters : null, search, sort).Include(x => x.Payments).ThenInclude(x => x.Attachments); 

        var totalCount = await query.CountAsync();
        var totalPages = (int)Math.Ceiling((decimal)totalCount / pageSize);
        var results = mapper.Map<IEnumerable<ProjectBillDto>>(await query
            .Skip(pageSize * page)
            .Take(pageSize)
            .ToListAsync());

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(ProjectBill))
            .ToListAsync();

        foreach (var obj in results)
        {
            obj.Type = typeMasters.FirstOrDefault(x => x.Value == obj.TypeFlag)?.Title ?? "";
        }

        var statusMasters = await db.StatusMasters
           .AsNoTracking()
           .Where(x => x.Entity == nameof(ProjectBill))
           .ToListAsync();

        foreach (var obj in results)
        {
            obj.Status = statusMasters.FirstOrDefault(x => x.Value == obj.StatusFlag)?.Title ?? "";
        }

        foreach (var _obj in results)
        {
            if (_obj.TypeFlag == 0)
                _obj.BillNo = "PROFORMA";

            if (_obj.GSTFlag != 0)
            {
                _obj.TaxRate = _obj.GSTFlag == 1 ? _obj.GSTShare : _obj.IGSTShare;

                _obj.Tax = _obj.GSTFlag == 1 ? _obj.GSTAmount : _obj.IGSTAmount;
            }

            foreach (var p in _obj.Payments)
            {
                _obj.RecievedPayment += p.Amount;
            }

            _obj.PendingPayment = await service.GetPendingAmount(_obj.ID);
            _obj.ChequeAmount = await service.GetChequeAmount(_obj.ID);
        }

        return Ok(new PagedResponse<ProjectBillDto>(results, totalCount, totalPages));
    }

    [HttpGet("{id:int}")]
    public async Task<ActionResult<ProjectBillDto>> GetByID(int id)
    {
        var responseDto = mapper.Map<ProjectBillDto>(await service.GetById(id));
        if (responseDto == null)
        {
            return NotFound();
        }

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(ProjectBill))
            .ToListAsync();

        responseDto.Type = typeMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        var statusMasters = await db.StatusMasters
          .AsNoTracking()
          .Where(x => x.Entity == nameof(ProjectBill))
          .ToListAsync();

        responseDto.Status = statusMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        
            if (responseDto.TypeFlag == 0)
                responseDto.BillNo = "PROFORMA";

            if (responseDto.GSTFlag != 0)
            {
                responseDto.TaxRate = responseDto.GSTFlag == 1 ? responseDto.GSTShare : responseDto.IGSTShare;

                responseDto.Tax = responseDto.GSTFlag == 1 ? responseDto.GSTAmount : responseDto.IGSTAmount;
            }

            foreach (var p in responseDto.Payments)
            {
                responseDto.RecievedPayment += p.Amount;
            }

            responseDto.PendingPayment = await service.GetPendingAmount(responseDto.ID);
            responseDto.ChequeAmount = await service.GetChequeAmount(responseDto.ID);
        

        return Ok(responseDto);
    }

    [HttpPost]
    public async Task<ActionResult<ProjectBillDto>> Post(ProjectBillDto dto)
    {
        var id = await service.Create(mapper.Map<ProjectBill>(dto));
        var responseDto = mapper.Map<ProjectBillDto>(await service.GetById(id));

        if (responseDto == null)
            return BadRequest("Not Created");

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(ProjectBill))
            .ToListAsync();

        responseDto.Type = typeMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        var statusMasters = await db.StatusMasters
  .AsNoTracking()
  .Where(x => x.Entity == nameof(ProjectBill))
  .ToListAsync();

        responseDto.Status = statusMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        if (responseDto.TypeFlag == 0)
            responseDto.BillNo = "PROFORMA";

        if (responseDto.GSTFlag != 0)
        {
            responseDto.TaxRate = responseDto.GSTFlag == 1 ? responseDto.GSTShare : responseDto.IGSTShare;

            responseDto.Tax = responseDto.GSTFlag == 1 ? responseDto.GSTAmount : responseDto.IGSTAmount;
        }

        foreach (var p in responseDto.Payments)
        {
            responseDto.RecievedPayment += p.Amount;
        }

        responseDto.PendingPayment = await service.GetPendingAmount(responseDto.ID);
        responseDto.ChequeAmount = await service.GetChequeAmount(responseDto.ID);
        //if (!string.IsNullOrEmpty(User.Identity?.Name))
        //{
        //    var currentContact = await contactService.Get().FirstOrDefaultAsync(x => x.Username == User.Identity.Name);
        //    if (currentContact != null)
        //    {
        //        var parent = await contactService.Get().SingleOrDefaultAsync(x => x.ID == responseDto.ContactID);
        //        if (parent != null)
        //        {
        //            await activityService.LogUserActivity(currentContact, nameof(parent), parent.ID, $"{parent.Name}", $"{nameof(ProjectBill).Replace(nameof(parent),"")} | {responseDto.Company.Title} | {responseDto.Designation} | {responseDto.ManValue}mV", "Created");
        //        }
        //    }
        //}

        return CreatedAtAction(nameof(GetByID), new { id = responseDto.ID }, responseDto);
    }

    [HttpPut("{id:int}")]
    public async Task<ActionResult<ProjectBillDto>> Put(int id, ProjectBillDto dto)
    {
        if (id != dto.ID)
        {
            return BadRequest();
        }

        await service.Update(mapper.Map<ProjectBill>(dto));
        var responseDto = mapper.Map<ProjectBillDto>(await service.GetById(id));

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(ProjectBill))
            .ToListAsync();

        responseDto.Type = typeMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        var statusMasters = await db.StatusMasters
  .AsNoTracking()
  .Where(x => x.Entity == nameof(ProjectBill))
  .ToListAsync();

        responseDto.Status = statusMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        if (responseDto.TypeFlag == 0)
            responseDto.BillNo = "PROFORMA";

        if (responseDto.GSTFlag != 0)
        {
            responseDto.TaxRate = responseDto.GSTFlag == 1 ? responseDto.GSTShare : responseDto.IGSTShare;

            responseDto.Tax = responseDto.GSTFlag == 1 ? responseDto.GSTAmount : responseDto.IGSTAmount;
        }

        foreach (var p in responseDto.Payments)
        {
            responseDto.RecievedPayment += p.Amount;
        }

        responseDto.PendingPayment = await service.GetPendingAmount(responseDto.ID);
        responseDto.ChequeAmount = await service.GetChequeAmount(responseDto.ID);
        //if (!string.IsNullOrEmpty(User.Identity?.Name))
        //{
        //    var currentContact = await contactService.Get().FirstOrDefaultAsync(x => x.Username == User.Identity.Name);
        //    if (currentContact != null)
        //    {
        //        var parent = await contactService.Get().SingleOrDefaultAsync(x => x.ID == responseDto.ContactID);
        //        if (parent != null)
        //        {
        //            await activityService.LogUserActivity(currentContact, nameof(parent), parent.ID, $"{parent.Name}", $"{nameof(ProjectBill).Replace(nameof(parent), "")} | {responseDto.Company.Title} | {responseDto.Designation} | {responseDto.ManValue}mV", "Updated");
        //        }
        //    }
        //}

        return Ok(responseDto);
    }

    [HttpDelete("{id:int}")]
    public async Task<IActionResult> Delete(int id)
    {
        var responseDto = mapper.Map<ProjectBillDto>(await service.GetById(id));
        if (responseDto == null) return BadRequest($"{nameof(ProjectBill)} not found!");

        await service.Delete(id);

        //if (!string.IsNullOrEmpty(User.Identity?.Name))
        //{
        //    var currentContact = await contactService.Get().FirstOrDefaultAsync(x => x.Username == User.Identity.Name);
        //    if (currentContact != null)
        //    {
        //        var parent = await contactService.Get().SingleOrDefaultAsync(x => x.ID == responseDto.ContactID);
        //        if (parent != null)
        //        {
        //            await activityService.LogUserActivity(currentContact, nameof(parent), parent.ID, $"{parent.Name}", $"{nameof(ProjectBill).Replace(nameof(parent), "")} | {responseDto.Company.Title} | {responseDto.Designation} | {responseDto.ManValue}mV", "Deleted");
        //        }
        //    }
        //}

        return NoContent();
    }


    [HttpGet("uid/{id:guid}")]
    [AllowAnonymous]
    public async Task<ActionResult<ProjectBillDto>> GetByGUID(Guid id)
    {
        var responseDto = mapper.Map<ProjectBillDto>(await service.GetById(id));
        if (responseDto == null)
        {
            return NotFound();
        }

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(ProjectBill))
            .ToListAsync();

        responseDto.Type = typeMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        var statusMasters = await db.StatusMasters
                        .AsNoTracking()
                        .Where(x => x.Entity == nameof(ProjectBill))
                        .ToListAsync();

        responseDto.Status = statusMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        return Ok(responseDto);
    }

    [HttpGet("SearchTagOptions")]
    public async Task<IActionResult> GetSearchTagOptions()
    {
        var results = await service.GetSearchTagOptions();
        return Ok(results);
    }

    [HttpGet("FieldOptions")]
    public async Task<IActionResult> GetFieldOptions(string field)
    {
        return Ok(await service.GetFieldOptions(field));
    }


    [HttpGet("RecievedFees/{id}")]
    public async Task<IActionResult> GetRecievedFees(int id, int BillID = 0)
    {

        var query = await service.GetRecievedFees(id, BillID);
        return Ok(query);

    }

    [HttpPost("Payment")]
    public async Task<ActionResult<ProjectBillPaymentDto>> PostPayment([FromBody] ProjectBillPaymentDto dto)
    {
        var model = await service.GetPaymentById(await service.CreatePayment(mapper.Map<ProjectBillPayment>(dto)));
        if (model != null)
        {
            return Ok(mapper.Map<ProjectBillPaymentDto>(model));
        }
        else
        {
            return BadRequest("NotModified");
        }
    }

    [HttpPut("Payment/{id}")]
    public async Task<ActionResult<ProjectBillPaymentDto>> PatchPayment(int id, [FromBody] ProjectBillPaymentDto dto)
    {
        if (id != dto.ID) return BadRequest("Id of entity for update not matching!");

        if (await service.UpdatePayment(mapper.Map<ProjectBillPayment>(dto)))
        {
            var model = await service.GetPaymentById(id);
            if (model != null)
            {
                return Ok(mapper.Map<ProjectBillPaymentDto>(model));
            }
        }

        return BadRequest("NotModified");
    }

    [HttpDelete("Payment/{id}")]
    public async Task<IActionResult> DeletePayment(int id)
    {
        if (await service.DeletePayment(id))
        {
            return Ok();
        }
        else
        {
            return BadRequest("NotModified");
        }
    }

    [AllowAnonymous]
    [HttpGet("EXCEL")]
    public async Task<IActionResult> Excel(string? filters = null, string? search = null, string? sort = null)
    {
        var report = await service.GetAnalysisExcel(filters != null ? DataTools.GetObjectFromJsonString<APIFilter>(filters).Filters : null, search, sort);

        if (report != null)
        {
            var filename = $"BillAnalysis-{DateTimeOffset.Now.ToString("dd-MMM-yyyy")}.xlsx";
            return File(
                report,
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                filename);
        }
        else
        {
            return BadRequest("Report cannot be generated");
        }
    }


    [HttpGet("Analysis")]
    public async Task<IActionResult> GetAnalysis(string? filters = null, string? search = null, string? sort = null)
    {
        var results = await service.GetAnalysisData(filters != null ? DataTools.GetObjectFromJsonString<APIFilter>(filters).Filters : null, search, sort);
        return Ok(results);
    }


    [HttpGet("LastBill/{id}")]
    public async Task<ActionResult<ProjectBillDto>> GetLastBill(int id)
    {
        var query = await service.GetLastBill(id);
        return Ok(mapper.Map<ProjectBillDto>(query));
    }

    [HttpGet("Pending/{id}")]
    public async Task<IActionResult> GetPendingBills(int id)
    {
        var query = await service.GetPendingBills(id);
        return Ok(query);
    }

    [AllowAnonymous]
    [HttpGet("Report/{id:guid}")]
    public async Task<IActionResult> GetReport(Guid id)
    {
        var reportDef = await service.GetReport(id);
        return File(
            reportDef.FileContent,
            reportDef.FileContentType,
            reportDef.Filename + reportDef.FileExtension);
    }
}