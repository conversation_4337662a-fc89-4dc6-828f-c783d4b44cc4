﻿using MyCockpitView.Utility.RDLCClient;
using MyCockpitView.WebApi.InspectionModule.Entities;
using MyCockpitView.WebApi.Services;

namespace MyCockpitView.WebApi.InspectionModule.Services;

public interface IInspectionService : IBaseEntityService<Inspection>
{
    Task<int> Create(Inspection Entity, IEnumerable<InspectionRecipient>? Recipients = null);
    Task<IEnumerable<int>> GetAssigneeContactIDs(int entityID, string stageCode, string propertyName);
    Task<string> GetInspectionItemHistoryString(int ItemID);
    Task<ReportDefinition> GetItemReport(string reportSize, Guid uid, string? sort = null);
    Task<Inspection> GetLastPending(Inspection Inspection);
    Task<ReportDefinition> GetMinutesReport(string reportSize, Guid uid, string? sort = null);
    Task<dynamic> GetTaskByStage(string Entity, int EntityID, string StageCode, string StageTitle, decimal StageDuration, DateTime? FollowUpDate = null);
    Task<bool> IsInspectionEditable(int ID, int ContactID);
    Task SendMinutes(int ID);
    Task TaskAction(int EntityID, string StageCode, string? taskComment = null);
}