﻿using AutoMapper;
using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.ProjectModule.Entities;
using System.ComponentModel.DataAnnotations;

namespace MyCockpitView.WebApi.ProjectModule.Dtos;

public class ProjectOutwardAttachmentDto : BaseBlobEntityDto
{

    public int ProjectOutwardID { get; set; }
}

public class ProjectOutwardAttachmentDtoMapperProfile : Profile
{
    public ProjectOutwardAttachmentDtoMapperProfile()
    {
        CreateMap<ProjectOutwardAttachment, ProjectOutwardAttachmentDto>()
                   .ReverseMap();

    }
}