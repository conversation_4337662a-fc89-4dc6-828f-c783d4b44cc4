﻿

using Microsoft.EntityFrameworkCore;
using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.Exceptions;
using MyCockpitView.WebApi.RequestTicketModule.Entities;
using MyCockpitView.WebApi.Services;

namespace MyCockpitView.WebApi.RequestTicketModule.Services;

public class RequestTicketAssigneeService : BaseEntityService<RequestTicketAssignee>, IRequestTicketAssigneeService
{
    public RequestTicketAssigneeService(EntitiesContext db) : base(db) { }

    public IQueryable<RequestTicketAssignee> Get(IEnumerable<QueryFilter>? Filters = null, string? Search = null, string? Sort = null)
    {

        IQueryable<RequestTicketAssignee> _query = base.Get(Filters);

        //Apply filters
        if (Filters != null)
        {

            if (Filters.Where(x => x.Key.Equals("contactID", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<RequestTicketAssignee>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("contactID", StringComparison.OrdinalIgnoreCase)))
                {
                    var isNumeric = Convert.ToInt32(_item.Value);

                    predicate = predicate.Or(x => x.ContactID == isNumeric);
                }
                _query = _query.Where(predicate);
            }
            if (Filters.Where(x => x.Key.Equals("RequestTicketID", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<RequestTicketAssignee>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("RequestTicketID", StringComparison.OrdinalIgnoreCase)))
                {
                    var isNumeric = Convert.ToInt32(_item.Value);

                    predicate = predicate.Or(x => x.RequestTicketID == isNumeric);
                }
                _query = _query.Where(predicate);
            }

        }

        if (Search != null && Search != "")
        {

            _query = _query.Where(x => x.FullName.ToLower().Contains(Search.ToLower())
                              );
        }

        return _query;

    }

    public async Task<int> Create(RequestTicketAssignee Entity)
    {

        var _exist = await Get()
            .Where(x => x.RequestTicketID == Entity.RequestTicketID
        && x.ContactID == Entity.ContactID
        && x.Email==Entity.Email
        && x.TypeFlag == Entity.TypeFlag).AnyAsync();

        if (_exist) throw new EntityServiceException("Assignee already exists");

        return await base.Create(Entity);

    }



}