﻿using AutoMapper;
using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.ProjectModule.Entities;
using System.ComponentModel.DataAnnotations;

namespace MyCockpitView.WebApi.ProjectModule.Dtos;

public class ProjectScopeDto : BaseEntityDto
{
    public int ProjectID { get; set; }

    public string? Title { get; set; }

    //
    //
    //public string? Code { get; set; }

    public decimal SharePercentage { get; set; }
    public decimal Amount { get; set; }
    public decimal VHr { get; set; }
    public decimal VHrCost { get; set; }
    public decimal XVHrCost { get; set; }
    public decimal CompoundCost { get; set; }
    public decimal VHrRate { get; set; }
    public decimal CompoundTenure { get; set; }
    public decimal CompoundRate { get; set; }
    public virtual ICollection<ProjectScopeServiceDto> Services { get; set; }=new List<ProjectScopeServiceDto>();
}

public class ProjectScopeDtoMapperProfile : Profile
{
    public ProjectScopeDtoMapperProfile()
    {
        CreateMap<ProjectScope, ProjectScopeDto>()
    .ReverseMap()
    .ForMember(dest => dest.Project, opt => opt.Ignore())
    .ForMember(dest => dest.Services, opt => opt.Ignore());

    }
}