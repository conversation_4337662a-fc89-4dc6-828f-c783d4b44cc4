﻿using AutoMapper;
using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.WFStageModule.Entities;

namespace MyCockpitView.WebApi.WFStageModule.Dtos;

public class WFStageActionDto : BaseEntity
{


    public int WFStageID { get; set; }


    public int TaskOutcomeFlag { get; set; }

    public int TaskStatusFlag { get; set; }


    public string? ActionByCondition { get; set; }
    public int ActionByCount { get; set; }



    public string? NextStageCode { get; set; }


    public string? ShowOnStatusFlag { get; set; }


    public string? ActivityText { get; set; }


    public string? ButtonClass { get; set; } = "primary";


    public string? ButtonText { get; set; }


    public string? ButtonTooltip { get; set; }

    public bool TriggerEntityFormSubmit { get; set; }

}

public class WFStageActionDtoMapperProfile : Profile
{
    public WFStageActionDtoMapperProfile()
    {

        CreateMap<WFStageAction, WFStageActionDto>()
                    .ReverseMap();
    }
}
