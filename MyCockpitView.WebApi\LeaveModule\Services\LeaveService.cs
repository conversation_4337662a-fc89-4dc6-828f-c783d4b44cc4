﻿using MyCockpitView.WebApi.Exceptions;
using MyCockpitView.WebApi.Services;
using MyCockpitView.WebApi.LeaveModule.Entities;
using MyCockpitView.WebApi.WFTaskModule.Entities;
using MyCockpitView.WebApi.WFTaskModule.Services;
using MyCockpitView.CoreModule;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.Utility.Common;
using MyCockpitView.WebApi.ActivityModule;
using Microsoft.AspNetCore.Identity;

namespace MyCockpitView.WebApi.LeaveModule.Services;

public class LeaveService : BaseEntityService<Leave>, ILeaveService
{
    public LeaveService(EntitiesContext db) : base(db)
    { }

    public IQueryable<Leave> Get(IEnumerable<QueryFilter>? Filters = null, string? Search = null, string? Sort = null)
    {

        IQueryable<Leave> _query = base.Get(Filters);


        //Apply filters
        if (Filters != null)
        {


            if (Filters.Where(x => x.Key.Equals("contactID", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<Leave>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("contactID", StringComparison.OrdinalIgnoreCase)))
                {
                    var isNumeric = Convert.ToInt32(_item.Value);

                    predicate = predicate.Or(x => x.ContactID == isNumeric);
                }
                _query = _query.Where(predicate);
            }

            if (Filters.Where(x => x.Key.Equals("CreatedDate", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var _item = Filters.First(x => x.Key.Equals("CreatedDate", StringComparison.OrdinalIgnoreCase));
                DateTime result = Convert.ToDateTime(_item.Value);
                _query = _query.Where(x => x.Created.Date == result.Date);

            }

            if (Filters.Where(x => x.Key.Equals("rangestart", StringComparison.OrdinalIgnoreCase)).Any())
            {

                var _item = Filters.First(x => x.Key.Equals("rangestart", StringComparison.OrdinalIgnoreCase));

                DateTime result = Convert.ToDateTime(_item.Value);
                _query = _query.Where(x => x.Start >= result || x.End >= result);

            }

            if (Filters.Where(x => x.Key.Equals("rangeend", StringComparison.OrdinalIgnoreCase)).Any())
            {

                var _item = Filters.First(x => x.Key.Equals("rangeend", StringComparison.OrdinalIgnoreCase));

                DateTime result = Convert.ToDateTime(_item.Value);
                var end = result.AddDays(1);
                _query = _query.Where(x => x.Start < end || x.End < end);

            }

            if (Filters.Where(x => x.Key.Equals("timelineRangeStart", StringComparison.OrdinalIgnoreCase)).Any()
                && Filters.Where(x => x.Key.Equals("timelineRangeEnd", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var start = Convert.ToDateTime(Filters.First(x => x.Key.Equals("timelineRangeStart", StringComparison.OrdinalIgnoreCase)).Value);

                var end = Convert.ToDateTime(Filters.First(x => x.Key.Equals("timelineRangeEnd", StringComparison.OrdinalIgnoreCase)).Value);

                _query = _query.Where(x =>
                (x.Start >= start && x.Start < end) //start is within range
                || (x.End > start && x.End <= end) //end is within range
                || (x.Start <= start && x.End >= end) // range contains the entire period
                );
            }
        }

        if (Search != null && Search != String.Empty)
        {
            _query = _query.Include(x => x.Contact)
          .Where(x => (x.Contact.FirstName + " " + x.Contact.LastName).ToLower().Contains(Search.ToLower()));

        }

        return _query
          .OrderByDescending(x => x.Start);

    }

    public async Task<Leave?> GetById(int Id)
    {

        return await db.Leaves.AsNoTracking()
             .Include(x => x.Contact)
             .SingleOrDefaultAsync(i => i.ID == Id);

    }



    public async Task<LeaveSummary> GetMonthSummary(int contactID, int year, int month)
    {

        var _employeeAppointment = await db.ContactAppointments.AsNoTracking()
          
          //.Where(x=>x.StatusFlag==McvConstant.APPOINTMENT_STATUSFLAG_APPOINTED)
          .Where(x => x.ContactID == contactID)
          .OrderBy(x => x.JoiningDate)
          .FirstOrDefaultAsync();

        var sharedService = new SharedService(db); ;
        //var _leavesPermissible = Convert.ToDecimal((await sharedService.GetPresetValue(McvConstant.LEAVE_PERMISSIBLE_TOTAL)));
        //var _emergencyPermissible = Convert.ToDecimal((await sharedService.GetPresetValue(McvConstant.LEAVE_PERMISSIBLE_EMERGENCY)));


        var _joiningDate = ClockTools.GetIST(_employeeAppointment.JoiningDate);
        var _joiningMonth = new DateTime(_joiningDate.Year, _joiningDate.Month, 1);

        var _start = new DateTime(year, _joiningDate.Month, 1);

        var _end = _start.AddYears(1);
        var _currentMonth = new DateTime(year, month, 1);

        if (_currentMonth < _start) //cycle start from previous year
        {
            _start = _start.AddYears(-1);
            _end = _start.AddYears(1);
        }

        if (_start.Year < 2023)
        { //old cycle
            _start = new DateTime(2023, 1, 1);
            _end = (new DateTime(2023, _joiningDate.Month, 1));//.AddDays(-1)
            //_leavesPermissible = (_end.Month - 1) * (_leavesPermissible / 12);
            //_emergencyPermissible = (_end.Month - 1) * (_emergencyPermissible / 12);
        }

        var _query = await db.Leaves.AsNoTracking()
                            
                          .Where(x => x.Start.Year == _currentMonth.Year
                                        && x.Start.Month == _currentMonth.Month)
                           .Where(x => x.ContactID == contactID && x.StatusFlag == 1)
                           .ToListAsync();



        return new LeaveSummary
        {
            Month = _currentMonth.Month,
            Year = _currentMonth.Year,
            Label = _currentMonth.ToString("MMM"), //(ClockTools.GetMonthName(_OfficeStart.Month)).Substring(0, 3),
            LeaveCycle = $"{_start.ToString("MMM")} {_start.Year} - {_end.AddDays(-1).ToString("MMM")} {_end.AddDays(-1).Year}",
            //Allowed = _leavesPermissible,
            //AllowedEmergency = _emergencyPermissible,
            ApprovedLeave = _query
                    .Where(x => x.TypeFlag == McvConstant.LEAVE_TYPEFLAG_APPROVED)
                    .Sum(x => x.Total),
            EmergencyLeave = _query
                    .Where(x => x.TypeFlag == McvConstant.LEAVE_TYPEFLAG_EMERGENCY)
                    .Sum(x => x.Total),
            Late = _query
                    .Where(x => x.TypeFlag == McvConstant.LEAVE_TYPEFLAG_LATE)
                    .Sum(x => x.Total),
            Penalty = _query
                    .Where(x => x.TypeFlag == McvConstant.LEAVE_TYPEFLAG_PENELTY)
                    .Sum(x => x.Total),
            ApprovedHalfDay = _query
                    .Where(x => x.TypeFlag == McvConstant.LEAVE_TYPEFLAG_APPROVED_HALFDAY)
                    .Sum(x => x.Total),
            ApprovedBreak = _query
                    .Where(x => x.TypeFlag == McvConstant.LEAVE_TYPEFLAG_APPROVED_BREAK)
                    .Sum(x => x.Total),
            ApprovedWFH = _query
                    .Where(x => x.TypeFlag == McvConstant.LEAVE_TYPEFLAG_APPROVED_WFH)
                    .Sum(x => x.Total),
            EmergencyBreak = _query
                    .Where(x => x.TypeFlag == McvConstant.LEAVE_TYPEFLAG_EMERGENCY_BREAK)
                    .Sum(x => x.Total),
            EmergencyHalfDay = _query
                    .Where(x => x.TypeFlag == McvConstant.LEAVE_TYPEFLAG_EMERGENCY_HALFDAY)
                    .Sum(x => x.Total),
            EmergencyWFH = _query
                    .Where(x => x.TypeFlag == McvConstant.LEAVE_TYPEFLAG_EMERGENCY_WFH)
                    .Sum(x => x.Total),
            Total = _query
                    .Where(x => x.TypeFlag == McvConstant.LEAVE_TYPEFLAG_APPROVED
                    || x.TypeFlag == McvConstant.LEAVE_TYPEFLAG_EMERGENCY
                    || x.TypeFlag == McvConstant.LEAVE_TYPEFLAG_EMERGENCY_HALFDAY
                    || x.TypeFlag == McvConstant.LEAVE_TYPEFLAG_APPROVED_HALFDAY
                    || x.TypeFlag == McvConstant.LEAVE_TYPEFLAG_LATE
                    || x.TypeFlag == McvConstant.LEAVE_TYPEFLAG_PENELTY)
                    .Sum(x => x.Total),
        };

    }

    public async Task<IEnumerable<LeaveSummary>> GetPerMonthSummary(int contactID, int index = 0)
    {

        var _result = new List<LeaveSummary>();

        var _employeeAppointment = await db.ContactAppointments.AsNoTracking()
             
             //.Where(x => x.StatusFlag == McvConstant.APPOINTMENT_STATUSFLAG_APPOINTED)
             .Where(x => x.ContactID == contactID)
             .OrderBy(x => x.JoiningDate)
             .FirstOrDefaultAsync();
        var sharedService = new SharedService(db);
        var _leavesPermissible = Convert.ToDecimal((await sharedService.GetPresetValue(McvConstant.LEAVE_PERMISSIBLE_TOTAL)));
        var _emergencyPermissible = Convert.ToDecimal((await sharedService.GetPresetValue(McvConstant.LEAVE_PERMISSIBLE_EMERGENCY)));
        var _joiningDate = ClockTools.GetIST(_employeeAppointment.JoiningDate);
        var _joiningMonth = new DateTime(_joiningDate.Year, _joiningDate.Month, 1);

        var _start = new DateTime(DateTime.UtcNow.Year, _joiningDate.Month, 1);
        _start = _start.AddYears(index);
        var _end = _start.AddYears(1);
        var _currentMonth = new DateTime(DateTime.UtcNow.Year, DateTime.UtcNow.Month, 1);


        if (_currentMonth < _start) //cycle start from previous year
        {
            _start = _start.AddYears(-1);
            _end = _start.AddYears(1);

        }

        if (_start.Year < 2023)
        { //old cycle
            _start = new DateTime(2023, 1, 1);
            _end = (new DateTime(2023, _joiningDate.Month, 1));//.AddDays(-1);

            _leavesPermissible = (_end.Month - 1) * (_leavesPermissible / 12);
            _emergencyPermissible = (_end.Month - 1) * (_emergencyPermissible / 12);
        }

        var _month = _start;
        while (_month < _end)
        {
            var _summary = await GetMonthSummary(contactID, _month.Year, _month.Month);
            _summary.Allowed = _leavesPermissible;
            _summary.AllowedEmergency = _emergencyPermissible;
            _summary.LeaveCycle = $"{_start.ToString("MMM")} {_start.Year} - {_end.AddDays(-1).ToString("MMM")} {_end.AddDays(-1).Year}";
            _result.Add(_summary);

            _month = _month.AddMonths(1);
        }

        return _result;

    }

    public async Task<LeaveSummary> GetTotalSummary(int contactID, int index = 0)
    {

        var _employeeAppointment = await db.ContactAppointments.AsNoTracking()
          
          // .Where(x => x.StatusFlag == McvConstant.APPOINTMENT_STATUSFLAG_APPOINTED)
          .Where(x => x.ContactID == contactID)
          .OrderBy(x => x.JoiningDate)
          .FirstOrDefaultAsync();
        var sharedService = new SharedService(db); ;
        var _leavesPermissible = Convert.ToDecimal((await sharedService.GetPresetValue(McvConstant.LEAVE_PERMISSIBLE_TOTAL)));
        var _emergencyPermissible = Convert.ToDecimal((await sharedService.GetPresetValue(McvConstant.LEAVE_PERMISSIBLE_EMERGENCY)));
        var _joiningDate = ClockTools.GetIST(_employeeAppointment.JoiningDate);
        var _joiningMonth = new DateTime(_joiningDate.Year, _joiningDate.Month, 1);

        var _start = new DateTime(DateTime.UtcNow.Year, _joiningDate.Month, 1);
        _start = _start.AddYears(index);
        var _end = _start.AddYears(1);
        var _currentMonth = new DateTime(DateTime.UtcNow.Year, DateTime.UtcNow.Month, 1);


        if (_currentMonth < _start) //cycle start from previous year
        {
            _start = _start.AddYears(-1);
            _end = _start.AddYears(1);

        }

        if (_start.Year < 2023)
        { //old cycle
            _start = new DateTime(2023, 1, 1);
            _end = (new DateTime(2023, _joiningDate.Month, 1));//.AddDays(-1);

            _leavesPermissible = (_end.Month - 1) * (_leavesPermissible / 12);
            _emergencyPermissible = (_end.Month - 1) * (_emergencyPermissible / 12);
        }

        var _query = await db.Leaves.AsNoTracking()
                            .Where(x => x.Start >= _start && x.End < _end)
                            .Where(x => x.ContactID == contactID && x.StatusFlag == 1)
                            .ToListAsync();


        return new LeaveSummary
        {
            Year = _start.Year,
            Label = _start.Year.ToString(),
            LeaveCycle = $"{_start.ToString("MMM")} {_start.Year} - {_end.AddDays(-1).ToString("MMM")} {_end.AddDays(-1).Year}",
            ApprovedLeave = _query
                .Where(x => x.TypeFlag == McvConstant.LEAVE_TYPEFLAG_APPROVED)
                .Sum(x => x.Total),
            EmergencyLeave = _query
                .Where(x => x.TypeFlag == McvConstant.LEAVE_TYPEFLAG_EMERGENCY)
                .Sum(x => x.Total),
            Late = _query
                .Where(x => x.TypeFlag == McvConstant.LEAVE_TYPEFLAG_LATE)
                .Sum(x => x.Total),
            Penalty = _query
                .Where(x => x.TypeFlag == McvConstant.LEAVE_TYPEFLAG_PENELTY)
                .Sum(x => x.Total),
            ApprovedHalfDay = _query
                .Where(x => x.TypeFlag == McvConstant.LEAVE_TYPEFLAG_APPROVED_HALFDAY)
                .Sum(x => x.Total),
            ApprovedBreak = _query
                .Where(x => x.TypeFlag == McvConstant.LEAVE_TYPEFLAG_APPROVED_BREAK)
                .Sum(x => x.Total),
            ApprovedWFH = _query
                .Where(x => x.TypeFlag == McvConstant.LEAVE_TYPEFLAG_APPROVED_WFH)
                .Sum(x => x.Total),
            EmergencyBreak = _query
                .Where(x => x.TypeFlag == McvConstant.LEAVE_TYPEFLAG_EMERGENCY_BREAK)
                .Sum(x => x.Total),
            EmergencyHalfDay = _query
                .Where(x => x.TypeFlag == McvConstant.LEAVE_TYPEFLAG_EMERGENCY_HALFDAY)
                .Sum(x => x.Total),
            EmergencyWFH = _query
                .Where(x => x.TypeFlag == McvConstant.LEAVE_TYPEFLAG_EMERGENCY_WFH)
                .Sum(x => x.Total),
            Total = _query
                .Where(x => x.TypeFlag == McvConstant.LEAVE_TYPEFLAG_APPROVED || x.TypeFlag == McvConstant.LEAVE_TYPEFLAG_EMERGENCY || x.TypeFlag == McvConstant.LEAVE_TYPEFLAG_EMERGENCY_HALFDAY || x.TypeFlag == McvConstant.LEAVE_TYPEFLAG_APPROVED_HALFDAY || x.TypeFlag == McvConstant.LEAVE_TYPEFLAG_LATE || x.TypeFlag == McvConstant.LEAVE_TYPEFLAG_PENELTY)
                .Sum(x => x.Total),
            Allowed = _leavesPermissible,
            AllowedEmergency = _emergencyPermissible,

        };

    }



    public async Task<IEnumerable<Leave>> GetSplitLeave(Leave Entity)
    {

        var sharedService = new SharedService(db); ;

        var _startTimespanIST = TimeSpan.Parse(await sharedService.GetPresetValue(McvConstant.OFFICE_OPEN_TIME));
        var _endTimeSpanIST = TimeSpan.Parse(await sharedService.GetPresetValue(McvConstant.OFFICE_CLOSE_TIME));

        var _leaveStartIST = ClockTools.GetIST(Entity.Start).Date.AddMilliseconds(_startTimespanIST.TotalMilliseconds);
        var _leaveEndIST = ClockTools.GetIST(Entity.End).Date.AddMilliseconds(_endTimeSpanIST.TotalMilliseconds);

        var results = new List<Leave>();

        if (Entity.TypeFlag == McvConstant.LEAVE_TYPEFLAG_APPROVED)// normal
        {

            //split leave
            if (Entity.Start.Month != Entity.End.Month)
            {
                var _monthEnd = (new DateTime(_leaveStartIST.Year, _leaveStartIST.Month, 1)).AddMonths(1).AddDays(-1);

                var _partA = new Leave();
                _partA.ContactID = Entity.ContactID;
                _partA.TypeFlag = Entity.TypeFlag;
                _partA.Start = ClockTools.GetUTC(_leaveStartIST);
                _partA.Reason = Entity.Reason;
                _partA.End = ClockTools.GetUTC(ClockTools.GetIST(_monthEnd).Date.AddMilliseconds(_endTimeSpanIST.TotalMilliseconds));


                _partA.Total = await GetTotalLeaveDurationIST(ClockTools.GetIST(_partA.Start), ClockTools.GetIST(_partA.End));
                results.Add(_partA);

                var _partB = new Leave();
                _partB.ContactID = Entity.ContactID;
                _partB.TypeFlag = Entity.TypeFlag;
                _partB.Start = ClockTools.GetUTC(ClockTools.GetIST(_monthEnd.AddDays(1)).Date.AddMilliseconds(_startTimespanIST.TotalMilliseconds));
                _partB.End = ClockTools.GetUTC(_leaveEndIST);
                _partB.Reason = Entity.Reason;

                _partB.Total = await GetTotalLeaveDurationIST(ClockTools.GetIST(_partB.Start), ClockTools.GetIST(_partB.End));
                results.Add(_partB);
            }
            else
            {
                Entity.Start = ClockTools.GetUTC(_leaveStartIST);
                Entity.End = ClockTools.GetUTC(_leaveEndIST);
                Entity.TypeFlag = Entity.TypeFlag;

                Entity.Total = await GetTotalLeaveDurationIST(ClockTools.GetIST(Entity.Start), ClockTools.GetIST(Entity.End));
                results.Add(Entity);
            }
        }
        else if (Entity.TypeFlag == McvConstant.LEAVE_TYPEFLAG_EMERGENCY)//EmergencyLeave
        {


            var _emergency = new Leave();
            _emergency.ContactID = Entity.ContactID;
            _emergency.TypeFlag = McvConstant.LEAVE_TYPEFLAG_EMERGENCY;
            _emergency.Start = ClockTools.GetUTC(_leaveStartIST);
            _emergency.End = ClockTools.GetUTC(ClockTools.GetIST(Entity.Start).Date.AddMilliseconds(_endTimeSpanIST.TotalMilliseconds)); // only one day emergency
            _emergency.Reason = Entity.Reason;
            _emergency.StatusFlag = McvConstant.LEAVE_STATUSFLAG_APPROVED;
            _emergency.Total = 1;
            results.Add(_emergency);

            if (ClockTools.GetUTC(_leaveEndIST) > _emergency.End)
            {
                Entity.Start = Entity.Start.AddDays(1);
                Entity.TypeFlag = McvConstant.LEAVE_TYPEFLAG_APPROVED;
                Entity.StatusFlag = McvConstant.LEAVE_STATUSFLAG_APPROVED;

                if (Entity.Start.Month != Entity.End.Month)
                {
                    var _monthEnd = (new DateTime(_leaveStartIST.Year, _leaveStartIST.Month, 1)).AddMonths(1).AddDays(-1);


                    var _partA = new Leave();
                    _partA.ContactID = Entity.ContactID;
                    _partA.TypeFlag = Entity.TypeFlag;
                    _partA.Start = Entity.Start;
                    _partA.Reason = Entity.Reason;
                    _partA.End = ClockTools.GetUTC(ClockTools.GetIST(_monthEnd).Date.AddMilliseconds(_endTimeSpanIST.TotalMilliseconds));
                    ;
                    _partA.StatusFlag = Entity.StatusFlag;
                    _partA.Total = await GetTotalLeaveDurationIST(ClockTools.GetIST(_partA.Start), ClockTools.GetIST(_partA.End));
                    results.Add(_partA);

                    var _partB = new Leave();
                    _partB.ContactID = Entity.ContactID;
                    _partB.TypeFlag = Entity.TypeFlag;
                    _partB.Start = ClockTools.GetUTC(ClockTools.GetIST(_monthEnd.AddDays(1)).Date.AddMilliseconds(_startTimespanIST.TotalMilliseconds));
                    _partB.End = ClockTools.GetUTC(_leaveEndIST);
                    _partB.Reason = Entity.Reason;
                    _partB.StatusFlag = Entity.StatusFlag;
                    _partB.Total = await GetTotalLeaveDurationIST(ClockTools.GetIST(_partB.Start), ClockTools.GetIST(_partB.End));
                    results.Add(_partB);
                }
                else
                {
                    Entity.StatusFlag = McvConstant.LEAVE_STATUSFLAG_APPROVED;
                    Entity.Start = ClockTools.GetUTC(_leaveStartIST);
                    Entity.End = ClockTools.GetUTC(_leaveEndIST);
                    Entity.TypeFlag = Entity.TypeFlag;

                    Entity.Total = await GetTotalLeaveDurationIST(ClockTools.GetIST(Entity.Start), ClockTools.GetIST(Entity.End));
                    results.Add(Entity);

                }
            }



        }
        else if (Entity.TypeFlag == McvConstant.LEAVE_TYPEFLAG_LATE || Entity.TypeFlag == McvConstant.LEAVE_TYPEFLAG_PENELTY)
        {


            Entity.Start = ClockTools.GetUTC(_leaveStartIST);
            Entity.End = ClockTools.GetUTC(_leaveEndIST);
            Entity.StatusFlag = McvConstant.LEAVE_STATUSFLAG_APPROVED;

            Entity.Total = await GetTotalLeaveDurationIST(ClockTools.GetIST(Entity.Start), ClockTools.GetIST(Entity.End));
            results.Add(Entity);
        }
        else if (Entity.TypeFlag == McvConstant.LEAVE_TYPEFLAG_APPROVED_BREAK || Entity.TypeFlag == McvConstant.LEAVE_TYPEFLAG_EMERGENCY_BREAK) //break
        {
            Entity.Total = 0m;//REQUIRED FOR COUNT
            if (Entity.TypeFlag == McvConstant.LEAVE_TYPEFLAG_EMERGENCY_BREAK)
            {
                Entity.StatusFlag = McvConstant.LEAVE_STATUSFLAG_APPROVED;

            }
            results.Add(Entity);
        }
        else if (Entity.TypeFlag == McvConstant.LEAVE_TYPEFLAG_APPROVED_HALFDAY || Entity.TypeFlag == McvConstant.LEAVE_TYPEFLAG_EMERGENCY_HALFDAY) //ApprovedHalfDay
        {
            Entity.Total = 0.5m;
            if (Entity.TypeFlag == McvConstant.LEAVE_TYPEFLAG_EMERGENCY_HALFDAY)
            {
                Entity.StatusFlag = McvConstant.LEAVE_STATUSFLAG_APPROVED;
            }
            results.Add(Entity);
        }
        else if (Entity.TypeFlag == McvConstant.LEAVE_TYPEFLAG_APPROVED_WFH ||
            Entity.TypeFlag == McvConstant.LEAVE_TYPEFLAG_EMERGENCY_WFH) //ApprovedWFH
        {


            Entity.Start = ClockTools.GetUTC(_leaveStartIST);
            Entity.End = ClockTools.GetUTC(_leaveEndIST);
            Entity.Total = (Entity.End - Entity.Start).Days > 0 ? (Entity.End - Entity.Start).Days : 1;
            if (Entity.TypeFlag == McvConstant.LEAVE_TYPEFLAG_EMERGENCY_WFH)
            {

                Entity.StatusFlag = McvConstant.LEAVE_STATUSFLAG_APPROVED;
            }
            results.Add(Entity);
        }


        return results;

    }

    public async Task<int> Create(Leave Entity)
    {

        var _exist = await db.Leaves.AsNoTracking()
            .AnyAsync(x => x.ContactID == Entity.ContactID && x.Start == Entity.Start
            && x.End == Entity.End && x.StatusFlag == McvConstant.LEAVE_STATUSFLAG_APPROVED);
        if (_exist) throw new EntityServiceException("Application already exists!");



        if (Entity.TypeFlag == McvConstant.LEAVE_TYPEFLAG_APPROVED_HALFDAY || Entity.TypeFlag == McvConstant.LEAVE_TYPEFLAG_EMERGENCY_HALFDAY) //ApprovedHalfDay
        {
            TimeSpan duration = Entity.End - Entity.Start;

            if (duration.TotalHours > 4)
            {
                throw new Exception("Half-Day can't exceed 4 hours");
            }
            Entity.Total = 0.5m; // Add half day for half-day leave

        }
        else if (Entity.TypeFlag == McvConstant.LEAVE_TYPEFLAG_APPROVED_BREAK || Entity.TypeFlag == McvConstant.LEAVE_TYPEFLAG_EMERGENCY_BREAK) //ApprovedHalfDay
        {

            Entity.Total = 0m;//REQUIRED FOR COUNT
        }
        else
        {

            Entity.Total = await GetTotalLeaveDurationIST(ClockTools.GetIST(Entity.Start), ClockTools.GetIST(Entity.End));
        }

        db.Leaves.Add(Entity);
        await db.SaveChangesAsync();

        if (Entity.StatusFlag == McvConstant.LEAVE_STATUSFLAG_PENDING)
        {
            await AssignTasks(Entity);
        }

        return Entity.ID;


    }

    public async Task ValidateOverlapp(Leave Entity)
    {

        var _query = await db.Leaves.AsNoTracking()
            .Where(x => x.StatusFlag != McvConstant.LEAVE_STATUSFLAG_REJECTED)
            .Where(x => x.ID != Entity.ID)
                          .Where(x => x.ContactID == Entity.ContactID)
                 .Where(x => (x.Start == Entity.Start) //equal start
                            || (x.End == Entity.End) //equal end
                            || (x.Start < Entity.Start && x.End > Entity.End) //inside
                            || (x.Start > Entity.Start && x.End < Entity.End) //outside
                            || (x.Start < Entity.Start && x.End > Entity.Start) //start overlapp
                            || (x.Start > Entity.Start && x.Start < Entity.End) //end overlapp
                 ).FirstOrDefaultAsync();
        if (_query != null)
        {
            var _typeMaster = await db.TypeMasters.AsNoTracking().FirstOrDefaultAsync(x => x.Entity == nameof(Leave)
            && x.Value == _query.TypeFlag);

            throw new EntityServiceException($"Application overlaps another {(_typeMaster != null ? _typeMaster.Title : "leave")} on {ClockTools.GetIST(_query.Start).ToString("dd MMM yyyy HH:mm")}-{ClockTools.GetIST(_query.End).ToString("dd MMM yyyy HH:mm")}");
        }

    }

    public async Task<decimal> GetTotalLeaveDurationIST(DateTime StartIST, DateTime EndIST)
    {

        var sharedService = new SharedService(db); ;
        bool isIncludeSundays = false;
        bool _presetOddSaturdayOff = false; Convert.ToBoolean(await sharedService.GetPresetValue(McvConstant.ODD_SATURDAY_OFF));
        bool _presetEvenSaturdayOff = false; Convert.ToBoolean(await sharedService.GetPresetValue(McvConstant.EVEN_SATURDAY_OFF));
        var _holidayDates = await db.HolidayMasters.AsNoTracking()
                .Select(x => x.HolidayDate)
                .ToListAsync();

        var days = 0.0m;

        var holidays = await db.HolidayMasters
                            .AsNoTracking()
                            .Where(x => x.TypeFlag == McvConstant.HOLIDAY_MASTER_TYPEFLAG_HOLIDAY)
                            .Select(x => x.HolidayDate)
                            .ToListAsync();

        var workingDays = await db.HolidayMasters
                            .AsNoTracking()
                            .Where(x => x.TypeFlag == McvConstant.HOLIDAY_MASTER_TYPEFLAG_WORKING)
                                            .Select(x => x.HolidayDate)
                            .ToListAsync();

        var start = StartIST;
        var stop = EndIST;

        while (start <= stop)
        {
            if (start.DayOfWeek == DayOfWeek.Saturday)
            {

                int index = 0;
                if (start.Day <= 7)
                {
                    index = 1;
                }
                else if (start.Day <= 14)
                {
                    index = 2;
                }
                else if (start.Day <= 21)
                {
                    index = 3;
                }
                else if (start.Day <= 28)
                {
                    index = 4;
                }
                else if (start.Day > 28)
                {
                    index = 5;
                }

                if (index % 2 == 0)
                {
                    if (!_presetOddSaturdayOff)
                        ++days;
                    else if (workingDays.Any(x => x.Date == start.Date))
                        ++days;
                }
                else
                {

                    if (!_presetEvenSaturdayOff)
                        ++days;
                    else if (workingDays.Any(x => x.Date == start.Date))
                        ++days;
                }

            }
            else if (start.DayOfWeek == DayOfWeek.Sunday)
            {
                if (isIncludeSundays)
                    ++days;
                else if (workingDays.Any(x => x.Date == start.Date))
                    ++days;
            }
            else
            {
                if (!holidays.Any(x => x.Date == start.Date))
                    ++days;
            }
            start = start.AddDays(1);
        }

        return days;

    }

    private bool IsNonWorkingSaturday(int Index, DateTime Date, int[] Allowed)
    {

        var MonthFirstDay = new DateTime(Date.Year, Date.Month, 1, 0, 0, 0);

        var _result = false;

        int firstDay = 0;

        if (MonthFirstDay.DayOfWeek == DayOfWeek.Sunday)
        {
            firstDay = 0;
        }
        else if (MonthFirstDay.DayOfWeek == DayOfWeek.Monday)
        {
            firstDay = 1;
        }
        else if (MonthFirstDay.DayOfWeek == DayOfWeek.Tuesday)
        {
            firstDay = 2;
        }
        else if (MonthFirstDay.DayOfWeek == DayOfWeek.Wednesday)
        {
            firstDay = 3;
        }
        else if (MonthFirstDay.DayOfWeek == DayOfWeek.Thursday)
        {
            firstDay = 4;
        }
        else if (MonthFirstDay.DayOfWeek == DayOfWeek.Friday)
        {
            firstDay = 5;
        }
        else if (MonthFirstDay.DayOfWeek == DayOfWeek.Saturday)
        {
            firstDay = 6;
        }

        int day = 0;
        if (Index == 1)
        {
            day = 7 - firstDay;
        }
        else if (Index == 2)
        {
            day = 14 - firstDay;

            if (day == Date.Day)
            {
                _result = true;
            }
        }
        else if (Index == 3)
        {
            day = 21 - firstDay;
        }
        else if (Index == 4)
        {
            day = 28 - firstDay;

            if (day == Date.Day)
            {
                _result = true;
            }
        }
        else if (Index == 5)
        {
            day = 35 - firstDay;
        }

        return _result;

    }


    public async Task Update(Leave Entity)
    {
        await ValidateOverlapp(Entity);


        if (Entity.TypeFlag == McvConstant.LEAVE_TYPEFLAG_APPROVED_HALFDAY || Entity.TypeFlag == McvConstant.LEAVE_TYPEFLAG_EMERGENCY_HALFDAY) //ApprovedHalfDay
        {
            TimeSpan duration = Entity.End - Entity.Start;

            if (duration.TotalHours > 4)
            {
                throw new Exception("Half-Day can't exceed 4 hours");
            }
            Entity.Total = 0.5m; // Add half day for half-day leave

        }
        else if (Entity.TypeFlag == McvConstant.LEAVE_TYPEFLAG_APPROVED_BREAK || Entity.TypeFlag == McvConstant.LEAVE_TYPEFLAG_EMERGENCY_BREAK) //ApprovedHalfDay
        {

            Entity.Total = 0m;//REQUIRED FOR COUNT
        }
        else
        {
            Entity.Total = await GetTotalLeaveDurationIST(ClockTools.GetIST(Entity.Start), ClockTools.GetIST(Entity.End));
        }
        await base.Update(Entity);


    }

    public async Task Delete(int Id)
    {

        var Entity = await db.Leaves.AsNoTracking()
             .SingleOrDefaultAsync(i => i.ID == Id);

        if (Entity == null)
        {
            throw new EntityServiceException($"{nameof(Entity)} not found!");
        }

        var _tasks = db.WFTasks.Where(x => x.Entity==nameof(Leave) && x.EntityID == Entity.ID);

        db.WFTasks.RemoveRange(await _tasks.ToListAsync());

        await base.Delete(Id);





    }

    public async Task ValidateApplication(Leave Entity, bool IsSelfApplied)
    {

        var sharedService = new SharedService(db);
        var _startTimespan = TimeSpan.Parse(await sharedService.GetPresetValue(McvConstant.OFFICE_OPEN_TIME));
        var _endTimeSpan = TimeSpan.Parse(await sharedService.GetPresetValue(McvConstant.OFFICE_CLOSE_TIME));
        var _startIST = ClockTools.GetIST(Entity.Start).Date.AddMilliseconds(_startTimespan.TotalMilliseconds);
        var _endIST = ClockTools.GetIST(Entity.End).Date.AddMilliseconds(_endTimeSpan.TotalMilliseconds);
        bool _presetOddSaturdayOff = Convert.ToBoolean(await sharedService.GetPresetValue(McvConstant.ODD_SATURDAY_OFF));
        bool _presetEvenSaturdayOff = Convert.ToBoolean(await sharedService.GetPresetValue(McvConstant.EVEN_SATURDAY_OFF));


        if (Entity.TypeFlag == McvConstant.LEAVE_TYPEFLAG_APPROVED_BREAK) //break
        {

            if (Entity.End < Entity.Start)
                throw new EntityServiceException("Invalid Dates! Start should be less than End!");

            if (IsSelfApplied)
            {
                if (Entity.Start < DateTime.UtcNow)
                    throw new EntityServiceException("Only post date/time can be selected for ApprovedBreak!");

                var _applicationPriorDays = Convert.ToInt32(await sharedService.GetPresetValue(McvConstant.BREAK_APPLICATION_PRIOR_DAYS));
                if ((ClockTools.GetIST(Entity.Start) - ClockTools.GetISTNow()).Days < (_applicationPriorDays - 1))
                    throw new EntityServiceException($"Break can only be applied {_applicationPriorDays} days prior!");
            }

            var _applicationDurationHours = Convert.ToDouble(await sharedService.GetPresetValue(McvConstant.BREAK_APPLICATION_DURATION_HOURS_PER_MONTH)) + 0.1;


            if ((Entity.End - Entity.Start).TotalHours > _applicationDurationHours)
                throw new EntityServiceException($"Break duration cannot exceed beyond {_applicationDurationHours} hours!");

            var _applicationAlowedPerMonth = Convert.ToInt32(await sharedService.GetPresetValue(McvConstant.BREAK_APPLICATION_ALLOWED_PER_MONTH));

            var _currentMonthBreaks = await db.Leaves.AsNoTracking()
                .Where(x => x.ContactID == Entity.ContactID)
                .Where(x => x.TypeFlag == Entity.TypeFlag)
                .Where(x => x.Start.Month == Entity.Start.Month && x.Start.Year == Entity.Start.Year).ToListAsync();
            if (_currentMonthBreaks.Count > _applicationAlowedPerMonth)
            {
                throw new EntityServiceException($"Only {_applicationAlowedPerMonth} {(_applicationAlowedPerMonth > 1 ? "breaks are" : "break is")} allowed in a month. You have already taken last on {ClockTools.GetIST(_currentMonthBreaks.OrderByDescending(x => x.Start).FirstOrDefault().Start).ToString("dd MMM yyyy HH:mm")}-{ClockTools.GetIST(_currentMonthBreaks.OrderByDescending(x => x.Start).FirstOrDefault().End).ToString("HH:mm")}");
            }

            if (Entity.Start < ClockTools.GetUTC(_startIST) || Entity.End > ClockTools.GetUTC(_endIST))
                throw new EntityServiceException("Date/Time selected should be between " + _startTimespan.ToString(@"hh\:mm") + " - " + _endTimeSpan.ToString(@"hh\:mm") + " on same Date!");

            await ValidateOverlapp(Entity);
        }
        else if (Entity.TypeFlag == McvConstant.LEAVE_TYPEFLAG_EMERGENCY_BREAK) //break
        {

            if (Entity.End < Entity.Start)
                throw new EntityServiceException("Invalid Dates! Start should be less than End!");


            var _applicationDurationHours = Convert.ToDouble(await sharedService.GetPresetValue(McvConstant.BREAK_APPLICATION_DURATION_HOURS_PER_MONTH)) + 0.1;

            if ((Entity.End - Entity.Start).TotalHours > _applicationDurationHours)
                throw new EntityServiceException($"Break duration cannot exceed beyond {_applicationDurationHours} hours!");


            if (Entity.Start < ClockTools.GetUTC(_startIST) || Entity.End > ClockTools.GetUTC(_endIST))
                throw new EntityServiceException("Date/Time selected should be between " + _startTimespan.ToString(@"hh\:mm") + " - " + _endTimeSpan.ToString(@"hh\:mm") + " on same Date!");

            await ValidateOverlapp(Entity);
        }
        else if (Entity.TypeFlag == McvConstant.LEAVE_TYPEFLAG_APPROVED_HALFDAY) //ApprovedHalfDay
        {
            if (Entity.End < Entity.Start)
                throw new EntityServiceException("Invalid Dates! Start should be less than End!");


            if (IsSelfApplied)
            {
                if (Entity.Start < DateTime.UtcNow)
                    throw new EntityServiceException("Only post date/time can be selected for Halfday!");

                var _applicationPriorDays = Convert.ToInt32(await sharedService.GetPresetValue(McvConstant.HALFDAY_APPLICATION_PRIOR_DAYS));
                if ((ClockTools.GetIST(Entity.Start) - ClockTools.GetISTNow()).Days < (_applicationPriorDays - 1))
                    throw new EntityServiceException($"Half-day can only be applied {_applicationPriorDays} days prior!");
            }

            var _applicationDurationHours = Convert.ToDouble(await sharedService.GetPresetValue(McvConstant.HALFDAY_APPLICATION_DURATION_HOURS_PER_MONTH)) + 0.1;
            if ((Entity.End - Entity.Start).TotalHours > _applicationDurationHours)
                throw new EntityServiceException($"Half-day duration cannot exceed beyond {_applicationDurationHours} hours!");


            var _applicationAlowedPerMonth = Convert.ToInt32(await sharedService.GetPresetValue(McvConstant.HALFDAY_APPLICATION_ALLOWED_PER_MONTH));

            var _currentMonthBreaks = await db.Leaves.AsNoTracking()
                 .Where(x => x.ContactID == Entity.ContactID)
                 .Where(x => x.TypeFlag == Entity.TypeFlag)
                 .Where(x => x.Start.Month == Entity.Start.Month && x.Start.Year == Entity.Start.Year).ToListAsync();
            if (_currentMonthBreaks.Count > _applicationAlowedPerMonth)
            {
                throw new EntityServiceException($"Only {_applicationAlowedPerMonth} {(_applicationAlowedPerMonth > 1 ? "half-days are" : "half-day is")} allowed in a month. You have already taken last on {ClockTools.GetIST(_currentMonthBreaks.OrderByDescending(x => x.Start).FirstOrDefault().Start).ToString("dd MMM yyyy HH:mm")}-{ClockTools.GetIST(_currentMonthBreaks.OrderByDescending(x => x.Start).FirstOrDefault().End).ToString("HH:mm")}");
            }


            if (Entity.Start < ClockTools.GetUTC(_startIST) || Entity.End > ClockTools.GetUTC(_endIST))
                throw new EntityServiceException("Date/Time selected should be between " + _startTimespan.ToString(@"hh\:mm") + " - " + _endTimeSpan.ToString(@"hh\:mm") + " on same Date!");



            await ValidateOverlapp(Entity);
        }
        else if (Entity.TypeFlag == McvConstant.LEAVE_TYPEFLAG_EMERGENCY_HALFDAY) //ApprovedHalfDay
        {
            if (Entity.End < Entity.Start)
                throw new EntityServiceException("Invalid Dates! Start should be less than End!");


            var _applicationDurationHours = Convert.ToInt32(await sharedService.GetPresetValue(McvConstant.HALFDAY_APPLICATION_DURATION_HOURS_PER_MONTH));
            if ((Entity.End - Entity.Start).TotalHours > _applicationDurationHours)
                throw new EntityServiceException($"Half-day duration cannot exceed beyond {_applicationDurationHours} hours!");



            if (Entity.Start < ClockTools.GetUTC(_startIST) || Entity.End > ClockTools.GetUTC(_endIST))
                throw new EntityServiceException("Date/Time selected should be between " + _startTimespan.ToString(@"hh\:mm") + " - " + _endTimeSpan.ToString(@"hh\:mm") + " on same Date!");



            await ValidateOverlapp(Entity);
        }
        else if (Entity.TypeFlag == McvConstant.LEAVE_TYPEFLAG_APPROVED_WFH) //ApprovedWFH
        {
            if (Entity.End < Entity.Start)
                throw new EntityServiceException("Invalid Dates! Start should be less than End!");

            if (IsSelfApplied)
            {

                if (Entity.Start < DateTime.UtcNow)
                    throw new EntityServiceException("Only post date/time can be selected for WorkFromHome!");

                var _applicationPriorDays = Convert.ToInt32(await sharedService.GetPresetValue(McvConstant.WFH_APPLICATION_PRIOR_DAYS));
                if ((Entity.Start - DateTime.UtcNow).Days < _applicationPriorDays)
                    throw new EntityServiceException($"WorkFromHome can only be applied {_applicationPriorDays} days prior!");
            }

            var _applicationDurationDays = Convert.ToInt32(await sharedService.GetPresetValue(McvConstant.WFH_APPLICATION_DURATION_DAYS_PER_MONTH));
            if ((Entity.End - Entity.Start).Days > _applicationDurationDays)
                throw new EntityServiceException($"WorkFromHome duration cannot exceed beyond {_applicationDurationDays} days!");


            if (Entity.Start < ClockTools.GetUTC(_startIST) || Entity.End > ClockTools.GetUTC(_endIST))
                throw new EntityServiceException("Date/Time selected should be between " + _startTimespan.ToString(@"hh\:mm") + " - " + _endTimeSpan.ToString(@"hh\:mm") + " on same Date!");



            await ValidateOverlapp(Entity);
        }
        else if (Entity.TypeFlag == McvConstant.LEAVE_TYPEFLAG_EMERGENCY_WFH) //ApprovedWFH
        {
            if (Entity.End < Entity.Start)
                throw new EntityServiceException("Invalid Dates! Start should be less than End!");


            var _applicationDurationDays = Convert.ToInt32(await sharedService.GetPresetValue(McvConstant.WFH_APPLICATION_DURATION_DAYS_PER_MONTH));
            if ((Entity.End - Entity.Start).Days > _applicationDurationDays)
                throw new EntityServiceException($"WorkFromHome duration cannot exceed beyond {_applicationDurationDays} days!");


            if (Entity.Start < ClockTools.GetUTC(_startIST) || Entity.End > ClockTools.GetUTC(_endIST))
                throw new EntityServiceException("Date/Time selected should be between " + _startTimespan.ToString(@"hh\:mm") + " - " + _endTimeSpan.ToString(@"hh\:mm") + " on same Date!");



            await ValidateOverlapp(Entity);
        }
        else if (Entity.TypeFlag == McvConstant.LEAVE_TYPEFLAG_APPROVED) //ApprovedLeave
        {

            var _holidayDates = await db.HolidayMasters.AsNoTracking()
            .Select(x => x.HolidayDate)
            .ToListAsync();

            var _leaveStart = Entity.Start;
            var _leaveEnd = Entity.End;

            var _presetApplicationDuration = Convert.ToInt32(await sharedService.GetPresetValue(McvConstant.LEAVE_APPLICATION_DURATION));

            var _currentAppicationDuration = ClockTools.GetValidWorkingDays(DateTime.UtcNow,
                _leaveStart,
                _presetEvenSaturdayOff,
                _presetOddSaturdayOff,
                _holidayDates);

            if (IsSelfApplied)
            {
                if (_currentAppicationDuration < _presetApplicationDuration)
                    throw new EntityServiceException("Leave must be applied " + _presetApplicationDuration + " days prior!");
            }


            await ValidateOverlapp(Entity);
        }
        else
        {
            Entity.Total = await GetTotalLeaveDurationIST(_startIST, _endIST);

            if (Entity.Total == 0) throw new EntityServiceException("Leave cannot be applied as leave days count comes to 0!");


            await ValidateOverlapp(Entity);
        }


    }

    public async Task LeaveStage2(WFTask task)
    {


        var _leave = await Get().Include(x => x.Contact).SingleOrDefaultAsync(x => x.ID == task.EntityID);
        if (_leave == null) return;

        _leave.StatusFlag = task.OutcomeFlag;
        db.Entry(_leave).State = EntityState.Modified;
        await db.SaveChangesAsync();

        var _emp = await db.Contacts.AsNoTracking()
            .SingleOrDefaultAsync(x => x.ID == _leave.ContactID);
    }

    private async Task AssignTasks(Leave _leave)
    {

     
        var _applicant = await db.Contacts.AsNoTracking()
        .Include(x => x.Appointments)
                        .SingleOrDefaultAsync(x => x.ID == _leave.ContactID);

        var _type = await db.TypeMasters.AsNoTracking()
            .Where(x => x.Entity == nameof(Leave) && x.Value == _leave.TypeFlag).AnyAsync() ? (await db.TypeMasters.AsNoTracking()
            .Where(x => x.Entity == nameof(Leave) && x.Value == _leave.TypeFlag)
            .FirstOrDefaultAsync()).Title : "Leave";

        var _subtitle = _applicant.FullName + " | " + ClockTools.GetIST(_leave.Start).ToString("dd MMM yyyy") + " - " + ClockTools.GetIST(_leave.End).ToString("dd MMM yyyy");
        if (_leave.TypeFlag == McvConstant.LEAVE_TYPEFLAG_APPROVED_BREAK || _leave.TypeFlag == McvConstant.LEAVE_TYPEFLAG_APPROVED_HALFDAY)
        {
            _subtitle = _applicant.FullName + " | " + ClockTools.GetIST(_leave.Start).ToString("dd MMM yyyy HH:mm") + " - " + ClockTools.GetIST(_leave.End).ToString("HH:mm");
        }
        var sharedService = new SharedService(db); ;
        var _dueDate = DateTime.UtcNow.AddDays(1).Date
                    .AddMinutes(await sharedService.GetBusinessEndMinutesIST()) < _leave.End ? DateTime.UtcNow.AddDays(1).Date
                    .AddMinutes(await sharedService.GetBusinessEndMinutesIST()) :
                    _leave.End;


        //var activeAppointmentManagers = _applicant.Appointments.Where(x => !x.IsDeleted && x.StatusFlag == McvConstant.APPOINTMENT_STATUSFLAG_APPOINTED && x.ManagerContactID != null)
        //    .Select(x => x.ManagerContactID.Value).ToList();

        var hrManagers = await sharedService.GetContactIDByRoleAsync(McvConstant.PERMISSION_LEAVE_SPECIAL_EDIT);

        //if (activeAppointmentManagers.Any())
        //{
        //    var taskService = new WFTaskService(db);

        //    foreach (var contactID in activeAppointmentManagers)
        //    {
        //        var _TLTask = new WFTask
        //        {
        //            ContactID = contactID,
        //            Title = _type + " Application",
        //            Subtitle = _subtitle,
        //            StageIndex = 1,

        //            OutcomeFlag = 0,
        //            Entity = nameof(Leave),
        //            EntityID = _leave.ID,
        //            StartDate = DateTime.UtcNow,
        //            DueDate = _dueDate
        //        };

        //        var _result = await taskService.CreateTask(_TLTask);

        //    }

        //    foreach (var _emp in hrManagers)
        //    {
        //        var _TLTask = new WFTask
        //        {
        //            ContactID = _emp,
        //            Title = _type + " Application",
        //            Subtitle = _subtitle,
        //            StageIndex = 1,

        //            OutcomeFlag = 0,
        //            Entity = nameof(Leave),
        //            EntityID = _leave.ID,
        //            StartDate = DateTime.UtcNow,
        //            DueDate = _dueDate
        //        };

        //        var _result = await taskService.CreateTask(_TLTask);

        //    }
        //}
        //else
        {


            var _masters = await sharedService.GetContactIDByRoleAsync(McvConstant.PERMISSION_MASTER);

            if (hrManagers.Any(x => x == _applicant.ID))
            {



                var taskService = new WFTaskService(db);

                foreach (var _emp in _masters)
                {
                    var _TLTask = new WFTask
                    {
                        ContactID = _emp,
                        Title = _type + " Application",
                        Subtitle = _subtitle,
                        StageIndex = 1,

                        OutcomeFlag = 0,
                        Entity = nameof(Leave),
                        EntityID = _leave.ID,
                        StartDate = DateTime.UtcNow,
                        DueDate = _dueDate
                    };

                    var _result = await taskService.CreateTask(_TLTask);

                }

            }
            else
            {

                var taskService = new WFTaskService(db);

                foreach (var _emp in hrManagers)
                {
                    var _TLTask = new WFTask
                    {
                        ContactID = _emp,
                        Title = _type + " Application",
                        Subtitle = _subtitle,
                        StageIndex = 1,

                        OutcomeFlag = 0,
                        Entity = nameof(Leave),
                        EntityID = _leave.ID,
                        StartDate = DateTime.UtcNow,
                        DueDate = _dueDate
                    };

                    var _result = await taskService.CreateTask(_TLTask);

                }
            }
        }


    }

}

public class LeaveSummary
{
    public int Month { get; set; }

    public int Year { get; set; }

    public string LeaveCycle { get; set; }

    public string Label { get; set; }

    public decimal ApprovedLeave { get; set; }

    public decimal EmergencyLeave { get; set; }

    public decimal Late { get; set; }

    public decimal Penalty { get; set; }

    public decimal ApprovedBreak { get; set; }

    public decimal ApprovedHalfDay { get; set; }

    public decimal ApprovedWFH { get; set; }


    public decimal EmergencyBreak { get; set; }

    public decimal EmergencyHalfDay { get; set; }

    public decimal EmergencyWFH { get; set; }


    public decimal Total { get; set; }

    public decimal Allowed { get; set; }

    public decimal Balance
    {
        get
        {
            return Allowed - Total;
        }
    }

    public decimal AllowedEmergency { get; set; }

    public decimal EmergencyBalance
    {
        get
        {
            return AllowedEmergency - (EmergencyLeave + EmergencyHalfDay);
            // return AllowedEmergency > (EmergencyLeave+EmergencyHalfDay) ? AllowedEmergency - (EmergencyLeave + EmergencyHalfDay) : 0;
        }
    }
}