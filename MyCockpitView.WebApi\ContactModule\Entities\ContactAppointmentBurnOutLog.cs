﻿using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.CoreModule;
using System.ComponentModel.DataAnnotations.Schema;


namespace MyCockpitView.WebApi.ContactModule.Entities;

public class ContactAppointmentBurnOutLog
{
  
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public Guid ID { get; set; }

    [Column(TypeName = "datetime2")]
    public DateTime LogDate { get; set; }
    public int ContactAppointmentID { get; set; }
    public int ContactID { get; set; }
    public int CompanyID { get; set; }

    public string? ContactName { get; set; }
    public decimal VHRRate { get; set; } = 0.0m;
    public decimal ManValue { get; set; } = 0.0m;

    public decimal AssignedMHr { get; set; } = 0.0m;

    public decimal AssignedAmount { get; set; } = 0.0m;

    public decimal BurnedMHr { get; set; } = 0.0m;

    public decimal BurnedAmount { get; set; } = 0.0m;

}

public class ContactAppointmentBurnOutLogConfiguration : IEntityTypeConfiguration<ContactAppointmentBurnOutLog>
{
    public void Configure(EntityTypeBuilder<ContactAppointmentBurnOutLog> builder)
    {

        builder.HasKey(e => e.ID);

        //builder.Property(e => e.ID)
        //    .HasDefaultValueSql("NEWID()");

        builder.HasIndex(e => e.ContactAppointmentID);
        builder.HasIndex(e => e.CompanyID);
        builder.HasIndex(e => e.ContactID);
        builder.HasIndex(e => e.ContactName);
    }
}
