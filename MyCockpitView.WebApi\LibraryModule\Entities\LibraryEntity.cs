﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using MyCockpitView.CoreModule;
using System.ComponentModel.DataAnnotations;

namespace MyCockpitView.WebApi.LibraryModule.Entities;

public class LibraryEntity : BaseEntity
{


    
    [StringLength(255)]
    public string? Title { get; set; }


    
    [StringLength(255)]
    public string? Subtitle { get; set; }


    
    [StringLength(255)]
    public string? Code { get; set; }

    
    public int CodeFlag { get; set; }


    //[Column("Attributes")]
    //public string? _attributes { get; set; }

    //[NotMapped]
    //public virtual ICollection<BaseAttribute> Attributes
    //{
    //    get
    //    {
    //        return _attributes != null && _attributes != string.Empty ?
    //            DataTools.GetObjectFromJsonString<ICollection<BaseAttribute>>(_attributes) :
    //            new List<BaseAttribute>();
    //    }
    //    set
    //    {
    //        _attributes = DataTools.GetJsonStringFromObject(value);
    //    }
    //}


    public string? Category { get; set; }

    public int? ParentID { get; set; }
    public virtual LibraryEntity? Parent { get; set; }
    public virtual ICollection<LibraryEntity> Children { get; set; } = new List<LibraryEntity>();
    public virtual ICollection<LibraryEntityAttachment> Attachments { get; set; } = new List<LibraryEntityAttachment>();
    public virtual ICollection<LibraryEntityAttribute> Attributes { get; set; } = new List<LibraryEntityAttribute>();

    public virtual ICollection<LibraryEntityVendor> Vendors { get; set; } = new List<LibraryEntityVendor>();

}
public class LibraryEntityConfiguration : BaseEntityConfiguration<LibraryEntity>, IEntityTypeConfiguration<LibraryEntity>
{
    public void Configure(EntityTypeBuilder<LibraryEntity> builder)
    {
      base.Configure(builder);

        builder.HasIndex(e => e.Title);
        builder.HasIndex(e => e.Subtitle);
        builder.HasIndex(e => e.Code);
        builder.HasIndex(e => e.Category);
        builder.HasIndex(e => e.ParentID);
    }
}

