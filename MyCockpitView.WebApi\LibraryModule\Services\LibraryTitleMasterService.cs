﻿



using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.LibraryModule.Entities;
using MyCockpitView.WebApi.Services;

namespace MyCockpitView.WebApi.LibraryModule.Services;
public class LibraryTitleMasterService : BaseEntityService<LibraryTitleMaster>, ILibraryTitleMasterService
{
    public LibraryTitleMasterService(EntitiesContext db) : base(db) { }

    public IQueryable<LibraryTitleMaster> Get(IEnumerable<QueryFilter>? Filters = null, string? Search = null, string? Sort = null)
    {
      
            IQueryable<LibraryTitleMaster> _query = base.Get(Filters);

            //Apply filters
            if (Filters != null)
            {

                if (Filters.Where(x => x.Key.Equals("Category", StringComparison.OrdinalIgnoreCase)).Any())
                {
                    var predicate = PredicateBuilder.False<LibraryTitleMaster>();
                    foreach (var _item in Filters.Where(x => x.Key.Equals("Category", StringComparison.OrdinalIgnoreCase)))
                    {
                        predicate = predicate.Or(x => x.Category==_item.Value);
                    }
                    _query = _query.Where(predicate);
                }
            }

            if (Search != null && Search != String.Empty)
            {
                _query = _query
                    .Where(x => x.Category.ToLower().Contains(Search.ToLower())
                    || x.Title.ToLower().Contains(Search.ToLower()));

            }

            return _query
              .OrderBy(x => x.Category).ThenBy(x=>x.Title);
      
    }

}