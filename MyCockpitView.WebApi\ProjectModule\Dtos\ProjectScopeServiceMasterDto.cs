﻿using AutoMapper;
using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.ProjectModule.Entities;
using System.ComponentModel.DataAnnotations;

namespace MyCockpitView.WebApi.ProjectModule.Dtos;

public class ProjectScopeServiceMasterDto : BaseEntityDto
{
    [StringLength(50)]
    public string? Title { get; set; }

    [StringLength(10)]
    public string? Abbreviation { get; set; }
}

public class ProjectScopeServiceMasterDtoMapperProfile : Profile
{
    public ProjectScopeServiceMasterDtoMapperProfile()
    {
        CreateMap<ProjectScopeServiceMaster, ProjectScopeServiceMasterDto>()
            .ReverseMap();

    }
}