﻿using AutoMapper;
using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.InspectionModule.Entities;

namespace MyCockpitView.WebApi.InspectionModule.Dtos;

public class InspectionRecipientDto : BaseEntityDto
{
    public int InspectionID { get; set; }
    public string? Name { get; set; }
    public string? Email { get; set; }
    public string? Company { get; set; }
    public int? ContactID { get; set; }
    public int PendingAgendaCount { get; set; } = 0;
}
public class InspectionRecipientDtoMapperProfile : Profile
{
    public InspectionRecipientDtoMapperProfile()
    {
        CreateMap<InspectionRecipient, InspectionRecipientDto>()
         .ReverseMap()
         .ForMember(dest => dest.Inspection, opt => opt.Ignore());

    }
}