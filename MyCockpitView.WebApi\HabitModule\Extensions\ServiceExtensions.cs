﻿
using MyCockpitView.WebApi.HabitModule.Services;

namespace MyCockpitView.WebApi.HabitModule.Extensions;

public static class ServiceExtensions
{

    public static IServiceCollection RegisterHabitServices(
     this IServiceCollection services)
    {
        services.AddScoped<IHabitService,HabitService>();
        services.AddScoped<IHabitAttachmentService, HabitAttachmentService>();
        return services;
    }
}
