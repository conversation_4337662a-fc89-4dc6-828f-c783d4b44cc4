﻿USE [NewarchDbStaging];
GO

SELECT 
    t.name AS TableName,
    SCHEMA_NAME(t.schema_id) AS SchemaName,
    c.name AS ColumnName,
    'Missing or case-mismatched in mcv.staging' AS Issue
FROM 
    sys.tables t
INNER JOIN 
    sys.columns c ON t.object_id = c.object_id
WHERE 
    NOT EXISTS (
        SELECT 1
        FROM [mcv.staging].sys.tables t2
        INNER JOIN [mcv.staging].sys.columns c2 ON t2.object_id = c2.object_id
        WHERE 
            t2.name = t.name 
            AND SCHEMA_NAME(t2.schema_id) = SCHEMA_NAME(t.schema_id)
            -- Case-sensitive check on column name
            AND c2.name COLLATE Latin1_General_BIN = c.name COLLATE Latin1_General_BIN
    )
ORDER BY SCHEMA_NAME(t.schema_id), t.name, c.name;
