﻿using AutoMapper;
using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.LibraryModule.Entities;

namespace MyCockpitView.WebApi.LibraryModule.Dtos;

public class LibraryEntityVendorDto : BaseEntityDto
{
    public int LibraryEntityID { get; set; }
    public int? ContactID { get; set; }
    public string? Name { get; set; }
    public string? Phone { get; set; }
    public string? Email { get; set; }

}

public class LibraryEntityVendorDtoMapperProfile : Profile
{
    public LibraryEntityVendorDtoMapperProfile()
    {
        CreateMap<LibraryEntityVendor, LibraryEntityVendorDto>()
             .ReverseMap()
            .ForMember(dest => dest.LibraryEntity, opt => opt.Ignore());

    }
}
