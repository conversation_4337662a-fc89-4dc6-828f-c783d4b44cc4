﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace MyCockpitView.WebApi.Migrations
{
    /// <inheritdoc />
    public partial class AK004 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<DateTime>(
                name: "LockoutEndDateUtc",
                table: "AspNetUsers",
                type: "datetime2",
                nullable: true);

            migrationBuilder.CreateTable(
                name: "PackageFeedbacks",
                columns: table => new
                {
                    ID = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    PackageID = table.Column<int>(type: "int", nullable: false),
                    PackageTitle = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: true),
                    PackagePartnerName = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: true),
                    PackagePartnerEmail = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: true),
                    PackagePartnerContactID = table.Column<int>(type: "int", nullable: true),
                    PackageAssociateName = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: true),
                    PackageAssociateEmail = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: true),
                    PackageAssociateContactID = table.Column<int>(type: "int", nullable: true),
                    ReviewerName = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: true),
                    ReviewerEmail = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: true),
                    ReviewerContactID = table.Column<int>(type: "int", nullable: true),
                    Rating = table.Column<int>(type: "int", nullable: false),
                    Comment = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    IsReadOnly = table.Column<bool>(type: "bit", nullable: false),
                    Created = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatedBy = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: true),
                    CreatedByContactID = table.Column<int>(type: "int", nullable: true),
                    Description = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false, defaultValue: false),
                    Modified = table.Column<DateTime>(type: "datetime2", nullable: false),
                    ModifiedBy = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: true),
                    ModifiedByContactID = table.Column<int>(type: "int", nullable: true),
                    OrderFlag = table.Column<int>(type: "int", nullable: false, defaultValue: 0),
                    SearchTags = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    StatusFlag = table.Column<int>(type: "int", nullable: false, defaultValue: 0),
                    TypeFlag = table.Column<int>(type: "int", nullable: false, defaultValue: 0),
                    UID = table.Column<Guid>(type: "uniqueidentifier", nullable: false, defaultValueSql: "NEWID()")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PackageFeedbacks", x => x.ID);
                    table.ForeignKey(
                        name: "FK_PackageFeedbacks_Packages_PackageID",
                        column: x => x.PackageID,
                        principalTable: "Packages",
                        principalColumn: "ID",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_PackageFeedbacks_Created",
                table: "PackageFeedbacks",
                column: "Created");

            migrationBuilder.CreateIndex(
                name: "IX_PackageFeedbacks_CreatedByContactID",
                table: "PackageFeedbacks",
                column: "CreatedByContactID");

            migrationBuilder.CreateIndex(
                name: "IX_PackageFeedbacks_IsDeleted",
                table: "PackageFeedbacks",
                column: "IsDeleted");

            migrationBuilder.CreateIndex(
                name: "IX_PackageFeedbacks_IsReadOnly",
                table: "PackageFeedbacks",
                column: "IsReadOnly");

            migrationBuilder.CreateIndex(
                name: "IX_PackageFeedbacks_Modified",
                table: "PackageFeedbacks",
                column: "Modified");

            migrationBuilder.CreateIndex(
                name: "IX_PackageFeedbacks_ModifiedByContactID",
                table: "PackageFeedbacks",
                column: "ModifiedByContactID");

            migrationBuilder.CreateIndex(
                name: "IX_PackageFeedbacks_OrderFlag",
                table: "PackageFeedbacks",
                column: "OrderFlag");

            migrationBuilder.CreateIndex(
                name: "IX_PackageFeedbacks_PackageAssociateContactID",
                table: "PackageFeedbacks",
                column: "PackageAssociateContactID");

            migrationBuilder.CreateIndex(
                name: "IX_PackageFeedbacks_PackageAssociateEmail",
                table: "PackageFeedbacks",
                column: "PackageAssociateEmail");

            migrationBuilder.CreateIndex(
                name: "IX_PackageFeedbacks_PackageAssociateName",
                table: "PackageFeedbacks",
                column: "PackageAssociateName");

            migrationBuilder.CreateIndex(
                name: "IX_PackageFeedbacks_PackageID",
                table: "PackageFeedbacks",
                column: "PackageID");

            migrationBuilder.CreateIndex(
                name: "IX_PackageFeedbacks_PackagePartnerContactID",
                table: "PackageFeedbacks",
                column: "PackagePartnerContactID");

            migrationBuilder.CreateIndex(
                name: "IX_PackageFeedbacks_PackagePartnerEmail",
                table: "PackageFeedbacks",
                column: "PackagePartnerEmail");

            migrationBuilder.CreateIndex(
                name: "IX_PackageFeedbacks_PackagePartnerName",
                table: "PackageFeedbacks",
                column: "PackagePartnerName");

            migrationBuilder.CreateIndex(
                name: "IX_PackageFeedbacks_PackageTitle",
                table: "PackageFeedbacks",
                column: "PackageTitle");

            migrationBuilder.CreateIndex(
                name: "IX_PackageFeedbacks_Rating",
                table: "PackageFeedbacks",
                column: "Rating");

            migrationBuilder.CreateIndex(
                name: "IX_PackageFeedbacks_ReviewerContactID",
                table: "PackageFeedbacks",
                column: "ReviewerContactID");

            migrationBuilder.CreateIndex(
                name: "IX_PackageFeedbacks_ReviewerEmail",
                table: "PackageFeedbacks",
                column: "ReviewerEmail");

            migrationBuilder.CreateIndex(
                name: "IX_PackageFeedbacks_ReviewerName",
                table: "PackageFeedbacks",
                column: "ReviewerName");

            migrationBuilder.CreateIndex(
                name: "IX_PackageFeedbacks_StatusFlag",
                table: "PackageFeedbacks",
                column: "StatusFlag");

            migrationBuilder.CreateIndex(
                name: "IX_PackageFeedbacks_TypeFlag",
                table: "PackageFeedbacks",
                column: "TypeFlag");

            migrationBuilder.CreateIndex(
                name: "IX_PackageFeedbacks_UID",
                table: "PackageFeedbacks",
                column: "UID");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "PackageFeedbacks");

            migrationBuilder.DropColumn(
                name: "LockoutEndDateUtc",
                table: "AspNetUsers");
        }
    }
}
