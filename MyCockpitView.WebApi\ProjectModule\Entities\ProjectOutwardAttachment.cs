﻿using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.CoreModule;

namespace MyCockpitView.WebApi.ProjectModule.Entities;

public class ProjectOutwardAttachment : BaseBlobEntity
{
    public int ProjectOutwardID { get; set; }

    public virtual ProjectOutward? ProjectOutward { get; set; }
}

public class ProjectOutwardAttachmentConfiguration : BaseBlobEntityConfiguration<ProjectOutwardAttachment>, IEntityTypeConfiguration<ProjectOutwardAttachment>
{
    public void Configure(EntityTypeBuilder<ProjectOutwardAttachment> builder)
    {
        base.Configure(builder);
     
    }
}