﻿
using System.Text.RegularExpressions;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.WebApi.Exceptions;
using MyCockpitView.WebApi.MeetingModule.Entities;
using MyCockpitView.WebApi.Services;
using MyCockpitView.WebApi.WFTaskModule.Entities;
using MyCockpitView.WebApi.WFTaskModule.Services;

namespace MyCockpitView.WebApi.MeetingModule.Services;

public class MeetingAttendeeService : BaseEntityService<MeetingAttendee>, IMeetingAttendeeService
{
    public MeetingAttendeeService(EntitiesContext db) : base(db) { }

    public async Task<int> Create(MeetingAttendee Entity)
    {
        Regex regex = new Regex(McvConstant.EMAIL_REGEX, RegexOptions.None);
        if (Entity.Email == null || !regex.IsMatch(Entity.Email))
            throw new EntityServiceException("Email Id is invalid.");

        return await base.Create(Entity);
    }


    public async Task Update(MeetingAttendee Entity)
    {

        Regex regex = new Regex(McvConstant.EMAIL_REGEX, RegexOptions.None);
        if (Entity.Email == null || !regex.IsMatch(Entity.Email))
            throw new EntityServiceException("Email Id is invalid.");

        var _meeting = await db.Meetings.AsNoTracking()
    .Where(x => x.ID == Entity.MeetingID).SingleOrDefaultAsync();

        if (Entity.TypeFlag == McvConstant.MEETING_ATTENDEE_TYPEFLAG_TO)//
        {
            await LogAttendeeTime(Entity.ContactID.Value, _meeting.ID, _meeting.StartDate, _meeting.EndDate, "MEETING_CLOSE");
        }
        else
        {
            if (Entity.ContactID != null && Entity.ContactID != _meeting.ContactID)
            {
                var taskIDs = await db.WFTasks.AsNoTracking()
            .Include(x => x.TimeEntries)
            .Include(x => x.Attachments)
             .Where(x => x.Entity != null && x.Entity==nameof(Meeting)
                    && x.EntityID == Entity.MeetingID && x.WFStageCode == "MEETING_CLOSE")
                    .Where(x => x.ContactID == Entity.ContactID)
                    .Select(x => x.ID)
                    .ToListAsync();
                var taskService = new WFTaskService(db);
                foreach (var item in taskIDs)
                {
                    await taskService.Delete(item);
                }
            }
        }

        await base.Update(Entity);
    }

    public async Task Delete(int Id)
    {

        var Entity = await Get()
             .SingleOrDefaultAsync(i => i.ID == Id);

        if (Entity == null) throw new EntityServiceException($"{nameof(MeetingAttendee)} not found!");

        var _meeting = await db.Meetings.AsNoTracking()
   .Where(x => x.ID == Entity.MeetingID).SingleOrDefaultAsync();

        if (Entity.TypeFlag == McvConstant.MEETING_ATTENDEE_TYPEFLAG_TO)
        {
            if (Entity.ContactID != null && Entity.ContactID != _meeting.ContactID)
            {
                var taskIDs = await db.WFTasks.AsNoTracking()
            .Include(x => x.TimeEntries)
            .Include(x => x.Attachments)
             .Where(x => x.Entity != null && x.Entity==nameof(Meeting)
                    && x.EntityID == Entity.MeetingID && x.WFStageCode == "MEETING_CLOSE")
                    .Where(x => x.ContactID == Entity.ContactID)
                    .Select(x => x.ID)
                    .ToListAsync();
                var taskService = new WFTaskService(db);
                foreach (var item in taskIDs)
                {
                    await taskService.Delete(item);
                }
            }
        }

        await base.Delete(Id);

    }


    public async Task ScaffoldPendingAttendees(int MeetingID)
    {

        var _meetingAgendas = await db.MeetingAgendas.AsNoTracking()
            .Where(x => x.MeetingID == MeetingID)
            .Where(x => x.ActionByContactID != null)
            //.Where(x => x.PreviousAgendaID != null)
            .ToListAsync();

        var _attendees = await db.MeetingAttendees.AsNoTracking()
                        
                        .Where(x => x.MeetingID == MeetingID
                        && x.ContactID != null).ToListAsync();



        Regex regex = new Regex(McvConstant.EMAIL_REGEX, RegexOptions.None);

        foreach (var item in _meetingAgendas)
        {
            if (!_attendees
                .Where(x => x.ContactID == item.ActionByContactID)
                .Any())
            {
                if (item.PreviousAgendaID != null)
                {
                    var _previousAgenda = await db.MeetingAgendas.AsNoTracking()
                 .Where(x => x.ID == item.PreviousAgendaID)
                 .FirstOrDefaultAsync();

                    if (_previousAgenda != null)
                    {
                        var _attendee = await db.MeetingAttendees.AsNoTracking()
                           
                           .Where(x => x.MeetingID == _previousAgenda.MeetingID
                           && x.ContactID != null
                           && x.ContactID == _previousAgenda.ActionByContactID)
                           .FirstOrDefaultAsync();
                        if (_attendee != null)
                        {
                            if (_attendee.Email != null && regex.IsMatch(_attendee.Email))
                            {

                                var _newAttendee = new MeetingAttendee
                                {
                                    MeetingID = MeetingID,
                                    Name = _attendee.Name,
                                    Company = _attendee.Company,
                                    Email = _attendee.Email,
                                    ContactID = _attendee.ContactID,
                                    TypeFlag = McvConstant.MEETING_ATTENDEE_TYPEFLAG_CC,
                                };
                                db.MeetingAttendees.Add(_newAttendee);
                                _attendees.Add(_newAttendee);
                            }
                        }
                    }
                }
                else
                {
                    var _contact = await db.Contacts.AsNoTracking()
                    .Include(x => x.AssociatedContacts)
                    .Include(x => x.AssociatedCompanies)
                    .Where(x => x.ID == item.ActionByContactID)
                    .FirstOrDefaultAsync();
                    if (_contact.Email1 != null && regex.IsMatch(_contact.Email1))
                    {
                        var _newAttendee = new MeetingAttendee
                        {
                            MeetingID = MeetingID,
                            Name = _contact.FullName,
                            Company = _contact.AssociatedCompanies.Any() ? _contact.AssociatedCompanies.FirstOrDefault().Company.FullName : "",
                            Email = _contact.Email1,
                            ContactID = _contact.ID,
                            TypeFlag = McvConstant.MEETING_ATTENDEE_TYPEFLAG_CC,
                        };
                        db.MeetingAttendees.Add(_newAttendee);
                        _attendees.Add(_newAttendee);
                    }
                }


            }
        }

        await db.SaveChangesAsync();
    }

    public async Task LogAttendeeTime(int ContactID, int MeetingID, DateTime Start, DateTime End, string StageCode)
    {

        var _meeting = await db.Meetings.AsNoTracking()
                         .SingleOrDefaultAsync(x => x.ID == MeetingID);

        var _contact = await db.Contacts.AsNoTracking()
                        .SingleOrDefaultAsync(x => x.ID == ContactID);

        if (_meeting == null || _meeting.StatusFlag == McvConstant.MEETING_STATUSFLAG_SCHEDULED
            || _contact == null || _contact.Username == null) return;

        var _manValue = 1.0m;
        var _companyID = 1;

        var sharedService = new SharedService(db); ;
        var _appointment = await sharedService.GetLastAppointment(_contact.ID);
        if (_appointment != null)
        {
            _manValue = _appointment.ManValue;
            _companyID = _appointment.CompanyID;
        }


        var _valueHourRate = Convert.ToDecimal(await sharedService.GetPresetValue(McvConstant.COMPANY_VHR_COST));


        var _task = await db.WFTasks
        .Where(x => x.Entity != null
           && x.Entity==nameof(Meeting)
           && x.EntityID == MeetingID
           && x.ContactID == ContactID
           && x.WFStageCode==StageCode).FirstOrDefaultAsync();
        if (_task != null)
        {
            _task.ManValue = _manValue;
            _task.VHrRate = _valueHourRate;
            _task.CompanyID = _companyID;
            _task.StartDate = Start;
            _task.CompletedDate = End;
            _task.DueDate = End;

            _task.MHrAssessed = Convert.ToDecimal((End - Start).TotalHours);
            _task.MHrAssigned = Convert.ToDecimal((End - Start).TotalHours);

            _task.VHrAssigned = Math.Round(_task.MHrAssigned * _task.ManValue, 2);
            _task.VHrAssessed = Math.Round(_task.MHrAssessed * _task.ManValue, 2);

            _task.VHrAssignedCost = Math.Round(_task.VHrAssigned * _task.VHrRate, 2);
            _task.VHrAssessedCost = Math.Round(_task.VHrAssessed * _task.VHrRate, 2);

            await db.SaveChangesAsync();
        }
        else
        {

            var taskService = new WFTaskService(db);

            var _taskEntity = await taskService.GetTaskByStage(nameof(Meeting), MeetingID, StageCode);

            _taskEntity.ContactID = ContactID;
            _taskEntity.ManValue = _manValue;
            _taskEntity.VHrRate = _valueHourRate;
            _taskEntity.CompanyID = _companyID;
            _taskEntity.StartDate = Start;
            _taskEntity.DueDate = End;
            _taskEntity.CompletedDate = End;
            _taskEntity.MHrAssessed = Convert.ToDecimal((End - Start).TotalHours);
            _taskEntity.MHrAssigned = Convert.ToDecimal((End - Start).TotalHours);
            _taskEntity.VHrAssigned = Math.Round(_taskEntity.MHrAssigned * _taskEntity.ManValue, 2);
            _taskEntity.VHrAssessed = Math.Round(_taskEntity.MHrAssessed * _taskEntity.ManValue, 2);
            _taskEntity.VHrAssignedCost = Math.Round(_taskEntity.VHrAssigned * _taskEntity.VHrRate, 2);
            _taskEntity.VHrAssessedCost = Math.Round(_taskEntity.VHrAssessed * _taskEntity.VHrRate, 2);
            _taskEntity.StatusFlag = 1;
            _taskEntity.Comment = "On behalf by " + _meeting.CreatedBy;

            var _taskID = await taskService.CreateTask(_taskEntity, true);
            _task = await db.WFTasks.AsNoTracking()
                    .Where(x => x.ID == _taskID).SingleOrDefaultAsync();


        }

        var _timeEntries = await db.TimeEntries
        .Where(x => x.WFTaskID == _task.ID)
        .ToListAsync();

        if (_timeEntries.Any())
        {
            db.TimeEntries.RemoveRange(_timeEntries);

            await db.SaveChangesAsync();
        }



        if (_task != null)
        {
            db.TimeEntries.Add(new TimeEntry
            {
                ContactID = ContactID,
                Entity = nameof(Meeting),
                EntityID = MeetingID,
                EntityTitle = _meeting.Code,
                StartDate = Start,
                EndDate = End,
                ManHours = Convert.ToDecimal((End - Start).TotalHours),
                WFTaskID = _task.ID,
                StatusFlag = 1,
                TaskTitle = (_task.Title + " " + _task.Subtitle).Trim(),
            });


            await db.SaveChangesAsync();
        }



    }



}