﻿
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.WebApi.ContactModule.Entities;
using MyCockpitView.CoreModule;
using System.ComponentModel.DataAnnotations;


namespace MyCockpitView.WebApi.WFTaskModule.Entities;

public class Assessment : BaseEntity
{
    public int ContactID { get; set; }
    public virtual Contact? Contact { get; set; }

    [StringLength(255)]
    public string? TaskTitle { get; set; }

    [StringLength(255)]
    public string? Category { get; set; }
    
    [Precision(18,2)] public decimal Points { get; set; }
    
    [Precision(18,2)] public decimal ScoredPoints { get; set; }

    public string? Comment { get; set; }

    public int? WFTaskID { get; set; }
    public virtual WFTask? WFTask { get; set; }

    [StringLength(255)]
    public string? Entity { get; set; }


    public int? EntityID { get; set; }
    [StringLength(255)]
    public string? EntityTitle { get; set; }
}

public class AssessmentConfiguration : BaseEntityConfiguration<Assessment>, IEntityTypeConfiguration<Assessment>
{
    public void Configure(EntityTypeBuilder<Assessment> builder)
    {
        base.Configure(builder);
    }
}
