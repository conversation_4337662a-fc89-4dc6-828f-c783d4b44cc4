﻿using AutoMapper;
using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.ContactModule.Dtos;
using MyCockpitView.WebApi.ProjectModule.Entities;
using System.ComponentModel.DataAnnotations;

namespace MyCockpitView.WebApi.ProjectModule.Dtos;

public class ProjectOutwardDto : BaseEntityDto
{
    public string? Title { get; set; }
    public string? Message { get; set; }
    public DateTime? SentDate { get; set; }
    public int ProjectID { get; set; }
    public int ContactID { get; set; }
    public virtual ContactListDto? Contact { get; set; }
    public virtual ICollection<ProjectOutwardAttachmentDto> Attachments { get; set; } = new List<ProjectOutwardAttachmentDto>();

}
public class ProjectOutwardDtoMapperProfile : Profile
{
    public ProjectOutwardDtoMapperProfile()
    {
        CreateMap<ProjectOutward, ProjectOutwardDto>()
           .ReverseMap()
           .ForMember(dest => dest.Contact, opt => opt.Ignore())
                .ForMember(dest => dest.Attachments, opt => opt.Ignore())
        .ForMember(dest => dest.Project, opt => opt.Ignore());

    }
}