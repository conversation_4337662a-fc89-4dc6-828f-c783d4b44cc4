﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using MyCockpitView.CoreModule;

using System.ComponentModel.DataAnnotations;

namespace MyCockpitView.WebApi.InspectionModule.Entities;

public class InspectionRecipient : BaseEntity
{
    [Required]
    public int InspectionID { get; set; }

  
    [Required]
    [StringLength(255)]
    public string? Name { get; set; }

    [StringLength(255)]
    public string? Email { get; set; }

    [StringLength(255)]
    public string? Company { get; set; }

    public int? ContactID { get; set; }

    public virtual Inspection? Inspection { get; set; }

}
public class InspectionRecipientConfiguration : BaseEntityConfiguration<InspectionRecipient>, IEntityTypeConfiguration<InspectionRecipient>
{
    public void Configure(EntityTypeBuilder<InspectionRecipient> builder)
    {
      
        base.Configure(builder);

        builder.HasIndex(e => e.Name);
        builder.HasIndex(e => e.Email);
        builder.HasIndex(e => e.Company);
        builder.HasIndex(e => e.ContactID);
       

    }
}