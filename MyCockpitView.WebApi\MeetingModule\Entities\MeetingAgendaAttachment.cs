﻿using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.CoreModule;
using System.ComponentModel.DataAnnotations;


namespace MyCockpitView.WebApi.MeetingModule.Entities;

public class MeetingAgendaAttachment : BaseBlobEntity
{
    [Required]
    public int MeetingAgendaID { get; set; }

    public virtual MeetingAgenda? MeetingAgenda { get; set; }
}

public class MeetingAgendaAttachmentConfiguration : BaseBlobEntityConfiguration<MeetingAgendaAttachment>, IEntityTypeConfiguration<MeetingAgendaAttachment>
{
    public void Configure(EntityTypeBuilder<MeetingAgendaAttachment> builder)
    {
        base.Configure(builder);
      

    }
}