﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace MyCockpitView.WebApi.Migrations
{
    /// <inheritdoc />
    public partial class AK013 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "IsReadOnly",
                table: "WFTasks",
                newName: "IsVersion");

            migrationBuilder.RenameIndex(
                name: "IX_WFTasks_IsReadOnly",
                table: "WFTasks",
                newName: "IX_WFTasks_IsVersion");

            migrationBuilder.RenameColumn(
                name: "IsReadOnly",
                table: "WFTaskAttachments",
                newName: "IsVersion");

            migrationBuilder.RenameIndex(
                name: "IX_WFTaskAttachments_IsReadOnly",
                table: "WFTaskAttachments",
                newName: "IX_WFTaskAttachments_IsVersion");

            migrationBuilder.RenameColumn(
                name: "<PERSON>ReadOnly",
                table: "WFStages",
                newName: "IsVersion");

            migrationBuilder.RenameIndex(
                name: "IX_WFStages_IsReadOnly",
                table: "WFStages",
                newName: "IX_WFStages_IsVersion");

            migrationBuilder.RenameColumn(
                name: "IsReadOnly",
                table: "WFStageActions",
                newName: "IsVersion");

            migrationBuilder.RenameIndex(
                name: "IX_WFStageActions_IsReadOnly",
                table: "WFStageActions",
                newName: "IX_WFStageActions_IsVersion");

            migrationBuilder.RenameColumn(
                name: "IsReadOnly",
                table: "TypeMasters",
                newName: "IsVersion");

            migrationBuilder.RenameIndex(
                name: "IX_TypeMasters_IsReadOnly",
                table: "TypeMasters",
                newName: "IX_TypeMasters_IsVersion");

            migrationBuilder.RenameColumn(
                name: "IsReadOnly",
                table: "Todos",
                newName: "IsVersion");

            migrationBuilder.RenameIndex(
                name: "IX_Todos_IsReadOnly",
                table: "Todos",
                newName: "IX_Todos_IsVersion");

            migrationBuilder.RenameColumn(
                name: "IsReadOnly",
                table: "TodoAttachments",
                newName: "IsVersion");

            migrationBuilder.RenameIndex(
                name: "IX_TodoAttachments_IsReadOnly",
                table: "TodoAttachments",
                newName: "IX_TodoAttachments_IsVersion");

            migrationBuilder.RenameColumn(
                name: "IsReadOnly",
                table: "TodoAgendas",
                newName: "IsVersion");

            migrationBuilder.RenameIndex(
                name: "IX_TodoAgendas_IsReadOnly",
                table: "TodoAgendas",
                newName: "IX_TodoAgendas_IsVersion");

            migrationBuilder.RenameColumn(
                name: "IsReadOnly",
                table: "TimeEntries",
                newName: "IsVersion");

            migrationBuilder.RenameIndex(
                name: "IX_TimeEntries_IsReadOnly",
                table: "TimeEntries",
                newName: "IX_TimeEntries_IsVersion");

            migrationBuilder.RenameColumn(
                name: "IsReadOnly",
                table: "TaskRequests",
                newName: "IsVersion");

            migrationBuilder.RenameIndex(
                name: "IX_TaskRequests_IsReadOnly",
                table: "TaskRequests",
                newName: "IX_TaskRequests_IsVersion");

            migrationBuilder.RenameColumn(
                name: "IsReadOnly",
                table: "StatusMasters",
                newName: "IsVersion");

            migrationBuilder.RenameIndex(
                name: "IX_StatusMasters_IsReadOnly",
                table: "StatusMasters",
                newName: "IX_StatusMasters_IsVersion");

            migrationBuilder.RenameColumn(
                name: "IsReadOnly",
                table: "RequestTicketAttachments",
                newName: "IsVersion");

            migrationBuilder.RenameIndex(
                name: "IX_RequestTicketAttachments_IsReadOnly",
                table: "RequestTicketAttachments",
                newName: "IX_RequestTicketAttachments_IsVersion");

            migrationBuilder.RenameColumn(
                name: "IsReadOnly",
                table: "RequestTicketAssignees",
                newName: "IsVersion");

            migrationBuilder.RenameIndex(
                name: "IX_RequestTicketAssignees_IsReadOnly",
                table: "RequestTicketAssignees",
                newName: "IX_RequestTicketAssignees_IsVersion");

            migrationBuilder.RenameColumn(
                name: "IsReadOnly",
                table: "ProjectScopeVersions",
                newName: "IsVersion");

            migrationBuilder.RenameIndex(
                name: "IX_ProjectScopeVersions_IsReadOnly",
                table: "ProjectScopeVersions",
                newName: "IX_ProjectScopeVersions_IsVersion");

            migrationBuilder.RenameColumn(
                name: "IsReadOnly",
                table: "ProjectScopeServices",
                newName: "IsVersion");

            migrationBuilder.RenameIndex(
                name: "IX_ProjectScopeServices_IsReadOnly",
                table: "ProjectScopeServices",
                newName: "IX_ProjectScopeServices_IsVersion");

            migrationBuilder.RenameColumn(
                name: "IsReadOnly",
                table: "ProjectScopeServiceMasters",
                newName: "IsVersion");

            migrationBuilder.RenameIndex(
                name: "IX_ProjectScopeServiceMasters_IsReadOnly",
                table: "ProjectScopeServiceMasters",
                newName: "IX_ProjectScopeServiceMasters_IsVersion");

            migrationBuilder.RenameColumn(
                name: "IsReadOnly",
                table: "ProjectScopes",
                newName: "IsVersion");

            migrationBuilder.RenameIndex(
                name: "IX_ProjectScopes_IsReadOnly",
                table: "ProjectScopes",
                newName: "IX_ProjectScopes_IsVersion");

            migrationBuilder.RenameColumn(
                name: "IsReadOnly",
                table: "Projects",
                newName: "IsVersion");

            migrationBuilder.RenameIndex(
                name: "IX_Projects_IsReadOnly",
                table: "Projects",
                newName: "IX_Projects_IsVersion");

            migrationBuilder.RenameColumn(
                name: "IsReadOnly",
                table: "ProjectOutwards",
                newName: "IsVersion");

            migrationBuilder.RenameIndex(
                name: "IX_ProjectOutwards_IsReadOnly",
                table: "ProjectOutwards",
                newName: "IX_ProjectOutwards_IsVersion");

            migrationBuilder.RenameColumn(
                name: "IsReadOnly",
                table: "ProjectOutwardAttachments",
                newName: "IsVersion");

            migrationBuilder.RenameIndex(
                name: "IX_ProjectOutwardAttachments_IsReadOnly",
                table: "ProjectOutwardAttachments",
                newName: "IX_ProjectOutwardAttachments_IsVersion");

            migrationBuilder.RenameColumn(
                name: "IsReadOnly",
                table: "ProjectNotes",
                newName: "IsVersion");

            migrationBuilder.RenameIndex(
                name: "IX_ProjectNotes_IsReadOnly",
                table: "ProjectNotes",
                newName: "IX_ProjectNotes_IsVersion");

            migrationBuilder.RenameColumn(
                name: "IsReadOnly",
                table: "ProjectInwards",
                newName: "IsVersion");

            migrationBuilder.RenameIndex(
                name: "IX_ProjectInwards_IsReadOnly",
                table: "ProjectInwards",
                newName: "IX_ProjectInwards_IsVersion");

            migrationBuilder.RenameColumn(
                name: "IsReadOnly",
                table: "ProjectInwardAttachments",
                newName: "IsVersion");

            migrationBuilder.RenameIndex(
                name: "IX_ProjectInwardAttachments_IsReadOnly",
                table: "ProjectInwardAttachments",
                newName: "IX_ProjectInwardAttachments_IsVersion");

            migrationBuilder.RenameColumn(
                name: "IsReadOnly",
                table: "ProjectGigPoints",
                newName: "IsVersion");

            migrationBuilder.RenameIndex(
                name: "IX_ProjectGigPoints_IsReadOnly",
                table: "ProjectGigPoints",
                newName: "IX_ProjectGigPoints_IsVersion");

            migrationBuilder.RenameColumn(
                name: "IsReadOnly",
                table: "ProjectConsultants",
                newName: "IsVersion");

            migrationBuilder.RenameIndex(
                name: "IX_ProjectConsultants_IsReadOnly",
                table: "ProjectConsultants",
                newName: "IX_ProjectConsultants_IsVersion");

            migrationBuilder.RenameColumn(
                name: "IsReadOnly",
                table: "ProjectBills",
                newName: "IsVersion");

            migrationBuilder.RenameIndex(
                name: "IX_ProjectBills_IsReadOnly",
                table: "ProjectBills",
                newName: "IX_ProjectBills_IsVersion");

            migrationBuilder.RenameColumn(
                name: "IsReadOnly",
                table: "ProjectBillPayments",
                newName: "IsVersion");

            migrationBuilder.RenameIndex(
                name: "IX_ProjectBillPayments_IsReadOnly",
                table: "ProjectBillPayments",
                newName: "IX_ProjectBillPayments_IsVersion");

            migrationBuilder.RenameColumn(
                name: "IsReadOnly",
                table: "ProjectBillPaymentAttachments",
                newName: "IsVersion");

            migrationBuilder.RenameIndex(
                name: "IX_ProjectBillPaymentAttachments_IsReadOnly",
                table: "ProjectBillPaymentAttachments",
                newName: "IX_ProjectBillPaymentAttachments_IsVersion");

            migrationBuilder.RenameColumn(
                name: "IsReadOnly",
                table: "ProjectAttachments",
                newName: "IsVersion");

            migrationBuilder.RenameIndex(
                name: "IX_ProjectAttachments_IsReadOnly",
                table: "ProjectAttachments",
                newName: "IX_ProjectAttachments_IsVersion");

            migrationBuilder.RenameColumn(
                name: "IsReadOnly",
                table: "ProjectAssociations",
                newName: "IsVersion");

            migrationBuilder.RenameIndex(
                name: "IX_ProjectAssociations_IsReadOnly",
                table: "ProjectAssociations",
                newName: "IX_ProjectAssociations_IsVersion");

            migrationBuilder.RenameColumn(
                name: "IsReadOnly",
                table: "ProjectAreas",
                newName: "IsVersion");

            migrationBuilder.RenameIndex(
                name: "IX_ProjectAreas_IsReadOnly",
                table: "ProjectAreas",
                newName: "IX_ProjectAreas_IsVersion");

            migrationBuilder.RenameColumn(
                name: "IsReadOnly",
                table: "ProcessLibraryEntityAttachments",
                newName: "IsVersion");

            migrationBuilder.RenameIndex(
                name: "IX_ProcessLibraryEntityAttachments_IsReadOnly",
                table: "ProcessLibraryEntityAttachments",
                newName: "IX_ProcessLibraryEntityAttachments_IsVersion");

            migrationBuilder.RenameColumn(
                name: "IsReadOnly",
                table: "ProcessLibraryEntities",
                newName: "IsVersion");

            migrationBuilder.RenameIndex(
                name: "IX_ProcessLibraryEntities_IsReadOnly",
                table: "ProcessLibraryEntities",
                newName: "IX_ProcessLibraryEntities_IsVersion");

            migrationBuilder.RenameColumn(
                name: "IsReadOnly",
                table: "Payrolls",
                newName: "IsVersion");

            migrationBuilder.RenameIndex(
                name: "IX_Payrolls_IsReadOnly",
                table: "Payrolls",
                newName: "IX_Payrolls_IsVersion");

            migrationBuilder.RenameColumn(
                name: "IsReadOnly",
                table: "Packages",
                newName: "IsVersion");

            migrationBuilder.RenameIndex(
                name: "IX_Packages_IsReadOnly",
                table: "Packages",
                newName: "IX_Packages_IsVersion");

            migrationBuilder.RenameColumn(
                name: "IsReadOnly",
                table: "PackageFeedbacks",
                newName: "IsVersion");

            migrationBuilder.RenameIndex(
                name: "IX_PackageFeedbacks_IsReadOnly",
                table: "PackageFeedbacks",
                newName: "IX_PackageFeedbacks_IsVersion");

            migrationBuilder.RenameColumn(
                name: "IsReadOnly",
                table: "PackageFeedbackAttachments",
                newName: "IsVersion");

            migrationBuilder.RenameIndex(
                name: "IX_PackageFeedbackAttachments_IsReadOnly",
                table: "PackageFeedbackAttachments",
                newName: "IX_PackageFeedbackAttachments_IsVersion");

            migrationBuilder.RenameColumn(
                name: "IsReadOnly",
                table: "PackageDesignIntents",
                newName: "IsVersion");

            migrationBuilder.RenameIndex(
                name: "IX_PackageDesignIntents_IsReadOnly",
                table: "PackageDesignIntents",
                newName: "IX_PackageDesignIntents_IsVersion");

            migrationBuilder.RenameColumn(
                name: "IsReadOnly",
                table: "PackageDeliverableTaskMaps",
                newName: "IsVersion");

            migrationBuilder.RenameIndex(
                name: "IX_PackageDeliverableTaskMaps_IsReadOnly",
                table: "PackageDeliverableTaskMaps",
                newName: "IX_PackageDeliverableTaskMaps_IsVersion");

            migrationBuilder.RenameColumn(
                name: "IsReadOnly",
                table: "PackageDeliverables",
                newName: "IsVersion");

            migrationBuilder.RenameIndex(
                name: "IX_PackageDeliverables_IsReadOnly",
                table: "PackageDeliverables",
                newName: "IX_PackageDeliverables_IsVersion");

            migrationBuilder.RenameColumn(
                name: "IsReadOnly",
                table: "PackageDeliverableMasters",
                newName: "IsVersion");

            migrationBuilder.RenameIndex(
                name: "IX_PackageDeliverableMasters_IsReadOnly",
                table: "PackageDeliverableMasters",
                newName: "IX_PackageDeliverableMasters_IsVersion");

            migrationBuilder.RenameColumn(
                name: "IsReadOnly",
                table: "PackageAttachments",
                newName: "IsVersion");

            migrationBuilder.RenameIndex(
                name: "IX_PackageAttachments_IsReadOnly",
                table: "PackageAttachments",
                newName: "IX_PackageAttachments_IsVersion");

            migrationBuilder.RenameColumn(
                name: "IsReadOnly",
                table: "PackageAssociations",
                newName: "IsVersion");

            migrationBuilder.RenameIndex(
                name: "IX_PackageAssociations_IsReadOnly",
                table: "PackageAssociations",
                newName: "IX_PackageAssociations_IsVersion");

            migrationBuilder.RenameColumn(
                name: "IsReadOnly",
                table: "Meetings",
                newName: "IsVersion");

            migrationBuilder.RenameIndex(
                name: "IX_Meetings_IsReadOnly",
                table: "Meetings",
                newName: "IX_Meetings_IsVersion");

            migrationBuilder.RenameColumn(
                name: "IsReadOnly",
                table: "MeetingAttendees",
                newName: "IsVersion");

            migrationBuilder.RenameIndex(
                name: "IX_MeetingAttendees_IsReadOnly",
                table: "MeetingAttendees",
                newName: "IX_MeetingAttendees_IsVersion");

            migrationBuilder.RenameColumn(
                name: "IsReadOnly",
                table: "MeetingAgendas",
                newName: "IsVersion");

            migrationBuilder.RenameIndex(
                name: "IX_MeetingAgendas_IsReadOnly",
                table: "MeetingAgendas",
                newName: "IX_MeetingAgendas_IsVersion");

            migrationBuilder.RenameColumn(
                name: "IsReadOnly",
                table: "MeetingAgendaAttachments",
                newName: "IsVersion");

            migrationBuilder.RenameIndex(
                name: "IX_MeetingAgendaAttachments_IsReadOnly",
                table: "MeetingAgendaAttachments",
                newName: "IX_MeetingAgendaAttachments_IsVersion");

            migrationBuilder.RenameColumn(
                name: "IsReadOnly",
                table: "LoginSessions",
                newName: "IsVersion");

            migrationBuilder.RenameColumn(
                name: "IsReadOnly",
                table: "Loans",
                newName: "IsVersion");

            migrationBuilder.RenameIndex(
                name: "IX_Loans_IsReadOnly",
                table: "Loans",
                newName: "IX_Loans_IsVersion");

            migrationBuilder.RenameColumn(
                name: "IsReadOnly",
                table: "LibraryTitleMasters",
                newName: "IsVersion");

            migrationBuilder.RenameIndex(
                name: "IX_LibraryTitleMasters_IsReadOnly",
                table: "LibraryTitleMasters",
                newName: "IX_LibraryTitleMasters_IsVersion");

            migrationBuilder.RenameColumn(
                name: "IsReadOnly",
                table: "LibraryEntityVendors",
                newName: "IsVersion");

            migrationBuilder.RenameIndex(
                name: "IX_LibraryEntityVendors_IsReadOnly",
                table: "LibraryEntityVendors",
                newName: "IX_LibraryEntityVendors_IsVersion");

            migrationBuilder.RenameColumn(
                name: "IsReadOnly",
                table: "LibraryEntityAttributes",
                newName: "IsVersion");

            migrationBuilder.RenameIndex(
                name: "IX_LibraryEntityAttributes_IsReadOnly",
                table: "LibraryEntityAttributes",
                newName: "IX_LibraryEntityAttributes_IsVersion");

            migrationBuilder.RenameColumn(
                name: "IsReadOnly",
                table: "LibraryEntityAttachments",
                newName: "IsVersion");

            migrationBuilder.RenameIndex(
                name: "IX_LibraryEntityAttachments_IsReadOnly",
                table: "LibraryEntityAttachments",
                newName: "IX_LibraryEntityAttachments_IsVersion");

            migrationBuilder.RenameColumn(
                name: "IsReadOnly",
                table: "LibraryEntities",
                newName: "IsVersion");

            migrationBuilder.RenameIndex(
                name: "IX_LibraryEntities_IsReadOnly",
                table: "LibraryEntities",
                newName: "IX_LibraryEntities_IsVersion");

            migrationBuilder.RenameColumn(
                name: "IsReadOnly",
                table: "LibraryAttributeMasters",
                newName: "IsVersion");

            migrationBuilder.RenameIndex(
                name: "IX_LibraryAttributeMasters_IsReadOnly",
                table: "LibraryAttributeMasters",
                newName: "IX_LibraryAttributeMasters_IsVersion");

            migrationBuilder.RenameColumn(
                name: "IsReadOnly",
                table: "Leaves",
                newName: "IsVersion");

            migrationBuilder.RenameIndex(
                name: "IX_Leaves_IsReadOnly",
                table: "Leaves",
                newName: "IX_Leaves_IsVersion");

            migrationBuilder.RenameColumn(
                name: "IsReadOnly",
                table: "LeaveAttachments",
                newName: "IsVersion");

            migrationBuilder.RenameIndex(
                name: "IX_LeaveAttachments_IsReadOnly",
                table: "LeaveAttachments",
                newName: "IX_LeaveAttachments_IsVersion");

            migrationBuilder.RenameColumn(
                name: "IsReadOnly",
                table: "Inspections",
                newName: "IsVersion");

            migrationBuilder.RenameIndex(
                name: "IX_Inspections_IsReadOnly",
                table: "Inspections",
                newName: "IX_Inspections_IsVersion");

            migrationBuilder.RenameColumn(
                name: "IsReadOnly",
                table: "InspectionRecipients",
                newName: "IsVersion");

            migrationBuilder.RenameIndex(
                name: "IX_InspectionRecipients_IsReadOnly",
                table: "InspectionRecipients",
                newName: "IX_InspectionRecipients_IsVersion");

            migrationBuilder.RenameColumn(
                name: "IsReadOnly",
                table: "InspectionItems",
                newName: "IsVersion");

            migrationBuilder.RenameIndex(
                name: "IX_InspectionItems_IsReadOnly",
                table: "InspectionItems",
                newName: "IX_InspectionItems_IsVersion");

            migrationBuilder.RenameColumn(
                name: "IsReadOnly",
                table: "InspectionItemAttachments",
                newName: "IsVersion");

            migrationBuilder.RenameIndex(
                name: "IX_InspectionItemAttachments_IsReadOnly",
                table: "InspectionItemAttachments",
                newName: "IX_InspectionItemAttachments_IsVersion");

            migrationBuilder.RenameColumn(
                name: "IsReadOnly",
                table: "HolidayMasters",
                newName: "IsVersion");

            migrationBuilder.RenameIndex(
                name: "IX_HolidayMasters_IsReadOnly",
                table: "HolidayMasters",
                newName: "IX_HolidayMasters_IsVersion");

            migrationBuilder.RenameColumn(
                name: "IsReadOnly",
                table: "Habits",
                newName: "IsVersion");

            migrationBuilder.RenameIndex(
                name: "IX_Habits_IsReadOnly",
                table: "Habits",
                newName: "IX_Habits_IsVersion");

            migrationBuilder.RenameColumn(
                name: "IsReadOnly",
                table: "HabitResponses",
                newName: "IsVersion");

            migrationBuilder.RenameIndex(
                name: "IX_HabitResponses_IsReadOnly",
                table: "HabitResponses",
                newName: "IX_HabitResponses_IsVersion");

            migrationBuilder.RenameColumn(
                name: "IsReadOnly",
                table: "HabitAttachments",
                newName: "IsVersion");

            migrationBuilder.RenameIndex(
                name: "IX_HabitAttachments_IsReadOnly",
                table: "HabitAttachments",
                newName: "IX_HabitAttachments_IsVersion");

            migrationBuilder.RenameColumn(
                name: "IsReadOnly",
                table: "Expenses",
                newName: "IsVersion");

            migrationBuilder.RenameIndex(
                name: "IX_Expenses_IsReadOnly",
                table: "Expenses",
                newName: "IX_Expenses_IsVersion");

            migrationBuilder.RenameColumn(
                name: "IsReadOnly",
                table: "ExpenseAttachments",
                newName: "IsVersion");

            migrationBuilder.RenameIndex(
                name: "IX_ExpenseAttachments_IsReadOnly",
                table: "ExpenseAttachments",
                newName: "IX_ExpenseAttachments_IsVersion");

            migrationBuilder.RenameColumn(
                name: "IsReadOnly",
                table: "DesignScriptMeasurements",
                newName: "IsVersion");

            migrationBuilder.RenameIndex(
                name: "IX_DesignScriptMeasurements_IsReadOnly",
                table: "DesignScriptMeasurements",
                newName: "IX_DesignScriptMeasurements_IsVersion");

            migrationBuilder.RenameColumn(
                name: "IsReadOnly",
                table: "DesignScriptMeasurementGroups",
                newName: "IsVersion");

            migrationBuilder.RenameIndex(
                name: "IX_DesignScriptMeasurementGroups_IsReadOnly",
                table: "DesignScriptMeasurementGroups",
                newName: "IX_DesignScriptMeasurementGroups_IsVersion");

            migrationBuilder.RenameColumn(
                name: "IsReadOnly",
                table: "DesignScriptItems",
                newName: "IsVersion");

            migrationBuilder.RenameIndex(
                name: "IX_DesignScriptItems_IsReadOnly",
                table: "DesignScriptItems",
                newName: "IX_DesignScriptItems_IsVersion");

            migrationBuilder.RenameColumn(
                name: "IsReadOnly",
                table: "DesignScriptItemMasters",
                newName: "IsVersion");

            migrationBuilder.RenameIndex(
                name: "IX_DesignScriptItemMasters_IsReadOnly",
                table: "DesignScriptItemMasters",
                newName: "IX_DesignScriptItemMasters_IsVersion");

            migrationBuilder.RenameColumn(
                name: "IsReadOnly",
                table: "DesignScriptEntityItemMaps",
                newName: "IsVersion");

            migrationBuilder.RenameIndex(
                name: "IX_DesignScriptEntityItemMaps_IsReadOnly",
                table: "DesignScriptEntityItemMaps",
                newName: "IX_DesignScriptEntityItemMaps_IsVersion");

            migrationBuilder.RenameColumn(
                name: "IsReadOnly",
                table: "DesignScriptEntities",
                newName: "IsVersion");

            migrationBuilder.RenameIndex(
                name: "IX_DesignScriptEntities_IsReadOnly",
                table: "DesignScriptEntities",
                newName: "IX_DesignScriptEntities_IsVersion");

            migrationBuilder.RenameColumn(
                name: "IsReadOnly",
                table: "DesignScriptDataCards",
                newName: "IsVersion");

            migrationBuilder.RenameIndex(
                name: "IX_DesignScriptDataCards_IsReadOnly",
                table: "DesignScriptDataCards",
                newName: "IX_DesignScriptDataCards_IsVersion");

            migrationBuilder.RenameColumn(
                name: "IsReadOnly",
                table: "DesignScriptDataCardEntityMaps",
                newName: "IsVersion");

            migrationBuilder.RenameIndex(
                name: "IX_DesignScriptDataCardEntityMaps_IsReadOnly",
                table: "DesignScriptDataCardEntityMaps",
                newName: "IX_DesignScriptDataCardEntityMaps_IsVersion");

            migrationBuilder.RenameColumn(
                name: "IsReadOnly",
                table: "DesignScriptDataCardAttributes",
                newName: "IsVersion");

            migrationBuilder.RenameIndex(
                name: "IX_DesignScriptDataCardAttributes_IsReadOnly",
                table: "DesignScriptDataCardAttributes",
                newName: "IX_DesignScriptDataCardAttributes_IsVersion");

            migrationBuilder.RenameColumn(
                name: "IsReadOnly",
                table: "DesignScriptDataCardAttachments",
                newName: "IsVersion");

            migrationBuilder.RenameIndex(
                name: "IX_DesignScriptDataCardAttachments_IsReadOnly",
                table: "DesignScriptDataCardAttachments",
                newName: "IX_DesignScriptDataCardAttachments_IsVersion");

            migrationBuilder.RenameColumn(
                name: "IsReadOnly",
                table: "Contacts",
                newName: "IsVersion");

            migrationBuilder.RenameIndex(
                name: "IX_Contacts_IsReadOnly",
                table: "Contacts",
                newName: "IX_Contacts_IsVersion");

            migrationBuilder.RenameColumn(
                name: "IsReadOnly",
                table: "ContactGroups",
                newName: "IsVersion");

            migrationBuilder.RenameIndex(
                name: "IX_ContactGroups_IsReadOnly",
                table: "ContactGroups",
                newName: "IX_ContactGroups_IsVersion");

            migrationBuilder.RenameColumn(
                name: "IsReadOnly",
                table: "ContactGroupMembers",
                newName: "IsVersion");

            migrationBuilder.RenameIndex(
                name: "IX_ContactGroupMembers_IsReadOnly",
                table: "ContactGroupMembers",
                newName: "IX_ContactGroupMembers_IsVersion");

            migrationBuilder.RenameColumn(
                name: "IsReadOnly",
                table: "ContactAttachments",
                newName: "IsVersion");

            migrationBuilder.RenameIndex(
                name: "IX_ContactAttachments_IsReadOnly",
                table: "ContactAttachments",
                newName: "IX_ContactAttachments_IsVersion");

            migrationBuilder.RenameColumn(
                name: "IsReadOnly",
                table: "ContactAssociations",
                newName: "IsVersion");

            migrationBuilder.RenameIndex(
                name: "IX_ContactAssociations_IsReadOnly",
                table: "ContactAssociations",
                newName: "IX_ContactAssociations_IsVersion");

            migrationBuilder.RenameColumn(
                name: "IsReadOnly",
                table: "ContactAppointments",
                newName: "IsVersion");

            migrationBuilder.RenameIndex(
                name: "IX_ContactAppointments_IsReadOnly",
                table: "ContactAppointments",
                newName: "IX_ContactAppointments_IsVersion");

            migrationBuilder.RenameColumn(
                name: "IsReadOnly",
                table: "ContactAppointmentAttachments",
                newName: "IsVersion");

            migrationBuilder.RenameIndex(
                name: "IX_ContactAppointmentAttachments_IsReadOnly",
                table: "ContactAppointmentAttachments",
                newName: "IX_ContactAppointmentAttachments_IsVersion");

            migrationBuilder.RenameColumn(
                name: "IsReadOnly",
                table: "Companies",
                newName: "IsVersion");

            migrationBuilder.RenameIndex(
                name: "IX_Companies_IsReadOnly",
                table: "Companies",
                newName: "IX_Companies_IsVersion");

            migrationBuilder.RenameColumn(
                name: "IsReadOnly",
                table: "Assessments",
                newName: "IsVersion");

            migrationBuilder.RenameIndex(
                name: "IX_Assessments_IsReadOnly",
                table: "Assessments",
                newName: "IX_Assessments_IsVersion");

            migrationBuilder.RenameColumn(
                name: "IsReadOnly",
                table: "AssessmentMasters",
                newName: "IsVersion");

            migrationBuilder.RenameIndex(
                name: "IX_AssessmentMasters_IsReadOnly",
                table: "AssessmentMasters",
                newName: "IX_AssessmentMasters_IsVersion");

            migrationBuilder.RenameColumn(
                name: "IsReadOnly",
                table: "AppSettingMasters",
                newName: "IsVersion");

            migrationBuilder.RenameIndex(
                name: "IX_AppSettingMasters_IsReadOnly",
                table: "AppSettingMasters",
                newName: "IX_AppSettingMasters_IsVersion");

            migrationBuilder.RenameColumn(
                name: "IsReadOnly",
                table: "Activities",
                newName: "IsVersion");

            migrationBuilder.RenameIndex(
                name: "IX_Activities_IsReadOnly",
                table: "Activities",
                newName: "IX_Activities_IsVersion");

            migrationBuilder.AddColumn<bool>(
                name: "IsVersion",
                table: "RequestTickets",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.CreateIndex(
                name: "IX_RequestTickets_IsVersion",
                table: "RequestTickets",
                column: "IsVersion");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_RequestTickets_IsVersion",
                table: "RequestTickets");

            migrationBuilder.DropColumn(
                name: "IsVersion",
                table: "RequestTickets");

            migrationBuilder.RenameColumn(
                name: "IsVersion",
                table: "WFTasks",
                newName: "IsReadOnly");

            migrationBuilder.RenameIndex(
                name: "IX_WFTasks_IsVersion",
                table: "WFTasks",
                newName: "IX_WFTasks_IsReadOnly");

            migrationBuilder.RenameColumn(
                name: "IsVersion",
                table: "WFTaskAttachments",
                newName: "IsReadOnly");

            migrationBuilder.RenameIndex(
                name: "IX_WFTaskAttachments_IsVersion",
                table: "WFTaskAttachments",
                newName: "IX_WFTaskAttachments_IsReadOnly");

            migrationBuilder.RenameColumn(
                name: "IsVersion",
                table: "WFStages",
                newName: "IsReadOnly");

            migrationBuilder.RenameIndex(
                name: "IX_WFStages_IsVersion",
                table: "WFStages",
                newName: "IX_WFStages_IsReadOnly");

            migrationBuilder.RenameColumn(
                name: "IsVersion",
                table: "WFStageActions",
                newName: "IsReadOnly");

            migrationBuilder.RenameIndex(
                name: "IX_WFStageActions_IsVersion",
                table: "WFStageActions",
                newName: "IX_WFStageActions_IsReadOnly");

            migrationBuilder.RenameColumn(
                name: "IsVersion",
                table: "TypeMasters",
                newName: "IsReadOnly");

            migrationBuilder.RenameIndex(
                name: "IX_TypeMasters_IsVersion",
                table: "TypeMasters",
                newName: "IX_TypeMasters_IsReadOnly");

            migrationBuilder.RenameColumn(
                name: "IsVersion",
                table: "Todos",
                newName: "IsReadOnly");

            migrationBuilder.RenameIndex(
                name: "IX_Todos_IsVersion",
                table: "Todos",
                newName: "IX_Todos_IsReadOnly");

            migrationBuilder.RenameColumn(
                name: "IsVersion",
                table: "TodoAttachments",
                newName: "IsReadOnly");

            migrationBuilder.RenameIndex(
                name: "IX_TodoAttachments_IsVersion",
                table: "TodoAttachments",
                newName: "IX_TodoAttachments_IsReadOnly");

            migrationBuilder.RenameColumn(
                name: "IsVersion",
                table: "TodoAgendas",
                newName: "IsReadOnly");

            migrationBuilder.RenameIndex(
                name: "IX_TodoAgendas_IsVersion",
                table: "TodoAgendas",
                newName: "IX_TodoAgendas_IsReadOnly");

            migrationBuilder.RenameColumn(
                name: "IsVersion",
                table: "TimeEntries",
                newName: "IsReadOnly");

            migrationBuilder.RenameIndex(
                name: "IX_TimeEntries_IsVersion",
                table: "TimeEntries",
                newName: "IX_TimeEntries_IsReadOnly");

            migrationBuilder.RenameColumn(
                name: "IsVersion",
                table: "TaskRequests",
                newName: "IsReadOnly");

            migrationBuilder.RenameIndex(
                name: "IX_TaskRequests_IsVersion",
                table: "TaskRequests",
                newName: "IX_TaskRequests_IsReadOnly");

            migrationBuilder.RenameColumn(
                name: "IsVersion",
                table: "StatusMasters",
                newName: "IsReadOnly");

            migrationBuilder.RenameIndex(
                name: "IX_StatusMasters_IsVersion",
                table: "StatusMasters",
                newName: "IX_StatusMasters_IsReadOnly");

            migrationBuilder.RenameColumn(
                name: "IsVersion",
                table: "RequestTicketAttachments",
                newName: "IsReadOnly");

            migrationBuilder.RenameIndex(
                name: "IX_RequestTicketAttachments_IsVersion",
                table: "RequestTicketAttachments",
                newName: "IX_RequestTicketAttachments_IsReadOnly");

            migrationBuilder.RenameColumn(
                name: "IsVersion",
                table: "RequestTicketAssignees",
                newName: "IsReadOnly");

            migrationBuilder.RenameIndex(
                name: "IX_RequestTicketAssignees_IsVersion",
                table: "RequestTicketAssignees",
                newName: "IX_RequestTicketAssignees_IsReadOnly");

            migrationBuilder.RenameColumn(
                name: "IsVersion",
                table: "ProjectScopeVersions",
                newName: "IsReadOnly");

            migrationBuilder.RenameIndex(
                name: "IX_ProjectScopeVersions_IsVersion",
                table: "ProjectScopeVersions",
                newName: "IX_ProjectScopeVersions_IsReadOnly");

            migrationBuilder.RenameColumn(
                name: "IsVersion",
                table: "ProjectScopeServices",
                newName: "IsReadOnly");

            migrationBuilder.RenameIndex(
                name: "IX_ProjectScopeServices_IsVersion",
                table: "ProjectScopeServices",
                newName: "IX_ProjectScopeServices_IsReadOnly");

            migrationBuilder.RenameColumn(
                name: "IsVersion",
                table: "ProjectScopeServiceMasters",
                newName: "IsReadOnly");

            migrationBuilder.RenameIndex(
                name: "IX_ProjectScopeServiceMasters_IsVersion",
                table: "ProjectScopeServiceMasters",
                newName: "IX_ProjectScopeServiceMasters_IsReadOnly");

            migrationBuilder.RenameColumn(
                name: "IsVersion",
                table: "ProjectScopes",
                newName: "IsReadOnly");

            migrationBuilder.RenameIndex(
                name: "IX_ProjectScopes_IsVersion",
                table: "ProjectScopes",
                newName: "IX_ProjectScopes_IsReadOnly");

            migrationBuilder.RenameColumn(
                name: "IsVersion",
                table: "Projects",
                newName: "IsReadOnly");

            migrationBuilder.RenameIndex(
                name: "IX_Projects_IsVersion",
                table: "Projects",
                newName: "IX_Projects_IsReadOnly");

            migrationBuilder.RenameColumn(
                name: "IsVersion",
                table: "ProjectOutwards",
                newName: "IsReadOnly");

            migrationBuilder.RenameIndex(
                name: "IX_ProjectOutwards_IsVersion",
                table: "ProjectOutwards",
                newName: "IX_ProjectOutwards_IsReadOnly");

            migrationBuilder.RenameColumn(
                name: "IsVersion",
                table: "ProjectOutwardAttachments",
                newName: "IsReadOnly");

            migrationBuilder.RenameIndex(
                name: "IX_ProjectOutwardAttachments_IsVersion",
                table: "ProjectOutwardAttachments",
                newName: "IX_ProjectOutwardAttachments_IsReadOnly");

            migrationBuilder.RenameColumn(
                name: "IsVersion",
                table: "ProjectNotes",
                newName: "IsReadOnly");

            migrationBuilder.RenameIndex(
                name: "IX_ProjectNotes_IsVersion",
                table: "ProjectNotes",
                newName: "IX_ProjectNotes_IsReadOnly");

            migrationBuilder.RenameColumn(
                name: "IsVersion",
                table: "ProjectInwards",
                newName: "IsReadOnly");

            migrationBuilder.RenameIndex(
                name: "IX_ProjectInwards_IsVersion",
                table: "ProjectInwards",
                newName: "IX_ProjectInwards_IsReadOnly");

            migrationBuilder.RenameColumn(
                name: "IsVersion",
                table: "ProjectInwardAttachments",
                newName: "IsReadOnly");

            migrationBuilder.RenameIndex(
                name: "IX_ProjectInwardAttachments_IsVersion",
                table: "ProjectInwardAttachments",
                newName: "IX_ProjectInwardAttachments_IsReadOnly");

            migrationBuilder.RenameColumn(
                name: "IsVersion",
                table: "ProjectGigPoints",
                newName: "IsReadOnly");

            migrationBuilder.RenameIndex(
                name: "IX_ProjectGigPoints_IsVersion",
                table: "ProjectGigPoints",
                newName: "IX_ProjectGigPoints_IsReadOnly");

            migrationBuilder.RenameColumn(
                name: "IsVersion",
                table: "ProjectConsultants",
                newName: "IsReadOnly");

            migrationBuilder.RenameIndex(
                name: "IX_ProjectConsultants_IsVersion",
                table: "ProjectConsultants",
                newName: "IX_ProjectConsultants_IsReadOnly");

            migrationBuilder.RenameColumn(
                name: "IsVersion",
                table: "ProjectBills",
                newName: "IsReadOnly");

            migrationBuilder.RenameIndex(
                name: "IX_ProjectBills_IsVersion",
                table: "ProjectBills",
                newName: "IX_ProjectBills_IsReadOnly");

            migrationBuilder.RenameColumn(
                name: "IsVersion",
                table: "ProjectBillPayments",
                newName: "IsReadOnly");

            migrationBuilder.RenameIndex(
                name: "IX_ProjectBillPayments_IsVersion",
                table: "ProjectBillPayments",
                newName: "IX_ProjectBillPayments_IsReadOnly");

            migrationBuilder.RenameColumn(
                name: "IsVersion",
                table: "ProjectBillPaymentAttachments",
                newName: "IsReadOnly");

            migrationBuilder.RenameIndex(
                name: "IX_ProjectBillPaymentAttachments_IsVersion",
                table: "ProjectBillPaymentAttachments",
                newName: "IX_ProjectBillPaymentAttachments_IsReadOnly");

            migrationBuilder.RenameColumn(
                name: "IsVersion",
                table: "ProjectAttachments",
                newName: "IsReadOnly");

            migrationBuilder.RenameIndex(
                name: "IX_ProjectAttachments_IsVersion",
                table: "ProjectAttachments",
                newName: "IX_ProjectAttachments_IsReadOnly");

            migrationBuilder.RenameColumn(
                name: "IsVersion",
                table: "ProjectAssociations",
                newName: "IsReadOnly");

            migrationBuilder.RenameIndex(
                name: "IX_ProjectAssociations_IsVersion",
                table: "ProjectAssociations",
                newName: "IX_ProjectAssociations_IsReadOnly");

            migrationBuilder.RenameColumn(
                name: "IsVersion",
                table: "ProjectAreas",
                newName: "IsReadOnly");

            migrationBuilder.RenameIndex(
                name: "IX_ProjectAreas_IsVersion",
                table: "ProjectAreas",
                newName: "IX_ProjectAreas_IsReadOnly");

            migrationBuilder.RenameColumn(
                name: "IsVersion",
                table: "ProcessLibraryEntityAttachments",
                newName: "IsReadOnly");

            migrationBuilder.RenameIndex(
                name: "IX_ProcessLibraryEntityAttachments_IsVersion",
                table: "ProcessLibraryEntityAttachments",
                newName: "IX_ProcessLibraryEntityAttachments_IsReadOnly");

            migrationBuilder.RenameColumn(
                name: "IsVersion",
                table: "ProcessLibraryEntities",
                newName: "IsReadOnly");

            migrationBuilder.RenameIndex(
                name: "IX_ProcessLibraryEntities_IsVersion",
                table: "ProcessLibraryEntities",
                newName: "IX_ProcessLibraryEntities_IsReadOnly");

            migrationBuilder.RenameColumn(
                name: "IsVersion",
                table: "Payrolls",
                newName: "IsReadOnly");

            migrationBuilder.RenameIndex(
                name: "IX_Payrolls_IsVersion",
                table: "Payrolls",
                newName: "IX_Payrolls_IsReadOnly");

            migrationBuilder.RenameColumn(
                name: "IsVersion",
                table: "Packages",
                newName: "IsReadOnly");

            migrationBuilder.RenameIndex(
                name: "IX_Packages_IsVersion",
                table: "Packages",
                newName: "IX_Packages_IsReadOnly");

            migrationBuilder.RenameColumn(
                name: "IsVersion",
                table: "PackageFeedbacks",
                newName: "IsReadOnly");

            migrationBuilder.RenameIndex(
                name: "IX_PackageFeedbacks_IsVersion",
                table: "PackageFeedbacks",
                newName: "IX_PackageFeedbacks_IsReadOnly");

            migrationBuilder.RenameColumn(
                name: "IsVersion",
                table: "PackageFeedbackAttachments",
                newName: "IsReadOnly");

            migrationBuilder.RenameIndex(
                name: "IX_PackageFeedbackAttachments_IsVersion",
                table: "PackageFeedbackAttachments",
                newName: "IX_PackageFeedbackAttachments_IsReadOnly");

            migrationBuilder.RenameColumn(
                name: "IsVersion",
                table: "PackageDesignIntents",
                newName: "IsReadOnly");

            migrationBuilder.RenameIndex(
                name: "IX_PackageDesignIntents_IsVersion",
                table: "PackageDesignIntents",
                newName: "IX_PackageDesignIntents_IsReadOnly");

            migrationBuilder.RenameColumn(
                name: "IsVersion",
                table: "PackageDeliverableTaskMaps",
                newName: "IsReadOnly");

            migrationBuilder.RenameIndex(
                name: "IX_PackageDeliverableTaskMaps_IsVersion",
                table: "PackageDeliverableTaskMaps",
                newName: "IX_PackageDeliverableTaskMaps_IsReadOnly");

            migrationBuilder.RenameColumn(
                name: "IsVersion",
                table: "PackageDeliverables",
                newName: "IsReadOnly");

            migrationBuilder.RenameIndex(
                name: "IX_PackageDeliverables_IsVersion",
                table: "PackageDeliverables",
                newName: "IX_PackageDeliverables_IsReadOnly");

            migrationBuilder.RenameColumn(
                name: "IsVersion",
                table: "PackageDeliverableMasters",
                newName: "IsReadOnly");

            migrationBuilder.RenameIndex(
                name: "IX_PackageDeliverableMasters_IsVersion",
                table: "PackageDeliverableMasters",
                newName: "IX_PackageDeliverableMasters_IsReadOnly");

            migrationBuilder.RenameColumn(
                name: "IsVersion",
                table: "PackageAttachments",
                newName: "IsReadOnly");

            migrationBuilder.RenameIndex(
                name: "IX_PackageAttachments_IsVersion",
                table: "PackageAttachments",
                newName: "IX_PackageAttachments_IsReadOnly");

            migrationBuilder.RenameColumn(
                name: "IsVersion",
                table: "PackageAssociations",
                newName: "IsReadOnly");

            migrationBuilder.RenameIndex(
                name: "IX_PackageAssociations_IsVersion",
                table: "PackageAssociations",
                newName: "IX_PackageAssociations_IsReadOnly");

            migrationBuilder.RenameColumn(
                name: "IsVersion",
                table: "Meetings",
                newName: "IsReadOnly");

            migrationBuilder.RenameIndex(
                name: "IX_Meetings_IsVersion",
                table: "Meetings",
                newName: "IX_Meetings_IsReadOnly");

            migrationBuilder.RenameColumn(
                name: "IsVersion",
                table: "MeetingAttendees",
                newName: "IsReadOnly");

            migrationBuilder.RenameIndex(
                name: "IX_MeetingAttendees_IsVersion",
                table: "MeetingAttendees",
                newName: "IX_MeetingAttendees_IsReadOnly");

            migrationBuilder.RenameColumn(
                name: "IsVersion",
                table: "MeetingAgendas",
                newName: "IsReadOnly");

            migrationBuilder.RenameIndex(
                name: "IX_MeetingAgendas_IsVersion",
                table: "MeetingAgendas",
                newName: "IX_MeetingAgendas_IsReadOnly");

            migrationBuilder.RenameColumn(
                name: "IsVersion",
                table: "MeetingAgendaAttachments",
                newName: "IsReadOnly");

            migrationBuilder.RenameIndex(
                name: "IX_MeetingAgendaAttachments_IsVersion",
                table: "MeetingAgendaAttachments",
                newName: "IX_MeetingAgendaAttachments_IsReadOnly");

            migrationBuilder.RenameColumn(
                name: "IsVersion",
                table: "LoginSessions",
                newName: "IsReadOnly");

            migrationBuilder.RenameColumn(
                name: "IsVersion",
                table: "Loans",
                newName: "IsReadOnly");

            migrationBuilder.RenameIndex(
                name: "IX_Loans_IsVersion",
                table: "Loans",
                newName: "IX_Loans_IsReadOnly");

            migrationBuilder.RenameColumn(
                name: "IsVersion",
                table: "LibraryTitleMasters",
                newName: "IsReadOnly");

            migrationBuilder.RenameIndex(
                name: "IX_LibraryTitleMasters_IsVersion",
                table: "LibraryTitleMasters",
                newName: "IX_LibraryTitleMasters_IsReadOnly");

            migrationBuilder.RenameColumn(
                name: "IsVersion",
                table: "LibraryEntityVendors",
                newName: "IsReadOnly");

            migrationBuilder.RenameIndex(
                name: "IX_LibraryEntityVendors_IsVersion",
                table: "LibraryEntityVendors",
                newName: "IX_LibraryEntityVendors_IsReadOnly");

            migrationBuilder.RenameColumn(
                name: "IsVersion",
                table: "LibraryEntityAttributes",
                newName: "IsReadOnly");

            migrationBuilder.RenameIndex(
                name: "IX_LibraryEntityAttributes_IsVersion",
                table: "LibraryEntityAttributes",
                newName: "IX_LibraryEntityAttributes_IsReadOnly");

            migrationBuilder.RenameColumn(
                name: "IsVersion",
                table: "LibraryEntityAttachments",
                newName: "IsReadOnly");

            migrationBuilder.RenameIndex(
                name: "IX_LibraryEntityAttachments_IsVersion",
                table: "LibraryEntityAttachments",
                newName: "IX_LibraryEntityAttachments_IsReadOnly");

            migrationBuilder.RenameColumn(
                name: "IsVersion",
                table: "LibraryEntities",
                newName: "IsReadOnly");

            migrationBuilder.RenameIndex(
                name: "IX_LibraryEntities_IsVersion",
                table: "LibraryEntities",
                newName: "IX_LibraryEntities_IsReadOnly");

            migrationBuilder.RenameColumn(
                name: "IsVersion",
                table: "LibraryAttributeMasters",
                newName: "IsReadOnly");

            migrationBuilder.RenameIndex(
                name: "IX_LibraryAttributeMasters_IsVersion",
                table: "LibraryAttributeMasters",
                newName: "IX_LibraryAttributeMasters_IsReadOnly");

            migrationBuilder.RenameColumn(
                name: "IsVersion",
                table: "Leaves",
                newName: "IsReadOnly");

            migrationBuilder.RenameIndex(
                name: "IX_Leaves_IsVersion",
                table: "Leaves",
                newName: "IX_Leaves_IsReadOnly");

            migrationBuilder.RenameColumn(
                name: "IsVersion",
                table: "LeaveAttachments",
                newName: "IsReadOnly");

            migrationBuilder.RenameIndex(
                name: "IX_LeaveAttachments_IsVersion",
                table: "LeaveAttachments",
                newName: "IX_LeaveAttachments_IsReadOnly");

            migrationBuilder.RenameColumn(
                name: "IsVersion",
                table: "Inspections",
                newName: "IsReadOnly");

            migrationBuilder.RenameIndex(
                name: "IX_Inspections_IsVersion",
                table: "Inspections",
                newName: "IX_Inspections_IsReadOnly");

            migrationBuilder.RenameColumn(
                name: "IsVersion",
                table: "InspectionRecipients",
                newName: "IsReadOnly");

            migrationBuilder.RenameIndex(
                name: "IX_InspectionRecipients_IsVersion",
                table: "InspectionRecipients",
                newName: "IX_InspectionRecipients_IsReadOnly");

            migrationBuilder.RenameColumn(
                name: "IsVersion",
                table: "InspectionItems",
                newName: "IsReadOnly");

            migrationBuilder.RenameIndex(
                name: "IX_InspectionItems_IsVersion",
                table: "InspectionItems",
                newName: "IX_InspectionItems_IsReadOnly");

            migrationBuilder.RenameColumn(
                name: "IsVersion",
                table: "InspectionItemAttachments",
                newName: "IsReadOnly");

            migrationBuilder.RenameIndex(
                name: "IX_InspectionItemAttachments_IsVersion",
                table: "InspectionItemAttachments",
                newName: "IX_InspectionItemAttachments_IsReadOnly");

            migrationBuilder.RenameColumn(
                name: "IsVersion",
                table: "HolidayMasters",
                newName: "IsReadOnly");

            migrationBuilder.RenameIndex(
                name: "IX_HolidayMasters_IsVersion",
                table: "HolidayMasters",
                newName: "IX_HolidayMasters_IsReadOnly");

            migrationBuilder.RenameColumn(
                name: "IsVersion",
                table: "Habits",
                newName: "IsReadOnly");

            migrationBuilder.RenameIndex(
                name: "IX_Habits_IsVersion",
                table: "Habits",
                newName: "IX_Habits_IsReadOnly");

            migrationBuilder.RenameColumn(
                name: "IsVersion",
                table: "HabitResponses",
                newName: "IsReadOnly");

            migrationBuilder.RenameIndex(
                name: "IX_HabitResponses_IsVersion",
                table: "HabitResponses",
                newName: "IX_HabitResponses_IsReadOnly");

            migrationBuilder.RenameColumn(
                name: "IsVersion",
                table: "HabitAttachments",
                newName: "IsReadOnly");

            migrationBuilder.RenameIndex(
                name: "IX_HabitAttachments_IsVersion",
                table: "HabitAttachments",
                newName: "IX_HabitAttachments_IsReadOnly");

            migrationBuilder.RenameColumn(
                name: "IsVersion",
                table: "Expenses",
                newName: "IsReadOnly");

            migrationBuilder.RenameIndex(
                name: "IX_Expenses_IsVersion",
                table: "Expenses",
                newName: "IX_Expenses_IsReadOnly");

            migrationBuilder.RenameColumn(
                name: "IsVersion",
                table: "ExpenseAttachments",
                newName: "IsReadOnly");

            migrationBuilder.RenameIndex(
                name: "IX_ExpenseAttachments_IsVersion",
                table: "ExpenseAttachments",
                newName: "IX_ExpenseAttachments_IsReadOnly");

            migrationBuilder.RenameColumn(
                name: "IsVersion",
                table: "DesignScriptMeasurements",
                newName: "IsReadOnly");

            migrationBuilder.RenameIndex(
                name: "IX_DesignScriptMeasurements_IsVersion",
                table: "DesignScriptMeasurements",
                newName: "IX_DesignScriptMeasurements_IsReadOnly");

            migrationBuilder.RenameColumn(
                name: "IsVersion",
                table: "DesignScriptMeasurementGroups",
                newName: "IsReadOnly");

            migrationBuilder.RenameIndex(
                name: "IX_DesignScriptMeasurementGroups_IsVersion",
                table: "DesignScriptMeasurementGroups",
                newName: "IX_DesignScriptMeasurementGroups_IsReadOnly");

            migrationBuilder.RenameColumn(
                name: "IsVersion",
                table: "DesignScriptItems",
                newName: "IsReadOnly");

            migrationBuilder.RenameIndex(
                name: "IX_DesignScriptItems_IsVersion",
                table: "DesignScriptItems",
                newName: "IX_DesignScriptItems_IsReadOnly");

            migrationBuilder.RenameColumn(
                name: "IsVersion",
                table: "DesignScriptItemMasters",
                newName: "IsReadOnly");

            migrationBuilder.RenameIndex(
                name: "IX_DesignScriptItemMasters_IsVersion",
                table: "DesignScriptItemMasters",
                newName: "IX_DesignScriptItemMasters_IsReadOnly");

            migrationBuilder.RenameColumn(
                name: "IsVersion",
                table: "DesignScriptEntityItemMaps",
                newName: "IsReadOnly");

            migrationBuilder.RenameIndex(
                name: "IX_DesignScriptEntityItemMaps_IsVersion",
                table: "DesignScriptEntityItemMaps",
                newName: "IX_DesignScriptEntityItemMaps_IsReadOnly");

            migrationBuilder.RenameColumn(
                name: "IsVersion",
                table: "DesignScriptEntities",
                newName: "IsReadOnly");

            migrationBuilder.RenameIndex(
                name: "IX_DesignScriptEntities_IsVersion",
                table: "DesignScriptEntities",
                newName: "IX_DesignScriptEntities_IsReadOnly");

            migrationBuilder.RenameColumn(
                name: "IsVersion",
                table: "DesignScriptDataCards",
                newName: "IsReadOnly");

            migrationBuilder.RenameIndex(
                name: "IX_DesignScriptDataCards_IsVersion",
                table: "DesignScriptDataCards",
                newName: "IX_DesignScriptDataCards_IsReadOnly");

            migrationBuilder.RenameColumn(
                name: "IsVersion",
                table: "DesignScriptDataCardEntityMaps",
                newName: "IsReadOnly");

            migrationBuilder.RenameIndex(
                name: "IX_DesignScriptDataCardEntityMaps_IsVersion",
                table: "DesignScriptDataCardEntityMaps",
                newName: "IX_DesignScriptDataCardEntityMaps_IsReadOnly");

            migrationBuilder.RenameColumn(
                name: "IsVersion",
                table: "DesignScriptDataCardAttributes",
                newName: "IsReadOnly");

            migrationBuilder.RenameIndex(
                name: "IX_DesignScriptDataCardAttributes_IsVersion",
                table: "DesignScriptDataCardAttributes",
                newName: "IX_DesignScriptDataCardAttributes_IsReadOnly");

            migrationBuilder.RenameColumn(
                name: "IsVersion",
                table: "DesignScriptDataCardAttachments",
                newName: "IsReadOnly");

            migrationBuilder.RenameIndex(
                name: "IX_DesignScriptDataCardAttachments_IsVersion",
                table: "DesignScriptDataCardAttachments",
                newName: "IX_DesignScriptDataCardAttachments_IsReadOnly");

            migrationBuilder.RenameColumn(
                name: "IsVersion",
                table: "Contacts",
                newName: "IsReadOnly");

            migrationBuilder.RenameIndex(
                name: "IX_Contacts_IsVersion",
                table: "Contacts",
                newName: "IX_Contacts_IsReadOnly");

            migrationBuilder.RenameColumn(
                name: "IsVersion",
                table: "ContactGroups",
                newName: "IsReadOnly");

            migrationBuilder.RenameIndex(
                name: "IX_ContactGroups_IsVersion",
                table: "ContactGroups",
                newName: "IX_ContactGroups_IsReadOnly");

            migrationBuilder.RenameColumn(
                name: "IsVersion",
                table: "ContactGroupMembers",
                newName: "IsReadOnly");

            migrationBuilder.RenameIndex(
                name: "IX_ContactGroupMembers_IsVersion",
                table: "ContactGroupMembers",
                newName: "IX_ContactGroupMembers_IsReadOnly");

            migrationBuilder.RenameColumn(
                name: "IsVersion",
                table: "ContactAttachments",
                newName: "IsReadOnly");

            migrationBuilder.RenameIndex(
                name: "IX_ContactAttachments_IsVersion",
                table: "ContactAttachments",
                newName: "IX_ContactAttachments_IsReadOnly");

            migrationBuilder.RenameColumn(
                name: "IsVersion",
                table: "ContactAssociations",
                newName: "IsReadOnly");

            migrationBuilder.RenameIndex(
                name: "IX_ContactAssociations_IsVersion",
                table: "ContactAssociations",
                newName: "IX_ContactAssociations_IsReadOnly");

            migrationBuilder.RenameColumn(
                name: "IsVersion",
                table: "ContactAppointments",
                newName: "IsReadOnly");

            migrationBuilder.RenameIndex(
                name: "IX_ContactAppointments_IsVersion",
                table: "ContactAppointments",
                newName: "IX_ContactAppointments_IsReadOnly");

            migrationBuilder.RenameColumn(
                name: "IsVersion",
                table: "ContactAppointmentAttachments",
                newName: "IsReadOnly");

            migrationBuilder.RenameIndex(
                name: "IX_ContactAppointmentAttachments_IsVersion",
                table: "ContactAppointmentAttachments",
                newName: "IX_ContactAppointmentAttachments_IsReadOnly");

            migrationBuilder.RenameColumn(
                name: "IsVersion",
                table: "Companies",
                newName: "IsReadOnly");

            migrationBuilder.RenameIndex(
                name: "IX_Companies_IsVersion",
                table: "Companies",
                newName: "IX_Companies_IsReadOnly");

            migrationBuilder.RenameColumn(
                name: "IsVersion",
                table: "Assessments",
                newName: "IsReadOnly");

            migrationBuilder.RenameIndex(
                name: "IX_Assessments_IsVersion",
                table: "Assessments",
                newName: "IX_Assessments_IsReadOnly");

            migrationBuilder.RenameColumn(
                name: "IsVersion",
                table: "AssessmentMasters",
                newName: "IsReadOnly");

            migrationBuilder.RenameIndex(
                name: "IX_AssessmentMasters_IsVersion",
                table: "AssessmentMasters",
                newName: "IX_AssessmentMasters_IsReadOnly");

            migrationBuilder.RenameColumn(
                name: "IsVersion",
                table: "AppSettingMasters",
                newName: "IsReadOnly");

            migrationBuilder.RenameIndex(
                name: "IX_AppSettingMasters_IsVersion",
                table: "AppSettingMasters",
                newName: "IX_AppSettingMasters_IsReadOnly");

            migrationBuilder.RenameColumn(
                name: "IsVersion",
                table: "Activities",
                newName: "IsReadOnly");

            migrationBuilder.RenameIndex(
                name: "IX_Activities_IsVersion",
                table: "Activities",
                newName: "IX_Activities_IsReadOnly");
        }
    }
}
