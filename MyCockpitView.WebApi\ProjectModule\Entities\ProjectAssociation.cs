﻿using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.WebApi.ContactModule.Entities;
using MyCockpitView.CoreModule;
namespace MyCockpitView.WebApi.ProjectModule.Entities;

public class ProjectAssociation : BaseEntity
{

    public string? Title { get; set; }
    public int ProjectID { get; set; }
    public virtual Project? Project { get; set; }
    public int ContactID { get; set; }
    public virtual Contact? Contact { get; set; }
    public decimal ValueHours { get; set; }
    public decimal ValueHourRate { get; set; }
    public decimal ShareValue { get; set; }
}

public class ProjectAssociationConfiguration : BaseEntityConfiguration<ProjectAssociation>, IEntityTypeConfiguration<ProjectAssociation>
{
    public void Configure(EntityTypeBuilder<ProjectAssociation> builder)
    {
        base.Configure(builder);
        // Properties
        builder.Property(pa => pa.Title)
            .HasMaxLength(255);

        builder.Property(pa => pa.ProjectID)
            .IsRequired();

        builder.Property(pa => pa.ContactID)
            .IsRequired();

        // Relationships
        builder.HasOne(pa => pa.Project)
            .WithMany(p => p.Associations)
            .HasForeignKey(pa => pa.ProjectID)
            .IsRequired()
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(pa => pa.Contact)
            .WithMany()
            .HasForeignKey(pa => pa.ContactID)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);
    }
}