﻿using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using MyCockpitView.CoreModule;
using MyCockpitView.Utility.Common;

namespace MyCockpitView.WebApi.ProcessLibraryModule.Entities;

public class ProcessLibraryEntity : BaseEntity
{

    [StringLength(255)]
    public string? Title { get; set; }
    [StringLength(255)]
    public string? Code { get; set; }
    public int CodeFlag { get; set; }


    [Column("Attributes")]
    public string? _attributes { get; set; }

    [NotMapped]
    public virtual ICollection<ProcessLibraryAttribute> Attributes
    {
        get
        {
            return _attributes != null && _attributes != string.Empty ?
                DataTools.GetObjectFromJsonString<ICollection<ProcessLibraryAttribute>>(_attributes) :
                new List<ProcessLibraryAttribute>();
        }
        set
        {
            _attributes = DataTools.GetJsonStringFromObject(value);
        }
    }

    public virtual ICollection<ProcessLibraryEntity> Children { get; set; }= new List<ProcessLibraryEntity>();
    public virtual ICollection<ProcessLibraryEntityAttachment> Attachments { get; set; }= new List<ProcessLibraryEntityAttachment>();

    public bool IsReadOnly { get; set; }

}
public class ProcessLibraryAttribute
{
    public string AttributeKey { get; set; }
    public string AttributeValue { get; set; }
    public int Order { get; set; } = 0;

}
public class ProcessLibraryEntityConfiguration : BaseEntityConfiguration<ProcessLibraryEntity>, IEntityTypeConfiguration<ProcessLibraryEntity>
{
    public void Configure(EntityTypeBuilder<ProcessLibraryEntity> builder)
    {
     base.Configure(builder);

        builder.HasIndex(x => x.IsVersion);
        builder.HasIndex(x => x.Title);
        builder.HasIndex(x => x.Code);
        builder.HasIndex(x => x.CodeFlag);
      


    }
}
