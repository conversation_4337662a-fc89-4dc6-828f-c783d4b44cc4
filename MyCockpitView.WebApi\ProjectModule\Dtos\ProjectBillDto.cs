﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.ProjectModule.Entities;
using System.ComponentModel.DataAnnotations;

namespace MyCockpitView.WebApi.ProjectModule.Dtos;

public class ProjectBillDto : BaseEntityDto
{
    public string? IRNNo { get; set; }
    public string? QRCode { get; set; }
    public bool IsPreDated { get; set; }
    [Required]
    public int ProjectID { get; set; }
    public string? ProjectCode { get; set; }
    public string? ProjectTitle { get; set; }
    public string? ProjectLocation { get; set; }
    public int? ProjectWorkOrderID { get; set; }
    public DateTime? WorkOrderDate { get; set; }
    public string? WorkOrderNo { get; set; }
    public int? CompanyID { get; set; }
    public int? ClientContactID { get; set; }

    [StringLength(255)]
    public string? ProformaInvoiceNo { get; set; }

    [StringLength(255)]
    public string? TaxInvoiceNo { get; set; }

    //[StringLength(255)]
    //public string? SequenceNo { get; set; }
    public bool IsIGSTApplicable { get; set; }
    public bool IsLumpSump { get; set; }

    [Precision(18, 2)] public decimal ProjectFee { get; set; } = 0.0m;
    [Precision(18, 2)] public decimal BillPercentage { get; set; } = 0.0m;


    [Precision(18, 2)] public decimal WorkPercentage { get; set; } = 0.0m;

    public DateTime ProformaDate { get; set; }
    public DateTime BillDate { get; set; }
    [Precision(18, 2)]
    public decimal BillAmount { get; set; } = 0.0m;
    [Precision(18, 2)]
    public decimal PreviousBillAmount { get; set; } = 0.0m;
    [Precision(18, 2)]
    public decimal DueAmount { get; set; } = 0.0m;
    [Precision(18, 2)]
    public decimal IGSTShare { get; set; } = 0.0m;
    [Precision(18, 2)]
    public decimal IGSTAmount { get; set; } = 0.0m;
    [Precision(18, 2)]
    public decimal CGSTShare { get; set; } = 0.0m;
    [Precision(18, 2)]
    public decimal CGSTAmount { get; set; } = 0.0m;
    [Precision(18, 2)]
    public decimal SGSTShare { get; set; } = 0.0m;
    [Precision(18, 2)]
    public decimal SGSTAmount { get; set; } = 0.0m;
    [Precision(18, 2)]
    public decimal PayableAmount { get; set; } = 0.0m;
    public string? ReverseTaxCharges { get; set; }
    public string? AmountInWords { get; set; }

    [StringLength(255)]
    public string? HSN { get; set; }

    [StringLength(255)]
    public string? ClientName { get; set; }

    public string? ClientAddress { get; set; }

    [StringLength(255)]
    public string? ClientGSTIN { get; set; }

    [StringLength(255)]
    public string? ClientPAN { get; set; }
    [StringLength(255)]
    public string? ClientTAN { get; set; }

    [StringLength(255)]
    public string? ClientGSTStateCode { get; set; }


    [StringLength(255)]
    public string? CompanyName { get; set; }

    public string? CompanyAddress { get; set; }

    [StringLength(255)]
    public string? CompanyGSTIN { get; set; }
    [StringLength(255)]
    public string? CompanyPAN { get; set; }
    [StringLength(255)]
    public string? CompanyTAN { get; set; }
    [StringLength(255)]
    public string? CompanyUDHYAM { get; set; }

    [StringLength(255)]
    public string? CompanyGSTStateCode { get; set; }
    public string? CompanyLogoUrl { get; set; }
    [StringLength(255)]
    public string? CompanyBank { get; set; }
    public string? CompanyBankBranch { get; set; }
    [StringLength(255)]
    public string? CompanyBankIFSCCode { get; set; }
    [StringLength(255)]
    public string? CompanySwiftCode { get; set; }
    [StringLength(255)]
    public string? CompanyBankAccount { get; set; }
    public string? CompanySignStampUrl { get; set; }
    public string? ProformaInvoiceUrl { get; set; }
    public string? TaxInvoiceUrl { get; set; }

    public List<BillStage> Stages { get; set; } = new List<BillStage>();
    public virtual ICollection<ProjectBillPaymentDto> Payments { get; set; } = new HashSet<ProjectBillPaymentDto>();
  
    public string? BillNo { get; set; }
    public decimal WorkCompletion { get; set; }
    public DateTime? BillConversionDate { get; set; }
    public decimal Amount { get; set; }
    public decimal Tax { get; set; }
    public decimal TaxRate { get; set; }
    public string? TaxSplit { get; set; }
    public decimal GSTShare { get; set; }
    public decimal GSTAmount { get; set; }
    public int GSTFlag { get; set; }
    public decimal TDSAmount { get; set; }
    public decimal Payable { get; set; }
    public string? ClientEmail { get; set; }
    public string? ClientPhone { get; set; }
    public string? AttendantName { get; set; }
    public string? AttendantEmail { get; set; }
    public string? AttendantPhone { get; set; }
    public int? AttendantContactID { get; set; }
    public string? WorkDetails { get; set; }
    public string? ClientHSN { get; set; }
    public string? Remarks { get; set; }

    public decimal PendingPayment { get; set; }

    public decimal RecievedPayment { get; set; }

    public decimal ChequeAmount { get; set; }

    public string? BlobUrl { get; set; }
}

public class ProjectBillDtoMapperProfile : Profile
{
    public ProjectBillDtoMapperProfile()
    {
        CreateMap<ProjectBill, ProjectBillDto>()
      .ReverseMap()
       .ForMember(dest => dest.Project, opt => opt.Ignore())
          .ForMember(dest => dest.Payments, opt => opt.Ignore());

    }
}