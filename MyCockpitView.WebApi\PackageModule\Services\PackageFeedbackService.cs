﻿



using MyCockpitView.WebApi.Services;
using MyCockpitView.WebApi.PackageModule.Entities;
using MyCockpitView.CoreModule;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.Utility.Common;
using MyCockpitView.WebApi.Exceptions;
using System.Text.RegularExpressions;
using System.Text;
using MyCockpitView.WebApi.RequestTicketModule.Entities;
using MyCockpitView.WebApi.PackageModule.Dtos;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json;

namespace MyCockpitView.WebApi.PackageModule.Services;
public class PackageFeedbackService : BaseEntityService<PackageFeedback>, IPackageFeedbackService
{
    public PackageFeedbackService(EntitiesContext db) : base(db)
    {
    }

    public IQueryable<PackageFeedback> Get(IEnumerable<QueryFilter>? Filters = null, string? Search = null, string? Sort = null)
    {

        IQueryable<PackageFeedback> _query = base.Get(Filters);

        //Apply filters
        if (Filters != null)
        {


            if (Filters.Where(x => x.Key.Equals("PackageID", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<PackageFeedback>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("PackageID", StringComparison.OrdinalIgnoreCase)))
                {
                    var isNumeric = Convert.ToInt32(_item.Value);

                    predicate = predicate.Or(x => x.PackageID == isNumeric);
                }
                _query = _query.Where(predicate);
            }

          
        }


        return _query;

    }

    public async Task<int> Create(PackageFeedback Entity)
    {
        if (Entity.PackageID == 0)
        {
            throw new Exception("PackageID is required");
        }

        if (Entity.Rating == 0)
        {
            throw new Exception("Rating is required");
        }

        if (string.IsNullOrEmpty(Entity.ReviewerEmail) || !DataTools.IsEmailValid(Entity.ReviewerEmail))
        {
            throw new Exception("Email is required");
        }

        var package = await db.Packages
            .Where(x => !x.IsDeleted)
            .AsNoTracking()
            .Include(x => x.Associations).ThenInclude(c => c.Contact)
            .SingleOrDefaultAsync(x => x.ID == Entity.PackageID);
        if (package == null)
        {
            throw new EntityServiceException("Package not found");
        }

        var contact = await db.Contacts
            .Where(x => !x.IsDeleted)
            .AsNoTracking()
            .SingleOrDefaultAsync(x => x.Email1 == Entity.ReviewerEmail || x.Email2 == Entity.ReviewerEmail);
        if (contact != null)
            Entity.ReviewerContactID = contact.ID;

        Entity.PackageTitle = package.Title;
        Entity.PackagePartnerName = package.Associations.Where(x => !x.IsDeleted).Any(x => x.TypeFlag == McvConstant.PACKAGE_ASSOCIATION_TYPEFLAG_PARTNER) ? package.Associations.Where(x => !x.IsDeleted).FirstOrDefault(x => x.TypeFlag == McvConstant.PACKAGE_ASSOCIATION_TYPEFLAG_PARTNER).Contact.FullName : null;
        Entity.PackagePartnerContactID = package.Associations.Where(x => !x.IsDeleted).Any(x => x.TypeFlag == McvConstant.PACKAGE_ASSOCIATION_TYPEFLAG_PARTNER) ? package.Associations.Where(x => !x.IsDeleted).FirstOrDefault(x => x.TypeFlag == McvConstant.PACKAGE_ASSOCIATION_TYPEFLAG_PARTNER).ContactID : (int?)null;
        Entity.PackagePartnerEmail = package.Associations.Where(x => !x.IsDeleted).Any(x => x.TypeFlag == McvConstant.PACKAGE_ASSOCIATION_TYPEFLAG_PARTNER) ? package.Associations.Where(x => !x.IsDeleted).FirstOrDefault(x => x.TypeFlag == McvConstant.PACKAGE_ASSOCIATION_TYPEFLAG_PARTNER).Contact.Email1 : null;

        Entity.PackageAssociateName = package.Associations.Where(x => !x.IsDeleted).Any(x => x.TypeFlag == McvConstant.PACKAGE_ASSOCIATION_TYPEFLAG_ASSOCIATE) ? package.Associations.Where(x => !x.IsDeleted).FirstOrDefault(x => x.TypeFlag == McvConstant.PACKAGE_ASSOCIATION_TYPEFLAG_ASSOCIATE).Contact.FullName : null;
        Entity.PackageAssociateContactID = package.Associations.Where(x => !x.IsDeleted).Any(x => x.TypeFlag == McvConstant.PACKAGE_ASSOCIATION_TYPEFLAG_ASSOCIATE) ? package.Associations.Where(x => !x.IsDeleted).FirstOrDefault(x => x.TypeFlag == McvConstant.PACKAGE_ASSOCIATION_TYPEFLAG_ASSOCIATE).ContactID : (int?)null;
        Entity.PackageAssociateEmail = package.Associations.Where(x => !x.IsDeleted).Any(x => x.TypeFlag == McvConstant.PACKAGE_ASSOCIATION_TYPEFLAG_ASSOCIATE) ? package.Associations.Where(x => !x.IsDeleted).FirstOrDefault(x => x.TypeFlag == McvConstant.PACKAGE_ASSOCIATION_TYPEFLAG_ASSOCIATE).Contact.Email1 : null;


        return await base.Create(Entity);
    }


    public async Task SendEmail(PackageFeedback feedback,IEnumerable<PackageFeedbackFile>? files=null)
    {


        var sharedService = new SharedService(db); ;
        var _emailSenderName = await sharedService.GetPresetValue(McvConstant.PACKAGE_FEEDBACK_EMAIL_SENDER_NAME);
        var _emailSenderAddress = await sharedService.GetPresetValue(McvConstant.PACKAGE_FEEDBACK_EMAIL_SENDER_ID);
        var _defaultCCList = await sharedService.GetPresetValue(McvConstant.PACKAGE_FEEDBACK_EMAIL_CC);
        var starsHtml = new string('★', feedback.Rating).Replace("★", "&#9733;");

        var _emailSubject = $"Your Feedback Has Been Received for Submission {feedback.PackageTitle}";

        var _emailMessage = $"Dear {feedback.ReviewerName},\r\n\r\nThank you for taking the time to review and share your feedback on the submission. We truly appreciate your insights and suggestions, which help us refine and enhance our work to better meet your expectations.\r\n\r\nOur team will carefully review your inputs and incorporate necessary revisions. If any clarifications are needed, we will reach out to you. Please feel free to share any additional thoughts you may have.\r\n\r\nWe value your collaboration and look forward to delivering a design that aligns with your vision.\r\n\r\n ";



        var emailTo = new List<(string name, string email)>();
            emailTo.Add((feedback.ReviewerName, feedback.ReviewerEmail));

        var emailCC = new List<(string name, string email)>();
            

        if (!string.IsNullOrEmpty(feedback.PackagePartnerEmail) && DataTools.IsEmailValid(feedback.PackagePartnerEmail))
            emailCC.Add((feedback.PackagePartnerName, feedback.PackagePartnerEmail));

        if (!string.IsNullOrEmpty(feedback.PackageAssociateEmail) && DataTools.IsEmailValid(feedback.PackageAssociateEmail))
            emailCC.Add((feedback.PackageAssociateName, feedback.PackageAssociateEmail));


        //default CC
        if (!string.IsNullOrWhiteSpace(_defaultCCList))
        {
            try
            {
                var jArray = JArray.Parse(_defaultCCList);
                foreach (var item in jArray)
                {
                    var name = item["name"]?.ToString().Trim();
                    var email = item["email"]?.ToString().Trim();

                    if (!string.IsNullOrWhiteSpace(email) &&
                        !emailTo.Any(a => a.email.Equals(email, StringComparison.OrdinalIgnoreCase)) &&
                        !emailCC.Any(a => a.email.Equals(email, StringComparison.OrdinalIgnoreCase)))
                    {
                        emailCC.Add((name, email));
                    }
                }
            }
            catch (JsonException ex)
            {
                Console.WriteLine($"JSON parsing error: {ex.Message}");
            }
        }


        var _replyToEmail = feedback.PackagePartnerEmail;
        var _replyParameter = $"mailto:{_replyToEmail}?cc=";

        foreach (var email in emailTo)
        {
            if (!email.email.Equals(_replyToEmail))
                _replyParameter = _replyParameter + email.email + ";";
        }
        foreach (var email in emailCC)
        {
            if (!email.email.Equals(_replyToEmail))
                _replyParameter = _replyParameter + email.email + ";";
        }

        _replyParameter = _replyParameter + $"&subject=RE:{_emailSubject.ToUpper()}";

        var sb = new StringBuilder();

        sb.AppendLine("<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Transitional//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd\">");
        sb.AppendLine("<html xmlns=\"http://www.w3.org/1999/xhtml\">");
        sb.AppendLine("<head>");
        sb.AppendLine("    <meta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\" />");
        sb.AppendLine("    <title>Email Design</title>");
        sb.AppendLine("    <meta name=\"viewport\" content=\"width=device-width; initial-scale=1.0;\" />");
        sb.AppendLine("    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=9; IE=8; IE=7; IE=EDGE\" />");
        sb.AppendLine("    <meta name=\"format-detection\" content=\"telephone=no\" />");
        sb.AppendLine("    <!--[if gte mso 9]><xml>");
        sb.AppendLine("    <o:OfficeDocumentSettings>");
        sb.AppendLine("    <o:AllowPNG />");
        sb.AppendLine("    <o:PixelsPerInch>96</o:PixelsPerInch>");
        sb.AppendLine("    </o:OfficeDocumentSettings>");
        sb.AppendLine("    </xml><![endif]-->");
        sb.AppendLine("    <style type=\"text/css\">");
        sb.AppendLine("        /* Some resets and issue fixes */");
        sb.AppendLine("        #outlook a {");
        sb.AppendLine("            padding: 0;");
        sb.AppendLine("        }");
        sb.AppendLine("");
        sb.AppendLine("        body {");
        sb.AppendLine("            width: 100% !important;");
        sb.AppendLine("            margin: 0;");
        sb.AppendLine("            -webkit-text-size-adjust: 100%;");
        sb.AppendLine("            -ms-text-size-adjust: 100%;");
        sb.AppendLine("        }");
        sb.AppendLine("");
        sb.AppendLine("        table {");
        sb.AppendLine("            mso-table-lspace: 0px;");
        sb.AppendLine("            mso-table-rspace: 0px;");
        sb.AppendLine("        }");
        sb.AppendLine("");
        sb.AppendLine("            table td {");
        sb.AppendLine("                border-collapse: collapse;");
        sb.AppendLine("            }");
        sb.AppendLine("");
        sb.AppendLine("        .ExternalClass * {");
        sb.AppendLine("            line-height: 115%;");
        sb.AppendLine("        }");
        sb.AppendLine("        /* End reset */");

        sb.AppendLine("    </style>");
        sb.AppendLine("</head>");
        sb.AppendLine("");
        sb.AppendLine("<body>");
        sb.AppendLine("");
        sb.AppendLine("");
        sb.AppendLine("    <div style=\"margin: 0 auto;font-family:Calibri;font-size:14px;line-height:1.8;padding-left:5px;padding-right:5px; max-width:500px;\">");
        sb.AppendLine("");
        sb.AppendLine("        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; border-collapse: collapse;\">");
        sb.AppendLine("            <tr>");
        sb.AppendLine("                <td style=\"font-size:16px; font-weight:bold;\">");
        sb.AppendLine(_emailSubject.ToUpper());
        sb.AppendLine("                </td>");
        sb.AppendLine("            </tr>");
        sb.AppendLine("        </table>");
        sb.AppendLine("");
        sb.AppendLine("<div style=\"border-bottom: 1px solid #cccccc; margin-top: 15px; margin-bottom: 15px; \"></div>");

        sb.AppendLine("");
        sb.AppendLine("        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; border-collapse: collapse;\">");
        sb.AppendLine("            <tr>");
        sb.AppendLine("                <td valign=\"top\" style=\"padding-top:5px;padding-bottom:5px;\">");
        sb.AppendLine("                    <pre style=\"font-family:Calibri;line-height:1.5; font-size: 14px;margin-top:0;margin-bottom:0; white-space: pre-wrap; white-space: -moz-pre-wrap;");
        sb.AppendLine("                                white-space: -pre-wrap; white-space: -o-pre-wrap; word-wrap: break-word;\">");

        sb.AppendLine(_emailMessage);

        sb.AppendLine("</pre>");
        sb.AppendLine("                </td>");
        sb.AppendLine("            </tr>");
        sb.AppendLine("            <tr>");
        sb.AppendLine("                <td valign=\"top\" style=\"padding-top:5px;padding-bottom:5px;\">");
        sb.AppendLine("<span style=\"font-size:20px;font-weight:bold;color:#545454\">Your Response:</span>");

        sb.AppendLine("                </td>");
        sb.AppendLine("            </tr>");
        sb.AppendLine("            <tr>");
        sb.AppendLine("                <td valign=\"top\" style=\"padding-top:5px;padding-bottom:5px;\">");
        sb.AppendLine($"Rating: <b style=\"color:#ffe100;font-size:24px;\">\r\n{starsHtml}</b>");

        sb.AppendLine("                </td>");
        sb.AppendLine("            </tr>");
        sb.AppendLine("            <tr>");
        sb.AppendLine("                <td valign=\"top\" style=\"padding-top:5px;padding-bottom:5px;\">");
        sb.AppendLine("Comment: \r\n");
        sb.AppendLine("                    <pre style=\"font-family:Calibri;line-height:1.5; font-size: 14px;font-weight:600; margin-top:0;margin-bottom:0; white-space: pre-wrap; white-space: -moz-pre-wrap;white-space: -pre-wrap; white-space: -o-pre-wrap; word-wrap: break-word;\">");
        sb.AppendLine($"{feedback.Comment} \r\n\r\n");
        sb.AppendLine("</pre>");
        sb.AppendLine("                </td>");
        sb.AppendLine("            </tr>");

        if (feedback.Attachments.Any())
        {

            sb.AppendLine("            <tr>");
            sb.AppendLine("                 <td valign=\"top\" style=\"font-weight:bold;\">");
            sb.AppendLine(" Kindly click the below links to download: ");
            sb.AppendLine("                </td>");
            sb.AppendLine("            </tr>");

            var _index = 1;
            foreach (var attachment in feedback.Attachments)
            {
                sb.AppendLine("            <tr>");
                sb.AppendLine("                <td valign=\"top\" style=\"padding-bottom:5px;\">");
                sb.AppendLine("                    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; border-collapse: collapse;\">");

                sb.AppendLine("            <tr>");
                sb.AppendLine("                <td >");
                sb.AppendLine(_index.ToString("D2"));
                sb.AppendLine("                </td>");
                sb.AppendLine("                <td  style=\"font-size:16px;\">");
                sb.AppendLine($"                    <a href=\"{attachment.Url}\">{attachment.Filename}</a>");

                sb.AppendLine("                </td>");
                sb.AppendLine("            </tr>");
                sb.AppendLine("                    </table>");
                sb.AppendLine("                </td>");
                sb.AppendLine("            </tr>");
                _index++;
            }
        }

        sb.AppendLine("        </table>");
        sb.AppendLine("");


        //FOOTER
        sb.AppendLine("<div style=\"border-bottom: 1px solid #cccccc; margin-top: 15px; margin-bottom: 15px; \"></div>");
        sb.AppendLine("        <table align=\"center\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; border-collapse: collapse;font-size:11px;\">");

        sb.AppendLine("");
        sb.AppendLine("            <tr>");
        sb.AppendLine("");
        sb.AppendLine("                <td align=\"center\" >");
        sb.AppendLine("This is a <b>MyCockpitView<sup>&copy;</sup></b> & <b>DesignScript<sup>&copy;</sup></b> generated e-mail for your information and necessary action.");
        sb.AppendLine("</td>");
        sb.AppendLine("            </tr>");
        sb.AppendLine("");
        sb.AppendLine("            <tr>");
        sb.AppendLine("");
        sb.AppendLine("                <td align=\"center\" >");
        sb.AppendLine("");
        sb.AppendLine("                    Powered by <b>Newarch<sup>&reg;</sup> Infotech LLP</b>");
        sb.AppendLine("");
        sb.AppendLine("                </td>");
        sb.AppendLine("            </tr>");
        sb.AppendLine("        </table>");
        sb.AppendLine("    </div>");

        sb.AppendLine("</body>");
        sb.AppendLine("");
        sb.AppendLine("</html>");

        var mailbody = sb.ToString();

        await sharedService.SendMail(
                _emailSubject.ToUpper(),
                _emailSenderName,
                _emailSenderAddress,
                sb.ToString(),
                emailTo,emailCC, attachments: files?.Select(x => (x.FileName, x.Base64Content)).ToList());


    }

    public async Task<PackageFeedback?> GetById(int Id)
    {

        return await db.PackageFeedbacks.AsNoTracking()
        .Include(x => x.Attachments)
            .SingleOrDefaultAsync(x => x.ID == Id);

    }
    public async Task<PackageFeedback?> GetById(Guid Id)
    {

        return await db.PackageFeedbacks.AsNoTracking()
        .Include(x => x.Attachments)
            .SingleOrDefaultAsync(x => x.UID == Id);

    }
}

public class PackageFeedbackFile
{

    public string? FileName { get; set; }
    public string? ContentType { get; set; }
    public string? Base64Content { get; set; }
}