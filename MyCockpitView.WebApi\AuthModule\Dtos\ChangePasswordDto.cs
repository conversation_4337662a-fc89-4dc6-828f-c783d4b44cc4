﻿

using System.ComponentModel.DataAnnotations;

namespace MyCockpitView.WebApi.AuthModule.Dtos
{
    public class ChangePasswordDto
    {
        [Display(Name = "Username")]
        public string? Username { get; set; }

        [DataType(DataType.Password)]
        [Display(Name = "Current password")]
        public string? OldPassword { get; set; }


        [StringLength(100, ErrorMessage = "The {0} must be at least {2} characters int.", MinimumLength = 6)]
        [DataType(DataType.Password)]
        [Display(Name = "New password")]
        public string? NewPassword { get; set; }

        [DataType(DataType.Password)]
        [Display(Name = "Confirm new password")]
        [Compare("NewPassword", ErrorMessage = "The new password and confirmation password do not match.")]
        public string? ConfirmPassword { get; set; }
    }
}