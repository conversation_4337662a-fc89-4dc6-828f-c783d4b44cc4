﻿using AutoMapper;
using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.DesignScriptModule.Entities;

namespace MyCockpitView.WebApi.DesignScriptModule.Dtos;

public class DesignScriptDataCardAttributeDto : BaseEntityDto
{
    public int DesignScriptDataCardID { get; set; }
    public string? AttributeKey { get; set; }
    public string? AttributeValue { get; set; }
    public bool IsHidden { get; set; }

}

public class DesignScriptDataCardAttributeDtoMapperProfile : Profile
{
    public DesignScriptDataCardAttributeDtoMapperProfile()
    {

        CreateMap<DesignScriptDataCardAttribute, DesignScriptDataCardAttributeDto>()
      .ReverseMap();
    }
}
