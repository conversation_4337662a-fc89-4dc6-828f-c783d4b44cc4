﻿
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using MyCockpitView.CoreModule;
using MyCockpitView.Utility.Common;
using MyCockpitView.WebApi.ContactModule.Entities;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace MyCockpitView.WebApi.PayrollModule.Entities;

public class Payroll : BaseEntity
{

    [Required]
    public int PersonContactID { get; set; }

    
    [StringLength(50)]
    public string? PersonName { get; set; }

    
    [StringLength(50)]
    public string? PersonBankAccountNo { get; set; }

    [Required]
    public int AppointmentID { get; set; }
    public virtual ContactAppointment Appointment { get; set; }

    [StringLength(50)]
    public string? AppointmentType { get; set; }

    [StringLength(50)]
    public string? Designation { get; set; }

    [Required]
    public int CompanyID { get; set; }

    
    [StringLength(50)]
    public string? CompanyName { get; set; }

    
    [StringLength(50)]
    public string? CompanyBankAccountNo { get; set; }

    
    [Column(TypeName = "datetime2")]
    public DateTime StartDate { get; set; }

    
    [Column(TypeName = "datetime2")]
    public DateTime EndDate { get; set; }

    public decimal ManValue { get; set; }
    public decimal VHrRate { get; set; }
    public decimal VHrAssessed { get; set; }
    public decimal VHrExpected { get; set; } //=> Math.Ceiling(190 * ManValue);
    public decimal VHrAssessedAmount { get; set; } //=> VHrAssessed * VHrRate;
    public decimal VHrExpectedAmount { get; set; } //=> VHrExpected * VHrRate;
    public decimal VHrExceeded { get; set; } //=>  VHrAssessed - VHrExpected;
    public decimal VHrExceededAmount { get; set; } //=> VHrDifference* VHrRate;
    public decimal LastBite { get; set; } = 0;
    public decimal RemunerationAmount { get; set; }
    public decimal BasicPayAmount { get; set; }
    public decimal HRAAmount { get; set; }
    public decimal SpecialAllowanceAmount { get; set; }
    public decimal PreTaxAdditionAmount { get; set; } //=> PreTaxAdditions.Any()?PreTaxAdditions.Sum(c => c.Amount):0;
    public decimal GrossPayAmount { get; set; } //=> VHrAssessedAmount + PreTaxAdditionAmount;
    public decimal TaxAmount { get; set; } //=> Taxes.Any() ? Taxes.Sum(c => c.Amount) : 0;
    public decimal NetRemuneration { get; set; } //=> GrossPay - TaxAmount;
    public decimal PostTaxAdditionAmount { get; set; } //=> PostTaxAdditions.Any() ? PostTaxAdditions.Sum(c => c.Amount) : 0;
    public decimal DeductionAmount { get; set; } //=> Deductions.Any() ? Deductions.Sum(c => c.Amount) : 0;
    public decimal NetPayAmount { get; set; } //=> GrossPayAmount - TaxAmount - DeductionAmount + PostTaxAdditionAmount;

    public string? LeaveCycle { get; set; }

    public decimal LeaveApprovedLeave { get; set; }

    public decimal LeaveEmergencyLeave { get; set; }

    public decimal LeaveLate { get; set; }

    public decimal LeavePenalty { get; set; }

    public decimal LeaveApprovedBreak { get; set; }

    public decimal LeaveApprovedHalfDay { get; set; }

    public decimal LeaveApprovedWFH { get; set; }


    public decimal LeaveEmergencyBreak { get; set; }

    public decimal LeaveEmergencyHalfDay { get; set; }

    public decimal LeaveEmergencyWFH { get; set; }


    public decimal LeaveTotal { get; set; }

    public decimal LeaveAllowed { get; set; }

    public decimal LeaveBalance { get; set; }

    public decimal LeaveAllowedEmergency { get; set; }

    public decimal LeaveEmergencyBalance { get; set; }


    [Column("PreTaxAdditions")]
    public string? _preTaxAdditions { get; set; }

    [NotMapped]
    public ICollection<PayrollFragment> PreTaxAdditions
    {
        get
        {
            return _preTaxAdditions != null && _preTaxAdditions != string.Empty ?
                DataTools.GetObjectFromJsonString<ICollection<PayrollFragment>>(_preTaxAdditions) :
                new List<PayrollFragment>();
        }
        set
        {
            _preTaxAdditions = value.Count != 0 ? DataTools.GetJsonStringFromObject(value) : null;
        }
    }

    [Column("PostTaxAdditions")]
    public string? _postTaxAdditions { get; set; }

    [NotMapped]
    public ICollection<PayrollFragment> PostTaxAdditions
    {
        get
        {
            return _postTaxAdditions != null && _postTaxAdditions != string.Empty ?
                DataTools.GetObjectFromJsonString<ICollection<PayrollFragment>>(_postTaxAdditions) :
                new List<PayrollFragment>();
        }
        set
        {
            _postTaxAdditions = value.Count != 0 ? DataTools.GetJsonStringFromObject(value) : null;
        }
    }

    [Column("Taxes")]
    public string? _taxes { get; set; }

    [NotMapped]
    public ICollection<PayrollFragment> Taxes
    {
        get
        {
            return _taxes != null && _taxes != string.Empty ?
                DataTools.GetObjectFromJsonString<ICollection<PayrollFragment>>(_taxes) :
                new List<PayrollFragment>();
        }
        set
        {
            _taxes = value.Count != 0 ? DataTools.GetJsonStringFromObject(value) : null;
        }
    }

    [Column("Deductions")]
    public string? _deductions { get; set; }

    [NotMapped]
    public ICollection<PayrollFragment> Deductions
    {
        get
        {
            return _deductions != null && _deductions != string.Empty ?
                DataTools.GetObjectFromJsonString<ICollection<PayrollFragment>>(_deductions) :
                new List<PayrollFragment>();
        }
        set
        {
            _deductions = value.Count != 0 ? DataTools.GetJsonStringFromObject(value) : null;
        }
    }

    [Column("Expenses")]
    public string? _expenses { get; set; }

    [NotMapped]
    public ICollection<PayrollFragment> Expenses
    {
        get
        {
            return _expenses != null && _expenses != string.Empty ?
                DataTools.GetObjectFromJsonString<ICollection<PayrollFragment>>(_expenses) :
                new List<PayrollFragment>();
        }
        set
        {
            _expenses = value.Count != 0 ? DataTools.GetJsonStringFromObject(value) : null;
        }
    }
}

public class PayrollFragment
{
    public string?  Title { get; set; }
    [Precision(14, 2)]
    public decimal Amount { get; set; } = 0;
    public string?  Remark { get; set; }
    public int? ExpenseID { get; set; }
}


public class PayrollConfiguration : BaseEntityConfiguration<Payroll>, IEntityTypeConfiguration<Payroll>
{
    public void Configure(EntityTypeBuilder<Payroll> builder)
    {

        base.Configure(builder);

        builder.HasIndex(x => x.PersonContactID);
        builder.HasIndex(x => x.PersonName);
        builder.HasIndex(x => x.StartDate);
        builder.HasIndex(x => x.PersonBankAccountNo);
        builder.HasIndex(x => x.AppointmentID);
        builder.HasIndex(x => x.CompanyName);
        builder.HasIndex(x => x.CompanyBankAccountNo);
        builder.HasIndex(x => x.EndDate);
    }

}
