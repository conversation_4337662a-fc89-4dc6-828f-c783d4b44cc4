﻿
using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.Services;
using MyCockpitView.WebApi.WFTaskModule.Entities;


using System.Data;


namespace MyCockpitView.WebApi.WFTaskModule.Services;

public class TaskRequestService : BaseEntityService<TaskRequest>, ITaskRequestService
{
    public TaskRequestService(EntitiesContext db) : base(db) { }

    public IQueryable<TaskRequest> Get(IEnumerable<QueryFilter>? Filters = null, string? Search = null, string? Sort = null)
    {

        IQueryable<TaskRequest> _query = base.Get(Filters)
            ;

        //Apply filters
        if (Filters != null)
        {


            if (Filters.Where(x => x.Key.Equals("TaskID", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<TaskRequest>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("TaskID", StringComparison.OrdinalIgnoreCase)))
                {
                    var isNumeric = Convert.ToInt32(_item.Value);

                    predicate = predicate.Or(x => x.WFTaskID == isNumeric);
                }
                _query = _query.Where(predicate);
            }

        }

        if (Search != null && Search != string.Empty)
        {
            var _keywords = Search.Split(' ');

            foreach (var _key in _keywords)
            {
                _query = _query
          .Where(x => x._searchTags.ToLower().Contains(_key.ToLower())
                    );
            }
        }

        if (Sort != null && Sort != string.Empty)
        {
            var _orderedQuery = _query.OrderBy(l => 0);
            var keywords = Sort.Replace("asc", "").Split(',');

            foreach (var key in keywords)
            {
                if (key.Trim().Equals("created", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenBy(x => x.Created);

                else if (key.Trim().Equals("created desc", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenByDescending(x => x.Created);

                else if (key.Trim().Equals("modified", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenBy(x => x.Modified);

                else if (key.Trim().Equals("modified desc", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenByDescending(x => x.Modified);

            }

            return _orderedQuery;
        }

        return _query
                      .OrderByDescending(x => x.Created);


    }


}