﻿
using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.WebApi.Responses;
using MyCockpitView.WebApi.ContactModule.Dtos;
using MyCockpitView.WebApi.ContactModule.Entities;
using MyCockpitView.WebApi.ContactModule.Services;
using MyCockpitView.Utility.Common;
using MyCockpitView.WebApi.ActivityModule;
using MyCockpitView.CoreModule;
using System.Diagnostics.Contracts;

namespace MyCockpitView.WebApi.ContactModule.Controllers;

[Authorize]
[ApiController]
[Route("[controller]")]
public class ContactAssociationController : ControllerBase
{
    private readonly IContactAssociationService service;
    private readonly EntitiesContext db;
    private readonly IActivityService activityService;
    private readonly IMapper mapper;
    private readonly IContactService contactService;

    public ContactAssociationController(EntitiesContext db, IContactAssociationService service, IMapper mapper, IActivityService activityService, IContactService contactService   )
    {
        this.db = db;
        this.service = service;
        this.activityService = activityService;
        this.mapper = mapper;
        this.contactService = contactService;
    }

    [HttpGet]
    public async Task<ActionResult<IEnumerable<ContactAssociationDto>>> Get(
        [FromQuery] string? filters = null,
        [FromQuery] string? search = null,
        [FromQuery] string? sort = null)
    {
        var query = service.Get(filters != null ? DataTools.GetObjectFromJsonString<APIFilter>(filters).Filters : null, search, sort);
        var results = mapper.Map<IEnumerable<ContactAssociationDto>>(await query.ToListAsync());

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(ContactAssociation))
            .ToListAsync();

        foreach (var obj in results)
        {
            obj.Type = typeMasters.FirstOrDefault(x => x.Value == obj.TypeFlag)?.Title ?? "";
        }

        var statusMasters = await db.StatusMasters
          .AsNoTracking()
          .Where(x => x.Entity == nameof(ContactAssociation))
          .ToListAsync();

        foreach (var obj in results)
        {
            obj.Status = statusMasters.FirstOrDefault(x => x.Value == obj.StatusFlag)?.Title ?? "";
        }

        return Ok(results);
    }

    [HttpGet("Pages")]
    public async Task<ActionResult<PagedResponse<ContactAssociationDto>>> GetPages(
        [FromQuery] int page = 0,
        [FromQuery] int pageSize = 50,
        [FromQuery] string? filters = null,
        [FromQuery] string? search = null,
        [FromQuery] string? sort = null)
    {
        var query = service.Get(filters != null ? DataTools.GetObjectFromJsonString<APIFilter>(filters).Filters : null, search, sort);

        var totalCount = await query.CountAsync();
        var totalPages = (int)Math.Ceiling((decimal)totalCount / pageSize);
        var results = mapper.Map<IEnumerable<ContactAssociationDto>>(await query
            .Skip(pageSize * page)
            .Take(pageSize)
            .ToListAsync());

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(ContactAssociation))
            .ToListAsync();

        foreach (var obj in results)
        {
            obj.Type = typeMasters.FirstOrDefault(x => x.Value == obj.TypeFlag)?.Title ?? "";
        }

        var statusMasters = await db.StatusMasters
           .AsNoTracking()
           .Where(x => x.Entity == nameof(ContactAssociation))
           .ToListAsync();

        foreach (var obj in results)
        {
            obj.Status = statusMasters.FirstOrDefault(x => x.Value == obj.StatusFlag)?.Title ?? "";
        }

        return Ok(new PagedResponse<ContactAssociationDto>(results, totalCount, totalPages));
    }

    [HttpGet("{id:int}")]
    public async Task<ActionResult<ContactAssociationDto>> GetByID(int id)
    {
        var responseDto = mapper.Map<ContactAssociationDto>(await service.GetById(id));
        if (responseDto == null)
        {
            return NotFound();
        }

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(ContactAssociation))
            .ToListAsync();

        responseDto.Type = typeMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        var statusMasters = await db.StatusMasters
          .AsNoTracking()
          .Where(x => x.Entity == nameof(ContactAssociation))
          .ToListAsync();

        responseDto.Status = statusMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        return Ok(responseDto);
    }

    [HttpPost]
    public async Task<ActionResult<ContactAssociationDto>> Post(ContactAssociationDto dto)
    {
        var id = await service.Create(mapper.Map<ContactAssociation>(dto));
        var responseDto = mapper.Map<ContactAssociationDto>(await service.GetById(id));

        if (responseDto == null)
            return BadRequest("Not Created");

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(ContactAssociation))
            .ToListAsync();

        responseDto.Type = typeMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        var statusMasters = await db.StatusMasters
  .AsNoTracking()
  .Where(x => x.Entity == nameof(ContactAssociation))
  .ToListAsync();

        responseDto.Status = statusMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        if (!string.IsNullOrEmpty(User.Identity?.Name))
        {
            var currentContact = await contactService.Get().FirstOrDefaultAsync(x => x.Username == User.Identity.Name);
            if (currentContact != null)
            {
                    await activityService.LogUserActivity(currentContact, nameof(Contact), responseDto.PersonContactID, $"{responseDto.Person.Name}", $"{nameof(ContactAssociation).Replace(nameof(Contact), "")} with Company | {responseDto.Company.Name}", "Created");

                await activityService.LogUserActivity(currentContact, nameof(Contact), responseDto.CompanyContactID, $"{responseDto.Company.Name}", $"{nameof(ContactAssociation).Replace(nameof(Contact), "")} with Person | {responseDto.Person.Name}", "Created");
            }
        }
        return CreatedAtAction(nameof(GetByID), new { id = responseDto.ID }, responseDto);
    }

    [HttpPut("{id:int}")]
    public async Task<ActionResult<ContactAssociationDto>> Put(int id, ContactAssociationDto dto)
    {
        if (id != dto.ID)
        {
            return BadRequest();
        }

        await service.Update(mapper.Map<ContactAssociation>(dto));
        var responseDto = mapper.Map<ContactAssociationDto>(await service.GetById(id));

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(ContactAssociation))
            .ToListAsync();

        responseDto.Type = typeMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        var statusMasters = await db.StatusMasters
  .AsNoTracking()
  .Where(x => x.Entity == nameof(ContactAssociation))
  .ToListAsync();

        responseDto.Status = statusMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        if (!string.IsNullOrEmpty(User.Identity?.Name))
        {
            var currentContact = await contactService.Get().FirstOrDefaultAsync(x => x.Username == User.Identity.Name);
            if (currentContact != null)
            {
                await activityService.LogUserActivity(currentContact, nameof(Contact), responseDto.PersonContactID, $"{responseDto.Person.Name}", $"{nameof(ContactAssociation).Replace(nameof(Contact), "")} with Company | {responseDto.Company.Name}", "Updated");

                await activityService.LogUserActivity(currentContact, nameof(Contact), responseDto.CompanyContactID, $"{responseDto.Company.Name}", $"{nameof(ContactAssociation).Replace(nameof(Contact), "")} with Person | {responseDto.Person.Name}", "Updated");
            }
        }

        return Ok(responseDto);
    }

    [HttpDelete("{id:int}")]
    public async Task<IActionResult> Delete(int id)
    {
        var responseDto = mapper.Map<ContactAssociationDto>(await service.GetById(id));
        if (responseDto == null) return BadRequest($"{nameof(ContactAssociation)} not found!");

        await service.Delete(id);

        if (!string.IsNullOrEmpty(User.Identity?.Name))
        {
            var currentContact = await contactService.Get().FirstOrDefaultAsync(x => x.Username == User.Identity.Name);
            if (currentContact != null)
            {
                await activityService.LogUserActivity(currentContact, nameof(Contact), responseDto.PersonContactID, $"{responseDto.Person.Name}", $"{nameof(ContactAssociation).Replace(nameof(Contact), "")} with Company | {responseDto.Company.Name}", "Deleted");

                await activityService.LogUserActivity(currentContact, nameof(Contact), responseDto.CompanyContactID, $"{responseDto.Company.Name}", $"{nameof(ContactAssociation).Replace(nameof(Contact), "")} with Person | {responseDto.Person.Name}", "Deleted");
            }
        }

        return NoContent();
    }


    [HttpGet("uid/{id:guid}")]
    [AllowAnonymous]
    public async Task<ActionResult<ContactAssociationDto>> GetByGUID(Guid id)
    {
        var responseDto = mapper.Map<ContactAssociationDto>(await service.GetById(id));
        if (responseDto == null)
        {
            return NotFound();
        }

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(ContactAssociation))
            .ToListAsync();

        responseDto.Type = typeMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        var statusMasters = await db.StatusMasters
                        .AsNoTracking()
                        .Where(x => x.Entity == nameof(ContactAssociation))
                        .ToListAsync();

        responseDto.Status = statusMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        return Ok(responseDto);
    }


    [HttpGet("SearchTagOptions")]
    public async Task<IActionResult> GetSearchTagOptions()
    {
        var results = await service.GetSearchTagOptions();
        return Ok(results);
    }


    [HttpGet("FieldOptions")]
    public async Task<IActionResult> GetFieldOptions(string field)
    {
        return Ok(await service.GetFieldOptions(field));
    }


}