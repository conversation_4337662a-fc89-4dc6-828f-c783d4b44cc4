﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.CompanyModule.Entities;
using System.ComponentModel.DataAnnotations;

namespace MyCockpitView.WebApi.CompanyModule.Dtos;

public class CompanyDto : BaseEntityDto
{
    public string? Title { get; set; }
    public string? Initials { get; set; }
    
    public string? GSTIN { get; set; }
    
    public string? GSTStateCode { get; set; }
    
    public string? PAN { get; set; }
    
    public string? TAN { get; set; }
    
    public string? UDHYAM { get; set; }
    public string? LogoUrl { get; set; }
    
    public string? Bank { get; set; }
    public string? BankBranch { get; set; }
    
    public string? BankIFSCCode { get; set; }
    
    public string? SwiftCode { get; set; }
    
    public string? BankAccount { get; set; }
    public string? Address { get; set; }
    public string? SignStampUrl { get; set; }
    public decimal VHrRate { get; set; } = 0.0m;

}
public class CompanyMapperProfile : Profile
{
    public CompanyMapperProfile()
    {

        CreateMap<Company, CompanyDto>()
        .ReverseMap();
    }
}