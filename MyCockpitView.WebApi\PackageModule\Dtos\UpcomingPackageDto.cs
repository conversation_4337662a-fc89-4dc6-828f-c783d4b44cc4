﻿namespace MyCockpitView.WebApi.PackageModule.Dtos;

public class UpcomingPackageDto
{
    public Guid ID { get { return Guid.NewGuid(); } }
    public string?  Company { get; set; }
    public string?  Project { get; set; }
    public string?  Status { get; set; }
    public string?  Partner { get; set; }
    public string?  Associate { get; set; }
    public string?  Client { get; set; }
    public decimal Fee { get; set; } = 0;
    public string?  Scope { get; set; }
    public string?  Phase { get; set; }
    public string?  Service { get; set; }

    public int? CompanyID { get; set; }
    public int? PartnerContactID { get; set; }
    public int? AssociateContactID { get; set; }
}
