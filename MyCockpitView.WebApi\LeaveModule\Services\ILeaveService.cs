﻿using MyCockpitView.WebApi.LeaveModule.Entities;
using MyCockpitView.WebApi.Services;
using MyCockpitView.WebApi.WFTaskModule.Entities;

namespace MyCockpitView.WebApi.LeaveModule.Services
{
    public interface ILeaveService: IBaseEntityService<Leave>
    {
        Task<LeaveSummary> GetMonthSummary(int contactID, int year, int month);
        Task<IEnumerable<LeaveSummary>> GetPerMonthSummary(int contactID, int index = 0);
        Task<IEnumerable<Leave>> GetSplitLeave(Leave Entity);
        Task<decimal> GetTotalLeaveDurationIST(DateTime StartIST, DateTime EndIST);
        Task<LeaveSummary> GetTotalSummary(int contactID, int index = 0);
        Task LeaveStage2(WFTask task);
        Task ValidateApplication(Leave Entity, bool IsSelfApplied);
        Task ValidateOverlapp(Leave Entity);
    }
}