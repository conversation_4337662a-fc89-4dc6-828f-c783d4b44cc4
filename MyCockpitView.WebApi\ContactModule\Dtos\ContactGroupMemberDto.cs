﻿using AutoMapper;
using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.ContactModule.Entities;

namespace MyCockpitView.WebApi.ContactModule.Dtos;

public class ContactGroupMemberDto : BaseEntityDto
{
    public int ContactGroupID { get; set; }
    public int ContactID { get; set; }
    public virtual ContactListDto? Contact { get; set; }
    public bool IsLeader { get; set; }
    public bool IsAssistantLeader { get; set; }
}

public class ContactGroupMemberMapperProfile : Profile
{
    public ContactGroupMemberMapperProfile()
    {

        CreateMap<ContactGroupMember, ContactGroupMemberDto>()
         .ReverseMap()
                    .ForMember(dest => dest.ContactGroup, opt => opt.Ignore())
           .ForMember(dest => dest.Contact, opt => opt.Ignore());

    }
}
