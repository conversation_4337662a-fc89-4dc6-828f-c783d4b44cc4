﻿
using AutoMapper;
using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.PayrollModule.Entities;

namespace MyCockpitView.WebApi.PayrollModule.Dtos;


public class PayrollListDto : BaseEntityDto
{
    public int PersonContactID { get; set; }

    public string? PersonName { get; set; }


    public string? AppointmentType { get; set; }


    public string? CompanyName { get; set; }


    public DateTime StartDate { get; set; }

    public decimal NetPayAmount { get; set; } //=> GrossPayAmount - TaxAmount - DeductionAmount + PostTaxAdditionAmount;
}

public class PayrollDto : PayrollListDto
{



    public string? PersonBankAccountNo { get; set; }


    public int AppointmentID { get; set; }




    public string? Designation { get; set; }



    public int CompanyID { get; set; }




    public string? CompanyBankAccountNo { get; set; }




    public DateTime EndDate { get; set; }
    public decimal ManValue { get; set; }
    public decimal VHrRate { get; set; }
    public decimal VHrAssessed { get; set; }
    public decimal VHrExpected { get; set; } //=> Math.Ceiling(190 * ManValue);
    public decimal VHrAssessedAmount { get; set; } //=> VHrAssessed * VHrRate;
    public decimal VHrExpectedAmount { get; set; } //=> VHrExpected * VHrRate;
    public decimal VHrExceeded { get; set; } //=>  VHrAssessed - VHrExpected;
    public decimal VHrExceededAmount { get; set; } //=> VHrDifference* VHrRate;
    public decimal LastBite { get; set; } = 0;
    public decimal RemunerationAmount { get; set; }
    public decimal BasicPayAmount { get; set; }
    public decimal HRAAmount { get; set; }
    public decimal SpecialAllowanceAmount { get; set; }
    public decimal PreTaxAdditionAmount { get; set; } //=> PreTaxAdditions.Any()?PreTaxAdditions.Sum(c => c.Amount):0;
    public decimal GrossPayAmount { get; set; } //=> VHrAssessedAmount + PreTaxAdditionAmount;
    public decimal TaxAmount { get; set; } //=> Taxes.Any() ? Taxes.Sum(c => c.Amount) : 0;
    public decimal NetRemuneration { get; set; } //=> GrossPay - TaxAmount;
    public decimal PostTaxAdditionAmount { get; set; } //=> PostTaxAdditions.Any() ? PostTaxAdditions.Sum(c => c.Amount) : 0;
    public decimal DeductionAmount { get; set; } //=> Deductions.Any() ? Deductions.Sum(c => c.Amount) : 0;
    public string? LeaveCycle { get; set; }
    public decimal LeaveApprovedLeave { get; set; }
    public decimal LeaveEmergencyLeave { get; set; }
    public decimal LeaveLate { get; set; }
    public decimal LeavePenalty { get; set; }
    public decimal LeaveApprovedBreak { get; set; }
    public decimal LeaveApprovedHalfDay { get; set; }
    public decimal LeaveApprovedWFH { get; set; }
    public decimal LeaveEmergencyBreak { get; set; }
    public decimal LeaveEmergencyHalfDay { get; set; }
    public decimal LeaveEmergencyWFH { get; set; }
    public decimal LeaveTotal { get; set; }
    public decimal LeaveAllowed { get; set; }
    public decimal LeaveBalance { get; set; }
    public decimal LeaveAllowedEmergency { get; set; }
    public decimal LeaveEmergencyBalance { get; set; }
    public ICollection<PayrollFragment> PreTaxAdditions { get; set; }
    public ICollection<PayrollFragment> PostTaxAdditions { get; set; }
    public ICollection<PayrollFragment> Taxes { get; set; }
    public ICollection<PayrollFragment> Deductions { get; set; }
    public ICollection<PayrollFragment> Expenses { get; set; }
}
public class PayrollDtoMapperProfile : Profile
{
    public PayrollDtoMapperProfile()
    {
        CreateMap<Payroll, PayrollDto>()
            .ReverseMap();
    }
}
