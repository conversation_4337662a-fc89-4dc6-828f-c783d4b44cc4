﻿
using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.WebApi.Responses;
using MyCockpitView.WebApi.ContactModule.Dtos;
using MyCockpitView.WebApi.ContactModule.Entities;
using MyCockpitView.WebApi.ContactModule.Services;
using MyCockpitView.Utility.Common;
using MyCockpitView.WebApi.ActivityModule;
using MyCockpitView.CoreModule;

namespace MyCockpitView.WebApi.ContactModule.Controllers;

[Authorize]
[ApiController]
[Route("[controller]")]
public class ContactController : ControllerBase
{
    private readonly IContactService service;
    private readonly EntitiesContext db;
    private readonly IActivityService activityService;
    private readonly IMapper mapper;

    public ContactController(
        EntitiesContext db,
        IContactService service,
        IMapper mapper,
        IActivityService activityService)
    {
        this.db = db;
        this.service = service;
        this.activityService = activityService;
        this.mapper = mapper;
    }

    [HttpGet]
    public async Task<ActionResult<IEnumerable<ContactListDto>>> Get(
        [FromQuery] string? filters = null,
        [FromQuery] string? search = null,
        [FromQuery] string? sort = null)
    {
        var query = service.Get(filters != null ? DataTools.GetObjectFromJsonString<APIFilter>(filters).Filters : null, search, sort);
        var results = mapper.Map<IEnumerable<ContactListDto>>(await query.ToListAsync());

        //var typeMasters = await db.TypeMasters
        //    .AsNoTracking()
        //    .Where(x => x.Entity == nameof(Contact))
        //    .ToListAsync();

        //foreach (var obj in results)
        //{
        //    obj.TypeValue = typeMasters.FirstOrDefault(x => x.Value == obj.TypeFlag)?.Title ?? "";
        //}

        //var statusMasters = await db.StatusMasters
        //  .AsNoTracking()
        //  .Where(x => x.Entity == nameof(Contact))
        //  .ToListAsync();

        //foreach (var obj in results)
        //{
        //    obj.StatusValue = statusMasters.FirstOrDefault(x => x.Value == obj.StatusFlag)?.Title ?? "";
        //}

        return Ok(results);
    }

    [HttpGet("Pages")]
    public async Task<ActionResult<PagedResponse<ContactListDto>>> GetPages(
        [FromQuery] int page = 0,
        [FromQuery] int pageSize = 50,
        [FromQuery] string? filters = null,
        [FromQuery] string? search = null,
        [FromQuery] string? sort = null)
    {
        var query = service.Get(filters != null ? DataTools.GetObjectFromJsonString<APIFilter>(filters).Filters : null, search, sort);

        var totalCount = await query.CountAsync();
        var totalPages = (int)Math.Ceiling((decimal)totalCount / pageSize);
        var results = mapper.Map<IEnumerable<ContactListDto>>(await query
            .Skip(pageSize * page)
            .Take(pageSize)
            .ToListAsync());

        //var typeMasters = await db.TypeMasters
        //    .AsNoTracking()
        //    .Where(x => x.Entity == nameof(Contact))
        //    .ToListAsync();

        //foreach (var obj in results)
        //{
        //    obj.TypeValue = typeMasters.FirstOrDefault(x => x.Value == obj.TypeFlag)?.Title ?? "";
        //}

        //var statusMasters = await db.StatusMasters
        //   .AsNoTracking()
        //   .Where(x => x.Entity == nameof(Contact))
        //   .ToListAsync();

        //foreach (var obj in results)
        //{
        //    obj.StatusValue = statusMasters.FirstOrDefault(x => x.Value == obj.StatusFlag)?.Title ?? "";
        //}

        foreach (var obj in results.Where(x => x.TypeFlag == McvConstant.CONTACT_TYPEFLAG_APPOINTED))
        {
            var activeAppointments = await db.ContactAppointments
                .AsNoTracking()
                
                .Where(x => x.StatusFlag == McvConstant.APPOINTMENT_STATUSFLAG_APPOINTED)
                .Where(x => x.ContactID == obj.ID)
                .OrderByDescending(x => x.JoiningDate)
                .ToListAsync();

            obj.Appointments = mapper.Map<IEnumerable<ContactAppointmentDto>>(activeAppointments);
        }

        return Ok(new PagedResponse<ContactListDto>(results, totalCount, totalPages));
    }

    [HttpGet("{id:int}")]
    public async Task<ActionResult<ContactDto>> GetByID(int id)
    {
        var responseDto = mapper.Map<ContactDto>(await service.GetById(id));
        if (responseDto == null)
        {
            return NotFound();
        }

        //var typeMasters = await db.TypeMasters
        //    .AsNoTracking()
        //    .Where(x => x.Entity == nameof(Contact))
        //    .ToListAsync();

        //responseDto.TypeValue = typeMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        //var statusMasters = await db.StatusMasters
        //  .AsNoTracking()
        //  .Where(x => x.Entity == nameof(Contact))
        //  .ToListAsync();

        //responseDto.StatusValue = statusMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        return Ok(responseDto);
    }

    [HttpPost]
    public async Task<ActionResult<ContactDto>> Post(ContactDto dto)
    {
        var id = await service.Create(mapper.Map<Contact>(dto));
        var responseDto = mapper.Map<ContactDto>(await service.GetById(id));

        if (responseDto == null)
            return BadRequest("Not Created");

  //      var typeMasters = await db.TypeMasters
  //          .AsNoTracking()
  //          .Where(x => x.Entity == nameof(Contact))
  //          .ToListAsync();

  //      responseDto.TypeValue = typeMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

  //      var statusMasters = await db.StatusMasters
  //.AsNoTracking()
  //.Where(x => x.Entity == nameof(Contact))
  //.ToListAsync();

  //      responseDto.StatusValue = statusMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        if (!string.IsNullOrEmpty(User.Identity?.Name))
        {
            var currentContact = await service.Get().FirstOrDefaultAsync(x => x.Username == User.Identity.Name);
            if (currentContact != null)
            {
                await activityService.LogUserActivity(
                    currentContact,
                    nameof(Contact),
                    responseDto.ID,
                    $"{responseDto.Name}",
                    $"{nameof(Contact)} | {responseDto.Name}",
                    "Created");
            }
        }

        return CreatedAtAction(nameof(GetByID), new { id = responseDto.ID }, responseDto);
    }

    [HttpPut("{id:int}")]
    public async Task<ActionResult<ContactDto>> Put(int id, ContactDto dto)
    {
        if (id != dto.ID)
        {
            return BadRequest();
        }

        await service.Update(mapper.Map<Contact>(dto));
        var responseDto = mapper.Map<ContactDto>(await service.GetById(id));

  //      var typeMasters = await db.TypeMasters
  //          .AsNoTracking()
  //          .Where(x => x.Entity == nameof(Contact))
  //          .ToListAsync();

  //      responseDto.TypeValue = typeMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

  //      var statusMasters = await db.StatusMasters
  //.AsNoTracking()
  //.Where(x => x.Entity == nameof(Contact))
  //.ToListAsync();

  //      responseDto.StatusValue = statusMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        if (!string.IsNullOrEmpty(User.Identity?.Name))
        {
            var currentContact = await service.Get().FirstOrDefaultAsync(x => x.Username == User.Identity.Name);
            if (currentContact != null)
            {
                await activityService.LogUserActivity(
                    currentContact,
                    nameof(Contact),
                    responseDto.ID,
                    $"{responseDto.FullName}",
                    $"{nameof(Contact)} | {responseDto.FullName}",
                    "Updated");
            }
        }

        return Ok(responseDto);
    }

    [HttpDelete("{id:int}")]
    public async Task<IActionResult> Delete(int id)
    {
        var responseDto = mapper.Map<ContactDto>(await service.GetById(id));
        if (responseDto == null) return BadRequest($"{nameof(Contact)} not found!");

        await service.Delete(id);

        if (!string.IsNullOrEmpty(User.Identity?.Name))
        {
            var currentContact = await service.Get().FirstOrDefaultAsync(x => x.Username == User.Identity.Name);
            if (currentContact != null)
            {
                await activityService.LogUserActivity(
                    currentContact,
                    nameof(Contact),
                    responseDto.ID,
                    $"{responseDto.FullName}",
                    $"{nameof(Contact)} | {responseDto.FullName}",
                    "Deleted");
            }
        }

        return NoContent();
    }

    [HttpGet("uid/{id:guid}")]
    [AllowAnonymous]
    public async Task<ActionResult<ContactDto>> GetByGUID(Guid id)
    {
        var responseDto = mapper.Map<ContactDto>(await service.GetById(id));
        if (responseDto == null)
        {
            return NotFound();
        }

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(Contact))
            .ToListAsync();

        responseDto.Type = typeMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        var statusMasters = await db.StatusMasters
                        .AsNoTracking()
                        .Where(x => x.Entity == nameof(Contact))
                        .ToListAsync();

        responseDto.Status = statusMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        return Ok(responseDto);
    }


    [HttpGet("SearchTagOptions")]
    public async Task<IActionResult> GetSearchTagOptions()
    {
        var results = await service.GetSearchTagOptions();
        return Ok(results);
    }

    [HttpGet("FieldOptions")]
    public async Task<IActionResult> GetFieldOptions(string field)
    {
        return Ok(await service.GetFieldOptions(field));
    }


    [HttpGet("Excel")]
    public async Task<IActionResult> GetExcel(
        [FromQuery] string? filters = null,
        [FromQuery] string? search = null,
        [FromQuery] string? sort = null)
    {
        var report = await service.getExcel(
            filters != null ? DataTools.GetObjectFromJsonString<APIFilter>(filters).Filters : null,
            search,
            sort);

        if (report == null)
        {
            return BadRequest("Report cannot be generated");
        }

        return File(
            report,
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            "ContactList.xlsx");
    }


    [HttpGet("EmailOptions")]
    public async Task<ActionResult<IEnumerable<object>>> GetEmailOptions(
        [FromQuery] string? filters = null,
        [FromQuery] string? search = null,
        [FromQuery] string? sort = null)
    {
        var results = await service.GetEmailContacts(
            filters != null ? DataTools.GetObjectFromJsonString<APIFilter>(filters).Filters : null,
            search,
            sort);
        return Ok(results);
    }
}