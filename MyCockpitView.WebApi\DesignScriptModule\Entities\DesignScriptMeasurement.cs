﻿using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using MyCockpitView.CoreModule;

namespace MyCockpitView.WebApi.DesignScriptModule.Entities;

public class DesignScriptMeasurement : BaseEntity
{
    public int DesignScriptMeasurementGroupID { get; set; }

    public virtual DesignScriptMeasurementGroup? DesignScriptMeasurementGroup { get; set; }

    [StringLength(50)]
    public string? Tag { get; set; }
    [Precision(14, 2)]
    public decimal? Length { get; set; } = 0;
    [Precision(14, 2)]
    public decimal? Breadth { get; set; } = 0;
    [Precision(14, 2)]
    public decimal? Height { get; set; } = 0;
    [Precision(14, 2)]
    public decimal? Number { get; set; } = 0;
    [Precision(14, 2)]
    public decimal? Area { get; set; } = 0;
    [Precision(14, 2)]
    public decimal? Total { get; set; } = 0;
    [Precision(14, 2)]
    public decimal? CenterToCenter { get; set; } = 0;
    [Precision(14, 2)]
    public decimal? Percentage { get; set; } = 0;
    [Precision(14, 2)]
    public decimal? Quantity { get; set; } = 0;
    public string? Unit { get; set; }

}

public class DesignScriptMeasurementConfiguration : BaseEntityConfiguration<DesignScriptMeasurement>, IEntityTypeConfiguration<DesignScriptMeasurement>
{
    public void Configure(EntityTypeBuilder<DesignScriptMeasurement> builder)
    {
      base.Configure(builder);

        builder.HasIndex(e => e.Tag);
        builder.HasIndex(e => e.Total);
    }
}