﻿
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.CoreModule;

namespace MyCockpitView.WebApi.DesignScriptModule.Entities;

public class DesignScriptDataCardAttribute : BaseEntity
{
    public int DesignScriptDataCardID { get; set; }
    public virtual DesignScriptDataCard? DesignScriptDataCard { get; set; }

    public string? AttributeKey { get; set; }
    public string? AttributeValue { get; set; }

    public bool IsHidden { get; set; }

}
public class DesignScriptDataCardAttributeConfiguration : BaseEntityConfiguration<DesignScriptDataCardAttribute>, IEntityTypeConfiguration<DesignScriptDataCardAttribute>
{
    public void Configure(EntityTypeBuilder<DesignScriptDataCardAttribute> builder)
    {
       base.Configure(builder);

        builder.HasIndex(e => e.<PERSON>H<PERSON><PERSON>);
    }
}