﻿

using AutoMapper;
using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.PackageModule.Entities;

namespace MyCockpitView.WebApi.PackageModule.Dtos;

public class PackageAttachmentDto : BaseBlobEntityDto
{
    public int PackageID { get; set; }
    public virtual ICollection<PackageAttachmentAttribute> Attributes { get; set; }=new List<PackageAttachmentAttribute>();
}

public class PackageAttachmentDtoMapperProfile : Profile
{
    public PackageAttachmentDtoMapperProfile()
    {
        CreateMap<PackageAttachment, PackageAttachmentDto>()
                          .ReverseMap()
                         .ForMember(dest => dest.Package, opt => opt.Ignore());

    }
}