﻿using AutoMapper;
using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.DesignScriptModule.Entities;

namespace MyCockpitView.WebApi.DesignScriptModule.Dtos;

public class DesignScriptDataCardDto : BaseEntityDto
{
    public string? Title { get; set; }
    public string? Subtitle { get; set; }
    public int CodeFlag { get; set; }
    public string? Code { get; set; }
    public string? LibraryCode { get; set; }
    public string? Category { get; set; }
    public Guid? LibraryEntityID { get; set; }
    public bool IsHidden { get; set; }
    public string? GFCTag { get; set; }
    public string? ProjectCode { get; set; }
    public int ProjectID { get; set; }
    public Guid ProjectUID { get; set; }
    public ICollection<Guid> Links { get; set; }= new List<Guid>();

    public string? History { get; set; }

    public virtual ICollection<DesignScriptDataCardAttachmentDto> Attachments { get; set; }=new List<DesignScriptDataCardAttachmentDto>();
    public virtual ICollection<DesignScriptDataCardEntityMapDto> Maps { get; set; } = new List<DesignScriptDataCardEntityMapDto>();
    public virtual ICollection<DesignScriptDataCardAttributeDto> Attributes { get; set; } = new List<DesignScriptDataCardAttributeDto>();
    public bool IsReadOnly { get; set; }
}

public class DesignScriptDataCardDtoMapperProfile : Profile
{
    public DesignScriptDataCardDtoMapperProfile()
    {
        CreateMap<DesignScriptDataCard, DesignScriptDataCardWithoutMapsDto>();

        CreateMap<DesignScriptDataCard, DesignScriptDataCardDto>()
      .ReverseMap()
       //.ForMember(dest => dest.Project, opt => opt.Ignore())
       .ForMember(dest => dest.Attachments, opt => opt.Ignore())
       .ForMember(dest => dest.Maps, opt => opt.Ignore());
    }
}
