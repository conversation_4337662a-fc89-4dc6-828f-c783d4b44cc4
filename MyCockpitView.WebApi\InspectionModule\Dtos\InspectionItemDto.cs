﻿using AutoMapper;
using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.InspectionModule.Entities;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace MyCockpitView.WebApi.InspectionModule.Dtos;

public class InspectionItemDto : BaseEntityDto
{
    [Required]
    public int InspectionID { get; set; }


    [StringLength(255)]
    public string? Title { get; set; }

    [StringLength(255)]
    public string? Subtitle { get; set; }

    public string? Comment { get; set; }


    [Column(TypeName = "datetime2")]
    public DateTime? DueDate { get; set; }


    [StringLength(255)]
    public string? ActionBy { get; set; }


    public int? ActionByContactID { get; set; }

    public string? PreviousHistory { get; set; }

    [Column(TypeName = "datetime2")]
    public DateTime? PreviousDueDate { get; set; }

    [StringLength(255)]
    public string? PreviousActionBy { get; set; }
    public string? PreviousComment { get; set; }


    public int? PreviousItemID { get; set; }

    public virtual ICollection<InspectionItemAttachmentDto> Attachments { get; set; } = new List<InspectionItemAttachmentDto>();


    [Column(TypeName = "datetime2")]
    public DateTime? InspectionDate { get; set; }

    public string? InspectionTitle { get; set; }


    public bool IsForwarded { get; set; }


    public int? PackageID { get; set; }


    public int ReminderCount { get; set; }

    [StringLength(255)]
    public string? UpdateFrom { get; set; }



    public bool IsInspection { get; set; }

    public bool NotDiscussed { get; set; }



    public bool SendUpdate { get; set; }

    public decimal Progress { get; set; }
    public decimal PreviousProgress { get; set; }


    public int? TodoID { get; set; }



    [StringLength(255)]
    public string? Zone { get; set; }


    [StringLength(255)]
    public string? Space { get; set; }


    [StringLength(255)]
    public string? Element { get; set; }


    public bool IsCustomSubtitle { get; set; }
    public string? ActionByName { get; set; }
    public string? ActionByEmail { get; set; }
    public string? ActionByCompany { get; set; }

    public int? ProjectID { get; set; }

    public int? DesignScriptEntityID { get; set; }

    //FOR INSPECTION

    public int? DesignScriptItemID { get; set; }


    public int JoinaryStatusFlag { get; set; } = 0;
    public string? JoinaryComment { get; set; }


    public int EndingStatusFlag { get; set; } = 0;
    public string? EndingComment { get; set; }

    public int MaterialStatusFlag { get; set; } = 0;
    public string? MaterialComment { get; set; }

    public int SizeStatusFlag { get; set; } = 0;
    public string? SizeComment { get; set; }

    //END //FOR INSPECTION

    public int? ProcessID { get; set; }
    public bool IsDelayed { get; set; }
}


public class InspectionItemDtoMapperProfile : Profile
{
    public InspectionItemDtoMapperProfile()
    {


        CreateMap<InspectionItem, InspectionItemDto>()
            
             .ForMember(dest => dest.IsDelayed, opt => opt.MapFrom(src => src.DueDate != null
                                                               && (src.DueDate.Value < DateTime.UtcNow) ? true : false))
              .ForMember(dest => dest.Attachments, opt => opt.MapFrom(src => src.Attachments))
            .ReverseMap()
            .ForMember(dest => dest.Inspection, opt => opt.Ignore())
            .ForMember(dest => dest.Attachments, opt => opt.Ignore());

    }
}


