﻿using AutoMapper;
using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.ProjectModule.Entities;
using System.ComponentModel.DataAnnotations;

namespace MyCockpitView.WebApi.ProjectModule.Dtos;

public class ProjectBillPaymentAttachmentDto : BaseBlobEntityDto
{

    public int ProjectBillPaymentID { get; set; }
}

public class ProjectBillPaymentAttachmentDtoMapperProfile : Profile
{
    public ProjectBillPaymentAttachmentDtoMapperProfile()
    {
        CreateMap<ProjectBillPaymentAttachment, ProjectBillPaymentAttachmentDto>()
                   .ReverseMap();

    }
}