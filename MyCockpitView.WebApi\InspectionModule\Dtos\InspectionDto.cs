﻿using AutoMapper;
using MyCockpitView.WebApi.ContactModule.Dtos;
using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.InspectionModule.Entities;

namespace MyCockpitView.WebApi.InspectionModule.Dtos;

public class InspectionDto : BaseEntityDto
{
   
    public int? ParentID { get; set; }
    public string? Title { get; set; }
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public string? Code { get; set; }
    public int ContactID { get; set; }
    public virtual ContactListDto? Contact { get; set; }
    public decimal Version { get; set; }
    public DateTime? ClosedOn { get; set; }
    public DateTime? FinalizedOn { get; set; }
    public bool IsEditable { get; set; }
    public bool IsDelayed { get; set; }
    public int? ProjectID { get; set; }
    public int? EntityID { get; set; }
    public string? Entity { get; set; }
    public string? EntityTitle { get; set; }
    public string? Location { get; set; }

    public bool IsSent { get; set; }

    public IEnumerable<InspectionRecipientDto> Recipients { get; set; }= new HashSet<InspectionRecipientDto>();
    public IEnumerable<InspectionItemDto> Items { get; set; } = new HashSet<InspectionItemDto>();
    public int Annexure { get; set; }

    public int? FunctionID { get; set; }

}

public class InspectionDtoMapperProfile : Profile
{
    public InspectionDtoMapperProfile()
    {
        CreateMap<Inspection, InspectionDto>()
            .ForMember(dest => dest.Items, opt => opt.MapFrom(src => src.Items))
            .ForMember(dest => dest.Recipients, opt => opt.MapFrom(src => src.Recipients))
     .ReverseMap()
         .ForMember(dest => dest.Items, opt => opt.Ignore())
         .ForMember(dest => dest.Recipients, opt => opt.Ignore())
          .ForMember(dest => dest.Contact, opt => opt.Ignore());

    }
}

