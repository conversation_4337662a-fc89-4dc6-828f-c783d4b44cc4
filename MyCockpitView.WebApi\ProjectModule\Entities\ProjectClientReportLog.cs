﻿using MyCockpitView.WebApi.Models;
using Newtonsoft.Json;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.ContactModule.Services;

namespace MyCockpitView.WebApi.ProjectModule.Entities;

public class ProjectClientReportLog
{
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public Guid ID { get; set; }
    public Guid? MessageID { get; set; }

    [Required]
    public DateTime SentDate { get; set; }
    public int ProjectID { get; set; }

    [Required]
    public string? Title { get; set; }

    public string? EmailToJson { get; set; }

    [NotMapped]
    public List<EmailContact> EmailTo
    {
        get => EmailToJson != null && EmailToJson != string.Empty ? JsonConvert.DeserializeObject<List<EmailContact>>(EmailToJson) : new List<EmailContact>();
        set => EmailToJson = JsonConvert.SerializeObject(value);
    }

    public string? EmailCCJson { get; set; }

    [NotMapped]
    public List<EmailContact> EmailCC
    {
        get => EmailCCJson != null && EmailCCJson != string.Empty ? JsonConvert.DeserializeObject<List<EmailContact>>(EmailCCJson) : new List<EmailContact>();
        set => EmailCCJson = JsonConvert.SerializeObject(value);
    }

    public string? HTMLContent { get; set; }



}

public class ProjectClientReportLogConfiguration :  IEntityTypeConfiguration<ProjectClientReportLog>
{
    public void Configure(EntityTypeBuilder<ProjectClientReportLog> builder)
    {
        builder.HasIndex(x => x.ProjectID);
        builder.HasIndex(x => x.SentDate);
    }
}