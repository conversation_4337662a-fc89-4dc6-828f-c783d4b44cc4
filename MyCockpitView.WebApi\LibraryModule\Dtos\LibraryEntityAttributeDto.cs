﻿using AutoMapper;
using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.LibraryModule.Entities;

namespace MyCockpitView.WebApi.LibraryModule.Dtos;

public class LibraryEntityAttributeDto : BaseEntityDto
{
    public int LibraryEntityID { get; set; }

    public string? AttributeKey { get; set; }
    public string? AttributeValue { get; set; }

}

public class LibraryEntityAttributeDtoMapperProfile : Profile
{
    public LibraryEntityAttributeDtoMapperProfile()
    {
        CreateMap<LibraryEntityAttribute, LibraryEntityAttributeDto>()
             .ReverseMap()
            .ForMember(dest => dest.LibraryEntity, opt => opt.Ignore());

    }
}