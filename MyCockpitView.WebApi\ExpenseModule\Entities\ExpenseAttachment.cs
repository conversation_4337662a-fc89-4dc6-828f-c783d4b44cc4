﻿using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.LibraryModule.Entities;
using System.ComponentModel.DataAnnotations;

namespace MyCockpitView.WebApi.ExpenseModule.Entities;

public class ExpenseAttachment : BaseBlobEntity
{
    [Required]
    public int ExpenseID { get; set; }

    public virtual Expense? Expense { get; set; }
}


public class ExpenseAttachmentConfiguration : BaseBlobEntityConfiguration<ExpenseAttachment>, IEntityTypeConfiguration<ExpenseAttachment>
{
    public void Configure(EntityTypeBuilder<ExpenseAttachment> builder)
    {
       base.Configure(builder);
    }
}
