
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using MyCockpitView.CoreModule;

namespace MyCockpitView.WebApi.DesignScriptModule.Entities;

public class DesignScriptEntity : BaseEntity
{
    [StringLength(256)]
    public string?  Title { get; set; }

    public int CodeFlag { get; set; }

    [StringLength(256)]
    public string? Code { get; set; }

    [StringLength(10)]
    public string? ProjectCode { get; set; }
    public int ProjectID { get; set; }

    public Guid ProjectUID { get; set; }
    public decimal SharePercentage { get; set; } = 0;
    public int? ProjectScopeID { get; set; }
    public virtual ICollection<DesignScriptEntity> Children { get; set; } = new List<DesignScriptEntity>();

    public virtual DesignScriptEntity? Parent { get; set; }
    public int? ParentID { get; set; }



    [StringLength(50)]
    public string? CostingUnit { get; set; } = "sqmt";

    [Precision(14, 2)]
    public decimal CostingQuantity { get; set; } = 0;

    [Precision(14, 2)]
    public decimal CostingRate { get; set; } = 0;

    [Precision(14, 2)]
    public decimal CostingAmount { get; set; } = 0;
    public string? CostingRemark { get; set; }


    //FOR MASTER PHASE
    public bool isMasterPhase { get; set; }

    [Precision(14, 2)]
    public decimal EstimateContingency { get; set; } = 0;

    [Precision(14, 2)]
    public decimal EstimateGST { get; set; } = 0;
    public string? EstimateRemark { get; set; }
    //---------------------

    public bool IsDetail_CIVIL_NA { get; set; }
    public bool IsDetail_CIVIL_Material { get; set; }
    public bool IsDetail_CIVIL_Joinary { get; set; }
    public bool IsDetail_CIVIL_Ending { get; set; }
    public bool IsDetail_CIVIL_Size { get; set; }

    public bool IsDetail_FINISHING_NA { get; set; }
    public bool IsDetail_FINISHING_Material { get; set; }
    public bool IsDetail_FINISHING_Joinary { get; set; }
    public bool IsDetail_FINISHING_Ending { get; set; }
    public bool IsDetail_FINISHING_Size { get; set; }

    public bool IsDetail_FURNITURE_NA { get; set; }
    public bool IsDetail_FURNITURE_Material { get; set; }
    public bool IsDetail_FURNITURE_Joinary { get; set; }
    public bool IsDetail_FURNITURE_Ending { get; set; }
    public bool IsDetail_FURNITURE_Size { get; set; }

    public bool IsDetail_LIGHTING_NA { get; set; }
    public bool IsDetail_LIGHTING_Material { get; set; }
    public bool IsDetail_LIGHTING_Joinary { get; set; }
    public bool IsDetail_LIGHTING_Ending { get; set; }
    public bool IsDetail_LIGHTING_Size { get; set; }

    public bool IsDetail_PLANTING_NA { get; set; }
    public bool IsDetail_PLANTING_Material { get; set; }
    public bool IsDetail_PLANTING_Joinary { get; set; }
    public bool IsDetail_PLANTING_Ending { get; set; }
    public bool IsDetail_PLANTING_Size { get; set; }

    public bool IsDetail_SERVICES_NA { get; set; }
    public bool IsDetail_SERVICES_Material { get; set; }
    public bool IsDetail_SERVICES_Joinary { get; set; }
    public bool IsDetail_SERVICES_Ending { get; set; }
    public bool IsDetail_SERVICES_Size { get; set; }

  
    public virtual ICollection<DesignScriptEntityItemMap> ItemMaps { get; set; } = new HashSet<DesignScriptEntityItemMap>();

    public virtual ICollection<DesignScriptDataCardEntityMap> DataCardMaps { get; set; } = new List<DesignScriptDataCardEntityMap>();


}

public class DesignScriptEntityConfiguration : BaseEntityConfiguration<DesignScriptEntity>, IEntityTypeConfiguration<DesignScriptEntity>
{
    public void Configure(EntityTypeBuilder<DesignScriptEntity> builder)
    {
      base.Configure(builder);

        builder.HasIndex(e => e.Title);
        builder.HasIndex(e => e.CodeFlag);
        builder.HasIndex(e => e.Code);
        builder.HasIndex(e => e.ProjectCode);
        builder.HasIndex(e => e.ProjectID);
        builder.HasIndex(e => e.ProjectScopeID);

    }
}