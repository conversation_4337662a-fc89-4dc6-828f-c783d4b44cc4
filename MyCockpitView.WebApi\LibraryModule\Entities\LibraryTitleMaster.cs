﻿using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.CoreModule;
using System.ComponentModel.DataAnnotations;

namespace MyCockpitView.WebApi.LibraryModule.Entities;

public class LibraryTitleMaster : BaseEntity
{
    [StringLength(255)]
    public string? Category { get; set; }

    [StringLength(255)]
    public string? Title { get; set; }

    [StringLength(255)]
    public string? Subtitle { get; set; }
}
public class LibraryTitleMasterConfiguration : BaseEntityConfiguration<LibraryTitleMaster>, IEntityTypeConfiguration<LibraryTitleMaster>
{
    public void Configure(EntityTypeBuilder<LibraryTitleMaster> builder)
    {
      base.Configure(builder);

        builder.HasIndex(e => e.Category);
        builder.HasIndex(e => e.Title);
        builder.HasIndex(e => e.Subtitle);
    }
}