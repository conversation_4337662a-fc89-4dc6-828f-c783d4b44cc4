﻿
using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.CoreModule;
using MyCockpitView.Utility.Common;
using MyCockpitView.WebApi.CompanyModule.Dtos;
using MyCockpitView.WebApi.CompanyModule.Entities;
using MyCockpitView.WebApi.CompanyModule.Services;
using MyCockpitView.WebApi.ContactModule.Dtos;
using MyCockpitView.WebApi.Exceptions;

namespace MyCockpitView.WebApi.CompanyModule.Controllers;

[Authorize]
[Route("[controller]")]
[ApiController]
public class CompanyController : ControllerBase
{
    ILogger<CompanyController> logger;
    private readonly ICompanyService service;
     private readonly IMapper mapper;
    private readonly EntitiesContext entitiesContext;
    public CompanyController(
        ILogger<CompanyController> logger,
        EntitiesContext entitiesContext,
        ICompanyService CompanyService,
         IMapper mapper
        )
    {
        this.logger = logger;
        this.entitiesContext = entitiesContext;
        service = CompanyService;
         this.mapper = mapper;
    }


    
    [HttpGet]
    public async Task<IActionResult> Get(string? filters = null, string? search = null, string? sort = null)
    {

        var query = service.Get(filters != null ? DataTools.GetObjectFromJsonString<APIFilter>(filters).Filters : null, search, sort);

        var results = mapper.Map<IEnumerable<CompanyDto>>(await query
           .ToListAsync());

        return Ok(results);

    }

    // GET: Companys/5
    
    [HttpGet("{id}")]
    public async Task<IActionResult> GetByID(int id)
    {

        var results = mapper.Map<IEnumerable<CompanyDto>>(await service.Get().SingleOrDefaultAsync(x => x.ID == id));

        if (results == null) throw new NotFoundException($"{nameof(Company)} not found!");

        return Ok(results);

    }

    [HttpGet("uid/{id:guid}")]
    public async Task<IActionResult> GetByGUID(Guid id)
    {

        var results = mapper.Map<IEnumerable<CompanyDto>>(await service.Get().SingleOrDefaultAsync(x => x.UID == id));

        if (results == null) throw new NotFoundException($"{nameof(Company)} not found!");

        return Ok(results);

    }


    [HttpPost]
    
    public async Task<IActionResult> Post([FromBody] Company Dto)
    {
        var id = await service.Create(Dto);
        var results = mapper.Map<IEnumerable<CompanyDto>>(await service.Get().SingleOrDefaultAsync(x => x.ID == id));

        if (results == null) throw new BadRequestException($"{nameof(Company)} could not be created!");

        return Ok(results);

    }


    [HttpPut("{id}")]
    
    //[ResponseType(typeof(int))]
    public async Task<IActionResult> Put(int id, [FromBody] Company Dto)
    {

        await service.Update(Dto);

        var results = mapper.Map<IEnumerable<CompanyDto>>(await service.Get().SingleOrDefaultAsync(x => x.ID == id));
        if (results == null) throw new NotFoundException($"{nameof(Company)} not found!");

        return Ok(results);
    }

    [HttpDelete("{id}")]
    
    public async Task<IActionResult> Delete(int id)
    {
        await service.Delete(id);
        return Ok();

    }
}
