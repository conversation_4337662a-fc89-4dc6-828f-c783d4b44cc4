﻿

using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.CoreModule;

namespace MyCockpitView.WebApi.PackageModule.Entities;

public class PackageDeliverableTaskMap : BaseEntity
{
    public int PackageID { get; set; }
    public int PackageDeliverableID { get; set; }
    public virtual PackageDeliverable? PackageDeliverable { get; set; }
    public int WFTaskID { get; set; }
}
public class PackageDeliverableTaskMapConfiguration : BaseEntityConfiguration<PackageDeliverableTaskMap>, IEntityTypeConfiguration<PackageDeliverableTaskMap>
{
    public void Configure(EntityTypeBuilder<PackageDeliverableTaskMap> builder)
    {
        base.Configure(builder);
        builder.HasIndex(x => x.WFTaskID);
    }
}