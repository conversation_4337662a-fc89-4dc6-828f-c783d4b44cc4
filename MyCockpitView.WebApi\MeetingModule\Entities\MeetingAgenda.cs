﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using MyCockpitView.CoreModule;

using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace MyCockpitView.WebApi.MeetingModule.Entities;

public class MeetingAgenda : BaseEntity
{

    [Required]
    public int MeetingID { get; set; }

    
    [StringLength(255)]
    public string? Title { get; set; }

    [StringLength(255)]
    public string? Subtitle { get; set; }

    public string? Comment { get; set; }

    
    [Column(TypeName = "datetime2")]
    public DateTime? DueDate { get; set; }

    
    [StringLength(255)]
    public string? ActionBy { get; set; }

    
    public int? ActionByContactID { get; set; }

    public string? PreviousHistory { get; set; }

    [Column(TypeName = "datetime2")]
    public DateTime? PreviousDueDate { get; set; }

    [StringLength(255)]
    public string? PreviousActionBy { get; set; }
    public string? PreviousComment { get; set; }

    
    public int? PreviousAgendaID { get; set; }

    public virtual ICollection<MeetingAgendaAttachment> Attachments { get; set; } = new List<MeetingAgendaAttachment>();

    public virtual Meeting? Meeting { get; set; }

    [Column(TypeName = "datetime2")]
    public DateTime? MeetingDate { get; set; }

    public string? MeetingTitle { get; set; }

    
    public bool IsForwarded { get; set; }

    
    public int? PackageID { get; set; }

    
    public int ReminderCount { get; set; }

    [StringLength(255)]
    public string? UpdateFrom { get; set; }


    
    public bool IsInspection { get; set; }
    
    public bool NotDiscussed { get; set; }


    
    public bool SendUpdate { get; set; }

    public decimal Progress { get; set; }
    public decimal PreviousProgress { get; set; }

    
    public int? TodoID { get; set; }


    
    [StringLength(255)]
    public string? Zone { get; set; }

    
    [StringLength(255)]
    public string? Space { get; set; }

    
    [StringLength(255)]
    public string? Element { get; set; }

    
    public bool IsCustomSubtitle { get; set; }
    public string? ActionByName { get; set; }
    public string? ActionByEmail { get; set; }
    public string? ActionByCompany { get; set; }
    
    public int? ProjectID { get; set; }
    
    public int? DesignScriptEntityID { get; set; }

    //FOR INSPECTION
    
    public int? DesignScriptItemID { get; set; }

    
    public int JoinaryStatusFlag { get; set; } = 0;
    public string? JoinaryComment { get; set; }

    
    public int EndingStatusFlag { get; set; } = 0;
    public string? EndingComment { get; set; }
    
    public int MaterialStatusFlag { get; set; } = 0;
    public string? MaterialComment { get; set; }
    
    public int SizeStatusFlag { get; set; } = 0;
    public string? SizeComment { get; set; }

    //END //FOR INSPECTION

    public int? ProcessID { get; set; }
}

public class MeetingAgendaConfiguration : BaseEntityConfiguration<MeetingAgenda>, IEntityTypeConfiguration<MeetingAgenda>
{
    public void Configure(EntityTypeBuilder<MeetingAgenda> builder)
    {
        base.Configure(builder);

        builder.HasIndex(e => e.IsVersion);
        builder.HasIndex(e => e.Title);
        builder.HasIndex(e => e.Subtitle);
        builder.HasIndex(e => e.DueDate);
        builder.HasIndex(e => e.ActionBy);
        builder.HasIndex(e => e.ActionByContactID);
        builder.HasIndex(e => e.PreviousAgendaID);
        builder.HasIndex(e => e.MeetingDate);
        builder.HasIndex(e => e.MeetingTitle);
        builder.HasIndex(e => e.IsForwarded);
        builder.HasIndex(e => e.ReminderCount);
        builder.HasIndex(e => e.IsForwarded);
        builder.HasIndex(e => e.ProjectID);
        builder.HasIndex(e => e.IsInspection);
        builder.HasIndex(e => e.NotDiscussed);
        builder.HasIndex(e => e.SendUpdate);
        builder.HasIndex(e => e.TodoID);



    }
}