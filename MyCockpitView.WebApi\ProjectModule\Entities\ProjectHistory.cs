﻿using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.CoreModule;
using System.ComponentModel.DataAnnotations.Schema;

namespace MyCockpitView.WebApi.ProjectModule.Entities;

public class ProjectHistory
{
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public Guid ID { get; set; }

    [Column(TypeName = "datetime2")]
    public DateTime RecordedDate { get; set; }
    public int StatusFlag { get; set; }
    public int ProjectID { get; set; }
    public Guid ProjectUID { get; set; }
    public string? Project { get; set; }
    public string? Code { get; set; }
    public string? Status { get; set; }
    public string? Partner { get; set; }
    public decimal TotalFee { get; set; }
    public decimal VHr { get; set; } = 0;
    public decimal VHrCost { get; set; } = 0;
    public decimal VHrAfterLastPayment { get; set; } = 0;
    public decimal VHrCostAfterLastPayment { get; set; } = 0;
    public DateTime? LastPaymentDate { get; set; }
    public decimal LastBillAmount { get; set; }
    public decimal LastPaymentAmount { get; set; }
    public decimal TotalBillAmount { get; set; }
    public decimal TotalPaymentAmount { get; set; }
    public decimal TotalBillPercentage { get; set; }

    public decimal TotalPaymentPercentage { get; set; }
    public bool IsPostAPR21 { get; set; }
    public decimal PartnerShare { get; set; }
    public decimal PartnerShareAmount { get; set; }

    public decimal CompanyShare { get; set; }
    public decimal CompanyShareAmount { get; set; }

    public decimal LastBite { get; set; }
    public decimal VHrCostShare { get; set; }
    public decimal LastBiteShare { get; set; }
    public decimal VHrCostPercentage { get; set; }
    public decimal KPI { get; set; }

    public decimal XCost { get; set; }
    public decimal XCostPercentage { get; set; }
    public decimal ProformaPercentage { get; set; }
    public decimal ProformaAmount { get; set; }
    public DateTime? ProformaDate { get; set; }

    public decimal CompletedPercentage { get; set; }
    public decimal ActivePercentage { get; set; }
    public decimal ProposedPercentage { get; set; }

    public decimal CompletedAmount { get; set; }
    public decimal ActiveAmount { get; set; }
    public decimal ProposedAmount { get; set; }
    public DateTime? LastMeetingDate { get; set; }
    public decimal LastMeetingIntervalInDays { get; set; }
}

public class ProjectHistoryConfiguration :  IEntityTypeConfiguration<ProjectHistory>
{
    public void Configure(EntityTypeBuilder<ProjectHistory> builder)
    {
        builder.HasIndex(x => x.ProjectID);
        builder.HasIndex(x => x.RecordedDate);
    }
}