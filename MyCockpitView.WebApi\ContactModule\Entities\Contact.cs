﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.CoreModule;
using Newtonsoft.Json;
using MyCockpitView.WebApi.LibraryModule.Entities;

namespace MyCockpitView.WebApi.ContactModule.Entities;

public class Contact : BaseEntity
{

    public bool IsCompany { get; set; }


    [Required]
    [StringLength(255)]
    public string? FullName { get; set; }
    //public string? FullName => ($"{Title} {FirstName} {LastName}").Trim();

    public string? Name => $"{FirstName} {LastName}".Trim();


    [StringLength(50)]
    public string? Title { get; set; }


    [Required]
    [StringLength(255)]
    public string? FirstName { get; set; }

    [StringLength(255)]
    public string? MiddleName { get; set; }

    [StringLength(255)]
    public string? LastName { get; set; }

    [StringLength(50)]
    public string? Gender { get; set; }

    public string? AddressesJson { get; set; }

    [NotMapped]
    public List<ContactAddress> Addresses
    {
        get => AddressesJson != null && AddressesJson != string.Empty ? JsonConvert.DeserializeObject<List<ContactAddress>>(AddressesJson) : new List<ContactAddress>();
        set => AddressesJson = JsonConvert.SerializeObject(value);
    }

    public string? PhonesJson { get; set; }

    [NotMapped]
    public List<ContactPhone> Phones
    {
        get => PhonesJson != null && PhonesJson != string.Empty ? JsonConvert.DeserializeObject<List<ContactPhone>>(PhonesJson) : new List<ContactPhone>();
        set => PhonesJson = JsonConvert.SerializeObject(value);
    }

    public string? EmailsJson { get; set; }

    [NotMapped]
    public List<ContactEmail> Emails
    {
        get => EmailsJson != null && EmailsJson != string.Empty ? JsonConvert.DeserializeObject<List<ContactEmail>>(EmailsJson) : new List<ContactEmail>();
        set => EmailsJson = JsonConvert.SerializeObject(value);
    }



    [StringLength(255)]
    public string? Website { get; set; }
    public DateTime? Birth { get; set; }

    public DateTime? Anniversary { get; set; }
    [StringLength(255)]
    public string? PAN { get; set; }

    [StringLength(255)]
    public string? TAN { get; set; }

    [StringLength(255)]
    public string? GSTIN { get; set; }

    [StringLength(255)]
    public string? HSN { get; set; }

    [StringLength(255)]
    public string? ARN { get; set; }

    [StringLength(255)]
    public string? PhotoFilename { get; set; }

    public string? PhotoUrl { get; set; }

    public string? Username { get; set; }

    [Column("Categories")]
    public string? _categories { get; set; } = string.Empty;

    [NotMapped]
    public string[] Categories
    {
        get
        {
            return _categories != null && _categories != string.Empty ?
                _categories.Split(',') :
                new string[] { };
        }
        set
        {
            _categories = string.Join(",", value);
        }
    }

    public string? Notes { get; set; }


    [Column("Urls")]
    public string? _urls { get; set; }

    [NotMapped]
    public string[] Urls
    {
        get
        {
            return _urls != null && _urls != string.Empty ?
                _urls.Split(',') :
                new string[] { };
        }
        set
        {
            _urls = string.Join(",", value);
        }
    }

    [StringLength(255)]
    public string? MaritalStatus { get; set; }

    [StringLength(255)]
    public string? FamilyContactName { get; set; }

    [StringLength(255)]
    public string? FamilyContactRelation { get; set; }

    [StringLength(255)]
    public string? FamilyContactPhone { get; set; }

    [StringLength(255)]
    public string? EmergencyContactName { get; set; }

    [StringLength(255)]
    public string? EmergencyContactRelation { get; set; }
    [StringLength(255)]
    public string? EmergencyContactPhone { get; set; }


    [StringLength(255)]
    public string? BankName { get; set; }
    [StringLength(255)]
    public string? IFSCCode { get; set; }
    [StringLength(255)]
    public string? UDHYAM { get; set; }
    [StringLength(255)]
    public string? AADHAAR { get; set; }
    [StringLength(255)]
    public string? BankAccountNo { get; set; }
    [StringLength(255)]
    public string? DrivingLicenseNo { get; set; }

    
    public virtual ICollection<ContactAssociation> AssociatedCompanies { get; set; } = new HashSet<ContactAssociation>();
    public virtual ICollection<ContactAssociation> AssociatedContacts { get; set; } = new HashSet<ContactAssociation>();
    public virtual ICollection<ContactAppointment> Appointments { get; set; } = new HashSet<ContactAppointment>();
    public virtual ICollection<ContactAttachment> Attachments { get; set; } = new HashSet<ContactAttachment>();

    public int? ParentID { get; set; }
    public virtual Contact? Parent { get; set; }
    public virtual ICollection<Contact> Children { get; set; } = new HashSet<Contact>();
    public virtual ICollection<LibraryEntity> LibraryEntities { get; set; } = new List<LibraryEntity>();

    //TO BE REMOVED//
    [StringLength(255)]
    public string? Email1 { get; set; }

    [StringLength(255)]
    public string? Email2 { get; set; }

    [StringLength(50)]
    public string? Phone1 { get; set; }

    [StringLength(50)]
    public string? Phone2 { get; set; }

    [StringLength(50)]
    public string? Phone3 { get; set; }

    [StringLength(255)]
    public string? Address1 { get; set; }


    [StringLength(255)]
    public string? Address1City { get; set; }

    [StringLength(255)]
    public string? Address1State { get; set; }

    [StringLength(255)]
    public string? Address1PostalCode { get; set; }

    [StringLength(255)]
    public string? Address1Country { get; set; }

    [StringLength(255)]
    public string? Address2 { get; set; }


    [StringLength(255)]
    public string? Address2City { get; set; }

    [StringLength(255)]
    public string? Address2State { get; set; }

    [StringLength(255)]
    public string? Address2PostalCode { get; set; }

    [StringLength(255)]
    public string? Address2Country { get; set; }

    [StringLength(255)]
    public string? Adhaar { get; set; }
    //------------//

}
public class ContactPhone
{
    public string? Title { get; set; }
    public string? Phone { get; set; }
    public bool IsPrimary { get; set; }
}

public class ContactEmail
{
    public string? Title { get; set; }
    public string? Email { get; set; }
    public bool IsPrimary { get; set; }
}

public class ContactAddress 
{
    public string? Title { get; set; }
    public string? Street { get; set; }
    public string? Area { get; set; }
    public string? City { get; set; }
    public string? State { get; set; }
    public string? Country { get; set; }
    public string? PinCode { get; set; }
    public bool IsPrimary { get; set; }
}

public class ContactConfiguration : BaseEntityConfiguration<Contact>,IEntityTypeConfiguration<Contact>
{
    public void Configure(EntityTypeBuilder<Contact> builder)
    {
        base.Configure(builder);
        builder.HasIndex(e => e.Username);

    }
}

