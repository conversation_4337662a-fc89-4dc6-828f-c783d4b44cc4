﻿
using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.WebApi.Responses;
using MyCockpitView.Utility.Common;
using MyCockpitView.WebApi.ActivityModule;
using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.ContactModule.Services;
using MyCockpitView.WebApi.TodoModule.Services;
using MyCockpitView.WebApi.TodoModule.Dtos;
using MyCockpitView.WebApi.TodoModule.Entities;
using MyCockpitView.WebApi.WFTaskModule.Services;

namespace MyCockpitView.WebApi.TodoModule.Controllers;

[Authorize]
[ApiController]
[Route("[controller]")]
public class TodoController : ControllerBase
{
    private readonly ITodoService service;
    private readonly EntitiesContext db;
    private readonly IActivityService activityService;
    private readonly IMapper mapper;
    private readonly IContactService contactService;
    private readonly IWFTaskService taskService;

    public TodoController(EntitiesContext db, ITodoService service, IMapper mapper, IActivityService activityService, IContactService contactService,IWFTaskService taskService   )
    {
        this.db = db;
        this.service = service;
        this.activityService = activityService;
        this.mapper = mapper;
        this.contactService = contactService;
        this.taskService = taskService;
    }

    [HttpGet]
    public async Task<ActionResult<IEnumerable<TodoDto>>> Get(
        [FromQuery] string? filters = null,
        [FromQuery] string? search = null,
        [FromQuery] string? sort = null)
    {
        var query = service.Get(filters != null ? DataTools.GetObjectFromJsonString<APIFilter>(filters).Filters : null, search, sort).Include(x=>x.AssignerContact).Include(x=>x.AssigneeContact);
        var results = mapper.Map<IEnumerable<TodoDto>>(await query.ToListAsync());

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(Todo))
            .ToListAsync();

        foreach (var obj in results)
        {
            obj.Type = typeMasters.FirstOrDefault(x => x.Value == obj.TypeFlag)?.Title ?? "";
        }

        var statusMasters = await db.StatusMasters
          .AsNoTracking()
          .Where(x => x.Entity == nameof(Todo))
          .ToListAsync();

        foreach (var obj in results)
        {
            obj.Status = statusMasters.FirstOrDefault(x => x.Value == obj.StatusFlag)?.Title ?? "";
        }

        return Ok(results);
    }

    [HttpGet("Pages")]
    public async Task<ActionResult<PagedResponse<TodoDto>>> GetPages(
        [FromQuery] int page = 0,
        [FromQuery] int pageSize = 50,
        [FromQuery] string? filters = null,
        [FromQuery] string? search = null,
        [FromQuery] string? sort = null)
    {
        var query = service.Get(filters != null ? DataTools.GetObjectFromJsonString<APIFilter>(filters).Filters : null, search, sort).Include(x => x.AssignerContact).Include(x => x.AssigneeContact); ;

        var totalCount = await query.CountAsync();
        var totalPages = (int)Math.Ceiling((decimal)totalCount / pageSize);
        var results = mapper.Map<IEnumerable<TodoDto>>(await query
            .Skip(pageSize * page)
            .Take(pageSize)
            .ToListAsync());

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(Todo))
            .ToListAsync();

        foreach (var obj in results)
        {
            obj.Type = typeMasters.FirstOrDefault(x => x.Value == obj.TypeFlag)?.Title ?? "";
        }

        var statusMasters = await db.StatusMasters
           .AsNoTracking()
           .Where(x => x.Entity == nameof(Todo))
           .ToListAsync();

        foreach (var obj in results)
        {
            obj.Status = statusMasters.FirstOrDefault(x => x.Value == obj.StatusFlag)?.Title ?? "";
        }

        return Ok(new PagedResponse<TodoDto>(results, totalCount, totalPages));
    }

    [HttpGet("{id:int}")]
    public async Task<ActionResult<TodoDto>> GetByID(int id)
    {
        var responseDto = mapper.Map<TodoDto>(await service.GetById(id));
        if (responseDto == null)
        {
            return NotFound();
        }

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(Todo))
            .ToListAsync();

        responseDto.Type = typeMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        var statusMasters = await db.StatusMasters
          .AsNoTracking()
          .Where(x => x.Entity == nameof(Todo))
          .ToListAsync();

        responseDto.Status = statusMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        return Ok(responseDto);
    }

    [HttpPost]
    public async Task<ActionResult<TodoDto>> Post(TodoDto dto)
    {
        var id = await service.Create(mapper.Map<Todo>(dto));
        var responseDto = mapper.Map<TodoDto>(await service.GetById(id));

        if (responseDto == null)
            return BadRequest("Not Created");

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(Todo))
            .ToListAsync();

        responseDto.Type = typeMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        var statusMasters = await db.StatusMasters
  .AsNoTracking()
  .Where(x => x.Entity == nameof(Todo))
  .ToListAsync();

        responseDto.Status = statusMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        if (!string.IsNullOrEmpty(User.Identity?.Name))
        {
            var currentContact = await contactService.Get().FirstOrDefaultAsync(x => x.Username == User.Identity.Name);
            if (currentContact != null)
            {
               
                    await activityService.LogUserActivity(currentContact, nameof(Todo), responseDto.ID, $"{responseDto.Title} | {responseDto.Subtitle}",
                                   "DueDate: " + ClockTools.GetIST(responseDto.DueDate).ToString("dd MMM yyyy")
                       + "| MHr Assigned: " + responseDto.MHrAssigned.ToString(), "Created");
                
            }
        }

        if (responseDto.StatusFlag != 1)
        {
            await taskService.StartFlow(nameof(Todo), responseDto.TypeFlag, responseDto.ID);
        }

        return CreatedAtAction(nameof(GetByID), new { id = responseDto.ID }, responseDto);
    }

    [HttpPut("{id:int}")]
    public async Task<ActionResult<TodoDto>> Put(int id, TodoDto dto)
    {
        if (id != dto.ID)
        {
            return BadRequest();
        }

        await service.Update(mapper.Map<Todo>(dto));
        var responseDto = mapper.Map<TodoDto>(await service.GetById(id));

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(Todo))
            .ToListAsync();

        responseDto.Type = typeMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        var statusMasters = await db.StatusMasters
  .AsNoTracking()
  .Where(x => x.Entity == nameof(Todo))
  .ToListAsync();

        responseDto.Status = statusMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        if (!string.IsNullOrEmpty(User.Identity?.Name))
        {
            var currentContact = await contactService.Get().FirstOrDefaultAsync(x => x.Username == User.Identity.Name);
            if (currentContact != null)
            {

                await activityService.LogUserActivity(currentContact, nameof(Todo), responseDto.ID, $"{responseDto.Title} | {responseDto.Subtitle}",
                               "DueDate: " + ClockTools.GetIST(responseDto.DueDate).ToString("dd MMM yyyy")
                   + "| MHr Assigned: " + responseDto.MHrAssigned.ToString(), "Updated");

            }
        }

        if (responseDto.StatusFlag == 0)
        {

            await taskService.UpdateTaskDue(nameof(Todo), responseDto.ID);

        }

        return Ok(responseDto);
    }

    [HttpDelete("{id:int}")]
    public async Task<IActionResult> Delete(int id)
    {
        var responseDto = mapper.Map<TodoDto>(await service.GetById(id));
        if (responseDto == null) return BadRequest($"{nameof(Todo)} not found!");

        await service.Delete(id);

        if (!string.IsNullOrEmpty(User.Identity?.Name))
        {
            var currentContact = await contactService.Get().FirstOrDefaultAsync(x => x.Username == User.Identity.Name);
            if (currentContact != null)
            {

                await activityService.LogUserActivity(currentContact, nameof(Todo), responseDto.ID, $"{responseDto.Title} | {responseDto.Subtitle}",
                               "DueDate: " + ClockTools.GetIST(responseDto.DueDate).ToString("dd MMM yyyy")
                   + "| MHr Assigned: " + responseDto.MHrAssigned.ToString(), "Deleted");

            }
        }

        await taskService.PurgePendingTasks(nameof(Todo), id);

        return NoContent();
    }


    [HttpGet("uid/{id:guid}")]
    [AllowAnonymous]
    public async Task<ActionResult<TodoDto>> GetByGUID(Guid id)
    {
        var responseDto = mapper.Map<TodoDto>(await service.GetById(id));
        if (responseDto == null)
        {
            return NotFound();
        }

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(Todo))
            .ToListAsync();

        responseDto.Type = typeMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        var statusMasters = await db.StatusMasters
                        .AsNoTracking()
                        .Where(x => x.Entity == nameof(Todo))
                        .ToListAsync();

        responseDto.Status = statusMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        return Ok(responseDto);
    }


    [HttpGet("SearchTagOptions")]
    public async Task<IActionResult> GetSearchTagOptions()
    {
        var results = await service.GetSearchTagOptions();
        return Ok(results);
    }

    [HttpGet("FieldOptions")]
    public async Task<IActionResult> GetFieldOptions(string field)
    {
        return Ok(await service.GetFieldOptions(field));
    }

    [HttpGet("SubjectOptions")]
    public async Task<IActionResult> GetSubjectOptions()
    {

        var query = service.Get().Where(x => x.ProjectID == null).Select(x => x.Title).Distinct();


        return Ok(await query.ToListAsync());

    }

}