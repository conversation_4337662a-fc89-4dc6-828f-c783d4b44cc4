﻿
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.Utility.Common;
using MyCockpitView.WebApi.ContactModule.Entities;
using MyCockpitView.WebApi.ContactModule.Services;
using MyCockpitView.WebApi.LeaveModule.Entities;
using MyCockpitView.WebApi.LeaveModule.Services;
using MyCockpitView.WebApi.PackageModule.Entities;
using MyCockpitView.WebApi.PackageModule.Services;
using MyCockpitView.WebApi.Services;
using MyCockpitView.WebApi.WFTaskModule.Services;
using System.Text;

namespace MyCockpitView.WebApi.AutoReportModule;

[ApiController]
[Route("[controller]")]
    public class DailyStudioScheduleController : ControllerBase
{
    private readonly ILeaveService leaveService;
    private readonly IWFTaskService wftaskService;
    private readonly IContactService contactService;
    private readonly IContactAppointmentService appointmentService;
    private readonly IPackageService packageService;
    private readonly EntitiesContext db;
    private readonly ISharedService sharedService;

    public DailyStudioScheduleController(
    ILeaveService leaveService,
    IWFTaskService wftaskService,
    IContactService contactService,
    IContactAppointmentService appointmentService,
    IPackageService packageService,
    EntitiesContext db,
    ISharedService sharedService)
        {
        this.leaveService = leaveService;
        this.wftaskService = wftaskService;
        this.contactService = contactService;
        this.appointmentService = appointmentService;
        this.packageService = packageService;
        this.db = db;
        this.sharedService = sharedService;
    }

        [HttpGet]
        public async Task<IActionResult> Get()
        {
            
                var istTimeZone = TimeZoneInfo.FindSystemTimeZoneById("India Standard Time");
                var todayIST = TimeZoneInfo.ConvertTime(DateTime.UtcNow, istTimeZone).Date;

                var startOfTodayUTC = TimeZoneInfo.ConvertTimeToUtc(todayIST, istTimeZone);
                var endOfTodayUTC = TimeZoneInfo.ConvertTimeToUtc(todayIST.AddDays(1).AddTicks(-1), istTimeZone);

                // Find the previous working day (skipping Sunday)
                DateTime previousWorkingDayIST = todayIST.AddDays(-1);
                while (previousWorkingDayIST.DayOfWeek == DayOfWeek.Sunday)
                {
                    previousWorkingDayIST = previousWorkingDayIST.AddDays(-1);
                }

                // Convert the previous working day to UTC
                var startOfPreviousDayUTC = TimeZoneInfo.ConvertTimeToUtc(previousWorkingDayIST, istTimeZone);
                var endOfPreviousDayUTC = TimeZoneInfo.ConvertTimeToUtc(previousWorkingDayIST.AddDays(1).AddTicks(-1), istTimeZone);


                var VHR_RATE = Convert.ToInt32((await db.AppSettingMasters.AsNoTracking().SingleOrDefaultAsync(x => x.PresetKey == McvConstant.COMPANY_VHR_COST)).PresetValue);
                //var monthlyExpectedMHr = Convert.ToDecimal(await sharedService.GetPresetValue(McvConstant.TEAM_MONTHLY_EXPECTED_MHR));

                var companies = await db.Companies
                   .Select(x => new
                   {
                       x.Title,
                       x.ID,
                   })
                   .ToListAsync();

                var contacts = await contactService.Get()
                    .Where(x => x.Username != null)
                    .Select(x => new
                    {
                        x.ID,
                        Name = x.FirstName + " " + x.LastName
                    })
                    .ToListAsync();

                var appointments = await appointmentService.Get()
                   .Where(x => x.StatusFlag == McvConstant.APPOINTMENT_STATUSFLAG_APPOINTED)
                   .Select(x => new
                   {
                       x.ID,
                       x.CompanyID,
                       x.ContactID,
                       x.ManValue,
                       x.IsStudioMember,
                       //x.ExpectedVhr,
                       //x.ExpectedRemuneration,

                   }).ToListAsync();

                var contactData = contacts.Join(appointments, a => a.ID, b => b.ContactID, (a, b) => new
                {
                    a.ID,
                    a.Name,
                    b.CompanyID,
                    b.ManValue,
                    //b.ExpectedVhr,
                    //b.ExpectedRemuneration,
                }).Join(companies, a => a.CompanyID, b => b.ID, (a, b) => new
                {
                    a.ID,
                    a.Name,
                    a.CompanyID,
                    a.ManValue,
                    //a.ExpectedVhr,
                    //a.ExpectedRemuneration,
                    Company = b.Title
                });



                var leaves = await leaveService.Get()
                    //.Where(x => x.StatusFlag == McvConstant.LEAVE_STATUSFLAG_APPROVED)
                    .Where(x => x.Start <= endOfTodayUTC) // Adjusted to UTC
                    .Where(x => x.End >= startOfTodayUTC) // Adjusted to UTC
                    .Select(x => new
                    {
                        x.ContactID,
                        x.TypeFlag,
                        x.Total,
                        x.Start,
                        x.End,

                    })
                    .ToListAsync();

                var tasks = await wftaskService.Get()
                    .Where(x => x.StatusFlag == McvConstant.WFTASK_STATUSFLAG_PENDING || x.StatusFlag == McvConstant.WFTASK_STATUSFLAG_PAUSED)
                    .Where(x => x.StartDate <= endOfTodayUTC) // Adjusted to UTC
                    .Where(x => x.DueDate >= startOfTodayUTC) // Adjusted to UTC
                    //.Where(x => x.Entity != null && x.Entity == nameof(Package) && x.StageIndex == 3) //STUDIO WORK
                    .Select(x => new
                    {
                        x.ContactID,
                        x.Title,
                        x.Subtitle,
                        x.EntityID,
                        x.StartDate,
                        x.DueDate,
                        x.MHrAssigned,
                        x.VHrAssigned,
                        x.AssignerContactID
                    })
                    .ToListAsync();

                var packages = await packageService.Get()
                    .Where(x => x.TypeFlag == McvConstant.PACKAGE_TYPEFLAG_ACTIVE && x.StatusFlag == McvConstant.PACKAGE_STATUSFLAG_ACTIVE)
                    .Include(x => x.Associations)
                    .Select(x => new
                    {
                        x.CompanyID,
                        x.ID,
                        x.Code,
                        x.StartDate,
                        x.FinalDate,
                        x.ProjectTitle,
                        PartnerID = x.Associations.Any(a => a.TypeFlag == McvConstant.PACKAGE_ASSOCIATION_TYPEFLAG_PARTNER) ? x.Associations.FirstOrDefault(a => a.TypeFlag == McvConstant.PACKAGE_ASSOCIATION_TYPEFLAG_PARTNER).ContactID : (int?)null,
                        AssociateID = x.Associations.Any(a => a.TypeFlag == McvConstant.PACKAGE_ASSOCIATION_TYPEFLAG_ASSOCIATE) ? x.Associations.FirstOrDefault(a => a.TypeFlag == McvConstant.PACKAGE_ASSOCIATION_TYPEFLAG_ASSOCIATE).ContactID : (int?)null,
                    })
                    .ToListAsync();



                var packageData = packages
                    .Where(x => x.FinalDate.Date == todayIST)
                    .Join(companies, a => a.CompanyID, b => b.ID, (a, b) => new
                    {
                        Company = b.Title,
                        a.ID,
                        a.Code,
                        a.StartDate,
                        a.FinalDate,
                        a.ProjectTitle,
                        a.AssociateID,
                        a.PartnerID
                    })
                    .Join(contactData, a => a.PartnerID, b => b.ID, (a, b) => new
                    {

                        a.Company,
                        a.ID,
                        a.Code,
                        a.StartDate,
                        a.FinalDate,
                        a.ProjectTitle,
                        a.AssociateID,
                        Partner = b.Name,

                    })
                    .Join(contactData, a => a.AssociateID, b => b.ID, (a, b) => new
                    {
                        a.Company,
                        a.Partner,
                        a.ID,
                        a.Code,
                        a.StartDate,
                        a.FinalDate,
                        a.ProjectTitle,
                        Associate = b.Name,
                    })
                    .ToList();

                var taskData = tasks.Join(packages, a => a.EntityID, b => b.ID, (a, b) => new
                {
                    a.ContactID,
                    a.Title,
                    a.Subtitle,
                    Package = b.Code,
                    a.StartDate,
                    a.DueDate,
                    a.MHrAssigned,
                    a.VHrAssigned,
                    a.AssignerContactID
                }).Join(contactData, a => a.ContactID, b => b.ID, (a, b) => new
                {
                    b.Company,
                    b.Name,
                    a.Title,
                    a.Subtitle,
                    a.Package,
                    a.StartDate,
                    a.DueDate,
                    a.MHrAssigned,
                    a.VHrAssigned,
                    a.AssignerContactID
                }).Join(contactData, a => a.AssignerContactID, b => b.ID, (a, b) => new
                {
                    a.Company,
                    a.Name,
                    a.Title,
                    a.Subtitle,
                    a.Package,
                    a.StartDate,
                    a.DueDate,
                    a.MHrAssigned,
                    a.VHrAssigned,
                    Assigner = b.Name
                }).ToList();

                var typeMaster = await db.TypeMasters.AsNoTracking()
                    .Where(x => x.Entity == nameof(Leave))
                    .Select(x => new
                    {
                        x.Title,
                        x.Value
                    })
                    .ToListAsync();
                var leaveData = leaves
                    .Join(typeMaster, a => a.TypeFlag, b => b.Value, (a, b) => new
                    {
                        a.ContactID,
                        a.Total,
                        a.Start,
                        a.End,
                        TypeValue = b.Title
                    })
                    .Join(contactData, a => a.ContactID, b => b.ID, (a, b) => new
                    {
                        b.Company,
                        b.Name,
                        a.TypeValue,
                        a.Total,
                        a.Start,
                        a.End,
                    }).ToList();

                // BURNOUT //
           

                var previousDayTasks = await wftaskService.Get()
                        //.Where(x => x.StatusFlag == McvConstant.WFTASK_STATUSFLAG_PENDING || x.StatusFlag == McvConstant.WFTASK_STATUSFLAG_PAUSED)
                        .Where(x => x.StartDate <= endOfPreviousDayUTC) // Task starts before or on the end of the day (UTC)
                        .Where(x => x.DueDate >= startOfPreviousDayUTC) // Task ends after or on the start of the day (UTC)
                    .Where(x => x.Entity != null && x.Entity == nameof(Package) && x.StageIndex == 3) //STUDIO WORK
                    .Select(x => new
                    {
                        x.ContactID,
                        x.Title,
                        x.Subtitle,
                        x.EntityID,
                        x.StartDate,
                        x.DueDate,
                        x.MHrAssigned,
                        x.VHrAssigned,
                        x.VHrAssignedCost,
                        x.AssignerContactID,
                        x.CompanyID,
                        x.StatusFlag
                    })
                    .ToListAsync();

                var previousDayLeaves = await leaveService.Get()
                  .Where(x => x.StatusFlag == McvConstant.LEAVE_STATUSFLAG_APPROVED)
                    .Where(x => x.Start <= endOfPreviousDayUTC) // Adjusted to UTC
                    .Where(x => x.End >= startOfPreviousDayUTC) // Adjusted to UTC
                  .Select(x => new
                  {
                      x.ContactID,
                      x.TypeFlag,
                      x.Total,
                      x.Start,
                      x.End,
                      IsHalfDay = x.TypeFlag == McvConstant.LEAVE_TYPEFLAG_APPROVED_HALFDAY || x.TypeFlag == McvConstant.LEAVE_TYPEFLAG_EMERGENCY_HALFDAY
                  })
                  .ToListAsync();

                var appointedContacts = contacts.Join(appointments.Where(a=>a.IsStudioMember), a => a.ID, b => b.ContactID, (a, b) => new
                {
                    ContactID=a.ID,
                    a.Name,
                    AppointmentID=b.ID,
                    b.CompanyID,
                    b.ManValue,
                    //b.ExpectedVhr,
                    //b.ExpectedRemuneration,
                }).ToList();

                var burnOutPerCompany = companies.Select(company =>
                {
                    decimal burnMHr = 0.0m;
                    decimal burnCost = 0.0m;


                    var burnedList = new List<ContactAppointmentBurnOutLog>();


                    // Filter contacts for the current company
                    var companyContacts = appointedContacts.Where(c => c.CompanyID == company.ID).ToList();

                    foreach (var contact in companyContacts)
                    {
                        // Default working hours
                        decimal mHrPerDay = 9.0m;
                        decimal assignedMHr = 0.0m;
                        decimal assignedMHRCost = 0.0m;

                        // Adjust working hours for leave
                        if (previousDayLeaves.Any(x => x.ContactID == contact.ContactID && x.IsHalfDay))
                        {
                            mHrPerDay = 4.5m; // Half-day leave
                        }
                        else if (previousDayLeaves.Any(x => x.ContactID == contact.ContactID))
                        {
                            mHrPerDay = 0; // Full-day leave
                        }

                        // Reduce working hours based on assigned tasks
                        if (mHrPerDay > 0)
                        {
                            var contactTasks = previousDayTasks
                            .Where(x => x.CompanyID == company.ID && x.ContactID == contact.ContactID)
                            .ToList();

                            if (contactTasks.Any())
                            {
                                assignedMHr = contactTasks.Sum(x=>x.MHrAssigned);
                                assignedMHRCost = contactTasks.Sum(x => x.VHrAssignedCost);
                                mHrPerDay -= assignedMHr < 9 ? assignedMHr : 9;
                            }
                            
                        }

                        // Accumulate remaining hours (burnout metric)
                        burnMHr += mHrPerDay;
                        burnCost += (mHrPerDay * contact.ManValue * VHR_RATE);

                        if (mHrPerDay > 0)
                        {
                            var log = new ContactAppointmentBurnOutLog
                            {

                                LogDate = startOfPreviousDayUTC,
                                ContactAppointmentID = contact.AppointmentID,
                                ContactID = contact.ContactID,
                                CompanyID = company.ID,
                                ContactName = contact.Name,
                                BurnedMHr = mHrPerDay,
                                BurnedAmount = (mHrPerDay * contact.ManValue * VHR_RATE),
                                AssignedMHr = assignedMHr,
                                AssignedAmount = assignedMHRCost,
                                VHRRate = VHR_RATE,
                                ManValue = contact.ManValue,
                            };
                            db.ContactAppointmentBurnOutLogs.Add(log);
                            burnedList.Add(log);
                        }
                    }

                    return new
                    {
                        Date= startOfPreviousDayUTC,
                        CompanyID = company.ID,
                        Company=company.Title,
                        BurnedMHr = burnMHr,
                        BurnedCost= burnCost,
                        BurnedList = burnedList,
                    };
                }).Where(x=>x.BurnedCost > 0).ToList();

                await db.SaveChangesAsync();

                //-------------//


                var _senderEmail = await sharedService.GetPresetValue(McvConstant.LOCK_PROJECT_EMAIL_SENDER_ID);
                var _senderName = await sharedService.GetPresetValue(McvConstant.LOCK_PROJECT_EMAIL_SENDER_NAME);

                var toList = new List<EmailContact>() {

                                                      new EmailContact {
                                                          Name = "People | Newarch",
                                                          Email = "<EMAIL>"
                                                      }
                                                    };

                var ccList = new List<EmailContact>() {
                     new EmailContact {
                                                          Name = "Core | Newarch",
                                                          Email = "<EMAIL>"
                                                      },
                                                        new EmailContact {
                                                            Name = "Backup | Newarch",
                                                            Email = "<EMAIL>"
                                                        }
                                                    };

                var dateFormat= await sharedService.GetPresetValue(McvConstant.DATE_FORMAT);
                var _reportTitle = $"Studio Schedule for {ClockTools.GetIST(todayIST).ToString(dateFormat)}";


                StringBuilder sb = new StringBuilder();
                sb.AppendLine("<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Transitional//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd\">");
                sb.AppendLine("<html xmlns=\"http://www.w3.org/1999/xhtml\">");
                sb.AppendLine("<head>");
                sb.AppendLine("    <meta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\" />");
                sb.AppendLine("    <title>Email Design</title>");
                sb.AppendLine("    <meta name=\"viewport\" content=\"width=device-width; initial-scale=1.0;\" />");
                sb.AppendLine("    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=9; IE=8; IE=7; IE=EDGE\" />");
                sb.AppendLine("    <meta name=\"format-detection\" content=\"telephone=no\" />");
                sb.AppendLine("    <!--[if gte mso 9]><xml>");
                sb.AppendLine("    <o:OfficeDocumentSettings>");
                sb.AppendLine("    <o:AllowPNG />");
                sb.AppendLine("    <o:PixelsPerInch>96</o:PixelsPerInch>");
                sb.AppendLine("    </o:OfficeDocumentSettings>");
                sb.AppendLine("    </xml><![endif]-->");
                sb.AppendLine("    <style type=\"text/css\">");
                sb.AppendLine("        /* Some resets and issue fixes */");
                sb.AppendLine("        #outlook a {");
                sb.AppendLine("            padding: 0;");
                sb.AppendLine("        }");
                sb.AppendLine("");
                sb.AppendLine("        body {");
                sb.AppendLine("            width: 100% !important;margin:0;");
                sb.AppendLine("            -webkit-text-size-adjust: 100%;");
                sb.AppendLine("            -ms-text-size-adjust: 100%;");
                sb.AppendLine("        }");

                sb.AppendLine("        table{");
                sb.AppendLine("            mso-table-lspace: 0px;");
                sb.AppendLine("            mso-table-rspace: 0px;");
                sb.AppendLine("        }");
                sb.AppendLine("");
                sb.AppendLine("        table td {");
                sb.AppendLine("            border-collapse: collapse;");
                sb.AppendLine("        }");
                sb.AppendLine("");
                sb.AppendLine("        .ExternalClass * {");
                sb.AppendLine("            line-height: 115%;");
                sb.AppendLine("        }");
                sb.AppendLine("        /* End reset */");

                sb.AppendLine("    </style>");
                sb.AppendLine("</head>");
                sb.AppendLine("");
                sb.AppendLine("<body>");
                sb.AppendLine("");

                sb.AppendLine("");
                sb.AppendLine("    <div style=\"margin: 0 auto;font-family:Calibri;font-size:14px;line-height:1.8;padding-left:5px;padding-right:5px; max-width:500px;\">");
                sb.AppendLine("");
                sb.AppendLine("        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; border-collapse: collapse;\">");
                sb.AppendLine("            <tr>");
                sb.AppendLine("                <td style=\"font-size: 16px; font-weight: bold; background-color: #005ba4; color: #fff; padding: 10px; text-align: center;\">");
                sb.AppendLine(_reportTitle.ToUpper());
                sb.AppendLine("                </td>");
                sb.AppendLine("            </tr>");
                sb.AppendLine("        </table>");
                sb.AppendLine("");

                //sb.AppendLine("<div style=\"border-bottom: 1px solid #cccccc; margin-top: 15px; margin-bottom: 15px; \"></div>");

                sb.AppendLine("        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width: 100%; border-collapse: collapse\">");
                //sb.AppendLine("            <tr>");
                //sb.AppendLine("                <td valign=\"top\" width=\"120\" style=\"font-weight:bold;padding-bottom:5px;\">");
                //sb.AppendLine("                    FollowUp Date:");
                //sb.AppendLine("                </td>");
                //sb.AppendLine("                <td valign=\"top\" style=\"padding-bottom: 5px\">");
                //sb.AppendLine(ClockTools.GetISTNow().ToString("dd MMM yyyy"));
                //sb.AppendLine("                </td>");
                //sb.AppendLine("            </tr>");


                sb.AppendLine("        </table>");
                sb.AppendLine("");
                sb.AppendLine("        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width: 100%; border-collapse: collapse\">");
                sb.AppendLine("            <tr>");
                sb.AppendLine("                <td valign=\"top\"  style=\"font-weight:bold;\">");
                sb.AppendLine("                    To:");
                sb.AppendLine("                </td>");
                sb.AppendLine("            </tr>");
                sb.AppendLine("            <tr>");
                sb.AppendLine("                <td valign=\"top\" style=\"padding-bottom: 5px;\">");
                sb.AppendLine("                    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width: 100%; border-collapse: collapse\">");
                foreach (var obj in toList)
                {
                    sb.AppendLine("                        <tr>");
                    sb.AppendLine("                            <td>");
                    sb.AppendLine(obj.Name + " <i> (" + obj.Email + ")</i>");
                    sb.AppendLine("                            </td>");
                    sb.AppendLine("                        </tr>");
                }
                sb.AppendLine("                    </table>");
                sb.AppendLine("                </td>");
                sb.AppendLine("            </tr>");
                sb.AppendLine("            <tr>");
                sb.AppendLine("                <td valign=\"top\"  style=\"font-weight:bold;\">");
                sb.AppendLine("                    CC:");
                sb.AppendLine("                </td>");
                sb.AppendLine("            </tr>");
                sb.AppendLine("            <tr>");
                sb.AppendLine("                <td valign=\"top\" style=\"padding-bottom: 5px;\">");
                sb.AppendLine("                    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width: 100%; border-collapse: collapse\">");
                foreach (var obj in ccList)
                {
                    sb.AppendLine("                        <tr>");
                    sb.AppendLine("                            <td>");
                    sb.AppendLine(obj.Name + " <i> (" + obj.Email + ")</i>");
                    sb.AppendLine("                            </td>");
                    sb.AppendLine("                        </tr>");
                }
                sb.AppendLine("                    </table>");
                sb.AppendLine("                </td>");
                sb.AppendLine("            </tr>");

                sb.AppendLine("        </table>");
                //sb.AppendLine("<div style=\"border-bottom: 1px solid #cccccc; margin-top: 15px; margin-bottom: 15px; \"></div>");

                sb.AppendLine("        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width: 100%; border-collapse: collapse\">");
                sb.AppendLine("            <tr style=\"background-color: #ff0000;color: #fff;\">");
                sb.AppendLine("                <td valign=\"top\" style=\"padding: 5px;\">");
                sb.AppendLine($"<h4 style=\"font-weight:bold;font-size:16px;margin:0;\">Burn-Out For { ClockTools.GetIST(startOfPreviousDayUTC).ToString(dateFormat)}</h4>");
                sb.AppendLine("                </td>");
                sb.AppendLine("                <td valign=\"top\" style=\"padding: 5px;text-align:end;\">");
                //sb.AppendLine($"<small>{taskData.Count.ToString("D2")} </small>");
                sb.AppendLine("                </td>");
                sb.AppendLine("            </tr>");
                sb.AppendLine("        </table>");
                sb.AppendLine("");
                foreach (var grp in burnOutPerCompany.GroupBy(x => x.Company))
                {
                    sb.AppendLine("        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width: 100%; border-collapse: collapse\">");
                    sb.AppendLine("            <tr style=\"background-color:#c1c1c1;\">");
                    sb.AppendLine("                <td valign=\"top\" style=\"padding: 2px 5px;\">");
                    sb.AppendLine($"<h4 style=\"font-weight:bold;font-size:14px;margin:0;\">{grp.Key} </h4>");
                    sb.AppendLine("                </td>");
                    sb.AppendLine("                <td valign=\"top\" style=\"padding: 2px 5px;text-align:end;\">");
                    //sb.AppendLine($"<small>{grp.Count().ToString("D2")} </small>");
                    sb.AppendLine("                </td>");
                    sb.AppendLine("            </tr>");
                    sb.AppendLine("        </table>");
                    sb.AppendLine("");
                    sb.AppendLine("        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width: 100%; border-collapse: collapse;\">");
                    foreach (var obj in grp)
                    {


                        sb.AppendLine("                        <tr>");

                        sb.AppendLine("                            <td style=\"width:50%;\">");
                        sb.AppendLine($"<h6 style=\"font-weight:bold;font-size:12px;margin:0;\">Total</h6>");
                        sb.AppendLine("                            </td>");

                        sb.AppendLine("<td style=\"text-align:end;\">");
                        sb.AppendLine($"<h6 style=\"font-size:16px;margin:0;color:#ff0000;\">{obj.BurnedCost.FormatAsCurrency()}</h6>");
                        sb.AppendLine("                            </td>");
                        sb.AppendLine("                        </tr>");
                        sb.AppendLine("                        <tr style=\"border-bottom:1px solid #cccccc;\">");

                        sb.AppendLine("                            <td style=\"width:50%;\">");
                        sb.AppendLine($"<small>Burned mHR</small>");
                        sb.AppendLine("                            </td>");

                        sb.AppendLine("<td style=\"text-align:end;\">");
                        sb.AppendLine($"<small>{obj.BurnedMHr} mHR</small>");
                        sb.AppendLine("                            </td>");
                        sb.AppendLine("                        </tr>");


                        foreach (var item in obj.BurnedList)
                        {


                            sb.AppendLine("                        <tr>");

                            sb.AppendLine("                            <td style=\"width:50%;\">");
                            sb.AppendLine($"<h6 style=\"font-weight:bold;font-size:12px;margin:0;\">{item.ContactName}</h6>");
                            sb.AppendLine("                            </td>");

                            sb.AppendLine("<td style=\"text-align:end;\">");
                            sb.AppendLine($"<h6 style=\"font-size:12px;margin:0;\">{item.BurnedAmount.FormatAsCurrency()}</h6>");
                            sb.AppendLine("                            </td>");
                            sb.AppendLine("                        </tr>");
                            sb.AppendLine("                        <tr style=\"border-bottom:1px solid #cccccc;\">");

                            sb.AppendLine("                            <td style=\"width:50%;\">");
                            sb.AppendLine($"<small>Burned mHR</small>");
                            sb.AppendLine("                            </td>");

                            sb.AppendLine("<td style=\"text-align:end;\">");
                            sb.AppendLine($"<small>{item.BurnedMHr} mHR</small>");
                            sb.AppendLine("                            </td>");
                            sb.AppendLine("                        </tr>");


                        }


                    }
                    sb.AppendLine("        </table>");
                }

                sb.AppendLine("<div style=\"border-bottom: 1px solid #cccccc; margin-top: 15px; margin-bottom: 15px; \"></div>");

                sb.AppendLine("        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width: 100%; border-collapse: collapse\">");
                sb.AppendLine("            <tr style=\"background-color: #8f8f8f;color: #fff;\">");
                sb.AppendLine("                <td valign=\"top\" style=\"padding: 5px;\">");
                sb.AppendLine($"<h4 style=\"font-weight:bold;font-size:16px;margin:0;\">TASKS </h4>");
                sb.AppendLine("                </td>");
                sb.AppendLine("                <td valign=\"top\" style=\"padding: 5px;text-align:end;\">");
                sb.AppendLine($"<small>{taskData.Count.ToString("D2")} </small>");
                sb.AppendLine("                </td>");
                sb.AppendLine("            </tr>");
                sb.AppendLine("        </table>");
                sb.AppendLine("");
                foreach (var grp in taskData.GroupBy(x => x.Company))
                {
                    sb.AppendLine("        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width: 100%; border-collapse: collapse\">");
                    sb.AppendLine("            <tr style=\"background-color:#c1c1c1\">");
                    sb.AppendLine("                <td valign=\"top\" style=\"padding: 2px 5px;\">");
                    sb.AppendLine($"<h4 style=\"font-weight:bold;font-size:14px;margin:0;\">{grp.Key} </h4>");
                    sb.AppendLine("                </td>");
                    sb.AppendLine("                <td valign=\"top\" style=\"padding: 2px 5px;text-align:end;\">");
                    sb.AppendLine($"<small>{grp.Count().ToString("D2")} </small>");
                    sb.AppendLine("                </td>");
                    sb.AppendLine("            </tr>");
                    sb.AppendLine("        </table>");
                    sb.AppendLine("");
                    sb.AppendLine("        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width: 100%; border-collapse: collapse;\">");
                    foreach (var obj in grp)
                    {


                        sb.AppendLine("                        <tr>");

                        sb.AppendLine("                            <td style=\"width:50%;\">");
                        sb.AppendLine($"<h6 style=\"font-weight:bold;font-size:12px;margin:0;\">{obj.Name}</h6>");
                        sb.AppendLine("                            </td>");

                        sb.AppendLine("<td style=\"text-align:end;\">");
                        sb.AppendLine($"<h6 style=\"font-size:12px;margin:0;\">{ClockTools.GetIST(obj.StartDate).ToString("HH:mm")}-{ClockTools.GetIST(obj.DueDate).ToString("HH:mm")} | {obj.MHrAssigned} mHR | {obj.VHrAssigned} vHr</h6>");
                        sb.AppendLine("                            </td>");
                        sb.AppendLine("                        </tr>");
                        sb.AppendLine("                        <tr style=\"border-bottom:1px solid #cccccc;\">");

                        sb.AppendLine("                            <td style=\"width:50%;\">");
                        sb.AppendLine($"<small>{obj.Title} | {obj.Subtitle}</small>");
                        sb.AppendLine("                            </td>");

                        sb.AppendLine("<td style=\"text-align:end;\">");
                        sb.AppendLine($"<small>Assigned by: {obj.Assigner}</small>");
                        sb.AppendLine("                            </td>");
                        sb.AppendLine("                        </tr>");



                    }
                    sb.AppendLine("        </table>");
                }

                sb.AppendLine("<div style=\"border-bottom: 1px solid #cccccc; margin-top: 15px; margin-bottom: 15px; \"></div>");


                sb.AppendLine("        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width: 100%; border-collapse: collapse\">");
                sb.AppendLine("            <tr style=\"background-color: #8f8f8f;color: #fff;\">");
                sb.AppendLine("                <td valign=\"top\" style=\"padding: 5px;\">");
                sb.AppendLine($"<h4 style=\"font-weight:bold;font-size:16px;margin:0;\">LEAVES </h4>");
                sb.AppendLine("                </td>");
                sb.AppendLine("                <td valign=\"top\" style=\"padding: 5px;text-align:end;\">");
                sb.AppendLine($"<small>{leaveData.Count.ToString("D2")} </small>");
                sb.AppendLine("                </td>");
                sb.AppendLine("            </tr>");
                sb.AppendLine("        </table>");
                sb.AppendLine("");
                foreach (var grp in leaveData.GroupBy(x => x.Company))
                {
                    sb.AppendLine("        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width: 100%; border-collapse: collapse\">");
                    sb.AppendLine("            <tr style=\"background-color:#c1c1c1\">");
                    sb.AppendLine("                <td valign=\"top\" style=\"padding: 2px 5px;\">");
                    sb.AppendLine($"<h4 style=\"font-weight:bold;font-size:14px;margin:0;\">{grp.Key} </h4>");
                    sb.AppendLine("                </td>");
                    sb.AppendLine("                <td valign=\"top\" style=\"padding: 2px 5px;text-align:end;\">");
                    sb.AppendLine($"<small>{grp.Count().ToString("D2")} </small>");
                    sb.AppendLine("                </td>");
                    sb.AppendLine("            </tr>");
                    sb.AppendLine("        </table>");
                    sb.AppendLine("");
                sb.AppendLine("        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width: 100%; border-collapse: collapse;\">");
       
                foreach (var obj in grp)
                {


                    sb.AppendLine("                        <tr>");

                    sb.AppendLine("                            <td style=\"width:50%;\">");
                    sb.AppendLine($"<h6 style=\"font-weight:bold;font-size:12px;margin:0;\">{obj.Name}</h6>");
                    sb.AppendLine("                            </td>");

                    sb.AppendLine("                            <td style=\"text-align:end;\">");
                    sb.AppendLine($"<h6 style=\"font-size:12px;margin:0;\">{ClockTools.GetIST(obj.Start).ToString("dd MMM yyyy")}-{ClockTools.GetIST(obj.End).ToString("dd MMM yyyy")}</h6>");
                    sb.AppendLine("                            </td>");
                    sb.AppendLine("                        </tr>");

                    sb.AppendLine("                        <tr style=\"border-bottom:1px solid #cccccc;\">");

                    sb.AppendLine("                            <td style=\"width:50%;\">");
                    sb.AppendLine($"<small>{obj.TypeValue}</small>");
                    sb.AppendLine("                            </td>");

                    sb.AppendLine("                            <td style=\"text-align:end;\">");
                    sb.AppendLine($"<small>{obj.Total} Day/s</small>");
                    sb.AppendLine("                            </td>");
                    sb.AppendLine("                        </tr>");



                }
                sb.AppendLine("        </table>");
            }

                sb.AppendLine("<div style=\"border-bottom: 1px solid #cccccc; margin-top: 15px; margin-bottom: 15px; \"></div>");

                sb.AppendLine("        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width: 100%; border-collapse: collapse\">");
                sb.AppendLine("            <tr style=\"background-color: #8f8f8f;color: #fff;\">");
                sb.AppendLine("                <td valign=\"top\" style=\"padding: 5px;\">");
                sb.AppendLine($"<h4 style=\"font-weight:bold;font-size:16px;margin:0;\">PACKAGES DUE {packageData.Count.ToString("D2")} </h4>");
                sb.AppendLine("                </td>");
                sb.AppendLine("                <td valign=\"top\" style=\"padding: 5px;text-align:end;\">");
                sb.AppendLine($"<small>{packageData.Count.ToString("D2")} </small>");
                sb.AppendLine("                </td>");
                sb.AppendLine("            </tr>");
                sb.AppendLine("        </table>");
                sb.AppendLine("");
                foreach (var grp in packageData.GroupBy(x=>x.Company))
                {
                    sb.AppendLine("        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width: 100%; border-collapse: collapse\">");
                    sb.AppendLine("            <tr style=\"background-color:#c1c1c1\">");
                    sb.AppendLine("                <td valign=\"top\" style=\"padding: 2px 5px;\">");
                    sb.AppendLine($"<h4 style=\"font-weight:bold;font-size:14px;margin:0;\">{grp.Key} </h4>");
                    sb.AppendLine("                </td>");
                    sb.AppendLine("                <td valign=\"top\" style=\"padding: 2px 5px;text-align:end;\">");
                    sb.AppendLine($"<small>{grp.Count().ToString("D2")} </small>");
                    sb.AppendLine("                </td>");
                    sb.AppendLine("            </tr>");
                    sb.AppendLine("        </table>");
                    sb.AppendLine("");
                    sb.AppendLine("        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width: 100%; border-collapse: collapse;\">");

                    foreach (var obj in grp)
                    {

                        sb.AppendLine("                        <tr>");

                        sb.AppendLine("                            <td style=\"width:50%;\">");
                        sb.AppendLine($"<h6 style=\"font-weight:bold;font-size:12px;margin:0;\">{obj.Code}</h6>");
                        sb.AppendLine("                            </td>");

                        sb.AppendLine("                            <td style=\"text-align:end;\">");
                        sb.AppendLine($"<h6 style=\"font-size:12px;margin:0;\">{ClockTools.GetIST(obj.StartDate).ToString("dd MMM yyyy")}-{ClockTools.GetIST(obj.FinalDate).ToString("dd MMM yyyy")}</h6>");
                        sb.AppendLine("                            </td>");
                        sb.AppendLine("                        </tr>");


                        sb.AppendLine("                        <tr style=\"border-bottom:1px solid #cccccc;\">");

                        sb.AppendLine("                            <td style=\"width:50%;\">");
                        sb.AppendLine($"<small>{obj.ProjectTitle}</small> ");
                        sb.AppendLine("                            </td>");

                        sb.AppendLine("                            <td style=\"text-align:end;\">");
                        sb.AppendLine($"<small>{obj.Partner} | {obj.Associate}</small> ");
                        sb.AppendLine("                            </td>");
                        sb.AppendLine("                        </tr>");

                    }

                    sb.AppendLine("        </table>");
                }

                //FOOTER
                sb.AppendLine("<div style=\"border-bottom: 1px solid #cccccc; margin-top: 15px; margin-bottom: 15px; \"></div>");
                sb.AppendLine("        <table align=\"center\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; border-collapse: collapse;font-size:11px;\">");

                sb.AppendLine("");
                sb.AppendLine("            <tr>");
                sb.AppendLine("");
                sb.AppendLine("                <td align=\"center\" >");
                sb.AppendLine("This is a <b>MyCockpitView<sup>&copy;</sup></b> & <b>DesignScript<sup>&copy;</sup></b> generated e-mail for your information and necessary action.");
                sb.AppendLine("</td>");
                sb.AppendLine("            </tr>");
                sb.AppendLine("");
                sb.AppendLine("            <tr>");
                sb.AppendLine("");
                sb.AppendLine("                <td align=\"center\" >");
                sb.AppendLine("");
                sb.AppendLine("                    Powered by <b>Newarch<sup>&reg;</sup> Infotech LLP</b>");
                sb.AppendLine("");
                sb.AppendLine("                </td>");
                sb.AppendLine("            </tr>");
                sb.AppendLine("        </table>");
                sb.AppendLine("    </div>");

                sb.AppendLine("</body>");
                sb.AppendLine("");
                sb.AppendLine("</html>");

                var _emailBody = sb.ToString();

        var emailTo = new List<(string name, string email)>();
        foreach (var obj in toList)
            emailTo.Add((obj.Name, obj.Email));

        var emailCC = new List<(string name, string email)>();
        foreach (var obj in ccList)
            emailCC.Add((obj.Name, obj.Email));

        await sharedService.SendMail(_reportTitle, _senderName, _senderEmail, _emailBody, emailTo, emailCC);


            return Ok(_emailBody);
           
        }

    }
