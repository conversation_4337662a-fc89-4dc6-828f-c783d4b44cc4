
using MediatR;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.OpenApi.Models;
using MyCockpitView.WebApi.ActivityModule;

using MyCockpitView.WebApi.AppSettingMasterModule;
using MyCockpitView.WebApi.AuthModule;
using MyCockpitView.WebApi.AuthModule.Extensions;
using MyCockpitView.WebApi.AzureBlobsModule;
using MyCockpitView.WebApi.CompanyModule.Extensions;
using MyCockpitView.WebApi.ContactModule.Extensions;
using MyCockpitView.WebApi.DesignScriptModule.Extensions;
using MyCockpitView.WebApi.Exceptions;
using MyCockpitView.WebApi.ExpenseModule.Extensions;
using MyCockpitView.WebApi.Extensions;
using MyCockpitView.WebApi.HabitModule.Extensions;
using MyCockpitView.WebApi.InspectionModule.Extensions;
using MyCockpitView.WebApi.LeaveModule.Extensions;
using MyCockpitView.WebApi.LibraryModule.Extensions;
using MyCockpitView.WebApi.MeetingModule.Extensions;
using MyCockpitView.WebApi.PackageModule.Extensions;
using MyCockpitView.WebApi.PayrollModule.Extensions;
using MyCockpitView.WebApi.ProcessLibraryModule.Extensions;
using MyCockpitView.WebApi.ProjectModule.Extensions;
using MyCockpitView.WebApi.RequestTicketModule.Extensions;
using MyCockpitView.WebApi.Services;
using MyCockpitView.WebApi.Settings;
using MyCockpitView.WebApi.StatusMasterModule;
using MyCockpitView.WebApi.TodoModule.Extensions;
using MyCockpitView.WebApi.TypeMasterModule;
using MyCockpitView.WebApi.WFStageModule.Extensions;
using MyCockpitView.WebApi.WFTaskModule.Extensions;

var builder = WebApplication.CreateBuilder(args);

//EF context
var sqlServerDbSettings = builder.Configuration.GetSection(nameof(SqlServerDbSettings)).Get<SqlServerDbSettings>();
builder.Services.AddSqlServerDb(sqlServerDbSettings);


//JWT bearer
var jwtSettings = builder.Configuration.GetSection(nameof(JwtSettings)).Get<JwtSettings>();
// var jwtSettings = new JwtSettings();
// builder.Configuration.Bind(nameof(JwtSettings), jwtSettings);
builder.Services.AddSingleton(jwtSettings);
builder.Services.AddJwtBearerAuthentication(jwtSettings);

builder.Services.AddHttpContextAccessor();

//MediatR
builder.Services.AddMediatR(typeof(Program));

//Shared Services
builder.Services.AddScoped<IAzureBlobService, AzureBlobService>();
builder.Services.AddScoped<ISharedService, SharedService>();
builder.Services.AddScoped<ICurrentUserService, CurrentUserService>();
builder.Services.AddScoped(typeof(IBaseEntityService<>), typeof(BaseEntityService<>));
builder.Services.AddScoped(typeof(IBaseAttachmentService<>), typeof(BaseAttachmentService<>));

//Module Services
builder.Services.RegisterActivityServices();
builder.Services.RegisterAppSettingMasterServices();
builder.Services.RegisterAuthServices();
builder.Services.RegisterCompanyServices();
builder.Services.RegisterStatusMasterServices();
builder.Services.RegisterTypeMasterServices();
builder.Services.RegisterContactServices();
builder.Services.RegisterDesignScriptServices();
builder.Services.RegisterExpenseServices();
builder.Services.RegisterHabitServices();
builder.Services.RegisterInspectionServices();
builder.Services.RegisterLeaveServices();
builder.Services.RegisterLibraryServices();
builder.Services.RegisterMeetingServices();
builder.Services.RegisterPackageServices();
builder.Services.RegisterPayrollServices();
builder.Services.RegisterProcessLibraryServices();
builder.Services.RegisterProjectServices();
builder.Services.RegisterRequestTicketServices();
builder.Services.RegisterTodoServices();
builder.Services.RegisterWFStageServices();
builder.Services.RegisterWFTaskServices();

//autoMapper
builder.Services.AddAutoMapper(typeof(Program));

builder.Services.ConfigureCors();

builder.Services.AddControllers()
    .AddJsonOptions(x =>{
    x.JsonSerializerOptions.Converters.Add(new DateTimeConverter());
}); 

// Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new OpenApiInfo { Title = "API", Version = "v1" });
    c.EnableAnnotations();

    c.AddSecurityDefinition(JwtBearerDefaults.AuthenticationScheme, new OpenApiSecurityScheme
    {
        Description = "JWT Authorization header using the Bearer scheme (Example: 'Bearer 12345abcdef')",
        Name = "Authorization",
        In = ParameterLocation.Header,
        Type = SecuritySchemeType.ApiKey,
        Scheme = JwtBearerDefaults.AuthenticationScheme
    });

    c.AddSecurityRequirement(new OpenApiSecurityRequirement
    {
        {
            new OpenApiSecurityScheme
            {
                Reference = new OpenApiReference
                {
                    Type = ReferenceType.SecurityScheme,
                    Id = JwtBearerDefaults.AuthenticationScheme
                }
            },
            Array.Empty<string>()
        }
    });
});

builder.Services.AddHealthChecks();

var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

//if (app.Environment.IsDevelopment())
//{
//    app.UseDeveloperExceptionPage();
//}

//Exceptions
app.AddGlobalErrorHandler();


//not required in Kubernetes
// app.UseHttpsRedirection();

app.MapHealthChecks("/health");

app.UseCors("CorsPolicy");

app.UseAuthentication();

app.UseAuthorization();

app.MapControllers();

app.MigrateDatabase();

app.Run();
