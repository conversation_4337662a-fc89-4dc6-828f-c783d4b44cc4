﻿using AutoMapper;
using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.WFTaskModule.Entities;


namespace MyCockpitView.WebApi.WFTaskModule.Dtos;

public class TaskRequestDto : BaseEntityDto
{
    public decimal OriginalMHr { get; set; } = 0;

    public decimal RequestedMHr { get; set; } = 0;

    public DateTime? OriginalDueDate { get; set; }

    public DateTime? RequestedDueDate { get; set; }

    public string? Comment { get; set; }
    public string? RequestMessage { get; set; }

    public int WFTaskID { get; set; }
}

public class TaskRequestDtoMapperProfile : Profile
{
    public TaskRequestDtoMapperProfile()
    {
        CreateMap<TaskRequest, TaskRequestDto>()

                .ReverseMap();
    }
}