﻿using MyCockpitView.Utility.RDLCClient;
using MyCockpitView.WebApi.MeetingModule.Entities;
using MyCockpitView.WebApi.Services;

namespace MyCockpitView.WebApi.MeetingModule.Services;

public interface IMeetingService : IBaseEntityService<Meeting>
{
    Task<bool> CheckIfDelayed(int ID);
    Task<int> Create(Meeting Entity, IEnumerable<MeetingAttendee>? Attendees = null);
    Task<ReportDefinition> GetAgendaReport(Meeting meeting, string? sort = null);
    IQueryable<MeetingAgenda> GetAgendas(int ID);
    Task<IEnumerable<int>> GetAssigneeContactIDs(int entityID, string stageCode, string propertyName);
    Task<byte[]> GetCalendarInvite(int MeetingID, bool isCancelled = false);
    Task<IEnumerable<MeetingAttendee>> GetLastAttendees(string Subject);
    Task<Meeting> GetLastPending(Meeting Meeting);
    Task<string> GetMeetingAgendaHistoryString(MeetingAgenda meetingAgenda);
    Task<ReportDefinition> GetMinutesReport(Meeting meeting, string? sort = null);
    Task<IEnumerable<string>> GetSubjectOptions(string? Filters = null);
    Task<dynamic> GetTaskByStage(string Entity, int EntityID, string StageCode, string StageTitle, decimal StageDuration, DateTime? FollowUpDate = null);
    Task<bool> IsMeetingEditable(int ID, int ContactID);
    Task SendMinutes(int ID);
    Task TaskAction(int EntityID, string StageCode, string? taskComment = null);
}