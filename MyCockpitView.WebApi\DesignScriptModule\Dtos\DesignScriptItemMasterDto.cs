﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.DesignScriptModule.Entities;
using System.ComponentModel.DataAnnotations;

namespace MyCockpitView.WebApi.DesignScriptModule.Dtos;

public class DesignScriptItemMasterDto : BaseEntityDto
{
    [StringLength(256)]
    public string? DSR { get; set; }

    [StringLength(256)]
    public string? DSRNumber { get; set; }

    [StringLength(256)]
    public string? Title { get; set; }

    [StringLength(256)]
    public string? ItemGroup { get; set; }

    [StringLength(256)]
    public string? Category { get; set; }
    public string? Specification { get; set; }

    [StringLength(256)]
    public string? Units { get; set; }
    [Precision(14, 2)]
    public decimal Rate { get; set; } = 0;

    public int CodeFlag { get; set; }

    [StringLength(256)]
    public string? Code { get; set; }

    [StringLength(256)]
    public string? ReferenceCode { get; set; }


    public string? DrawingSpecification { get; set; }

}

public class DesignScriptItemMasterDtoMapperProfile : Profile
{
    public DesignScriptItemMasterDtoMapperProfile()
    {

        CreateMap<DesignScriptItemMaster, DesignScriptItemMasterDto>()
         .ReverseMap();

    }
}