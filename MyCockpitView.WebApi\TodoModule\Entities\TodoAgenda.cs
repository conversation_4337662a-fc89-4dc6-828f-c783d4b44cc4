﻿
using System.ComponentModel.DataAnnotations;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.CoreModule;


namespace MyCockpitView.WebApi.TodoModule.Entities;

public class TodoAgenda : BaseEntity
{
    
    [StringLength(255)]
    public string? Title { get; set; }

    public string? Comment { get; set; }

    
    public int TodoID { get; set; }

    public virtual Todo? Todo { get; set; }

}

public class TodoAgendaConfiguration : BaseEntityConfiguration<TodoAgenda>, IEntityTypeConfiguration<TodoAgenda>
{
    public void Configure(EntityTypeBuilder<TodoAgenda> builder)
    {

        base.Configure(builder);

        builder.Property(ta => ta.Title)
            .HasMaxLength(255)
            .IsRequired();

        builder.Property(ta => ta.Comment);

        builder.Property(ta => ta.TodoID)
            .IsRequired();

        // Relationships
        builder.HasOne(ta => ta.Todo)
            .WithMany(t => t.Agendas)
            .HasForeignKey(ta => ta.TodoID)
            .IsRequired()
            .OnDelete(DeleteBehavior.Cascade);
    }
}
