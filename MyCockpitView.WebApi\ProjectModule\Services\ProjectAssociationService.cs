﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.WebApi.ProjectModule.Entities;
using MyCockpitView.WebApi.Services;





namespace MyCockpitView.WebApi.ProjectModule.Services;

public class ProjectAssociationService : BaseEntityService<ProjectAssociation>, IProjectAssociationService
{
    public ProjectAssociationService(EntitiesContext db) : base(db) { }

    public async Task<ProjectAssociation?> GetById(int Id)
    {

        return await db.ProjectAssociations.AsNoTracking()
               .Include(x => x.Contact)
                .SingleOrDefaultAsync(i => i.ID == Id);

    }

}
