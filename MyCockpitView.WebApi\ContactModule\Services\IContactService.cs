﻿using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.ContactModule.Entities;
using MyCockpitView.WebApi.Services;

namespace MyCockpitView.WebApi.ContactModule.Services;

public interface IContactService: IBaseEntityService<Contact>
{
    Task<IEnumerable<EmailContact>> GetEmailContacts(IEnumerable<QueryFilter>? Filters = null, string? Search = null, string? Sort = null);
    Task<byte[]> getExcel(IEnumerable<QueryFilter>? Filters = null, string? Search = null, string? Sort = null);
}