﻿
using AutoMapper;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.MeetingModule.Dtos;
using MyCockpitView.WebApi.PackageModule.Entities;
using MyCockpitView.WebApi.ProjectModule.Dtos;
using MyCockpitView.WebApi.ProjectModule.Entities;
using Newtonsoft.Json;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Text.RegularExpressions;

namespace MyCockpitView.WebApi.PackageModule.Dtos;

    public class PackageListDto : BaseEntityDto
    {
    public int ProjectID { get; set; }
    public string? ProjectCode { get; set; }
    public string? ProjectTitle { get; set; }
    public string? Title { get; set; }
    public string? Code { get; set; }
    public string? PhaseCode { get; set; }
    public string? PhaseTitle { get; set; }
    public int? DesignScriptEntityID { get; set; }
    public string? Stage { get; set; }
    public DateTime? SubmissionDate { get; set; }
    public int VersionFlag { get; set; }
    public decimal Version { get; set; }
    public DateTime FinalDate { get; set; }
    public DateTime StartDate { get; set; }
    public bool IsDelayed { get; set; }
    public string? ActiveStage { get; set; }
    public virtual ProjectListDto? Project { get; set; }
    public decimal VHrRate { get; set; }
    public decimal VHrAssigned { get; set; }
    public decimal VHrConsumed { get; set; }
    public decimal VHrConsumedInclusive { get; set; }
    public decimal VHrCost { get; set; }
    public decimal VHrConsumedCost { get; set; }
    public decimal VHrConsumedInclusiveCost { get; set; }
    public decimal DValue { get; set; }
    public decimal RValue { get; set; }
    public DateTime? ProposedFinalDate { get; set; }
    public DateTime? ProposedStartDate { get; set; }
    public decimal ProposedVHrAssigned { get; set; }
    public decimal ProposedVHrAssignedCost { get; set; }
    public string? ProposedPriority { get; set; }
    public decimal ProposedProbablity { get; set; }
    public decimal StageServicePercentage { get; set; }
    public decimal StageServiceAmount { get; set; }

    public bool IsExternalDependant { get; set; }

}
    public class PackageDto : PackageListDto
    {

        public string?  DeliveryMode { get; set; }

        public string?  Purpose { get; set; }

        public string?  EmailID { get; set; }
        public string?  CC { get; set; }

        public int Revision { get; set; }
        public bool IsAnnexure { get; set; }
        public int AnnexureIndex { get; set; }
        public string?  Annexure { get; set; }

    public virtual ICollection<PackageAttachmentDto> Attachments { get; set; } = new HashSet<PackageAttachmentDto>(); 
        public virtual ICollection<PackageAssociationDto> Associations { get; set; } = new HashSet<PackageAssociationDto>();

        public string?  SubmissionMessage { get; set; }

        public string?  CloudFile { get; set; }
        public int CompanyID { get; set; }

    public ICollection<MeetingAgendaDto> MeetingAgendas { get; set; } = new HashSet<MeetingAgendaDto>();

        public ICollection<PackageDeliverableDto> Deliverables { get; set; } = new HashSet<PackageDeliverableDto>();

    public virtual ICollection<PackageFeedbackDto> Feedbacks { get; set; } = new List<PackageFeedbackDto>();

    public bool IsSubmissionSetProcessed { get; set; }

    public List<SpaceDiveUrl>? SpaceDiveUrls { get; set; }
    public decimal VHrAssignedCost { get; set; } = 0;

    //public virtual ICollection<PackageDesignIntentDto> DesignIntents { get; set; } = new List<PackageDesignIntentDto>();


}

public class PackageDtoMapperProfile: Profile
{
    public PackageDtoMapperProfile()
    {
        CreateMap<Package, PackageDto>()
                    .ForMember(dest => dest.IsDelayed, opt => opt.MapFrom(src => src.FinalDate < DateTime.UtcNow))
            .ReverseMap()
          .ForMember(dest => dest.Project, opt => opt.Ignore())
               .ForMember(dest => dest.Attachments, opt => opt.Ignore())
                .ForMember(dest => dest.Deliverables, opt => opt.Ignore())
                 .ForMember(dest => dest.Feedbacks, opt => opt.Ignore())
                .ForMember(dest => dest.Associations, opt => opt.Ignore());

        CreateMap<Package, PackageListDto>()
            .ForMember(dest=> dest.IsDelayed,opt=>opt.MapFrom(src=> src.FinalDate < DateTime.UtcNow));


      


    }
}

