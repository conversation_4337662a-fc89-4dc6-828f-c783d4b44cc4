﻿

using System.Data;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.Exceptions;
using MyCockpitView.WebApi.ProjectModule.Entities;
using MyCockpitView.WebApi.Services;
using Newtonsoft.Json;

namespace MyCockpitView.WebApi.ProjectModule.Services;

public class ProjectScopeVersionService : BaseEntityService<ProjectScopeVersion>, IProjectScopeVersionService
{
    public ProjectScopeVersionService(EntitiesContext db) : base(db) { }

    public IQueryable<ProjectScopeVersion> Get(IEnumerable<QueryFilter>? Filters = null, string? Search = null, string? Sort = null)
    {

        IQueryable<ProjectScopeVersion> _query = base.Get(Filters);

        //Apply filters
        if (Filters != null)
        {

            if (Filters.Where(x => x.Key.Equals("projectID", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<ProjectScopeVersion>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("projectID", StringComparison.OrdinalIgnoreCase)))
                {
                    var isNumeric = Convert.ToInt32(_item.Value);

                    predicate = predicate.Or(x => x.ProjectID == isNumeric);
                }
                _query = _query.Where(predicate);
            }

        }

        if (Search != null && Search != String.Empty)
        {
            Search = Search.ToLower();
            _query = _query
                 .Where(x => x.Scopes.ToLower().Contains(Search.ToLower())
                                           );

        }

        if (Sort != null && Sort != String.Empty)
        {
            switch (Sort.ToLower())
            {
                case "createddate":
                    return _query
                            .OrderBy(x => x.Created);

                case "modifieddate":
                    return _query
                            .OrderBy(x => x.Modified);

                case "createddate desc":
                    return _query
                            .OrderByDescending(x => x.Created);

                case "modifieddate desc":
                    return _query
                            .OrderByDescending(x => x.Modified);


            }
        }

        return _query.OrderBy(x => x.OrderFlag);

    }

    public async Task<int> Create(int ProjectID)
    {

        var _projectScopes = await db.ProjectScopes.AsNoTracking()
            .Include(x => x.Services)
            .Where(x => x.ProjectID == ProjectID).ToListAsync();

        if (!_projectScopes.Any())
            throw new EntityServiceException("No scopes defined in project!");

        var _projectScopeVersion = new ProjectScopeVersion
        {
            ProjectID = ProjectID,
            Scopes = JsonConvert.SerializeObject(_projectScopes, Formatting.Indented, new JsonSerializerSettings
            {
                ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
                //PreserveReferencesHandling = PreserveReferencesHandling.All
            }),
            Created = DateTime.UtcNow
        };

        db.ProjectScopeVersions.Add(_projectScopeVersion);
        await db.SaveChangesAsync();

        return _projectScopeVersion.ID;

    }

}