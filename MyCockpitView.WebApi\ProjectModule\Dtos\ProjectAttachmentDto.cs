﻿using AutoMapper;
using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.ProjectModule.Entities;
using System.ComponentModel.DataAnnotations;

namespace MyCockpitView.WebApi.ProjectModule.Dtos;


public class ProjectAttachmentDto : BaseBlobEntityDto
{

    public int ProjectID { get; set; }


}

public class ProjectAttachmentDtoMapperProfile : Profile
{
    public ProjectAttachmentDtoMapperProfile()
    {
        CreateMap<ProjectAttachment, ProjectAttachmentDto>()
                   .ReverseMap();

    }
}