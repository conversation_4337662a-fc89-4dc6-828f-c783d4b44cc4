version: '3.4'

services:
  newarch-webapi:
    container_name: newarch-webapi
    image: newarchdockyard.azurecr.io/newarch-webapi
    build:
      context: .
    depends_on:
      - newarch-sqldb
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=https://+:443;http://+:80
      - ASPNETCORE_HTTPS_PORT=8081
      - SqlServerDbSettings__Server=newarch-sqldb
      - SqlServerDbSettings__Database=Newarch
      - SqlServerDbSettings__Password=MyC0ckpitView@2025
    volumes:
      - ${APPDATA}/Microsoft/UserSecrets:/root/.microsoft/usersecrets:ro
      - ${USERPROFILE}/.aspnet/https:/root/.aspnet/https:ro
    ports:
      - 8080:80
      - 8081:443

  newarch-sqldb:
    container_name: newarch-sqldb
    image: mcr.microsoft.com/mssql/server:2022-latest
    environment:
      - ACCEPT_EULA=Y
      - MSSQL_SA_PASSWORD=MyC0ckpitView@2025
      - MSSQL_PID=Express
    volumes:
      - "C:/Git/mssql/:/var/opt/mssql/"
    ports:
      - 1433:1433
