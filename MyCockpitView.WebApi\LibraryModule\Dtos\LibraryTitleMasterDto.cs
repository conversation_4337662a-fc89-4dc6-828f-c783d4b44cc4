﻿using AutoMapper;
using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.LibraryModule.Entities;
using System.ComponentModel.DataAnnotations;

namespace MyCockpitView.WebApi.LibraryModule.Dtos;

public class LibraryTitleMasterDto : BaseEntityDto
{
    [StringLength(255)]
    public string? Category { get; set; }

    [StringLength(255)]
    public string? Title { get; set; }

    [StringLength(255)]
    public string? Subtitle { get; set; }
}

public class LibraryTitleMasterDtoMapperProfile : Profile
{
    public LibraryTitleMasterDtoMapperProfile()
    {
        CreateMap<LibraryTitleMaster, LibraryTitleMasterDto>()
             .ReverseMap();

    }
}