﻿using AutoMapper;
using MyCockpitView.WebApi.ContactModule.Dtos;
using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.MeetingModule.Entities;

namespace MyCockpitView.WebApi.MeetingModule.Dtos;

public class MeetingDto : BaseEntityDto
{
    public string? Title { get; set; }
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public string? Code { get; set; }
    public int ContactID { get; set; }
    public virtual ContactListDto? Contact { get; set; }
    public decimal Version { get; set; }
    public DateTime? ClosedOn { get; set; }
    public DateTime? FinalizedOn { get; set; }
    public bool IsEditable { get; set; }
    public bool IsDelayed { get; set; }
    public int? ProjectID { get; set; }
    public int? EntityID { get; set; }
    public string? Entity { get; set; }
    public string? EntityTitle { get; set; }
    public string? Location { get; set; }

    public bool IsSent { get; set; }

    public IEnumerable<MeetingAttendeeDto> Attendees { get; set; }= new HashSet<MeetingAttendeeDto>();
    public IEnumerable<MeetingAgendaDto> Agendas { get; set; } = new HashSet<MeetingAgendaDto>();
    public int Annexure { get; set; }

    public int? FunctionID { get; set; }
    public decimal Sequence { get; set; }
}

public class MeetingDtoMapperProfile : Profile
{
    public MeetingDtoMapperProfile()
    {
        CreateMap<Meeting, MeetingDto>()
            .ForMember(dest => dest.Agendas, opt => opt.MapFrom(src => src.Agendas))
            .ForMember(dest => dest.Attendees, opt => opt.MapFrom(src => src.Attendees))
     .ReverseMap()
         .ForMember(dest => dest.Agendas, opt => opt.Ignore())
         .ForMember(dest => dest.Attendees, opt => opt.Ignore())
          .ForMember(dest => dest.Contact, opt => opt.Ignore());

    }
}

