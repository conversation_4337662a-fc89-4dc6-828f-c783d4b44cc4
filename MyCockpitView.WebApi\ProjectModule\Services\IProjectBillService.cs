﻿using MyCockpitView.CoreModule;
using MyCockpitView.Utility.RDLCClient;
using MyCockpitView.WebApi.ProjectModule.Entities;
using MyCockpitView.WebApi.Services;

namespace MyCockpitView.WebApi.ProjectModule.Services;

public interface IProjectBillService: IBaseEntityService<ProjectBill>
{
    Task<int> Create(ProjectBill Entity, IEnumerable<ProjectBillPayment> Payments = null);
    Task<int> CreatePayment(ProjectBillPayment Entity);
    Task<bool> DeletePayment(int Id);
    Task<IEnumerable<BillAnalysis>> GetAnalysisData(IEnumerable<QueryFilter>? Filters = null, string? Search = null, string? Sort = null);
    Task<byte[]> GetAnalysisExcel(IEnumerable<QueryFilter>? Filters = null, string? Search = null, string? Sort = null);

    Task<decimal> GetChequeAmount(int ID);
    Task<string> GetCode(int ProjectID, DateTime BillDate);
    string GetFilename(string Guid);
    Task<ProjectBill> GetLastBill(int id);
    Task<int> GetNextOrder(int companyID,DateTime BillDate);
    Task<ProjectBillPayment> GetPaymentById(int Id);
    Task<decimal> GetPendingAmount(int ID);
    Task<decimal> GetRecievedFees(int ProjectID, int BillID = 0);
    Task<ReportDefinition> GetReport(Guid UID);
    Task<ReportDefinition> GetReport(ProjectBill _bill);
    Task<bool> UpdatePayment(ProjectBillPayment Entity);

    Task<IEnumerable<ProjectActivity>> GetPendingBills(int ProjectID);
}