﻿using AutoMapper;
using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.ContactModule.Dtos;
using MyCockpitView.WebApi.ProjectModule.Entities;
using System.ComponentModel.DataAnnotations;

namespace MyCockpitView.WebApi.ProjectModule.Dtos;

public class ProjectAssociationDto : BaseEntityDto
{
    public string? Title { get; set; }
    public int ProjectID { get; set; }
    public int ContactID { get; set; }
    public virtual ContactListDto? Contact { get; set; }
    public decimal ValueHours { get; set; }

    public decimal ValueHourRate { get; set; }
    public decimal ShareValue { get; set; }
}

public class ProjectAssociationDtoMapperProfile : Profile
{
    public ProjectAssociationDtoMapperProfile()
    {
        CreateMap<ProjectAssociation, ProjectAssociationDto>()
                   .ReverseMap();

    }
}