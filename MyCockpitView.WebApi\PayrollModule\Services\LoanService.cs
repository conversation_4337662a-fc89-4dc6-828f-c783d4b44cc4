﻿using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.PayrollModule.Entities;
using MyCockpitView.WebApi.Services;


using System.Data;



namespace MyCockpitView.WebApi.PayrollModule.Services;
public class LoanService : BaseEntityService<Loan>,ILoanService
{
    public LoanService(EntitiesContext db) : base(db) {}

    public IQueryable<Loan> Get( IEnumerable<QueryFilter>? Filters = null, string? Search = null, string? Sort = null)
    {
      
            IQueryable<Loan> _query = base.Get(Filters);

            //Apply filters
            if (Filters != null)
            {
                
                if (Filters.Where(x => x.Key.Equals("contactID", StringComparison.OrdinalIgnoreCase)).Any())
                {
                    var predicate = PredicateBuilder.False<Loan>();
                    foreach (var _item in Filters.Where(x => x.Key.Equals("contactID", StringComparison.OrdinalIgnoreCase)))
                    {
                        var isNumeric = Convert.ToInt32(_item.Value);
                        
                            predicate = predicate.Or(x => x.PersonContactID == isNumeric);
                    }
                    _query = _query.Where(predicate);
                }

                if (Filters.Where(x => x.Key.Equals("companyID", StringComparison.OrdinalIgnoreCase)).Any())
                {
                    var predicate = PredicateBuilder.False<Loan>();
                    foreach (var _item in Filters.Where(x => x.Key.Equals("companyID", StringComparison.OrdinalIgnoreCase)))
                    {
                        var isNumeric = Convert.ToInt32(_item.Value);

                        predicate = predicate.Or(x => x.CompanyID == isNumeric);
                    }
                    _query = _query.Where(predicate);
                }


            }

            if (Search != null && Search != String.Empty)
            {
                Search = Search.ToLower();
                    _query = _query
                         .Where(x => x.PersonName.ToLower().Contains( Search.ToLower())
                                                   );
                
            }

            if (Sort != null && Sort != String.Empty)
            {
                switch (Sort.ToLower())
                {
                    case "createddate":
                        return _query
                                .OrderBy(x => x.Created);

                    case "modifieddate":
                        return _query
                                .OrderBy(x => x.Modified);

                    case "createddate desc":
                        return _query
                                .OrderByDescending(x => x.Created);

                    case "modifieddate desc":
                        return _query
                                .OrderByDescending(x => x.Modified);

                }
            }

            return _query.OrderByDescending(x => x.Created);

    }

}