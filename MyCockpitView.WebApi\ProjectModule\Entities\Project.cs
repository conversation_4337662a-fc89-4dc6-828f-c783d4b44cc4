using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.WebApi.ContactModule.Entities;
using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.CompanyModule.Entities;

using System.ComponentModel.DataAnnotations;

namespace MyCockpitView.WebApi.ProjectModule.Entities;

public class Project : BaseEntity
    {
    [StringLength(255)]
    public string? Code { get; set; }
    [StringLength(255)]
    public string? Title { get; set; }
    [StringLength(255)]
    public string? Location { get; set; }
    [StringLength(255)]
    public string? City { get; set; }
    [StringLength(255)]
    public string? State { get; set; }
    [StringLength(255)]
    public string? Country { get; set; }
    [StringLength(255)]
    public string? StateCode { get; set; }

        public DateTime? ContractCompletionDate { get; set; }

        public DateTime? ExpectedCompletionDate { get; set; }

        public DateTime? InquiryConvertionDate { get; set; }

    [StringLength(255)]
    public string? BillingTitle { get; set; }
        
        public int CompanyID { get; set; }
        public virtual Company? Company { get; set; }
        public string? Segment { get; set; }
        public int? ClientContactID { get; set; }
        public virtual Contact? ClientContact { get; set; }

        public int? ReferredByContactID { get; set; }
        public virtual Contact? ReferredByContact { get; set; }
    [Precision(14, 2)]
    public decimal? ExpectedMHr { get; set; } = 0;

        public virtual ICollection<ProjectInward> Inwards { get; set; } = new HashSet<ProjectInward>();
        public virtual ICollection<ProjectNote> Notes { get; set; } = new HashSet<ProjectNote>();
        public virtual ICollection<ProjectAttachment> Attachments { get; set; }= new HashSet<ProjectAttachment>();
        public virtual ICollection<ProjectAssociation> Associations { get; set; } = new HashSet<ProjectAssociation>();

    public string? Comment { get; set; }
    [Precision(14, 2)]
    public decimal TotalFee { get; set; } = 0;
    [Precision(14, 2)]
    public decimal CompanyFee { get; set; } = 0;
    [Precision(14, 2)]
    public decimal Discount { get; set; } = 0;
    [Precision(14, 2)]

    public bool IsRepeatClient { get; set; }

    public virtual ICollection<ProjectBill> Bills { get; set; } = new List<ProjectBill>();
    public virtual ICollection<ProjectConsultant> Consultants { get; set; } = new List<ProjectConsultant>();
   
    public virtual ICollection<ProjectScope> Scopes { get; set; } = new List<ProjectScope>();
    public virtual ICollection<ProjectScopeVersion> ScopeVersions { get; set; } = new List<ProjectScopeVersion>();
    public virtual ICollection<ProjectOutward> Outwards { get; set; } = new List<ProjectOutward>();

    public virtual Company? CompanyAccount { get; set; }
    public string? OfferType { get; set; }
    public DateTime? OfferDue { get; set; }
    public virtual ICollection<ProjectArea> Areas { get; set; } = new HashSet<ProjectArea>();

    public virtual ICollection<ProjectGigPoint> GigPoints { get; set; }= new HashSet<ProjectGigPoint>();

    public string? HSNCode { get; set; }

}
public class ProjectConfiguration : BaseEntityConfiguration<Project>, IEntityTypeConfiguration<Project>
{
    public void Configure(EntityTypeBuilder<Project> builder)
    {
        base.Configure(builder);

        // Relationships
        builder.HasOne(p => p.Company)
            .WithMany()
            .HasForeignKey(p => p.CompanyID)
            .IsRequired(false)
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasOne(p => p.ClientContact)
            .WithMany()
            .HasForeignKey(p => p.ClientContactID)
            .IsRequired(false)
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasOne(p => p.ReferredByContact)
            .WithMany()
            .HasForeignKey(p => p.ReferredByContactID)
            .IsRequired(false)
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasIndex(e => e.Location);
        builder.HasIndex(e => e.BillingTitle);
        builder.HasIndex(e => e.Title);
        builder.HasIndex(e => e.Code);
        builder.HasIndex(e => e.Segment);
    }
}
