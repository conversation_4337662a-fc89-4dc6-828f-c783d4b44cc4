﻿

using System.Data;

using System.Globalization;




using MyCockpitView.WebApi.Exceptions;
using System.Text.RegularExpressions;
using MyCockpitView.WebApi.Services;
using MyCockpitView.WebApi.ProjectModule.Entities;
using MyCockpitView.CoreModule;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.Utility.Common;
using MyCockpitView.WebApi.AzureBlobsModule;
using MyCockpitView.Utility.RDLCClient;
using MyCockpitView.Utility.Excel;

namespace MyCockpitView.WebApi.ProjectModule.Services;

public class ProjectBillService : BaseEntityService<ProjectBill>, IProjectBillService
{
    public ProjectBillService(EntitiesContext db) : base(db) { }
    public IQueryable<ProjectBill> Get(IEnumerable<QueryFilter>? Filters = null, string? Search = null, string? Sort = null)
    {

        var _query = base.Get(Filters);



        //Apply filters
        if (Filters != null)
        {


            if (Filters.Where(x => x.Key.Equals("isPaymentDue", StringComparison.OrdinalIgnoreCase)).Any())
            {
                _query = _query.Include(x => x.Payments).Where(x => !x.Payments.Any());
            }

            if (Filters.Where(x => x.Key.Equals("projectID", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<ProjectBill>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("projectID", StringComparison.OrdinalIgnoreCase)))
                {
                    var isNumeric = Convert.ToInt32(_item.Value);

                    predicate = predicate.Or(x => x.ProjectID == isNumeric);
                }
                _query = _query.Where(predicate);
            }

            if (Filters.Where(x => x.Key.Equals("CompanyID", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<ProjectBill>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("CompanyID", StringComparison.OrdinalIgnoreCase)))
                {
                    var isNumeric = Convert.ToInt32(_item.Value);

                    predicate = predicate.Or(x => x.CompanyID == isNumeric);
                }
                _query = _query.Where(predicate);
            }

            if (Filters.Where(x => x.Key.Equals("projectstatusFlag", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<ProjectBill>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("projectstatusFlag", StringComparison.OrdinalIgnoreCase)))
                {
                    var isNumeric = Convert.ToInt32(_item.Value);

                    predicate = predicate.Or(x => x.Project.StatusFlag == isNumeric);
                }
                _query = _query.Include(x => x.Project).Where(predicate);
            }

            if (Filters.Where(x => x.Key.Equals("rangestart", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var _item = Filters.First(x => x.Key.Equals("rangestart", StringComparison.OrdinalIgnoreCase));

                DateTime result = Convert.ToDateTime(_item.Value);
                _query = _query.Where(x => x.BillDate >= result);
            }

            if (Filters.Where(x => x.Key.Equals("rangeend", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var _item = Filters.First(x => x.Key.Equals("rangeend", StringComparison.OrdinalIgnoreCase));
                DateTime result = Convert.ToDateTime(_item.Value);

                var end = result.AddDays(1);
                _query = _query.Where(x => x.BillDate < end);

            }
        }

        if (Search != null && Search != String.Empty)
        {
            _query = _query.Include(x => x.Project.ClientContact);

            _query = _query.Where(x => x.Project.Code.ToString().ToLower().Contains(Search.ToLower())
                                    || x.Project.Title.ToLower().Contains(Search.ToLower())
                                    || x.BillNo.ToLower().Contains(Search.ToLower())
                                    || (x.Project.ClientContact.FirstName + " " + x.Project.ClientContact.LastName).ToLower().Contains(Search.ToLower())
                                       );

        }

        if (Sort != null && Sort != String.Empty)
        {


            if (Sort.ToLower().Contains("projecttitle") || Sort.ToLower().Contains("projectcode"))
                _query = _query.Include(x => x.Project);

            if (Sort.ToLower().Contains("client") || Sort.ToLower().Contains("client"))
                _query = _query.Include(x => x.Project.ClientContact);

            var _orderedQuery = _query.OrderBy(l => 0);
            var keywords = Sort.Replace("asc", "").Split(',');

            foreach (var key in keywords)
            {
                if (key.Trim().Equals("created", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenBy(x => x.Created);

                else if (key.Trim().Equals("created desc", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenByDescending(x => x.Created);

                else if (key.Trim().Equals("modified", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenBy(x => x.Modified);

                else if (key.Trim().Equals("modified desc", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenByDescending(x => x.Modified);

                else if (key.Trim().Equals("billdate", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenBy(x => x.BillDate);

                else if (key.Trim().Equals("billdate desc", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenByDescending(x => x.BillDate);

                else if (key.Trim().Equals("projecttitle", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenBy(x => x.Project.Title);

                else if (key.Trim().Equals("projectcode", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenBy(x => x.Project.Code.ToString());


                else if (key.Trim().Equals("client", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenBy(x => x.Project.ClientContact.FullName);
            }

            return _orderedQuery;
        }

        return _query
          .OrderByDescending(x => x.BillDate);

    }

    public async Task<ProjectBill?> GetById(int Id)
    {

        return await db.ProjectBills.AsNoTracking()
          .Include(x => x.Payments)
             .SingleOrDefaultAsync(i => i.ID == Id);

    }

    public async Task<decimal> GetPendingAmount(int ID)
    {

        var _bill = await Get()
              .SingleOrDefaultAsync(i => i.ID == ID);

        if (_bill.TypeFlag == 1)
        {
            var _totalPayment = 0.0m;
            foreach (var obj in _bill.Payments)
            {
                _totalPayment = _totalPayment + obj.BillAmountReceived;
            }
            if (_totalPayment < _bill.Amount)
            {
                return Math.Round(_bill.Amount - _totalPayment, 2);
            }
        }

        return 0;

    }

    public async Task<decimal> GetChequeAmount(int ID)
    {

        var _bill = await Get()
              .SingleOrDefaultAsync(i => i.ID == ID);

        var _tds = Convert.ToDecimal((await db.AppSettingMasters.AsNoTracking()
            .SingleOrDefaultAsync(x => x.PresetKey.ToUpper().Trim() == "TAX_TDS")).PresetValue);

        return Math.Round(_bill.Payable / ((_tds / 100) + 1), 2);

    }

    public async Task<decimal> GetRecievedFees(int ProjectID, int BillID = 0)
    {

        var _query = Get().Where(x => x.ProjectID == ProjectID && x.TypeFlag == 1);

        _query = _query.Where(x => x.ID != BillID);

        var _bills = await _query.ToListAsync();
        var _total = 0.0m;
        foreach (var bill in _bills)
        {
            _total = _total + bill.BillAmount;
        }
        return _total;

    }

    public async Task<String> GetCode(int ProjectID, DateTime BillDate)
    {

        var _project = await db.Projects.AsNoTracking()
            .SingleOrDefaultAsync(x => x.ID == ProjectID);
        if (_project == null) return null;



        var _projectBillCount = await Get()
                       .Where(x => x.ProjectID == ProjectID
                       && x.TypeFlag == McvConstant.PROJECT_BILL_TYPEFLAG_INVOICE)
                       .CountAsync();

        var _projectBillCode = (_project.Code.ToString());

        if (_projectBillCount <= 26)
        {
            string alpha = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";

            _projectBillCode = _projectBillCode + "-" + alpha[Convert.ToInt32(_projectBillCount)];
        }

        var _index = await GetNextOrder(_project.CompanyID,BillDate);

        return ClockTools.GetFinancialYearShort(BillDate) + "/" + _index.ToString("000") + "/" + _projectBillCode;

    }

    public async Task<int> GetNextOrder(int companyID,DateTime BillDate)
    {

        var _start = new DateTime(BillDate.Month < 4 ? BillDate.AddYears(-1).Year : BillDate.Year, 4, 1);
        var _end = _start.AddYears(1);

        var _lastBills = Get()
                           .Where(x => x.TypeFlag == McvConstant.PROJECT_BILL_TYPEFLAG_INVOICE
                           && x.CompanyID == companyID
                           && x.BillDate >= _start
                           && x.BillDate < _end);

        if (await _lastBills.AnyAsync())
            return await _lastBills.MaxAsync(x => x.OrderFlag) + 1;

        return 1;

    }

    public async Task<int> Create(ProjectBill Entity, IEnumerable<ProjectBillPayment> Payments = null)
    {
        var project = await db.Projects.AsNoTracking()
            .Include(x=>x.ClientContact)
            .SingleOrDefaultAsync(x => x.ID == Entity.ProjectID);

        if (project == null) throw new EntityServiceException($"{nameof(Project)} not found!");

        var company = await db.Companies.AsNoTracking()
            .SingleOrDefaultAsync(x => x.ID == project.CompanyID);

        Entity.CompanyID = company.ID;
        Entity.CompanyName = company.Title;
        Entity.CompanyAddress = company.Address;
        Entity.CompanyGSTIN = company.GSTIN;
        Entity.CompanyPAN = company.PAN;
        Entity.CompanyUDHYAM = company.UDHYAM;
        Entity.CompanyGSTStateCode = company.GSTStateCode;
        Entity.CompanyBank = company.Bank;
        Entity.CompanyBankBranch = company.BankBranch;
        Entity.CompanyBankAccount = company.BankAccount;
        Entity.CompanyBankIFSCCode = company.BankIFSCCode;
        Entity.CompanySwiftCode = company.SwiftCode;
        Entity.CompanySignStampUrl = company.SignStampUrl;
        Entity.CompanyLogoUrl = company.LogoUrl;

        Entity.ClientContactID = project.ClientContactID;
        Entity.ClientName = project.ClientContact.FullName;
        //Entity.ClientEmail = project.ClientContact.Email1;
        //Entity.ClientPhone = project.ClientContact.Phone1;
        //Entity.ClientAddress = project.ClientContact.pr;
        Entity.ClientGSTIN = project.ClientContact.GSTIN;
        Entity.ClientPAN = project.ClientContact.PAN;
        Entity.ClientTAN = project.ClientContact.TAN;
        //Entity.ClientGSTStateCode = project.GSTStateCode;

        if ((Entity.BillNo == null || Entity.BillNo == String.Empty) && Entity.TypeFlag == 1)
        {
            Entity.BillNo = await GetCode(Entity.ProjectID, Entity.BillDate);
            Entity.BillConversionDate = DateTime.UtcNow;
            Entity.OrderFlag = await GetNextOrder(project.CompanyID,Entity.BillDate);
        }

        Entity.AmountInWords = Entity.Payable.ToWords();


        db.ProjectBills.Add(Entity);
        await db.SaveChangesAsync();

        if (Payments != null && Payments.Any())
        {
            foreach (var _item in Payments)
            {
                _item.ProjectBillID = Entity.ID;
                await CreatePayment(_item);
            }
        }

        var _project = await db.Projects.AsNoTracking().SingleOrDefaultAsync(x => x.ID == Entity.ProjectID);
        if (_project != null)
        {
            //  _project.TypeFlag = 0;
            db.Entry(_project).State = EntityState.Modified;
            await db.SaveChangesAsync();

        }

        //var _client = await _db.Contacts.AsNoTracking().SingleOrDefaultAsync(x => x.ID == Entity.ClientContactID);
        //if (_client != null)
        //{
        //    Entity.ClientName=_client.FullName;
        //    Entity.ClientHSN = _client.HSN;
        //    Entity.ClientGSTIN = _client.GSTIN;

        //}

        //var _attendant = await _db.Contacts.AsNoTracking().SingleOrDefaultAsync(x => x.ID == Entity.AttendantContactID);
        //if (_attendant != null)
        //{
        //    Entity.AttendantName = _attendant.FullName;
        //}

        var sharedService = new SharedService(db);
        var azureBlobService = new AzureBlobService();
        var reportDefinition = await GetReport(Entity);
        if (reportDefinition != null)
        {
            var azureBlobKey = await sharedService.GetPresetValue(McvConstant.AZURE_STORAGE_KEY);

            Entity.BlobUrl = await azureBlobService.UploadAsync(azureBlobKey, await sharedService.GetPresetValue(McvConstant.BLOB_CONTAINER_ATTACHMENTS), $"{nameof(ProjectBill)}/{Entity.UID}/{Guid.NewGuid()}/{reportDefinition.Filename}{reportDefinition.FileExtension}", new MemoryStream(reportDefinition.FileContent));
            await db.SaveChangesAsync();
        }

        return Entity.ID;

    }

    public async Task Update(ProjectBill Entity)
    {

        //convert from Proforma to Invoice
        if ((Entity.BillNo == null
            || Entity.BillNo == String.Empty
            || Entity.BillNo.Trim().ToLower().Contains("proforma"))
            && Entity.TypeFlag == 1)
        {
            Entity.BillConversionDate = DateTime.UtcNow;
            Entity.BillNo = await GetCode(Entity.ProjectID, Entity.BillDate); //need new Billdate for getting sequence no.
            Entity.OrderFlag = await GetNextOrder(Entity.CompanyID.Value,Entity.BillDate);
            Entity.StatusFlag = 0;
        }

        Entity.AmountInWords = Entity.Payable.ToWords();

        //var _client = await _db.Contacts.AsNoTracking().SingleOrDefaultAsync(x => x.ID == Entity.ClientContactID);
        //if (_client != null)
        //{
        //    Entity.ClientName = _client.FullName;
        //    Entity.ClientHSN = _client.HSN;
        //    Entity.ClientGSTIN = _client.GSTIN;

        //}

        //var _attendant = await _db.Contacts.AsNoTracking().SingleOrDefaultAsync(x => x.ID == Entity.AttendantContactID);
        //if (_attendant != null)
        //{
        //    Entity.AttendantName = _attendant.FullName;
        //}


        var sharedService = new SharedService(db);
        var azureBlobService = new AzureBlobService();
        var reportDefinition = await GetReport(Entity);
        if (reportDefinition != null)
        {
            var azureBlobKey = await sharedService.GetPresetValue(McvConstant.AZURE_STORAGE_KEY);

            Entity.BlobUrl = await azureBlobService.UploadAsync(azureBlobKey, await sharedService.GetPresetValue(McvConstant.BLOB_CONTAINER_ATTACHMENTS), $"{nameof(ProjectBill)}/{Entity.UID}/{Guid.NewGuid()}/{reportDefinition.Filename}{reportDefinition.FileExtension}", new MemoryStream(reportDefinition.FileContent));

        }
        db.Entry(Entity).State = EntityState.Modified;

        await db.SaveChangesAsync();



    }

    public async Task Delete(int Id)
    {

        var Entity = await Get()
             .SingleOrDefaultAsync(i => i.ID == Id);

        Entity.IsDeleted = true;

        db.Entry(Entity).State = EntityState.Modified;

        await db.SaveChangesAsync();

    }

    public async Task<IEnumerable<BillAnalysis>> GetAnalysisData(IEnumerable<QueryFilter>? Filters = null, string? Search = null, string? Sort = null)
    {

        var _statusMasters =await db.StatusMasters.AsNoTracking()
            .Where(x => x.Entity == nameof(Project))
            .Select(x=> new
            {
                x.Title,
                x.Value
            })
            .ToListAsync();

        var _billStatusMasters =await db.StatusMasters.AsNoTracking()
            .Where(x => x.Entity==nameof(ProjectBill))
                        .Select(x => new
                        {
                            x.Title,
                            x.Value
                        })
            .ToListAsync();

        var _typeMaster =await db.TypeMasters.AsNoTracking()
            .Where(x => x.Entity==nameof(ProjectBill))
                        .Select(x => new
                        {
                            x.Title,
                            x.Value
                        })
            .ToListAsync();


        var _query = await Get(Filters)
        .Include(x => x.Project).ThenInclude(c=>c.Associations).ThenInclude(c => c.Contact)
        .Include(x => x.Payments)
        .Select(x=> new
        {
            x.ID,
            x.ProjectID,
            ProjectCode=x.Project.Code,
            ProjectTitle=x.Project.Title,
            ProjectPartner= x.Project.Associations.Any(a => a.TypeFlag == McvConstant.PROJECT_ASSOCIATION_TYPEFLAG_PARTNER) ?x.Project.Associations.FirstOrDefault(a => a.TypeFlag == McvConstant.PROJECT_ASSOCIATION_TYPEFLAG_PARTNER).Contact.FullName : "N/A",
            ProjectStatusFlag = x.Project.StatusFlag,
            ProjectCompanyFee=x.Project.CompanyFee,
            x.ClientName,
            x.ClientGSTIN,
            x.ClientGSTStateCode,
            x.ClientHSN,
            x.BillNo,
            x.BillDate,
            x.WorkCompletion,
            x.Amount,
            x.Payable,
            x.PreviousBillAmount,
            x.BillAmount,
            x.CGSTShare,
            x.CGSTAmount,
            x.IGSTShare,
            x.IGSTAmount,
            x.SGSTShare,
            x.SGSTAmount,
            x.Created,
            x.TypeFlag,
            x.StatusFlag,
            Payments= x.Payments.Select(p=> new
            {
                p.ID,
                p.Amount,
                p.TransactionDate
            }),
            x.Remarks,
            x.CompanyID,
            x.CompanyName,

        })
        .ToListAsync();

       var result= _query
            .Select(x => new BillAnalysis
        {
            ID = x.ID,
            ProjectCode = x.ProjectCode.ToString(),
            ProjectID = x.ProjectID,
            Project = x.ProjectTitle,
            ProjectStatus = _statusMasters.FirstOrDefault(m => m.Value == x.ProjectStatusFlag).Title,
            Partner = x.ProjectPartner,
            Client = x.ClientName,
            TotalAmount = x.ProjectCompanyFee,
            TotalReceived = x.PreviousBillAmount,
            WorkCompletion = x.WorkCompletion,
            BillNo = x.BillNo,
            CreatedDate = x.Created,
            BillDate = x.BillDate,
            CurrentAmount = x.BillAmount,
            CurrentRecieved = x.Payments.Any() ? x.Payments.Sum(b => b.Amount) : 0,
            PaymentDate = x.Payments.Any() ? x.Payments.OrderByDescending(c => c.TransactionDate).FirstOrDefault().TransactionDate : (DateTime?)null,
            Score = x.Payments.Any() ? x.Payments.Sum(b => b.Amount - (x.CGSTAmount + x.IGSTAmount + x.SGSTAmount)) : 0,

            IGSTShare = x.IGSTShare,
            IGSTAmount = x.IGSTAmount,
            CGSTShare = x.CGSTShare,
            CGSTAmount = x.CGSTAmount,
            SGSTShare = x.SGSTShare,
            SGSTAmount = x.SGSTAmount,

            StateCode = x.ClientGSTStateCode,
            GSTIN = x.ClientGSTIN,
            HSN = x.ClientHSN,
            Remarks = x.Remarks,
            TypeFlag = x.TypeFlag,
            StatusFlag = x.StatusFlag,
            Status = _billStatusMasters.FirstOrDefault(s => s.Value == x.StatusFlag).Title,
            Type = _typeMaster.FirstOrDefault(s => s.Value == x.TypeFlag).Title,
            CompanyID=x.CompanyID.Value,
            CompanyName=x.CompanyName
        });

        if (Search != null && Search != String.Empty)
        {


            result = result.Where(x => x.Client.ToLower().Contains(Search.ToLower())
                                    || x.Project.ToLower().Contains(Search.ToLower())
                                    || x.BillNo.ToLower().Contains(Search.ToLower())
                                    || x.Partner.ToLower().Contains(Search.ToLower())
                                    || x.Status.ToLower().Contains(Search.ToLower())
                                       || x.CompanyName.ToLower().Contains(Search.ToLower())
                                       );

        }

        if (Sort != null && Sort != String.Empty)
        {



            var _orderedQuery = result.OrderBy(l => 0);
            var keywords = Sort.Replace("asc", "").Split(',');

            foreach (var key in keywords)
            {
                if (key.Trim().Equals("billdate", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenBy(x => x.BillDate);

                else if (key.Trim().Equals("billdate desc", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenByDescending(x => x.BillDate);

                else if (key.Trim().Equals("cr", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenBy(x => x.Partner);

                else if (key.Trim().Equals("cr desc", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenBy(x => x.Partner);

                else if (key.Trim().Equals("leader", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenBy(x => x.Partner);

                else if (key.Trim().Equals("leader desc", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenByDescending(x => x.Partner);

                else if (key.Trim().Equals("project", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenBy(x => x.Project);

                else if (key.Trim().Equals("project desc", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenByDescending(x => x.Project);

                else if (key.Trim().Equals("projectCode", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenBy(x => x.ProjectCode);

                else if (key.Trim().Equals("projectCode desc", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenByDescending(x => x.ProjectCode);

                else if (key.Trim().Equals("client", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenBy(x => x.Client);

                else if (key.Trim().Equals("client desc", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenByDescending(x => x.Client);

                else if (key.Trim().Equals("status", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenBy(x => x.Status);

                else if (key.Trim().Equals("status desc", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenByDescending(x => x.Status);
            }

            return  _orderedQuery.ToList();
        }

        return  result
          .OrderByDescending(x => x.BillDate).ToList();

    }

    public async Task<byte[]> GetAnalysisExcel(IEnumerable<QueryFilter>? Filters = null, string? Search = null, string? Sort = null)
    {

        var _dataSet = new DataSet();
        var results = await GetAnalysisData(Filters, Search, Sort);

        foreach (var item in results)
        {
            item.CreatedDate = ClockTools.GetIST(item.CreatedDate);
            item.BillDate = ClockTools.GetIST(item.BillDate);
            if (item.PaymentDate != null)
                item.PaymentDate = ClockTools.GetIST(item.PaymentDate.Value);
        }

        _dataSet.Tables.Add(DataTools.ToDataTable(results));


        return ExcelUtility.ExportExcel(_dataSet);

    }



    public async Task<ProjectBill> GetLastBill(int id)
    {

        return await Get().Include(x => x.Payments)
            .Where(x => x.ProjectID == id)
            .OrderByDescending(x => x.BillDate)
            .FirstOrDefaultAsync();

    }


    #region Payment

    public async Task<ProjectBillPayment> GetPaymentById(int Id)
    {

        return await db.ProjectBillPayments
            .AsNoTracking()
             .SingleOrDefaultAsync(i => i.ID == Id);

    }

    public async Task<int> CreatePayment(ProjectBillPayment Entity)
    {

        db.ProjectBillPayments.Add(Entity);
        await db.SaveChangesAsync();

        return Entity.ID;

    }

    public async Task<bool> UpdatePayment(ProjectBillPayment Entity)
    {


        db.Entry(Entity).State = EntityState.Modified;

        await db.SaveChangesAsync();

        return true;

    }

    public async Task<bool> DeletePayment(int Id)
    {

        var _entity = await db.ProjectBillPayments
             .SingleOrDefaultAsync(i => i.ID == Id);

        if (_entity == null)
        {
            return false;
        }

        db.ProjectBillPayments.Remove(_entity);

        await db.SaveChangesAsync();

        return true;

    }

    #endregion Payment


    #region RDLC


    public string? GetFilename(string Guid)
    {
        return Guid.Split(']')[1];

    }

    public async Task<ReportDefinition> GetReport(Guid UID)
    {

        var _bill = await Get()
            .SingleOrDefaultAsync(x => x.UID == UID);

        return await GetReport(_bill);

    }

    public async Task<ReportDefinition> GetReport(ProjectBill _bill)
    {
        if (_bill == null) throw new EntityServiceException("Bill/Proforma not found!");
        var projectService = new ProjectService(db);
        var project = await projectService.Get()
          .SingleOrDefaultAsync(x => x.ID == _bill.ProjectID);

        if (project == null) throw new EntityServiceException($"{nameof(Project)} not found!");

        var company = await db.Companies.AsNoTracking()
            .SingleOrDefaultAsync(x => x.ID == project.CompanyID);

        var textInfo = new CultureInfo("en-IN", false).TextInfo;
        var _projectName = project.Code + "-" + textInfo.ToTitleCase(project.Title.ToLower());

        var sharedService = new SharedService(db); ;
        var _reportProperties = new List<ReportProperties>
            {
                new ReportProperties() { PropertyName = "Project", PropertyValue = (project.BillingTitle + " At " + project.Location.ToUpper()) },
                new ReportProperties() { PropertyName = "ReportType", PropertyValue = _bill.TypeFlag == 1 ? "Bill" : "Proforma" },
                new ReportProperties() { PropertyName = "Fees", PropertyValue = project.TotalFee.ToString() },
                new ReportProperties() { PropertyName = "CompanyPANNo", PropertyValue = company.PAN },
                new ReportProperties() { PropertyName = "CompanyMSMECode", PropertyValue = company.UDHYAM },
                new ReportProperties() { PropertyName = "CompanyGSTCode", PropertyValue = company.GSTIN },
                new ReportProperties() { PropertyName = "CompanyHSNCode", PropertyValue = project.HSNCode },
                new ReportProperties() { PropertyName = "CompanyAddress", PropertyValue = company.Address },
                  new ReportProperties() { PropertyName = "CompanyName", PropertyValue = company.Title },
                          new ReportProperties() { PropertyName = "CompanyBankName", PropertyValue = company.Bank },
                                  new ReportProperties() { PropertyName = "CompanyBankBranch", PropertyValue = company.BankBranch },
                    new ReportProperties() { PropertyName = "CompanyBankAccount", PropertyValue = company.BankAccount },
                      new ReportProperties() { PropertyName = "CompanyBankIFSCCode", PropertyValue = company.BankIFSCCode },
                       new ReportProperties() { PropertyName = "CompanyLogo", PropertyValue = company.LogoUrl },
                                   new ReportProperties() { PropertyName = "CompanySignStamp", PropertyValue = company.SignStampUrl!=null ? company.SignStampUrl : "NA"},
                        new ReportProperties() { PropertyName = "ShowHeader", PropertyValue = (_bill.TypeFlag==McvConstant.PROJECT_BILL_TYPEFLAG_INVOICE && company.Initials=="NIT" ? "True" : "False") },
                        new ReportProperties() { PropertyName = "IRN", PropertyValue = (_bill.IRNNo!=null ? _bill.IRNNo : "") }
            };

        var _previousBills = await db.ProjectBills.AsNoTracking()
         
         .Where(x => x.ProjectID == _bill.ProjectID
         && x.TypeFlag == 1
         && x.BillDate <= _bill.BillDate
         && x.ID != _bill.ID)
         .ToListAsync();

        var _billData = _previousBills.Any() ?
            _previousBills.Select(Prev => new BillRDLCData
            {
                BillNo = _bill.BillNo,
                WorkCompletion = Math.Round(_bill.WorkCompletion, 2),
                BillDate = _bill.BillDate,
                Amount = _bill.Amount,
                GSTShare = _bill.GSTShare,
                GSTAmount = _bill.GSTAmount,
                GSTFlag = _bill.GSTFlag,
                CGSTShare = _bill.CGSTShare,
                CGSTAmount = _bill.CGSTAmount,
                IGSTShare = _bill.IGSTShare,
                IGSTAmount = _bill.IGSTAmount,
                SGSTShare = _bill.SGSTShare,
                SGSTAmount = _bill.SGSTAmount,

                Total = _bill.BillAmount,
                PreviousAmount = _bill.PreviousBillAmount,
                Payable = _bill.Payable,
                TypeFlag = _bill.TypeFlag,
                ClientName = _bill.ClientName,
                ClientAddress = _bill.ClientAddress,
                ReverseTaxCharges = _bill.ReverseTaxCharges,
                GSTIN = _bill.ClientGSTIN,
                HSN = _bill.ClientHSN,
                AmountInWords = _bill.AmountInWords,
                StateCode = _bill.ClientGSTStateCode,
                PrevBillDate = Prev.BillDate,
                PrevWorkCompletion = Math.Round(Prev.WorkCompletion, 2),
                PrevAmount = Prev.BillAmount,
                WorkDetails = _bill.WorkDetails,
                Remarks = _bill.Remarks,
                Attn = _bill.AttendantName,
                TDSAmount = _bill.TDSAmount

            }) :
            new List<BillRDLCData> {new BillRDLCData
                {
                      BillNo = _bill.BillNo,

                    WorkCompletion = Math.Round(_bill.WorkCompletion, 2),
                    BillDate = _bill.BillDate,
                    Amount = _bill.Amount,
                    GSTShare = _bill.GSTShare,
                    GSTAmount = _bill.GSTAmount,
                    GSTFlag = _bill.GSTFlag,
                    CGSTShare = _bill.CGSTShare,
                    CGSTAmount = _bill.CGSTAmount,
                    IGSTShare = _bill.IGSTShare,
                    IGSTAmount = _bill.IGSTAmount,
                    SGSTShare = _bill.SGSTShare,
                    SGSTAmount = _bill.SGSTAmount,

                    Total = _bill.BillAmount,
                    PreviousAmount = _bill.PreviousBillAmount,
                    Payable = _bill.Payable,
                    TypeFlag = _bill.TypeFlag,
                    ClientName = _bill.ClientName,
                    ClientAddress = _bill.ClientAddress,
                    ReverseTaxCharges = _bill.ReverseTaxCharges,
                    GSTIN = _bill.ClientGSTIN,
                    HSN = _bill.ClientHSN,
                    AmountInWords = _bill.AmountInWords,
                    StateCode = _bill.ClientGSTStateCode,
                     WorkDetails=_bill.WorkDetails,
                         Remarks=_bill.Remarks,
                    Attn=_bill.AttendantName,
                    TDSAmount=_bill.TDSAmount
                } };

        var _reportContainerUrl = await sharedService.GetPresetValue(McvConstant.RDLC_REPORT_CONTAINER_URL);
        var DEVMODE = Convert.ToBoolean((await sharedService.GetPresetValue(McvConstant.DEVMODE)));


        var _reportPath = $"Bill.rdlc";

        var _reportDef = new ReportDefinition()
        {
            ReportName = $"Bill",
            ReportPath = DEVMODE ? $"{_reportContainerUrl}DEV/{_reportPath}" : $"{_reportContainerUrl}{_reportPath}",
            ReportDataSet = DataTools.ToDataTable(_billData),
            ReportProperties = _reportProperties,
            Filename = Regex.Replace($"{_projectName}-{(_bill.TypeFlag == 1 ? _bill.BillNo : "Proforma")}", @"[^\w\d\(\)\[\]\-\.]", "-")
            //RenderType = renderType
        };

        var reportServiceApi = await sharedService.GetPresetValue(McvConstant.RDLC_PROCESSOR_API);
        return await ReportClient.GenerateReport(_reportDef, reportServiceApi);

    }
    #endregion RDLC

    public async Task<IEnumerable<ProjectActivity>> GetPendingBills(int ProjectID)
    {

        var _project = await db.Projects.AsNoTracking()
          .SingleOrDefaultAsync(x => x.ID == ProjectID);

        if (_project == null) return null;

        IEnumerable<ProjectActivity> queryResult = new List<ProjectActivity>();
        TextInfo textInfo = new CultureInfo("en-IN", false).TextInfo;
        var _projectName = _project.Code.ToString() + "-" + textInfo.ToTitleCase(_project.Title.ToLower());

        var _pendingBills = await db.ProjectBills.AsNoTracking()
            .Include(x => x.Payments)
            .Where(x => x.ProjectID == ProjectID
            && x.TypeFlag == 1)
            .Where(x => !x.Payments.Any())
            .Select(x => new ProjectActivity()
            {
                Head = "Tax Invoice",
                Date = x.BillDate,
                Title = x.BillNo,
                Person = x.ModifiedBy,
                Project = _projectName,
                DownloadUrl = "https://mycockpitview.in/newarch-Bills/Report/" + x.UID
            }).ToListAsync();

        var _proformas = await db.ProjectBills.AsNoTracking()
            .Where(x => x.ProjectID == ProjectID
            && x.TypeFlag == 0)
            .Select(x => new ProjectActivity()
            {
                Head = "Proforma",
                Date = x.BillDate,
                Title = Math.Round(x.WorkCompletion, 1).ToString() + "%", //+ | " + i.Payable.ToString("C", new CultureInfo("en-IN", false)),
                Person = x.ModifiedBy,
                Project = _projectName,
                DownloadUrl = "https://mycockpitview.in/newarch-Bills/Report/" + x.UID
            })
            .ToListAsync();

        return queryResult.Concat(_pendingBills).Concat(_proformas);


    }
}



public class BillRDLCData
{
    public string? BillNo { get; set; }
    public decimal WorkCompletion { get; set; }
    public DateTime BillDate { get; set; }
    public decimal Amount { get; set; }
    public decimal GSTShare { get; set; }
    public decimal GSTAmount { get; set; }
    public int GSTFlag { get; set; }
    public decimal CGSTShare { get; set; }
    public decimal CGSTAmount { get; set; }
    public decimal IGSTShare { get; set; }
    public decimal IGSTAmount { get; set; }
    public decimal SGSTShare { get; set; }
    public decimal SGSTAmount { get; set; }

    public decimal Total { get; set; }
    public decimal PreviousAmount { get; set; }
    public decimal Payable { get; set; }
    public int TypeFlag { get; set; }
    public string? ClientName { get; set; }
    public string? ClientAddress { get; set; }
    public string? Attn { get; set; }
    public string? ReverseTaxCharges { get; set; }
    public string? WorkDetails { get; set; }
    public string? GSTIN { get; set; }
    public string? HSN { get; set; }
    public string? AmountInWords { get; set; }
    public string? StateCode { get; set; }
    public string? Remarks { get; set; }
    public DateTime? PrevBillDate { get; set; }
    public decimal PrevWorkCompletion { get; set; }
    public decimal PrevAmount { get; set; }
    public decimal TDSAmount { get; set; }
}
public class BillAnalysis
{
    public string? ProjectCode { get; set; }
    public string? Project { get; set; }

    public string? ProjectStatus { get; set; }
    public string? Partner { get; set; }
    public string? Client { get; set; }
    public decimal TotalAmount { get; set; }
    public decimal TotalReceived { get; set; }
    public DateTime CreatedDate { get; set; }
    public DateTime BillDate { get; set; }
    public string? BillNo { get; set; }
    public decimal WorkCompletion { get; set; }
    public decimal CurrentAmount { get; set; }
    public decimal CurrentRecieved { get; set; }
    public DateTime? PaymentDate { get; set; }
    public decimal Score { get; set; }

    public decimal IGSTShare { get; set; }
    public decimal IGSTAmount { get; set; }
    public decimal CGSTShare { get; set; }
    public decimal CGSTAmount { get; set; }
    public decimal SGSTShare { get; set; }
    public decimal SGSTAmount { get; set; }
    public string? Status { get; set; }
    public string? StateCode { get; set; }
    public string? GSTIN { get; set; }
    public string? HSN { get; set; }
    public string? Remarks { get; set; }
    public int ID { get; set; }
    public int ProjectID { get; set; }

    public int TypeFlag { get; set; }
    public string? Type { get; set; }

    public int StatusFlag { get; set; }

    public int CompanyID { get; set; }
    public string? CompanyName { get; set; }
}