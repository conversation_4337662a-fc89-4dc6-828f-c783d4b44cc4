﻿using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.CoreModule;
using System.ComponentModel.DataAnnotations;
using MyCockpitView.Utility.Common;
using System.ComponentModel.DataAnnotations.Schema;

namespace MyCockpitView.WebApi.PackageModule.Entities;

public class PackageFeedback : BaseEntity
{
    public int PackageID { get; set; }
    public virtual Package? Package { get; set; }

    [StringLength(255)]
    public string? PackageTitle { get; set; }

    [StringLength(255)]
    public string? PackagePartnerName { get; set; }
    [StringLength(255)]
    public string? PackagePartnerEmail { get; set; }
    public int? PackagePartnerContactID { get; set; }

    [StringLength(255)]
    public string? PackageAssociateName { get; set; }
    [StringLength(255)]
    public string? PackageAssociateEmail { get; set; }
    public int? PackageAssociateContactID { get; set; }

    [StringLength(255)]
    public string? ReviewerName { get; set; }

    [StringLength(255)]
    public string? ReviewerEmail { get; set; }
    public int? ReviewerContactID { get; set; }
    public int Rating { get; set; }
    public string? Comment { get; set; }

    public virtual ICollection<PackageFeedbackAttachment> Attachments { get; set; } = new List<PackageFeedbackAttachment>();
}

public class PackageFeedbackConfiguration : BaseEntityConfiguration<PackageFeedback>, IEntityTypeConfiguration<PackageFeedback>
{
    public void Configure(EntityTypeBuilder<PackageFeedback> builder)
    {
        base.Configure(builder);
        builder.HasIndex(x => x.PackageTitle);
        builder.HasIndex(x => x.PackagePartnerName);
        builder.HasIndex(x => x.PackagePartnerEmail);
        builder.HasIndex(x => x.PackagePartnerContactID);
        builder.HasIndex(x => x.PackageAssociateEmail);
        builder.HasIndex(x => x.PackageAssociateName);
        builder.HasIndex(x => x.PackageAssociateContactID);
        builder.HasIndex(x => x.ReviewerContactID);
        builder.HasIndex(x => x.ReviewerEmail);
        builder.HasIndex(x => x.ReviewerName);
        builder.HasIndex(x => x.Rating);
    }
}

