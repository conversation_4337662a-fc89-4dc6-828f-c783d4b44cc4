﻿
using Newtonsoft.Json;
using System.Data;
using System.Text;
using System.Text.RegularExpressions;
using MyCockpitView.WebApi.Exceptions;
using MyCockpitView.WebApi.ContactModule.Entities;
using MyCockpitView.WebApi.ExpenseModule.Services;
using MyCockpitView.WebApi.DesignScriptModule.Entities;
using MyCockpitView.WebApi.DesignScriptModule.Services;
using MyCockpitView.WebApi.LeaveModule.Services;
using MyCockpitView.WebApi.Services;
using MyCockpitView.WebApi.MeetingModule.Entities;
using MyCockpitView.WebApi.ProjectModule.Services;
using MyCockpitView.WebApi.WFTaskModule.Services;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.CoreModule;
using MyCockpitView.Utility.Common;
using MyCockpitView.Utility.RDLCClient;

namespace MyCockpitView.WebApi.MeetingModule.Services;

public class MeetingService : BaseEntityService<Meeting>, IMeetingService
{
    public MeetingService(EntitiesContext db) : base(db) { }

    public IQueryable<Meeting> Get(IEnumerable<QueryFilter>? Filters = null, string? Search = null, string? Sort = null)
    {

        IQueryable<Meeting> _query = base.Get(Filters);


        //Apply filters
        if (Filters != null)
        {


            if (Filters.Where(x => x.Key.Equals("ProjectID", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<Meeting>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("ProjectID", StringComparison.OrdinalIgnoreCase)))
                {
                    var isNumeric = Convert.ToInt32(_item.Value);

                    predicate = predicate.Or(x => x.ProjectID != null && x.ProjectID == isNumeric);
                }
                _query = _query.Where(predicate);
            }

            if (Filters.Where(x => x.Key.Equals("contactID", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<Meeting>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("contactID", StringComparison.OrdinalIgnoreCase)))
                {
                    var isNumeric = Convert.ToInt32(_item.Value);

                    predicate = predicate.Or(x => x.ContactID == isNumeric);
                }
                _query = _query.Where(predicate);
            }

            if (Filters.Where(x => x.Key.Equals("attendeeContactID", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<Meeting>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("attendeeContactID", StringComparison.OrdinalIgnoreCase)))
                {
                    var isNumeric = Convert.ToInt32(_item.Value);

                    predicate = predicate.Or(x => x.ContactID == isNumeric || x.Attendees.Any(c => c.TypeFlag == 0 && c.ContactID == isNumeric));
                }
                _query = _query.Include(x => x.Attendees).Where(predicate);
            }

            if (Filters.Where(x => x.Key.Equals("startDate", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var _item = Filters.First(x => x.Key.Equals("startDate", StringComparison.OrdinalIgnoreCase));
                DateTime result = Convert.ToDateTime(_item.Value);
                _query = _query.Where(x => x.StartDate.Date == result.Date);
            }

            if (Filters.Where(x => x.Key.Equals("endDate", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var _item = Filters.First(x => x.Key.Equals("endDate", StringComparison.OrdinalIgnoreCase));

                DateTime result = Convert.ToDateTime(_item.Value);
                _query = _query.Where(x => x.EndDate.Date == result.Date);
            }

            if (Filters.Where(x => x.Key.Equals("sentDate", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var _item = Filters.First(x => x.Key.Equals("sentDate", StringComparison.OrdinalIgnoreCase));

                DateTime result = Convert.ToDateTime(_item.Value);
                _query = _query.Where(x => x.FinalizedOn.Value.Date == result.Date);
            }

            if (Filters.Where(x => x.Key.Equals("rangestart", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var _item = Filters.First(x => x.Key.Equals("rangestart", StringComparison.OrdinalIgnoreCase));

                DateTime result = Convert.ToDateTime(_item.Value);
                _query = _query.Where(x => x.StartDate >= result || x.EndDate >= result);
            }

            if (Filters.Where(x => x.Key.Equals("rangeend", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var _item = Filters.First(x => x.Key.Equals("rangeend", StringComparison.OrdinalIgnoreCase));

                DateTime result = Convert.ToDateTime(_item.Value);
                var end = result.AddDays(1);
                _query = _query.Where(x => x.StartDate < end || x.EndDate < end);

            }

           

            if (Filters.Where(x => x.Key.Equals("parentID", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<Meeting>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("parentID", StringComparison.OrdinalIgnoreCase)))
                {
                    var isNumeric = Convert.ToInt32(_item.Value);

                    predicate = predicate.Or(x => x.ParentID != null && x.ParentID == isNumeric);
                }
                _query = _query.Where(predicate);
            }
        }

        if (Search != null && Search != string.Empty)
        {
            var _keywords = Search.Split(' ');

            foreach (var _key in _keywords)
            {
                _query = _query.Include(x => x.Contact)
                     .Where(x => x.Title.ToLower().Contains(_key.ToLower())
                                   || (x.Contact.FirstName + " " + x.Contact.LastName).ToLower().Contains(_key.ToLower())
                                        );
            }
        }

        if (Sort != null && Sort != String.Empty)
        {
            switch (Sort.ToLower())
            {
                case "sentdate":
                    return _query
                            .OrderBy(x => x.FinalizedOn);

                case "sentdate desc":
                    return _query
                            .OrderByDescending(x => x.FinalizedOn);
            }
        }

        return _query
                .OrderByDescending(x => x.StartDate);

    }

    public async Task<int> Create(Meeting Entity, IEnumerable<MeetingAttendee>? Attendees = null)
    {
        using (var transaction = db.Database.BeginTransaction())
        {

            if (Entity.ProjectID != null)
            {
                var _project = await db.Projects.AsNoTracking().SingleOrDefaultAsync(x => x.ID == Entity.ProjectID);
                if (_project != null)
                {
                    Entity.Title = _project.Code + "-" + _project.Title;
                }
            }

            if (Entity.Title == null || Entity.Title == String.Empty)
                throw new EntityServiceException("Subject is required. Please enter proper subject and try again!");


            var _lastMeeting = await GetLastPending(Entity);
            if (_lastMeeting != null)
            {
                var _typeFlagMasters = await db.TypeMasters.AsNoTracking().FirstOrDefaultAsync(x => x.Entity == nameof(Meeting) && x.Value == _lastMeeting.TypeFlag);

                throw new EntityServiceException($"Last {(_typeFlagMasters != null ? _typeFlagMasters.Title : nameof(Meeting))} for this subject {_lastMeeting.Code} is pending. Please close & send it before creating new!");

            }

            if (Attendees != null)
            {
                foreach (var _person in Attendees)
                {
                    if (_person.Name != null)
                    {
                        if (!Entity.Attendees.Any(x => x.Name == _person.Name && x.Email == _person.Email))
                        {
                            var _attModel = new MeetingAttendee
                            {
                                Name = _person.Name,
                                Email = _person.Email,
                                Company = _person.Company,
                                TypeFlag = _person.TypeFlag,
                                ContactID = _person.ContactID,
                            };
                            Entity.Attendees.Add(_attModel);
                        }
                    }
                }
            }

            if (Entity.TypeFlag == McvConstant.MEETING_TYPEFLAG_MEETING)
            {
                var _lastorder = 0.0m;
                var _query = db.Meetings.AsNoTracking()
                    
                    .Where(x => !x.IsVersion)
                    .Where(x => x.TypeFlag == Entity.TypeFlag);
                if (Entity.ProjectID != null)
                {
                    _query = _query.Where(x => x.ProjectID == Entity.ProjectID);
                }
                else
                {
                    _query = _query.Where(x => x.Title==Entity.Title);
                }

                if (await _query.AnyAsync())
                {
                    _lastorder = await _query
                       .MaxAsync(x => x.Sequence);
                }

                _lastorder++;

                Entity.Sequence = _lastorder;
            }
            else if (Entity.TypeFlag == McvConstant.MEETING_TYPEFLAG_INSPECTION)
            {
                var _lastorder = 0.0m;
                var _query = db.Meetings.AsNoTracking()
                    .Where(x => !x.IsVersion)
                    .Where(x => x.TypeFlag == Entity.TypeFlag);
                if (Entity.ProjectID != null)
                {
                    _query = _query.Where(x => x.ProjectID == Entity.ProjectID);
                }
                else
                {
                    _query = _query.Where(x => x.Title==Entity.Title);
                }

                if (await _query.AnyAsync())
                {
                    _lastorder = await _query
                       .MaxAsync(x => x.Sequence);
                }

                _lastorder++;

                Entity.Sequence = _lastorder;
            }
            else if (Entity.TypeFlag == McvConstant.MEETING_TYPEFLAG_CNOTE)
            {
                var _lastorder = 1.0m;
                var _query = db.Meetings.AsNoTracking()
                    .Where(x => !x.IsVersion)
                    .Where(x => x.TypeFlag == McvConstant.MEETING_TYPEFLAG_MEETING
                    || x.TypeFlag == McvConstant.MEETING_TYPEFLAG_CNOTE
                    );
                if (Entity.ProjectID != null)
                {
                    _query = _query.Where(x => x.ProjectID == Entity.ProjectID);
                }
                else
                {
                    _query = _query.Where(x => x.Title==Entity.Title);
                }

                var _lastSent = await _query.OrderByDescending(x => x.StartDate).FirstOrDefaultAsync();

                if (_lastSent != null)
                {
                    _lastorder = _lastSent.Sequence;
                }

                Entity.Sequence = _lastorder + 0.1m;
                //Entity.Version =  _lastSent.Version + 1;
                Entity.StatusFlag = 1;

            }

            Entity.Code = GetCode(Entity);
            db.Meetings.Add(Entity);
            await db.SaveChangesAsync();

            if (Entity.TypeFlag == McvConstant.MEETING_TYPEFLAG_MEETING || Entity.TypeFlag == McvConstant.MEETING_TYPEFLAG_INSPECTION)
            {

                var meetingAgendaService = new MeetingAgendaService(db);
                await meetingAgendaService.ScaffoldPendingAgenda(Entity.ID, Entity.Title, Entity.ProjectID, Entity.TypeFlag);

                var meetingAttendeeService = new MeetingAttendeeService(db);
                await meetingAttendeeService.ScaffoldPendingAttendees(Entity.ID);
            }

            if (Entity.TypeFlag == McvConstant.MEETING_TYPEFLAG_MEETING
                || Entity.TypeFlag == McvConstant.MEETING_TYPEFLAG_INSPECTION)
            {
                var taskService = new WFTaskService(db);
                await taskService.StartFlow(nameof(Meeting), Entity.TypeFlag, Entity.ID);
            }
            transaction.Commit();
        }


        return Entity.ID;

    }

    public async Task Update(Meeting Entity)
    {

        var meeting = await db.Meetings.AsNoTracking()
        .Include(x => x.Attendees)
        .SingleOrDefaultAsync(x => x.ID == Entity.ID);

        if (meeting == null) throw new EntityServiceException($"{nameof(Meeting)} not found!");

        //var _oldStatus = _entity.StatusFlag;
        var _oldStart = meeting.StartDate;
        var _oldEnd = meeting.EndDate;
        var _attendees = meeting.Attendees;


        //increment version if status is sent
        if (Entity.StatusFlag >= McvConstant.MEETING_STATUSFLAG_SENT)
        {
            Entity.Version++;
        }
        Entity.Code = GetCode(Entity);

        if (Entity.StatusFlag != McvConstant.MEETING_STATUSFLAG_SCHEDULED
            && (_oldStart != Entity.StartDate || _oldEnd != Entity.EndDate))
        {
            var meetingAttendeeService = new MeetingAttendeeService(db);
            foreach (var _obj in _attendees.Where(x => x.TypeFlag == 0))
            {
                await meetingAttendeeService.LogAttendeeTime(_obj.ContactID.Value, Entity.ID, Entity.StartDate, Entity.EndDate, "MEETING_CLOSE");
            }
            await meetingAttendeeService.LogAttendeeTime(Entity.ContactID, Entity.ID, Entity.StartDate, Entity.EndDate, "MEETING_CLOSE");

        }

        await base.Update(Entity);

        if (Entity.TypeFlag == McvConstant.MEETING_TYPEFLAG_MEETING || Entity.TypeFlag == McvConstant.MEETING_TYPEFLAG_INSPECTION)
        {
            if (Entity.StatusFlag == McvConstant.MEETING_STATUSFLAG_SCHEDULED || Entity.StatusFlag == McvConstant.MEETING_STATUSFLAG_ATTENDED)
            {
                var meetingAgendaService = new MeetingAgendaService(db);
                await meetingAgendaService.ScaffoldPendingAgenda(Entity.ID, Entity.Title, Entity.ProjectID, Entity.TypeFlag);

                var meetingAttendeeService = new MeetingAttendeeService(db);
                await meetingAttendeeService.ScaffoldPendingAttendees(Entity.ID);
            }

        }

        var taskService = new WFTaskService(db);
        await taskService.UpdateTaskDue(nameof(Meeting), Entity.ID);


    }

    private string GetCode(Meeting Entity)
    {
        var _docKey = "MOM";
        if (Entity.TypeFlag == McvConstant.MEETING_TYPEFLAG_CNOTE)
            _docKey = "NOTE";
        else if (Entity.TypeFlag == McvConstant.MEETING_TYPEFLAG_INSPECTION)
            _docKey = "INSP";

        var _docCode = $"{Entity.Title.Replace(" | ", "-")}-{_docKey}-#{Entity.Sequence:F1}";

        return _docCode.ToUpper().Trim();
    }

    public async Task<bool> IsMeetingEditable(int ID, int ContactID)
    {

        var _meeting = await db.Meetings.AsNoTracking()
            .Where(x => x.ID == ID)
            .SingleOrDefaultAsync();

        if (_meeting == null) throw new EntityServiceException("Meeting not found!");

        if (_meeting.IsVersion) return false;

        if (_meeting.ContactID != ContactID) return false;

        if (_meeting.StatusFlag != McvConstant.MEETING_STATUSFLAG_SENT) return true;


        if (_meeting.StatusFlag == McvConstant.MEETING_STATUSFLAG_SENT)
        {
            if (_meeting.TypeFlag == McvConstant.MEETING_TYPEFLAG_MEETING
                || _meeting.TypeFlag == McvConstant.MEETING_TYPEFLAG_CNOTE)
            {
                var _nextMeeting = await Get()
                    .Where(x => x.ID != _meeting.ID)
                    .Where(x => !x.IsVersion)
                    .Where(x => x.Title == _meeting.Title)
                    .Where(x => x.StartDate > _meeting.StartDate)
                    .Where(x => x.TypeFlag == McvConstant.MEETING_TYPEFLAG_MEETING
                    || x.TypeFlag == McvConstant.MEETING_TYPEFLAG_CNOTE)
                    .OrderByDescending(x => x.StartDate)
                    .FirstOrDefaultAsync();

                if (_nextMeeting == null)
                {
                    var sharedService = new SharedService(db);
                    var MEETING_UPDATE_LIMIT = Convert.ToInt32((await sharedService.GetPresetValue(McvConstant.MEETING_UPDATE_ALLOW_DURATION)));

                    return ClockTools.GetDaysDifference(_meeting.FinalizedOn != null ? _meeting.FinalizedOn.Value : _meeting.Modified, DateTime.UtcNow) < MEETING_UPDATE_LIMIT;
                }
            }
            else
            {
                var _nextMeeting = await db.Meetings.AsNoTracking()
                      .Where(x => x.ID != _meeting.ID)
                      .Where(x => !x.IsVersion)
                       .Where(x => x.Title == _meeting.Title)
                    .Where(x => x.StartDate > _meeting.StartDate)
                   .Where(x => x.TypeFlag == McvConstant.MEETING_TYPEFLAG_INSPECTION)
                   .OrderByDescending(x => x.StartDate).FirstOrDefaultAsync();

                if (_nextMeeting == null)
                {
                    var sharedService = new SharedService(db); ;
                    var MEETING_UPDATE_LIMIT = Convert.ToInt32((await sharedService.GetPresetValue(McvConstant.MEETING_UPDATE_ALLOW_DURATION)));

                    return ClockTools.GetDaysDifference(_meeting.FinalizedOn != null ? _meeting.FinalizedOn.Value : _meeting.Modified, DateTime.UtcNow) <= MEETING_UPDATE_LIMIT;
                }
            }
        }

        return false;

    }

    public async Task<bool> CheckIfDelayed(int ID)
    {

        var _meeting = await Get()
            .Where(x => x.ParentID == null)
            .Where(x => x.ID == ID)
            .SingleOrDefaultAsync();

        if (_meeting == null) return false;

        var _task = await db.WFTasks.AsNoTracking()
            .Where(x => x.Entity != null && x.Entity==nameof(Meeting)
            && x.EntityID == _meeting.ID && x.StatusFlag == 0)
            .OrderBy(x => x.DueDate)
            .FirstOrDefaultAsync();

        if (_task != null)
        {
            return (_task.DueDate < DateTime.UtcNow);
        }

        return false;

    }


    public async Task<Meeting?> GetById(int Id)
    {

        return
            await db.Meetings.AsNoTracking()
                    .Include(x => x.Contact)
                    .Include(x => x.Attendees)
                     .Include(x => x.Agendas).ThenInclude(a => a.Attachments)
                    .SingleOrDefaultAsync(x => x.ID == Id);

    }

    public async Task<Meeting?> GetById(Guid Id)
    {

        return await db.Meetings.AsNoTracking()
                    .Include(x => x.Contact)
                    .Include(x => x.Attendees)
                     .Include(x => x.Agendas).ThenInclude(a => a.Attachments)
                    .SingleOrDefaultAsync(x => x.UID == Id);

    }


    public async Task<byte[]> GetCalendarInvite(int MeetingID, bool isCancelled = false)
    {

        var meeting = await db.Meetings.AsNoTracking()
      .Where(x => x.ID == MeetingID)
      .Include(x => x.Contact)
      .Include(x => x.Attendees)
      .SingleOrDefaultAsync();

        var _companyName = "Newarch Landscapes LLP";

        if (meeting == null) throw new EntityServiceException("Meeting entity not found!");

        StringBuilder sb = new StringBuilder();
        var sharedService = new SharedService(db);
        var rootApi = await sharedService.GetPresetValue(McvConstant.ROOT_API);
        var url = $"{rootApi}/meetings/report/{meeting.UID.ToString()}";
        string now = DateTime.UtcNow.ToString("yyyyMMddTHHmmssZ");
        var dtStart = (meeting.StartDate).ToString("yyyyMMddTHHmmssZ");
        var dtEnd = (meeting.EndDate).ToString("yyyyMMddTHHmmssZ");
        var meetingType = (meeting.TypeFlag != McvConstant.MEETING_TYPEFLAG_INSPECTION ? "Meeting" : "Inspection");
        var startTime = meeting.StartDate.ToString("HH:mm");
        var endTime = meeting.EndDate.ToString("HH:mm");
        var meetingDate = meeting.StartDate.ToString("dd MMM yyy");

        //FIXED HEADER
        sb.AppendLine("BEGIN:VCALENDAR");
        sb.AppendLine("VERSION:2.0");
        sb.AppendLine("CALSCALE:GREGORIAN");
        sb.AppendLine($"METHOD:{(isCancelled ? "CANCEL" : "REQUEST")}");
        sb.AppendLine("BEGIN:VEVENT");
        sb.AppendLine($"UID:{meeting.UID.ToString()}");
        sb.AppendLine($"URL:{url}");
        sb.AppendLine($"DTSTART:{dtStart}");
        sb.AppendLine($"DTEND:{dtEnd}");
        sb.AppendLine($"SUMMARY:{meeting.Title}");
        sb.AppendLine($"SEQUENCE:{(isCancelled ? "1" : "0")}");
        sb.AppendLine($"DESCRIPTION:{meetingType} scheduled on {meetingDate} from {startTime} to {endTime}.");
        sb.AppendLine("LOCATION;LANGUAGE=en-IN:{meeting.}");
        sb.AppendLine($"STATUS:{(isCancelled ? "CANCELLED" : "CONFIRMED")}");
        sb.AppendLine("TRANSP:OPAQUE");
        sb.AppendLine($"ORGANIZER;CN={meeting.Contact.FullName}:mailto:{meeting.Contact.Email1}");
        foreach (var _attendee in meeting.Attendees.Where(x => x.TypeFlag == 0))
        {
            sb.AppendLine($"ATTENDEE;CUTYPE=INDIVIDUAL;ROLE=REQ-PARTICIPANT;RSVP=TRUE;PARTSTAT=NEEDS-ACTION;CN={_attendee.Name}:mailto:{_attendee.Email}");
        }
        sb.AppendLine("END:VEVENT");
        sb.AppendLine("END:VCALENDAR");


        var bytes = Encoding.UTF8.GetBytes(sb.ToString());

        return bytes;

    }


    //old method
    public IQueryable<MeetingAgenda> GetAgendas(int ID)
    {

        return db.MeetingAgendas.AsNoTracking()
            .Include(x => x.Attachments)
                //.Include(x => x.Links)
                .Where(x => x.MeetingID == ID)
                .OrderBy(x => x.ID);

    }



    public async Task<IEnumerable<MeetingAttendee>> GetLastAttendees(string Subject)
    {

        var _lastMeetings = await Get()
                                .Where(x => x.Title==Subject
                                 && (x.StatusFlag == 2 || x.StatusFlag == 3))
                        .OrderByDescending(x => x.StartDate)
                        .ToListAsync();

        var _attendeeList = new List<MeetingAttendee>();

        foreach (var _lastMeeting in _lastMeetings)
        {
            var _attendees = await db.MeetingAttendees.AsNoTracking()
                                    .Where(x => x.MeetingID == _lastMeeting.ID)
                                    .ToListAsync();
            foreach (var item in _attendees)
            {
                if (!_attendeeList.Any(x => x.Name == item.Name
                                        && x.Company == item.Company))
                {
                    _attendeeList.Add(item);
                }
            }
        }

        return _attendeeList;

    }

    public async Task<IEnumerable<String>> GetSubjectOptions(string Filters = null)
    {

        var _subjects = await Get()
            .Where(x => x.ProjectID == null)
            .Select(x => x.Title.Trim())
            .Distinct()
            .ToListAsync();

        //Apply filters
        if (Filters != null)
        {
            APIFilter _filter = JsonConvert.DeserializeObject<APIFilter>(Filters);
            var predicate = PredicateBuilder.False<Meeting>();

            if (_filter.Filters.Where(x => x.Key == "statusFlag").Any())
            {
                foreach (var _item in _filter.Filters.Where(x => x.Key == "statusFlag"))
                {
                    predicate = predicate.Or(x => x.StatusFlag.ToString() == _item.Value);
                }
            }

            // _query = _query.Where(predicate);
        }

        return _subjects;

    }

    #region RDLC
    public async Task<ReportDefinition> GetAgendaReport(Meeting meeting, string sort = null)
    {



        var _dsEntities = new List<DesignScriptEntity>();

        if (meeting.ProjectID != null)
        {
            var designScriptEntityService = new DesignScriptEntityService(db);

            _dsEntities = await designScriptEntityService.Get()
                                .Where(x => x.ProjectID == meeting.ProjectID)
                                .ToListAsync();
        }


        var reportTitle = "Agenda List";

        var _meetingDate = ClockTools.GetIST(meeting.StartDate).ToString("dd MMM yyyy HH:mm");
        var _reportProperties = new List<ReportProperties>
            {
                new ReportProperties() { PropertyName = "ReportTitle", PropertyValue = reportTitle.ToString() },
                new ReportProperties() { PropertyName = "Title", PropertyValue =meeting.Title.ToString() },
                new ReportProperties() { PropertyName = "Number", PropertyValue = $"#{meeting.Sequence.ToString("000")}" },
                new ReportProperties() { PropertyName = "StartDate", PropertyValue = ClockTools.GetIST(meeting.StartDate).ToString("dd MMM yyyy HH:mm") },
                                    new ReportProperties() { PropertyName = "EndDate", PropertyValue = ClockTools.GetIST(meeting.EndDate).ToString("HH:mm") },
                                     new ReportProperties() { PropertyName = "Code", PropertyValue = meeting.Code ?? "000" },
                new ReportProperties() { PropertyName = "PreparedBy", PropertyValue = meeting.CreatedBy },
                new ReportProperties() { PropertyName = "Location", PropertyValue = meeting.Location ??"Newarch" },

            };


        var today = DateTime.UtcNow;
        var agendas = meeting.Agendas;
        if (sort != null)
        {
            if (sort.ToLower() == "duedate")
                agendas = agendas.OrderBy(x => x.DueDate).ToList();
            else if (sort.ToLower() == "actionby")
                agendas = agendas.OrderBy(x => x.ActionBy).ToList();
            else if (sort.ToLower() == "agenda")
                agendas = agendas.OrderBy(x => x.Title).ThenBy(x => x.Subtitle).ToList();
        }
        var _minutesAgendas = agendas.Select(x => new
        {
            x.ID,
            Title = x.Title,
            Subtitle = x.Subtitle,
            Reminder = x.ReminderCount,
            Comment = x.Comment,
            DueDate = x.DueDate != null ? ClockTools.GetIST(x.DueDate.Value).ToString("dd MMM yyyy") : "",
            ActionBy = x.ActionBy,
            History = x.PreviousHistory,
            Status = x.StatusFlag == McvConstant.MEETING_AGENDA_STATUSFLAG_RESOLVED ? "RESOLVED" : "PENDING",
            Breadcrumb = x.DesignScriptEntityID != null ? getDSBreadcrumb(_dsEntities, x.DesignScriptEntityID.Value) : string.Empty,
            IsDelayed = x.StatusFlag == McvConstant.MEETING_AGENDA_STATUSFLAG_PENDING && x.DueDate != null && x.DueDate.Value < today
        });


        var sharedService = new SharedService(db); ;
        var _reportContainerUrl = await sharedService.GetPresetValue(McvConstant.RDLC_REPORT_CONTAINER_URL);
        var DEVMODE = Convert.ToBoolean((await sharedService.GetPresetValue(McvConstant.DEVMODE)));

        var _reportPath = $"MeetingAgenda-a4.rdlc";

        var _reportDef = new ReportDefinition()
        {
            ReportName = "MeetingAgenda",
            ReportPath = DEVMODE ? $"{_reportContainerUrl}DEV/{_reportPath}" : $"{_reportContainerUrl}{_reportPath}",
            ReportDataSet = DataTools.ToDataTable(_minutesAgendas),
            ReportProperties = _reportProperties,
            Filename = $"{reportTitle}-{meeting.Code}-{ClockTools.GetISTNow().ToString("yyMMddHHmm")}",
            RenderType = "PDF"
        };

        _reportDef.SubReports.Add(new ReportDefinition()
        {
            ReportName = "MeetingMinutesAttendee",
            ReportPath = DEVMODE ? $"{_reportContainerUrl}DEV/MeetingMinutesAttendee.rdlc" : $"{_reportContainerUrl}MeetingMinutesAttendee.rdlc",
            ReportDataSet = DataTools.ToDataTable(meeting.Attendees.Select(x => new
            {
                Name = x.Name,
                Email = x.Email,
                Company = x.Company,
                TypeFlag = x.TypeFlag,
                AgendaCount = meeting.Agendas.Where(m => m.ActionByContactID != null && m.ActionByContactID == x.ContactID).Count(),
                x.OrderFlag
            })),
        });
        var reportServiceApi = await sharedService.GetPresetValue(McvConstant.RDLC_PROCESSOR_API);
        return await ReportClient.GenerateReport(_reportDef, reportServiceApi);

    }
    public async Task<ReportDefinition> GetMinutesReport(Meeting meeting, string sort = null)
    {


        var reportTitle = "Minutes Of Meeting";

        if (!meeting.IsVersion && meeting.StatusFlag != McvConstant.MEETING_STATUSFLAG_SENT)
            return await GetAgendaReport(meeting, sort);
        else
        {
            if (meeting.TypeFlag == McvConstant.MEETING_TYPEFLAG_INSPECTION)
                reportTitle = "Inspection Report";
            else if (meeting.TypeFlag == McvConstant.MEETING_TYPEFLAG_CNOTE)
                reportTitle = "Communication Note";
        }

        var _dsEntities = new List<DesignScriptEntity>();

        if (meeting.ProjectID != null)
        {
            var designScriptEntityService = new DesignScriptEntityService(db);

            _dsEntities = await designScriptEntityService.Get()
                                .Where(x => x.ProjectID == meeting.ProjectID)
                                .ToListAsync();
        }




        var meetingDate = ClockTools.GetIST(meeting.StartDate).ToString("dd MMM yyyy HH:mm");
        var _reportProperties = new List<ReportProperties>
            {
                new ReportProperties() { PropertyName = "ReportTitle", PropertyValue = reportTitle.ToString() },
                new ReportProperties() { PropertyName = "Title", PropertyValue =meeting.Title.ToString() },
                new ReportProperties() { PropertyName = "Number", PropertyValue = $"#{meeting.Sequence.ToString("000")}" },
                new ReportProperties() { PropertyName = "StartDate", PropertyValue = ClockTools.GetIST(meeting.StartDate).ToString("dd MMM yyyy HH:mm") },
                                    new ReportProperties() { PropertyName = "EndDate", PropertyValue = ClockTools.GetIST(meeting.EndDate).ToString("HH:mm") },
                                     new ReportProperties() { PropertyName = "Code", PropertyValue = meeting.Code },
                new ReportProperties() { PropertyName = "PreparedBy", PropertyValue = meeting.Contact.FullName.ToString() },
                new ReportProperties() { PropertyName = "Location", PropertyValue = meeting.Location },

            };


        //var _consolidatedAttachments = new List<MeetingMinutesAttachment>();
        //var _fileIndex = 1;
        //foreach (var item in meeting.Agendas)
        //{
        //    if (item.Attachments.Any())
        //    {
        //        foreach (var attachment in item.Attachments)
        //        {
        //            var _extension = DataTools.GetFileExtension(attachment.Filename);
        //            if (!_consolidatedAttachments.Any(x => x.Filename == attachment.Filename))
        //                _consolidatedAttachments.Add(new MeetingMinutesAttachment()
        //                {
        //                    MeetingAgendaID = attachment.MeetingAgendaID,
        //                    Guidname = attachment.Guidname,
        //                    Reference = _fileIndex.ToString("00"),
        //                    Filename = attachment.Filename,
        //                    Url = attachment.ThumbUrl,
        //                    IsImage = _extension.Equals(".jpg", StringComparison.OrdinalIgnoreCase)
        //                    || _extension.Equals(".png", StringComparison.OrdinalIgnoreCase)
        //                    || _extension.Equals(".jpeg", StringComparison.OrdinalIgnoreCase)
        //                });
        //            _fileIndex++;
        //        }
        //    }
        //}
        var today = DateTime.UtcNow;
        var agendas = meeting.Agendas;
        if (sort != null)
        {
            if (sort.ToLower() == "duedate")
                agendas = agendas.OrderBy(x => x.DueDate).ToList();
            else if (sort.ToLower() == "actionby")
                agendas = agendas.OrderBy(x => x.ActionBy).ToList();
            else if (sort.ToLower() == "agenda")
                agendas = agendas.OrderBy(x => x.Title).ThenBy(x => x.Subtitle).ToList();
        }
        var _minutesAgendas = agendas.Select(x => new
        {
            x.ID,
            Title = x.Title,
            Subtitle = x.Subtitle,
            Reminder = x.ReminderCount,
            Comment = x.Comment,
            DueDate = x.DueDate != null ? ClockTools.GetIST(x.DueDate.Value).ToString("dd MMM yyyy") : "",
            ActionBy = x.ActionBy,
            History = x.PreviousHistory,
            Status = x.StatusFlag == McvConstant.MEETING_AGENDA_STATUSFLAG_RESOLVED ? "RESOLVED" : "PENDING",
            Breadcrumb = x.DesignScriptEntityID != null ? getDSBreadcrumb(_dsEntities, x.DesignScriptEntityID.Value) : string.Empty,
            IsDelayed = x.StatusFlag == McvConstant.MEETING_AGENDA_STATUSFLAG_PENDING && x.DueDate != null && x.DueDate.Value < today
        });



        var _images = meeting.Agendas
            
            .Where(x => x.Attachments.Where(a => !a.IsDeleted)
                                    .Where(a => a.ThumbUrl != null).Any())
            .SelectMany(join => join.Attachments, (x, y) => new
            {
                Url = y.ThumbUrl,
                ParentID = y.MeetingAgendaID
            });


        //if (_consolidatedAttachments.Any())
        //{
        //    foreach (var item in _minutesAgendas)
        //    {
        //        var _attachments = meeting.Agendas.SingleOrDefault(x => x.ID == item.ID).Attachments;
        //        item.References = string.Join(",", _consolidatedAttachments.Where(p => _attachments.Any(a => a.Filename == p.Filename))
        //                     .Select(p => p.Reference));
        //    }
        //}
        var sharedService = new SharedService(db); ;
        var _reportContainerUrl = await sharedService.GetPresetValue(McvConstant.RDLC_REPORT_CONTAINER_URL);
        var DEVMODE = Convert.ToBoolean((await sharedService.GetPresetValue(McvConstant.DEVMODE)));

        var _reportPath = $"MeetingMinutes-a4.rdlc";

        var _reportDef = new ReportDefinition()
        {
            ReportName = "MeetingMinutes",
            ReportPath = DEVMODE ? $"{_reportContainerUrl}DEV/{_reportPath}" : $"{_reportContainerUrl}{_reportPath}",
            ReportDataSet = DataTools.ToDataTable(_minutesAgendas),
            ReportProperties = _reportProperties,
            Filename = $"MeetingMinutes-{meeting.Code}-{ClockTools.GetISTNow().ToString("yyMMddHHmm")}",
            RenderType = "PDF"
        };

        _reportDef.SubReports.Add(new ReportDefinition()
        {
            ReportName = "MeetingMinutesAttendee",
            ReportPath = DEVMODE ? $"{_reportContainerUrl}DEV/MeetingMinutesAttendee.rdlc" : $"{_reportContainerUrl}MeetingMinutesAttendee.rdlc",
            ReportDataSet = DataTools.ToDataTable(meeting.Attendees.Select(x => new
            {
                Name = x.Name,
                Email = x.Email,
                Company = x.Company,
                TypeFlag = x.TypeFlag,
                AgendaCount = meeting.Agendas.Where(m => m.ActionByContactID != null && m.ActionByContactID == x.ContactID).Count(),
                x.OrderFlag
            })),
        });

        _reportDef.SubReports.Add(new ReportDefinition()
        {
            ReportName = "MeetingMinutesImages",
            ReportPath = DEVMODE ? $"{_reportContainerUrl}DEV/MeetingMinutesImages.rdlc" : $"{_reportContainerUrl}MeetingMinutesImages.rdlc",
            ReportDataSet = DataTools.ToDataTable(_images),
        });
        var reportServiceApi = await sharedService.GetPresetValue(McvConstant.RDLC_PROCESSOR_API);
        return await ReportClient.GenerateReport(_reportDef, reportServiceApi);

    }


    private string getDSBreadcrumb(IEnumerable<DesignScriptEntity> _dsEntities, int ID)
    {

        var breadcrumb = string.Empty;

        var entity = _dsEntities.FirstOrDefault(x => x.ID == ID);
        if (entity != null)
        {
            breadcrumb = $"{entity.Code}-{entity.Title}";
            while (entity.ParentID != null)
            {
                entity = _dsEntities.FirstOrDefault(x => x.ID == entity.ParentID);
                breadcrumb = $"{entity.Code}-{entity.Title} > {breadcrumb}";
            }

        }
        return breadcrumb;
    }


    #endregion RDLC




    public async Task SendMinutes(int ID)
    {

        var _meeting = await Get()
            .Include(x => x.Contact)
            .Include(x => x.Attendees)
                .Include(x => x.Agendas).ThenInclude(c => c.Attachments)
            .SingleOrDefaultAsync(x => x.ID == ID);

        if (_meeting == null) throw new EntityServiceException("Meeting not found!");

        var _tos = _meeting.Attendees
            .Where(x => x.TypeFlag == 0)
            .Where(x => x.Email != null)
            .Select(x => new MeetingEmailContact
            {
                Name = x.Name,
                Email = x.Email,
                Company = x.Company,
                ID = x.ContactID.Value,
                OrderFlag = x.OrderFlag
            })
            .ToList();
        var _ccs = _meeting.Attendees
            .Where(x => x.TypeFlag == 1)
            .Where(x => x.Email != null)
            .Select(x => new MeetingEmailContact
            {
                Name = x.Name,
                Email = x.Email,
                Company = x.Company,
                ID = x.ContactID.Value,
                OrderFlag = x.OrderFlag
            })
            .Where(x => !_tos.Any(c => c.Email == x.Email))
            .ToList();

        if (_meeting.ProjectID != null)
        {
            var projectService = new ProjectService(db);
            var _project = await projectService.Get()
                    .Include(x => x.Associations).ThenInclude(c => c.Contact)
                .SingleOrDefaultAsync(x => x.ID == _meeting.ProjectID);
            if (_project != null)
            {
                Regex myRegex = new Regex(McvConstant.EMAIL_REGEX, RegexOptions.None);
                foreach (var _leader in _project.Associations.Where(x => x.TypeFlag == 0))
                {
                    if (_leader != null && _leader.Contact.Email1 != null && myRegex.IsMatch(_leader.Contact.Email1))
                    {
                        var _attendee = new MeetingEmailContact
                        {
                            Email = _leader.Contact.Email1,
                            Name = _leader.Contact.FullName,
                            OrderFlag = _ccs.Any() ? _ccs.Max(x => x.OrderFlag) : 0
                        };


                        //      var _leaves = await db.Leaves.AsNoTracking()
                        //.Where(x => x.ContactID == _leader.ID)
                        //.Where(x => x.StatusFlag == McvConstant.LEAVE_STATUSFLAG_APPROVED)
                        //.Where(x => x.TypeFlag == McvConstant.LEAVE_TYPEFLAG_APPROVED)
                        //.Where(x => x.End > DateTime.UtcNow)
                        //.ToListAsync();

                        //      if (_leaves.Any())
                        //      {
                        //          _attendee.Leaves += $" On leave(";



                        //          foreach (var leave in _leaves)
                        //          {
                        //              _attendee.Leaves += $"{(leave.Start > DateTime.UtcNow ? ClockTools.GetIST(leave.Start).ToString("dd MMM yyyy") : ClockTools.GetISTNow().ToString("dd MMM yyyy"))}-{ClockTools.GetIST(leave.End).ToString("dd MMM yyyy")}";
                        //          }

                        //          _attendee.Leaves += $")";
                        //      }

                        if (!_tos.Any(x => x.Email.ToLower() == _attendee.Email.ToLower()) && !_ccs.Any(x => x.Email.ToLower() == _attendee.Email.ToLower()))
                            _ccs.Add(_attendee);
                    }
                }

                foreach (var _leader in _project.Associations.Where(x => x.TypeFlag == 1))
                {
                    if (_leader != null && _leader.Contact.Email1 != null && myRegex.IsMatch(_leader.Contact.Email1))
                    {
                        var _attendee = new MeetingEmailContact
                        {
                            Email = _leader.Contact.Email1,
                            Name = _leader.Contact.FullName,
                            OrderFlag = _ccs.Any() ? _ccs.Max(x => x.OrderFlag) : 0
                        };

                        if (!_tos.Any(x => x.Email.ToLower() == _attendee.Email.ToLower()) && !_ccs.Any(x => x.Email.ToLower() == _attendee.Email.ToLower()))
                            _ccs.Add(_attendee);
                    }
                }

            }


        }


        var leaveService = new LeaveService(db);
        var _leaves = await leaveService.Get()
                        .Where(x => x.ContactID == _meeting.Contact.ID)
                        .Where(x => x.StatusFlag == McvConstant.LEAVE_STATUSFLAG_APPROVED)
                        .Where(x => x.TypeFlag == McvConstant.LEAVE_TYPEFLAG_APPROVED)
                        .Where(x => x.End > DateTime.UtcNow)
                        .OrderBy(x => x.Start)
                        .ToListAsync();

        var leaves = "";

        if (_leaves.Any())
        {
            leaves += $"On leave(";



            foreach (var leave in _leaves)
            {
                leaves += $"{(leave.Start > DateTime.UtcNow ? ClockTools.GetIST(leave.Start).ToString("dd MMM yyyy") : ClockTools.GetISTNow().ToString("dd MMM yyyy"))}-{ClockTools.GetIST(leave.End).ToString("dd MMM yyyy")} | ";
            }

            leaves += $")";
        }

        var sharedService = new SharedService(db); ;
        //CC TO STUDIO
        var _defaultCCList = await sharedService.GetPresetValue(McvConstant.MEETING_EMAIL_CC);
        if (_defaultCCList != null)
        {
            Regex myRegex = new Regex(McvConstant.EMAIL_REGEX, RegexOptions.None);
            foreach (Match myMatch in myRegex.Matches(_defaultCCList))
            {
                var _address = new MeetingEmailContact
                {
                    Email = myMatch.Value.Trim(),
                    Name = "",
                };
                if (myMatch.Success && !_ccs.Any(x => x.Email.ToLower() == myMatch.Value.Trim().ToLower()) && !_tos.Any(x => x.Email.ToLower() == myMatch.Value.Trim().ToLower()))
                {
                    _ccs.Add(_address);
                }
            }
        }

        foreach (var item in _tos)
        {
            item.PendingAgendaCount = _meeting.Agendas.Where(x => x.StatusFlag == McvConstant.MEETING_AGENDA_STATUSFLAG_PENDING)
                .Where(x => x.ActionByContactID != null && x.ActionByContactID == item.ID).Count();
        }

        foreach (var item in _ccs)
        {
            item.PendingAgendaCount = _meeting.Agendas.Where(x => x.StatusFlag == McvConstant.MEETING_AGENDA_STATUSFLAG_PENDING)
                .Where(x => x.ActionByContactID != null && x.ActionByContactID == item.ID).Count();
        }


        var title = $"{_meeting.Title}";

        var subtitle = $"#{_meeting.Sequence.ToString("000")} | {ClockTools.GetIST(_meeting.StartDate).ToString("dd MMM yyyy HH:mm")}" + (_meeting.Version > 0 ? " | R" + _meeting.Version.ToString() : "");

        if (_meeting.TypeFlag == McvConstant.MEETING_TYPEFLAG_CNOTE)
        {
            subtitle = $"#{_meeting.Sequence.ToString("000.0")} | {ClockTools.GetIST(_meeting.StartDate).ToString("dd MMM yyyy HH:mm")}" + (_meeting.Version > 0 ? " | R" + _meeting.Version.ToString() : "");
        }
        else if (_meeting.TypeFlag == McvConstant.MEETING_TYPEFLAG_INSPECTION)
        {
            subtitle = $"#{_meeting.Sequence.ToString("000")} | {ClockTools.GetIST(_meeting.StartDate).ToString("dd MMM yyyy HH:mm")}" + (_meeting.Version > 0 ? " | R" + _meeting.Version.ToString() : "");
        }


        var _emailSubject = $"MINUTES OF MEETING | {title} | {subtitle}";
        if (_meeting.TypeFlag == McvConstant.MEETING_TYPEFLAG_CNOTE)
            _emailSubject = $"COMMUNICATION NOTE | {title} | {subtitle}";
        else if (_meeting.TypeFlag == McvConstant.MEETING_TYPEFLAG_INSPECTION)
            _emailSubject = $"INSPECTION REPORT | {title} | {subtitle}";

        var _version = $"#{_meeting.Sequence.ToString("000")}";

        if (_meeting.TypeFlag == McvConstant.MEETING_TYPEFLAG_CNOTE)
            _version = $"#{_meeting.Sequence.ToString("000.0")}";
        else if (_meeting.TypeFlag == McvConstant.MEETING_TYPEFLAG_INSPECTION)
            _version = $"#{_meeting.Sequence.ToString("000")}";


        var _replyParameter = $"mailto:{_meeting.Contact.Email1}?cc={_defaultCCList}&subject=RE:{_emailSubject.ToUpper()}";

        //Create a new StringBuilder object
        StringBuilder sb = new StringBuilder();

        sb.AppendLine("<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Transitional//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd\">");
        sb.AppendLine("<html xmlns=\"http://www.w3.org/1999/xhtml\">");
        sb.AppendLine("<head>");
        sb.AppendLine("    <meta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\" />");
        sb.AppendLine("    <title>Email Design</title>");
        sb.AppendLine("    <meta name=\"viewport\" content=\"width=device-width; initial-scale=1.0;\" />");
        sb.AppendLine("    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=9; IE=8; IE=7; IE=EDGE\" />");
        sb.AppendLine("    <meta name=\"format-detection\" content=\"telephone=no\" />");
        sb.AppendLine("    <!--[if gte mso 9]><xml>");
        sb.AppendLine("    <o:OfficeDocumentSettings>");
        sb.AppendLine("    <o:AllowPNG />");
        sb.AppendLine("    <o:PixelsPerInch>96</o:PixelsPerInch>");
        sb.AppendLine("    </o:OfficeDocumentSettings>");
        sb.AppendLine("    </xml><![endif]-->");
        sb.AppendLine("    <style type=\"text/css\">");
        sb.AppendLine("        /* Some resets and issue fixes */");
        sb.AppendLine("        #outlook a {");
        sb.AppendLine("            padding: 0;");
        sb.AppendLine("        }");
        sb.AppendLine("");
        sb.AppendLine("        body {");
        sb.AppendLine("            width: 100% !important;margin:0;");
        sb.AppendLine("            -webkit-text-size-adjust: 100%;");
        sb.AppendLine("            -ms-text-size-adjust: 100%;");
        sb.AppendLine("        }");

        sb.AppendLine("        table{");
        sb.AppendLine("            mso-table-lspace: 0px;");
        sb.AppendLine("            mso-table-rspace: 0px;");
        sb.AppendLine("        }");
        sb.AppendLine("");
        sb.AppendLine("        table td {");
        sb.AppendLine("            border-collapse: collapse;");
        sb.AppendLine("        }");
        sb.AppendLine("        .download-button {");
        sb.AppendLine("            background-color: #3498db;");
        sb.AppendLine("            color: #fff;");
        sb.AppendLine("            text-decoration: none;");
        sb.AppendLine("            padding: 10px 20px;");
        sb.AppendLine("            border-radius: 5px;");
        sb.AppendLine("        }");
        sb.AppendLine("");
        sb.AppendLine("            .download-button:hover {");
        sb.AppendLine("                background-color: #1e6bb8;");
        sb.AppendLine("            }");
        sb.AppendLine("");
        sb.AppendLine("        .ExternalClass * {");
        sb.AppendLine("            line-height: 115%;");
        sb.AppendLine("        }");
        sb.AppendLine("        /* End reset */");


        sb.AppendLine("    </style>");
        sb.AppendLine("</head>");
        sb.AppendLine("");
        sb.AppendLine("<body>");
        sb.AppendLine("");

        sb.AppendLine("");
        sb.AppendLine("    <div style=\"margin: 0 auto;font-family:Calibri;font-size:14px;line-height:1.8;padding-left:5px;padding-right:5px;padding-top:10px;pading-bottom:10px; max-width:500px;\">");
        sb.AppendLine("");
        sb.AppendLine("        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; border-collapse: collapse;padding: 6px; background-color: #e9e9e9;\">");
        sb.AppendLine("            <tr>");
        sb.AppendLine("                <td style=\"font-size:18px; font-weight:bold; text-align:center;\">");
        sb.Append(title);
        sb.AppendLine("                </td>");
        sb.AppendLine("            </tr>");
        sb.AppendLine("            <tr>");
        sb.AppendLine("                <td style=\"font-size:14px; font-weight:bold; text-align:center; color: #494949;\">");
        sb.Append(subtitle);
        sb.AppendLine("                </td>");
        sb.AppendLine("            </tr>");
        sb.AppendLine("        </table>");
        sb.AppendLine("");


        sb.AppendLine("<div style=\"border-bottom: 1px solid #cccccc; margin-top: 15px; margin-bottom: 15px; \"></div>");

        sb.AppendLine("        <table align=\"center\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; border-collapse: collapse;\">");

        sb.AppendLine("            <tr>");
        sb.AppendLine("                <td style=\"text-align: center; font-size: 18px; font-weight: bold;\">");

        var rootApi = await sharedService.GetPresetValue(McvConstant.MEETING_MINUTES_URL_ROOT);
        if (_meeting.TypeFlag == McvConstant.MEETING_TYPEFLAG_MEETING)
        {

            sb.AppendLine($"                    <a href=\"{rootApi}{(await RecordVersion(ID)).ToString()}\">MINUTES OF MEETING</a>");
        }

        else if (_meeting.TypeFlag == McvConstant.MEETING_TYPEFLAG_INSPECTION)
        {
            sb.AppendLine($"                    <a href=\"{rootApi}{(await RecordVersion(ID)).ToString()}\">INSPECTION REPORT</a>");
        }

        else //if (_meeting.TypeFlag == McvConstant.MEETING_TYPEFLAG_CNOTE)
        {
            sb.AppendLine($"                    <a href=\"{rootApi}{(await RecordVersion(ID)).ToString()}\">COMMUNICATION NOTE </a>");

        }


        sb.AppendLine("                </td>");
        sb.AppendLine("            </tr>");
        sb.AppendLine("            <tr>");
        sb.AppendLine("                <td style=\"text-align: center; font-size:11px;color:#808080;\">");
        sb.AppendLine(" To view, kindly click the above link. ");
        sb.AppendLine("                </td>");
        sb.AppendLine("            </tr>");
        sb.AppendLine("        </table>");
        sb.AppendLine("");

        //BOLKI PREVIEW
        if (_meeting.TypeFlag == McvConstant.MEETING_TYPEFLAG_CNOTE && _meeting.Agendas.First().Attachments.Any(x => x.TypeFlag == McvConstant.MEETING_AGENDA_ATTACHMENT_TYPEFLAG_BOLKI))
        {

            sb.AppendLine("<div style=\"border-bottom: 1px solid #cccccc; margin-top: 15px; margin-bottom: 15px; \"></div>");

            sb.AppendLine("        <table align=\"center\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; border-collapse: collapse;\">");
            foreach (var attachment in _meeting.Agendas.First().Attachments.Where(x => x.TypeFlag == McvConstant.MEETING_AGENDA_ATTACHMENT_TYPEFLAG_BOLKI))
            {

                var thumburl = attachment.Url.Replace(".mp4", "_thumb.jpg");

                sb.AppendLine("            <tr>");
                sb.AppendLine("                <td style=\"padding-bottom:6px;\">");
                sb.AppendLine($"                     <a href=\"{rootApi}{(await RecordVersion(ID)).ToString()}\">");
                sb.AppendLine("                     <div style=\"position:relative;\">");
                sb.AppendLine("                         <img style=\"position:absolute; top:10%; left:5%; width:50px; z-index:99;\" src=\"https://mycockpitview.in/newarch/assets/icons/video.png\" alt=\"click to play\" />");
                sb.AppendLine("                         <img style=\"cursor:pointer; width:300px; max-width: 90%; border: 1px solid #dfdfdf; border-radius: 6px;\" src=\"" + thumburl + "\" alt=\"\" />");
                sb.AppendLine("                     </div>");
                sb.AppendLine("                 ");

                sb.AppendLine("                </td>");
                sb.AppendLine("            </tr>");
            }
            sb.AppendLine("        </table>");
            sb.AppendLine("");

        }

        sb.AppendLine("<div style=\"border-bottom: 1px solid #cccccc; margin-top: 15px; margin-bottom: 15px; \"></div>");
        sb.AppendLine("        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; border-collapse: collapse;\">");
        //sb.AppendLine("            <tr>");
        //sb.AppendLine("                <td valign=\"top\" width=\"80\" style=\"font-weight:bold;\">");
        //sb.AppendLine("                    Date:");
        //sb.AppendLine("                </td>");
        //sb.AppendLine("                <td valign=\"top\" style=\"padding: 2px;\">");
        //sb.Append(ClockTools.GetIST(_meeting.StartDate).ToString("dd MMM yyyy HH:mm"));
        //sb.AppendLine("                </td>");
        //sb.AppendLine("            </tr>");
        //sb.AppendLine("            <tr>");
        //sb.AppendLine("                <td valign=\"top\" width=\"80\" style=\"font-weight:bold;\">");
        //sb.AppendLine("                    No.");
        //sb.AppendLine("                </td>");
        //sb.AppendLine("                <td valign=\"top\" style=\"padding: 2px;\">");
        //sb.Append(_version);
        //sb.AppendLine("                </td>");
        //sb.AppendLine("            </tr>");
        //sb.AppendLine("            <tr>");
        //sb.AppendLine("                <td valign=\"top\" width=\"80\" style=\"font-weight:bold;\">");
        //sb.AppendLine("                    Subject:");
        //sb.AppendLine("                </td>");
        //sb.AppendLine("                <td valign=\"top\" style=\"padding: 2px;\">");
        //sb.Append(_meeting.Title);
        //sb.AppendLine("                </td>");
        //sb.AppendLine("            </tr>");
        sb.AppendLine("            <tr>");
        sb.AppendLine("                <td rowspan=\"2\" valign=\"top\" width=\"80\" style=\"font-weight:bold;\">");
        sb.AppendLine("                    From:");
        sb.AppendLine("                </td>");
        sb.AppendLine("                <td valign=\"top\" style=\"padding: 2px;\">");
        sb.Append(_meeting.Contact.FullName + " <i>(" + _meeting.Contact.Email1 + ")</i>");
        sb.AppendLine("                </td>");
        sb.AppendLine("            </tr>");
        sb.AppendLine("            <tr>");
        sb.AppendLine("                <td valign=\"top\" style=\"padding: 2px;\">");
        sb.Append($"<small>{leaves}</small>");
        sb.AppendLine("                </td>");
        sb.AppendLine("            </tr>");
        sb.AppendLine("        </table>");
        sb.AppendLine("");
        sb.AppendLine("        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; border-collapse: collapse;\">");


        sb.AppendLine("            <tr>");
        sb.AppendLine("                <td valign=\"top\"  style=\"font-weight:bold;\">");
        sb.AppendLine(_meeting.TypeFlag != McvConstant.MEETING_TYPEFLAG_CNOTE ? "Attendee:" : " To:");
        sb.AppendLine("                </td>");
        sb.AppendLine("            </tr>");

        sb.AppendLine("            <tr>");
        sb.AppendLine("                <td valign=\"top\" style=\"padding: 2px;\">");
        sb.AppendLine("        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; border-collapse: collapse;\">");
        foreach (var item in _tos.OrderBy(x => x.OrderFlag))
        {
            sb.AppendLine("            <tr>");
            sb.AppendLine("                <td >");
            sb.Append(item.Name + "<i> (" + item.Email + ")</i>");
            sb.AppendLine("                </td>");
            sb.AppendLine("                <td valign=\"top\" width=\"70px\">");
            sb.Append(item.PendingAgendaCount > 0 ? "Pending " + item.PendingAgendaCount.ToString("00") : "");
            sb.AppendLine("                </td>");
            sb.AppendLine("            </tr>");

        }
        sb.AppendLine("        </table>");
        sb.AppendLine("                </td>");
        sb.AppendLine("            </tr>");

        sb.AppendLine("            <tr>");
        sb.AppendLine("                <td valign=\"top\"  style=\"font-weight:bold;\">");
        sb.AppendLine("                    CC:");
        sb.AppendLine("                </td>");
        sb.AppendLine("            </tr>");

        sb.AppendLine("            <tr>");
        sb.AppendLine("                <td valign=\"top\" style=\"padding: 2px;\">");
        sb.AppendLine("        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; border-collapse: collapse;\">");
        foreach (var item in _ccs.OrderBy(x => x.OrderFlag))
        {
            sb.AppendLine("            <tr>");
            sb.AppendLine("                <td >");
            sb.Append(item.Name + "<i> (" + item.Email + ")</i>");
            sb.AppendLine("                </td>");
            sb.AppendLine("                <td valign=\"top\" width=\"70px\">");
            sb.Append(item.PendingAgendaCount > 0 ? "Pending " + item.PendingAgendaCount.ToString("00") : "");
            sb.AppendLine("                </td>");
            sb.AppendLine("            </tr>");

        }
        sb.AppendLine("        </table>");

        sb.AppendLine("                </td>");
        sb.AppendLine("            </tr>");

        sb.AppendLine("        </table>");


        if (_meeting.TypeFlag == McvConstant.MEETING_TYPEFLAG_CNOTE && _meeting.Agendas != null && _meeting.Agendas.Any())
        {

            var _index = 1;
            foreach (var obj in _meeting.Agendas)
            {
                sb.AppendLine("        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; border-collapse: collapse;\">");
                sb.AppendLine("                        <tr>");
                sb.AppendLine("                            <td valign=\"top\" style=\"padding: 2px; \">");
                sb.AppendLine(obj.Title + " " + obj.Subtitle);
                sb.AppendLine("                            </td>");
                sb.AppendLine("                        </tr>");
                sb.AppendLine("        </table>");

                sb.AppendLine("        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; border-collapse: collapse;\">");


                //Comment
                sb.AppendLine("                        <tr>");
                sb.AppendLine("                            <td valign=\"top\" style=\"padding: 2px;font-weight:bold; \">");
                sb.AppendLine("Comment:");
                sb.AppendLine("                            </td>");
                sb.AppendLine("                        </tr>");
                sb.AppendLine("                        <tr>");
                sb.AppendLine("                            <td valign=\"top\" style=\"padding: 2px;text-decoration: line-through; \">");
                sb.AppendLine("                    <pre style=\"font-family: Calibri;font-size: 11px;margin-top: 0;margin-bottom: 0;white-space: pre-wrap;white-space: -moz-pre-wrap;white-space: -pre-wrap;white-space: -o-pre-wrap;word-wrap: break-word;\">");
                sb.AppendLine(obj.PreviousComment != null ? obj.PreviousComment.Trim() : "");
                sb.AppendLine("                        </pre>");
                sb.AppendLine("                            </td>");
                sb.AppendLine("                        </tr>");
                sb.AppendLine("                        <tr>");
                sb.AppendLine("                            <td valign=\"top\" style=\"padding: 2px; \">");
                sb.AppendLine("                    <pre style=\"font-family: Calibri;font-size: 14px;margin-top: 0;margin-bottom: 0;white-space: pre-wrap;white-space: -moz-pre-wrap;white-space: -pre-wrap;white-space: -o-pre-wrap;word-wrap: break-word;\">");
                sb.AppendLine(obj.Comment != null ? obj.Comment.Trim() : "");
                sb.AppendLine("                        </pre>");
                sb.AppendLine("                            </td>");
                sb.AppendLine("                        </tr>");

                //Actionby
                sb.AppendLine("                        <tr>");
                sb.AppendLine("                            <td valign=\"top\" style=\"padding: 2px;font-weight:bold; \" >");
                sb.AppendLine("Action By:");
                sb.AppendLine("                            </td>");
                sb.AppendLine("                        </tr>");
                sb.AppendLine("                        <tr>");
                sb.AppendLine("                            <td valign=\"top\" style=\"padding: 2px;text-decoration: line-through;font-size:11px; \">");
                sb.AppendLine(obj.PreviousActionBy);
                sb.AppendLine("                            </td>");
                sb.AppendLine("                        </tr>");
                sb.AppendLine("                        <tr>");
                sb.AppendLine("                            <td valign=\"top\" style=\"padding: 2px; \">");
                sb.AppendLine(obj.ActionBy);
                sb.AppendLine("                            </td>");
                sb.AppendLine("                        </tr>");

                //Status
                sb.AppendLine("                        <tr>");
                sb.AppendLine("                            <td valign=\"top\" style=\"padding: 2px;font-weight:bold; \" >");
                sb.AppendLine("Status:");
                sb.AppendLine("                            </td>");
                sb.AppendLine("                        </tr>");
                sb.AppendLine("                        <tr>");
                sb.AppendLine("                            <td valign=\"top\" style=\"padding: 2px;text-decoration: line-through;font-size:11px; \">");
                sb.AppendLine("PENDING");
                sb.AppendLine("                            </td>");
                sb.AppendLine("                        </tr>");
                sb.AppendLine("                        <tr>");
                sb.AppendLine("                            <td valign=\"top\" style=\"padding: 2px; \">");
                sb.AppendLine(obj.StatusFlag == 1 ? "RESOLVED" : "PENDING");
                sb.AppendLine("                            </td>");
                sb.AppendLine("                        </tr>");

                //DueDate
                sb.AppendLine("                        <tr>");
                sb.AppendLine("                            <td valign=\"top\" style=\"padding: 2px;font-weight:bold; \">");
                sb.AppendLine("Due Date:");
                sb.AppendLine("                            </td>");
                sb.AppendLine("                        </tr>");
                sb.AppendLine("                        <tr>");
                sb.AppendLine("                            <td valign=\"top\" style=\"padding: 2px;text-decoration: line-through;font-size:11px; \">");
                sb.AppendLine(obj.PreviousDueDate != null ? ClockTools.GetIST(obj.PreviousDueDate.Value).ToString("dd MMM yyyy") : "");
                sb.AppendLine("                            </td>");
                sb.AppendLine("                        </tr>");
                sb.AppendLine("                        <tr>");
                sb.AppendLine("                            <td valign=\"top\" style=\"padding: 2px; \">");
                sb.AppendLine(obj.DueDate != null ? ClockTools.GetIST(obj.DueDate.Value).ToString("dd MMM yyyy") : "");
                sb.AppendLine("                            </td>");
                sb.AppendLine("                        </tr>");

                ////attachments
                //sb.AppendLine("                        <tr>");
                //sb.AppendLine("                            <td valign=\"top\" style=\"padding: 2px;font-weight:bold; \">");
                //sb.AppendLine("Attachments:");
                //sb.AppendLine("                            </td>");
                //sb.AppendLine("                        </tr>");
                //sb.AppendLine("                        <tr>");
                //sb.AppendLine("                            <td valign=\"top\" style=\"padding: 2px; \">");
                //sb.AppendLine(obj.Attachments.Count.ToString()+" files attached.");
                //sb.AppendLine("                            </td>");
                //sb.AppendLine("                        </tr>");


                sb.AppendLine("        </table>");


                _index++;
            }

        }



        //FOOTER
        sb.AppendLine("<div style=\"border-bottom: 1px solid #cccccc; margin-top: 15px; margin-bottom: 15px; \"></div>");
        sb.AppendLine("        <table align=\"center\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; border-collapse: collapse;font-size:11px;\">");

        sb.AppendLine("");
        sb.AppendLine("            <tr>");
        sb.AppendLine("");
        sb.AppendLine("                <td align=\"center\" >");
        sb.AppendLine("This is a <b>MyCockpitView<sup>&copy;</sup></b> & <b>DesignScript<sup>&copy;</sup></b> generated e-mail for your information and necessary action.");
        sb.AppendLine("</td>");
        sb.AppendLine("            </tr>");
        sb.AppendLine("");
        sb.AppendLine("            <tr>");
        sb.AppendLine("");
        sb.AppendLine("                <td align=\"center\" >");
        sb.AppendLine("");
        sb.AppendLine("                    Powered by <b>Newarch<sup>&reg;</sup> Infotech LLP</b>");
        sb.AppendLine("");
        sb.AppendLine("                </td>");
        sb.AppendLine("            </tr>");
        sb.AppendLine("        </table>");
        sb.AppendLine("    </div>");

        sb.AppendLine("</body>");
        sb.AppendLine("");
        sb.AppendLine("</html>");

        var _emailBody = sb.ToString();

        var _senderName = await sharedService.GetPresetValue(McvConstant.MEETING_EMAIL_SENDER_NAME);
        var _senderEmail = await sharedService.GetPresetValue(McvConstant.MEETING_EMAIL_SENDER_ID);
        var _creator = _meeting.Attendees.FirstOrDefault(x => x.ContactID == _meeting.ContactID);
        if (_creator == null)
        {
            _creator = new MeetingAttendee { Name = _meeting.Contact.FullName, Email = _meeting.Contact.Email1 };

            //_creator = new MeetingAttendee { Name = _meeting.Contact.FullName, Email = _meeting.Contact.Emails.FirstOrDefault(c => c.IsPrimary).Email };
        
    }

        var emailTo = new List<(string name, string email)>();
        foreach (var obj in _tos)
            emailTo.Add((obj.Name, obj.Email));

        var emailCC = new List<(string name, string email)>();
        foreach (var obj in _ccs)
            emailCC.Add((obj.Name, obj.Email));

        if (_defaultCCList != null)
        {
            Regex myRegex = new Regex(McvConstant.EMAIL_REGEX, RegexOptions.None);
            foreach (Match myMatch in myRegex.Matches(_defaultCCList))
            {
                if (!emailTo.Any(a => a.email.Equals(myMatch.Value.Trim())) && !emailCC.Any(a => a.email.Equals(myMatch.Value.Trim())))
                    emailCC.Add(("Company", myMatch.Value.Trim()));

            }
        }

        await sharedService.SendMail(_emailSubject.ToUpper().Trim(),
        _senderName,
          _senderEmail,
          _emailBody, emailTo, emailCC, replyAddress: _creator.Email, replyName: _creator.Name);

        if (_meeting.ProjectID != null)
        {
            var _project = await db.Projects.SingleOrDefaultAsync(x => x.ID == _meeting.ProjectID);
            if (_project != null && _project.StatusFlag == McvConstant.PROJECT_STATUSFLAG_LOCKED)
            {
                _project.StatusFlag = McvConstant.PROJECT_STATUSFLAG_INPROGRESS;
                await db.SaveChangesAsync();
            }
        }

        var meetingAgendaService = new MeetingAgendaService(db);
        if (_meeting.TypeFlag == McvConstant.MEETING_TYPEFLAG_CNOTE || _meeting.TypeFlag == McvConstant.MEETING_TYPEFLAG_MEETING)
        {
            foreach (var agenda in _meeting.Agendas.Where(x => x.StatusFlag == McvConstant.MEETING_AGENDA_STATUSFLAG_PENDING && x.ActionByContactID != null && !x.IsForwarded && x.PackageID == null && x.TodoID == null))
            {
                await meetingAgendaService.AssignAgendaTasks(agenda.ID);
            }
        }

        if (_meeting.ProjectID != null)
        {
            var projectService = new ProjectService(db);
            await projectService.RecordHistory(_meeting.ProjectID.Value);
        }

    }
    public async Task Delete(int Id)
    {

        var _entity = await Get()

            .SingleOrDefaultAsync(x => x.ID == Id);

        if (_entity == null) throw new EntityServiceException($"{nameof(Meeting)} not found.");

        var meetingAttendeeService = new MeetingAttendeeService(db);
        var attendees = await meetingAttendeeService.Get().Where(x => x.MeetingID == Id)
            .Select(x => x.ID).ToListAsync();
        foreach (var i in attendees)
        {
            await meetingAttendeeService.Delete(i);
        }


        var meetingAgendaService = new MeetingAgendaService(db);
        var agendas = await meetingAgendaService.Get().Where(x => x.MeetingID == Id)
      .Select(x => x.ID).ToListAsync();
        foreach (var i in agendas)
        {
            await meetingAgendaService.Delete(i);
        }


        var taskService = new WFTaskService(db);
        var tasks = await taskService.Get()
            .Where(x => x.Entity == nameof(Meeting) && x.EntityID == Id)
            .Select(x => x.ID)
            .ToListAsync();
        foreach (var i in tasks)
        {
            await taskService.Delete(i);
        }


        var expenseService = new ExpenseService(db);
        var _vouchers = await expenseService.Get()
               .Where(x => x.Entity == nameof(Meeting)
               && x.EntityID == Id)
               .Select(x => x.ID)
               .ToListAsync();

        foreach (var i in _vouchers)
        {
            await expenseService.Delete(i);
        }


        await base.Delete(Id);

    }

    public async Task<Meeting> GetLastPending(Meeting Meeting)
    {
        //pending meetings or C-note
        var _query = db.Meetings.AsNoTracking()
            .Where(x => x.ParentID == null)
                          .Where(x => x.Title==Meeting.Title
                                   && x.StatusFlag != McvConstant.MEETING_STATUSFLAG_SENT)
                          //.Where(x => x.StartDate <= Meeting.StartDate)
                          .OrderByDescending(x => x.StartDate);

        var _lastNote = await _query
               .Where(x => x.TypeFlag == McvConstant.MEETING_TYPEFLAG_CNOTE)
               .FirstOrDefaultAsync();

        var _lastMeet = await _query
             .Where(x => x.TypeFlag == McvConstant.MEETING_TYPEFLAG_MEETING)
                .FirstOrDefaultAsync();


        if (Meeting.TypeFlag == McvConstant.MEETING_TYPEFLAG_CNOTE) //C-note
        {
            return _lastNote;
        }
        else if (Meeting.TypeFlag == McvConstant.MEETING_TYPEFLAG_INSPECTION) //Inspection
        {
            return await _query
               .Where(x => x.TypeFlag == McvConstant.MEETING_TYPEFLAG_INSPECTION)
               .FirstOrDefaultAsync();
        }
        else
        {
            if (_lastNote != null) return _lastNote;

            return _lastMeet;
        }

    }


    public async Task<Guid> RecordVersion(int ID)
    { //RECORD ENTITY HISTORY
        var meeting = await Get()
            .Where(x => !x.IsVersion)
            .Where(x => !x.IsDeleted)
                    .Include(x => x.Contact)
                    .Include(x => x.Attendees)
                     .Include(x => x.Agendas)
                     .ThenInclude(a => a.Attachments)
                    .SingleOrDefaultAsync(x => x.ID == ID);

        if (meeting == null) throw new EntityServiceException("Meeting not found!");

        var newEntity = new Meeting();
        // Set values from the original entity to the new entity
        db.Entry(newEntity).CurrentValues.SetValues(meeting);
        // Detach the newAttendee if it's already being tracked
        if (db.Entry(newEntity).State != EntityState.Detached)
        {
            db.Entry(newEntity).State = EntityState.Detached;
        }
        newEntity.ID = default(int);
        newEntity.UID = default(Guid);
        // Create a new entity with a deep copy of the original entity's properties

        db.Entry(newEntity).State = EntityState.Added;

        // Add the new entity to the context
        db.Meetings.Add(newEntity);


        // Reset the ID property
        newEntity.ID = 0;
        newEntity.ParentID = meeting.ID;
        newEntity.IsVersion = true;


        foreach (var item in meeting.Attendees)
        {
            // Create a new instance and copy the values
            var newAttendee = new MeetingAttendee();
            db.Entry(newAttendee).CurrentValues.SetValues(item);

            // Detach the newAttendee if it's already being tracked
            if (db.Entry(newAttendee).State != EntityState.Detached)
            {
                db.Entry(newAttendee).State = EntityState.Detached;
            }

            // Set ID to default (assuming ID is an int)
            newAttendee.ID = default(int);
            newAttendee.UID = default(Guid);
            newAttendee.IsVersion = true;
            newAttendee.ParentID = item.ID;
            // Attach the new entity to the context
            db.Entry(newAttendee).State = EntityState.Added;

            // Add the new entity to the context
            db.MeetingAttendees.Add(newAttendee);

            // Add the new entity to the collection
            newEntity.Attendees.Add(newAttendee);

        }

        foreach (var item in meeting.Agendas)
        {
            // Create a new instance and copy the values
            var newAgenda = new MeetingAgenda();
            db.Entry(newAgenda).CurrentValues.SetValues(item);

            // Detach the newAgenda if it's already being tracked
            if (db.Entry(newAgenda).State != EntityState.Detached)
            {
                db.Entry(newAgenda).State = EntityState.Detached;
            }

            // Set ID to default (assuming ID is an int)
            newAgenda.ID = default(int);
            newAgenda.UID = default(Guid);
            newAgenda.IsVersion = true;
            newAgenda.ParentID = item.ID;
            // Attach the new entity to the context
            db.Entry(newAgenda).State = EntityState.Added;


            foreach (var attachment in item.Attachments)
            {

                // Create a new instance and copy the values
                var newAttachment = new MeetingAgendaAttachment();
                db.Entry(newAttachment).CurrentValues.SetValues(attachment);

                // Detach the newAttachment if it's already being tracked
                if (db.Entry(newAttachment).State != EntityState.Detached)
                {
                    db.Entry(newAttachment).State = EntityState.Detached;
                }

                // Set ID to default (assuming ID is an int)
                newAttachment.ID = default(int);
                newAttachment.UID = default(Guid);
                newAttachment.IsVersion = true;
                newAttachment.ParentID = attachment.ID;
                // Attach the new entity to the context
                db.Entry(newAttachment).State = EntityState.Added;
                // Add the new entity to the context
                db.MeetingAgendaAttachments.Add(newAttachment);

                newAgenda.Attachments.Add(newAttachment);
            }
            // Add the new entity to the context
            db.MeetingAgendas.Add(newAgenda);

            // Add the new entity to the collection
            newEntity.Agendas.Add(newAgenda);
        }

        await db.SaveChangesAsync();

        return newEntity.UID;

    }


    public async Task<string> GetMeetingAgendaHistoryString(MeetingAgenda meetingAgenda)
    {


        var _commentHistory = new StringBuilder();

        //Line 1
        if (meetingAgenda.UpdateFrom != null)
        {
            if (meetingAgenda.UpdateFrom.ToUpper() == "SUBMISSION" && meetingAgenda.PackageID != null)
            {
                var _package = await db.Packages.AsNoTracking().SingleOrDefaultAsync(x => x.ID == meetingAgenda.PackageID);
                if (_package.SubmissionDate != null)
                    _commentHistory.AppendLine($"[{ClockTools.GetIST(_package != null ? _package.SubmissionDate.Value : meetingAgenda.Modified).ToString("dd MMM yyyy HH:mm")} {(_package != null ? _package.CreatedBy : meetingAgenda.ModifiedBy)} {meetingAgenda.UpdateFrom.ToUpper()}] ");
            }
            else
            {
                _commentHistory.AppendLine("[" + ClockTools.GetIST(meetingAgenda.Modified).ToString("dd MMM yyyy HH:mm")
                  + " "
                  + meetingAgenda.ModifiedBy
                  + " "
                  + meetingAgenda.UpdateFrom.ToUpper() + "] ");
            }
        }
        else
        {
            _commentHistory.AppendLine("[" + ClockTools.GetIST(meetingAgenda.MeetingDate.Value).ToString("dd MMM yyyy HH:mm")
              + " "
              + meetingAgenda.CreatedBy
              + " "
              + (meetingAgenda.TypeFlag == McvConstant.MEETING_TYPEFLAG_CNOTE ? " C-NOTE " :
              (meetingAgenda.TypeFlag == McvConstant.MEETING_TYPEFLAG_INSPECTION ? " INSPECTION " : " MEETING")) + "] ");
        }


        //line 2                   
        _commentHistory.AppendLine((meetingAgenda.Comment != null ? meetingAgenda.Comment.Trim() : (meetingAgenda.PreviousComment != null ? meetingAgenda.PreviousComment.Trim() : "NOT DISCUSSED")));


        //line 3
        if (meetingAgenda.ActionBy != null)
            _commentHistory.Append(" Action By: " + meetingAgenda.ActionBy);

        if (meetingAgenda.DueDate != null)
            _commentHistory.AppendLine(" Due: " + ClockTools.GetIST(meetingAgenda.DueDate.Value).ToString("dd MMM yyyy"));

        //if (_agenda.Meeting.TypeFlag == 1)
        //{
        //    if (_agenda.Comment == null || _agenda.Comment == String.Empty)
        //    {
        //        _commentHistory = new StringBuilder();

        //    }
        //}

        //line 4
        if (meetingAgenda.PreviousHistory != null)
        {
            _commentHistory.AppendLine("  ");//blank line
            _commentHistory.AppendLine(meetingAgenda.PreviousHistory);
        }

        return _commentHistory.ToString().Trim();

    }

    public async Task<IEnumerable<int>> GetAssigneeContactIDs(int entityID, string stageCode, string propertyName)
    {
        var Entity = await Get()
           .Include(x => x.Contact)
           .Where(x => x.ID == entityID).SingleOrDefaultAsync();

        if (Entity == null) throw new EntityServiceException($"{nameof(Meeting)} not found!");

        var _list = new List<int>();

        if (stageCode.Equals("MEETING_CLOSE", StringComparison.OrdinalIgnoreCase) //Close Meeting
        || stageCode.Equals("MEETING_PREPARE_MINUTES", StringComparison.OrdinalIgnoreCase)//Prepare Minutes Meeting
        || stageCode.Equals("CNOTE_PREPARE", StringComparison.OrdinalIgnoreCase)//Prepare Minutes CNOTE
        )
        {
            var _type = DataTools.GetPropertyType(Entity, propertyName);
            if (_type == typeof(Contact))
            {
                _list.Add(((Contact)DataTools.GetPropertyValue(Entity, propertyName)).ID);
            }
        }
        else if (stageCode.Equals("MEETING_TRAVEL_TIME", StringComparison.OrdinalIgnoreCase))//Log Travel time
        {
            var _type = DataTools.GetPropertyType(Entity, propertyName);
            if (_type == typeof(Contact))
            {
                _list.Add(((Contact)DataTools.GetPropertyValue(Entity, propertyName)).ID);
            }


            var _attendees = await db.MeetingAttendees.AsNoTracking()
              .Where(x => x.MeetingID == entityID)
               .Where(x => x.TypeFlag == 0)
               .Where(x => db.Contacts.Where(c => c.Username != null)
                .Select(c => c.ID).Any(c => c == x.ContactID))
               .Select(x => x.ContactID)
               .ToListAsync();

            var _contacts = new List<int>();

            foreach (var i in _attendees)
                _contacts.Add(i.Value);

            if (!_contacts.Any(x => x == Entity.ContactID))
                _contacts.Add(Entity.ContactID);

            return _contacts;

        }
        else
        {
            throw new EntityServiceException($"{nameof(Meeting)} Task assignee not found for stage {stageCode}!");
        }
        return _list;
    }

    public async Task<dynamic> GetTaskByStage(string Entity, int EntityID, string StageCode, string StageTitle, decimal StageDuration, DateTime? FollowUpDate = null)
    {


        var sharedService = new SharedService(db); ;

        var _entity = await Get().SingleOrDefaultAsync(x => x.ID == EntityID);

        if (_entity == null) throw new EntityServiceException($"{nameof(Meeting)} not found!");

        var _startTimeSpan = TimeSpan.Parse(await sharedService.GetPresetValue(McvConstant.OFFICE_OPEN_TIME));
        var _endTimeSpan = TimeSpan.Parse(await sharedService.GetPresetValue(McvConstant.OFFICE_CLOSE_TIME));

        var _nextDue = DateTime.UtcNow.AddDays(Decimal.ToDouble(StageDuration)).Date;

        if (FollowUpDate != null)
            _nextDue = FollowUpDate.Value.Date;

        if (StageCode == "MEETING_CLOSE" || StageCode == "MEETING_PREPARE_MINUTES"//Prepare Minutes Meeting
                || StageCode == "CNOTE_PREPARE")//Work
        {
            _nextDue = _entity.EndDate.AddDays(1);

        }
        else if (StageCode == "MEETING_TRAVEL_TIME")
        {
            _nextDue = _entity.EndDate.AddDays(1);
        }

        var start = DateTime.UtcNow;
        if (_nextDue < start)
            start = _nextDue;

        return new
        {
            Title = StageTitle,
            Entity = Entity,
            EntityID = EntityID,
            Subtitle = $"{_entity.Code}",
            WFStageCode = StageCode,
            StartDate = ClockTools.GetUTC(ClockTools.GetIST(_nextDue).Date
              .AddMinutes(_startTimeSpan.TotalMinutes)),
            DueDate = ClockTools.GetUTC(ClockTools.GetIST(_nextDue).Date
              .AddMinutes(_endTimeSpan.TotalMinutes)),
            MHrAssigned = 0,
            IsPreAssignedTimeTask = false
        };
    }

    public async Task TaskAction(int EntityID, string StageCode, string taskComment = null)
    {
        var Entity = await Get()
                      .SingleOrDefaultAsync(x => x.ID == EntityID);

        if (Entity == null) throw new EntityServiceException($"{nameof(Meeting)} not found!");

        if (StageCode == "SYS_MEETING_CLOSE") //Close Meeting
        {

            if (Entity.StatusFlag != McvConstant.MEETING_STATUSFLAG_ATTENDED)
            {
                Entity.StatusFlag = McvConstant.MEETING_STATUSFLAG_ATTENDED;

                Entity.ClosedOn = DateTime.UtcNow;
                db.Entry(Entity).State = EntityState.Modified;
                await db.SaveChangesAsync();
                db.Entry(Entity).State = EntityState.Detached;
            }
            var meetingAttendeeService = new MeetingAttendeeService(db);
            var _attendees = await meetingAttendeeService.Get()
                .Where(x => x.MeetingID == Entity.ID)
                 .Where(x => x.TypeFlag == McvConstant.MEETING_ATTENDEE_TYPEFLAG_TO)
                 .ToListAsync();


            foreach (var _obj in _attendees)
            {
                await meetingAttendeeService.LogAttendeeTime(_obj.ContactID.Value, Entity.ID, Entity.StartDate, Entity.EndDate, "MEETING_CLOSE");
            }

            await meetingAttendeeService.LogAttendeeTime(Entity.ContactID, Entity.ID, Entity.StartDate, Entity.EndDate, "MEETING_CLOSE");

        }
        else if (StageCode == "SYS_MEETING_DELETE")
        {

            var meetingAttendeeService = new MeetingAttendeeService(db);
            var attendees = await meetingAttendeeService.Get().Where(x => x.MeetingID == EntityID)
                .Select(x => x.ID).ToListAsync();
            foreach (var i in attendees)
            {
                await meetingAttendeeService.Delete(i);
            }


            var meetingAgendaService = new MeetingAgendaService(db);
            var agendas = await meetingAgendaService.Get().Where(x => x.MeetingID == EntityID)
          .Select(x => x.ID).ToListAsync();
            foreach (var i in agendas)
            {
                await meetingAgendaService.Delete(i);
            }


            var taskService = new WFTaskService(db);
            var tasks = await taskService.Get()
                .Where(x => x.Entity == nameof(Meeting) && x.EntityID == EntityID)

                .ToListAsync();
            foreach (var i in tasks)
            {
                i.IsDeleted = true;
            }


            var expenseService = new ExpenseService(db);
            var _vouchers = await expenseService.Get()
                   .Where(x => x.Entity == nameof(Meeting)
                   && x.EntityID == EntityID)
                   .Select(x => x.ID)
                   .ToListAsync();

            foreach (var i in _vouchers)
            {
                await expenseService.Delete(i);
            }


            await base.Delete(EntityID);

        }
        else if (StageCode == "SYS_MEETING_SEND") //Send Minutes
        {

            await SendMinutes(Entity.ID);

            Entity.FinalizedOn = DateTime.UtcNow;
            Entity.StatusFlag = McvConstant.MEETING_STATUSFLAG_SENT;
            db.Entry(Entity).State = EntityState.Modified;
            await db.SaveChangesAsync();

        }

    }


}

public class MeetingEmailContact
{
    public int PendingAgendaCount { get; set; }

    public string? Leaves { get; set; }

    public int OrderFlag { get; set; }

    public int ID { get; set; }
    public Guid UID { get; set; }
    public string? Name { get; set; }
    public string? Email { get; set; }
    public string? Company { get; set; }
    public string? TypeValue { get; set; }
    public int TypeFlag { get; set; }
    public string? PhotoUrl { get; set; }
}