﻿

using System.Data;




using MyCockpitView.WebApi.Exceptions;
using MyCockpitView.WebApi.ContactModule.Entities;
using MyCockpitView.WebApi.MeetingModule.Services;
using MyCockpitView.WebApi.ProjectModule.Services;
using MyCockpitView.WebApi.Services;
using MyCockpitView.WebApi.TodoModule.Entities;
using MyCockpitView.WebApi.WFTaskModule.Services;
using MyCockpitView.CoreModule;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.Utility.Common;

namespace MyCockpitView.WebApi.TodoModule.Services;

public class TodoService : BaseEntityService<Todo>, ITodoService
{
    public TodoService(EntitiesContext db) : base(db) { }

    public IQueryable<Todo> Get(IEnumerable<QueryFilter>? Filters = null, string? Search = null, string? Sort = null)
    {

        IQueryable<Todo> _query = base.Get(Filters);

        //Apply filters
        if (Filters != null)
        {

            if (Filters.Where(x => x.Key.Equals("assignerContactID", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<Todo>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("assignerContactID", StringComparison.OrdinalIgnoreCase)))
                {
                    var isNumeric = Convert.ToInt32(_item.Value);

                    predicate = predicate.Or(x => x.AssignerContactID == isNumeric);
                }
                _query = _query.Where(predicate);
            }
            if (Filters.Where(x => x.Key.Equals("assigneeContactID", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<Todo>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("assigneeContactID", StringComparison.OrdinalIgnoreCase)))
                {
                    var isNumeric = Convert.ToInt32(_item.Value);

                    predicate = predicate.Or(x => x.AssigneeContactID == isNumeric);
                }
                _query = _query.Where(predicate);
            }


            if (Filters.Where(x => x.Key.Equals("projectid", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<Todo>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("projectid", StringComparison.OrdinalIgnoreCase)))
                {
                    predicate = predicate.Or(x => x.ProjectID != null && x.ProjectID.ToString() == _item.Value);
                }
                _query = _query.Where(predicate);
            }
        }

        if (Search != null && Search != String.Empty)
        {
            Search = Search.ToLower();
            _query = _query
                 .Where(x => x.Title.ToLower().Contains(Search.ToLower())
                 || x.Subtitle.ToLower().Contains(Search.ToLower())
                                            || (x.AssigneeContact.FirstName + " " + x.AssigneeContact.LastName).ToLower().Contains(Search.ToLower())
                                            || x._searchTags.ToLower().Contains(Search.ToLower())
                                           );

        }

        if (Sort != null && Sort != String.Empty)
        {
            switch (Sort.ToLower())
            {
                case "createddate":
                    return _query
                            .OrderBy(x => x.Created);

                case "modifieddate":
                    return _query
                            .OrderBy(x => x.Modified);

                case "createddate desc":
                    return _query
                            .OrderByDescending(x => x.Created);

                case "modifieddate desc":
                    return _query
                            .OrderByDescending(x => x.Modified);

                case "duedate":
                    return _query
                            .OrderBy(x => x.DueDate);

                case "duedate desc":
                    return _query
                            .OrderByDescending(x => x.DueDate);
            }
        }

        return _query.OrderBy(x => x.DueDate);

    }

    public async Task<Todo?> GetById(int Id)
    {

        return await db.Todos.AsNoTracking()
           .Include(x => x.AssigneeContact)
                     .Include(x => x.AssignerContact)
                     .Include(x => x.Agendas)
                     .Include(x => x.Attachments)
              .SingleOrDefaultAsync(i => i.ID == Id);

    }

    public async Task<int> Create(Todo Entity, ICollection<TodoAgenda>? Agendas = null)
    {

        if (Entity.ProjectID != null)
        {
            var _project = await db.Projects.AsNoTracking().SingleOrDefaultAsync(x => x.ID == Entity.ProjectID);
            if (_project != null)
            {
                Entity.Title = _project.Code + "-" + _project.Title;
            }
        }

        Entity.StartDate = DateTime.UtcNow;

        if (Agendas != null)
        {
            Entity.Agendas = Agendas;
        }

        return await base.Create(Entity);
    }

    public async Task Delete(int Id)
    {

        await base.Delete(Id);

        var _meetingAgendas = await db.MeetingAgendas.AsNoTracking()
                .Where(x => !x.IsVersion)
    .Where(x => x.TodoID != null && x.TodoID == Id)
    .ToListAsync();

        var meetingAgendaService = new MeetingAgendaService(db);
        foreach (var agenda in _meetingAgendas)
        {
            await meetingAgendaService.AssignAgendaTasks(agenda.ID);

        }

    }





    public async Task<IEnumerable<int>> GetAssigneeContactIDs(int entityID, string stageCode, string propertyName)
    {
        var todo = await Get()
       .Include(x => x.AssigneeContact)
       .Include(x => x.AssignerContact)
       .Where(x => x.ID == entityID)
       .SingleOrDefaultAsync();

        if (todo == null) throw new EntityServiceException($"{nameof(Todo)} not found!");

        var _list = new List<int>();

        if (stageCode.Equals("TODO_ACCEPT", StringComparison.OrdinalIgnoreCase) //Accept Todo
        || stageCode.Equals("TODO_WORK", StringComparison.OrdinalIgnoreCase)//Todo Work
        || stageCode.Equals("TODO_REVIEW", StringComparison.OrdinalIgnoreCase)//Acknowledge
        || stageCode.Equals("TODO_RE_ASSIGN", StringComparison.OrdinalIgnoreCase))//Reassign
        {
            var _type = DataTools.GetPropertyType(todo, propertyName);
            if (_type == typeof(Contact))
            {
                _list.Add(((Contact)DataTools.GetPropertyValue(todo, propertyName)).ID);
            }
        }
        else
        {
            throw new EntityServiceException($"{nameof(Todo)} Task assignee not found for stage {stageCode}!");
        }

        return _list;
    }

    public async Task<dynamic> GetTaskByStage(string Entity, int EntityID, string StageCode, string StageTitle, decimal StageDuration, DateTime? FollowUpDate = null)
    {


        var sharedService = new SharedService(db); ;

        var _entity = await Get().SingleOrDefaultAsync(x => x.ID == EntityID);

        if (_entity == null) throw new EntityServiceException($"{nameof(Todo)} not found!");

        var _startTimeSpan = TimeSpan.Parse(await sharedService.GetPresetValue(McvConstant.OFFICE_OPEN_TIME));
        var _endTimeSpan = TimeSpan.Parse(await sharedService.GetPresetValue(McvConstant.OFFICE_CLOSE_TIME));

        var _nextDue = _entity.DueDate.Date;// DateTime.UtcNow.AddDays(Decimal.ToDouble(StageDuration)).Date;

        if (FollowUpDate != null)
            _nextDue = FollowUpDate.Value.Date;

        if (StageCode == "TODO_WORK")//Work
        {
            _nextDue = _entity.DueDate.Date;

            return new
            {
                Title = StageTitle,
                Entity = Entity,
                EntityID = EntityID,
                Subtitle = $"{_entity.Title}-{_entity.Subtitle}",
                WFStageCode = StageCode,
                StartDate = ClockTools.GetUTC(ClockTools.GetIST(_entity.DueDate.AddHours(0 - Decimal.ToDouble(_entity.MHrAssigned))).Date.AddMinutes(_startTimeSpan.TotalMinutes)),
                DueDate = _entity.DueDate,
                MHrAssigned = _entity.MHrAssigned,
                IsPreAssignedTimeTask = false
            };
        }


        return new
        {
            Title = StageTitle,
            Entity = Entity,
            EntityID = EntityID,
            Subtitle = $"{_entity.Title}-{_entity.Subtitle}",
            WFStageCode = StageCode,
            StartDate = ClockTools.GetUTC(ClockTools.GetISTNow().Date
                  .AddMinutes(_startTimeSpan.TotalMinutes)),
            DueDate = ClockTools.GetUTC(ClockTools.GetIST(_nextDue).Date
                  .AddMinutes(_endTimeSpan.TotalMinutes)),
            MHrAssigned = 0,
            IsPreAssignedTimeTask = false
        };
    }

    public async Task TaskAction(int EntityID, string StageCode, int WFTaskID, string taskComment = null)
    {
        var Entity = await Get()
                      .SingleOrDefaultAsync(x => x.ID == EntityID);

        if (Entity == null) throw new EntityServiceException($"{nameof(Todo)} not found!");

        if (StageCode == "SYS_TODO_COMPLETE") //Close Todo
        {

            Entity.StatusFlag = 1;
            db.Entry(Entity).State = EntityState.Modified;
            await db.SaveChangesAsync();

            var meetingService = new MeetingService(db);
            var meetingAgendaService = new MeetingAgendaService(db);
            var _meetingAgendas = await meetingAgendaService.Get()
                .Where(x => !x.IsVersion)
                .Where(x => !x.IsForwarded)
                            .Where(x => x.TodoID != null
                            && x.TodoID == Entity.ID)
                            .ToListAsync();


            foreach (var item in _meetingAgendas)
            {
                item.PreviousHistory = await meetingService.GetMeetingAgendaHistoryString(item);
                item.Comment = ($"TODO COMPLETED | {Entity.Title}-{Entity.Subtitle} \n {taskComment}").Trim();
                item.ReminderCount = 0;
                item.UpdateFrom = "TODO";

                await meetingAgendaService.Update(item);
            }

            if (Entity.ProjectID != null)
            {
                var projectService = new ProjectService(db);
                await projectService.RecordHistory(Entity.ProjectID.Value);
            }

        }
        else if (StageCode == "SYS_TODO_DISCARD") //discard
        {

            Entity.StatusFlag = 2;
            db.Entry(Entity).State = EntityState.Modified;
            await db.SaveChangesAsync();


            var meetingService = new MeetingService(db);
            var meetingAgendaService = new MeetingAgendaService(db);
            var _meetingAgendas = await meetingAgendaService.Get()
                .Where(x => !x.IsVersion)
                .Where(x => !x.IsForwarded)
                            .Where(x => x.TodoID != null
                            && x.TodoID == Entity.ID)
                            .ToListAsync();


            foreach (var item in _meetingAgendas)
            {
                item.PreviousHistory = await meetingService.GetMeetingAgendaHistoryString(item);
                item.Comment = ($"TODO DISCARDED | {Entity.Title}-{Entity.Subtitle} \n {taskComment}").Trim();
                item.ReminderCount = 0;
                item.UpdateFrom = "TODO";
                item.TodoID = null;

                await meetingAgendaService.Update(item);
            }

        }
        else if (StageCode == "SYS_TODO_RE_ASSIGN") //reassign
        {
            var wftaskService = new WFTaskService(db);
            await wftaskService.StartFlow(nameof(Todo), Entity.TypeFlag, Entity.ID);
        }
        else if (StageCode == "SYS_TODO_START_TIME") //Start Time
        {
            var timeEntryService = new TimeEntryService(db);
            await timeEntryService.StartTimeLog(WFTaskID);
        }
        else if (StageCode == "SYS_TODO_PAUSE_TIME") //Pause Time
        {
            var timeEntryService = new TimeEntryService(db);
            await timeEntryService.EndTimeLog(WFTaskID, true);
        }
        else if (StageCode == "SYS_TODO_STOP_TIME") //Stop Time
        {
            var timeEntryService = new TimeEntryService(db);
            await timeEntryService.EndTimeLog(WFTaskID);
        }

    }
}