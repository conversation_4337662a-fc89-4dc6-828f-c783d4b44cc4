﻿
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.CoreModule;
using System.ComponentModel.DataAnnotations;

namespace MyCockpitView.WebApi.LeaveModule.Entities;

public class LeaveAttachment : BaseBlobEntity
{
    [Required]
    public int LeaveID { get; set; }

    public virtual Leave? Leave { get; set; }
}

public class LeaveAttachmentConfiguration : BaseBlobEntityConfiguration<LeaveAttachment>, IEntityTypeConfiguration<LeaveAttachment>
{
    public void Configure(EntityTypeBuilder<LeaveAttachment> builder)
    {
      base.Configure(builder);
    }
}

