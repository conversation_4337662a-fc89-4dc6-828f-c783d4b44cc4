﻿
using MyCockpitView.WebApi.ContactModule.Entities;
using MyCockpitView.CoreModule;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;


namespace MyCockpitView.WebApi.RequestTicketModule.Entities;

public class RequestTicket : BaseEntity
{
    [Required]
    [StringLength(255)]
    public string? Title { get; set; }

    [StringLength(255)]
    public string? Subtitle { get; set; }

    [Required]
    [StringLength(255)]
    public string? Purpose { get; set; }

    [Required]
    [StringLength(50)]
    public string? Code { get; set; }

    [Required]
    [Column(TypeName = "datetime2")]
    public DateTime NextReminderDate { get; set; }

    public string? RequestMessage { get; set; }
    public string? ResolutionMessage { get; set; }

    [Required]
    public int AssignerContactID { get; set; }
    public virtual Contact? AssignerContact { get; set; }

    
    [Precision(18,2)] public decimal ReminderInterval { get; set; }
    public int RepeatCount { get; set; }

    [StringLength(255)]
    public string? Entity { get; set; }
    public int? EntityID { get; set; }
    [StringLength(255)]
    public string? EntityTitle { get; set; }
    public int? ProjectID { get; set; }

    public virtual ICollection<RequestTicketAssignee> Assignees { get; set; } = new List<RequestTicketAssignee>();
    public virtual ICollection<RequestTicketAttachment> Attachments { get; set; } = new List<RequestTicketAttachment>();
}
public class RequestTicketConfiguration : BaseEntityConfiguration<RequestTicket>, IEntityTypeConfiguration<RequestTicket>
{
    public void Configure(EntityTypeBuilder<RequestTicket> builder)
    {
        base.Configure(builder);
        builder.HasIndex(e => e.Title);
        builder.HasIndex(e => e.Subtitle);
        builder.HasIndex(e => e.Purpose);
        builder.HasIndex(e => e.Code);
        builder.HasIndex(e => e.NextReminderDate);
        builder.HasIndex(e => e.ProjectID);
        builder.HasIndex(e => e.ParentID);
        builder.HasIndex(e => e.AssignerContactID);
        builder.HasIndex(e => e.Entity);
        builder.HasIndex(e => e.EntityID);
    }
}