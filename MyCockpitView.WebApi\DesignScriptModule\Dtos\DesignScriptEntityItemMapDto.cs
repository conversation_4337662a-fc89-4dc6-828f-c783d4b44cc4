﻿using AutoMapper;
using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.DesignScriptModule.Entities;

namespace MyCockpitView.WebApi.DesignScriptModule.Dtos;

public class DesignScriptEntityItemMapDto : BaseEntityDto
{

    public string CostingUnit { get; set; } = "sqmt";
    public decimal CostingQuantity { get; set; } = 0;
    public decimal CostingRate { get; set; } = 0;
    public decimal CostingAmount { get; set; } = 0;
    public string? CostingRemark { get; set; }
    public int DesignScriptEntityID { get; set; }
    public int DesignScriptItemID { get; set; }
    public int ProjectID { get; set; }
    public virtual DesignScriptItemDto? DesignScriptItem { get; set; }
    public virtual ICollection<DesignScriptMeasurementGroupDto> MeasurementGroups { get; set; } = new List<DesignScriptMeasurementGroupDto>();
}

public class DesignScriptQuantityDtoMapperProfile : Profile
{
    public DesignScriptQuantityDtoMapperProfile()
    {

        CreateMap<DesignScriptEntityItemMap, DesignScriptEntityItemMapDto>()
      .ForMember(dest => dest.MeasurementGroups, opt => opt.MapFrom(src => src.MeasurementGroups))
       .ReverseMap()
        .ForMember(dest => dest.MeasurementGroups, opt => opt.Ignore())
       //.ForMember(dest => dest.Project, opt => opt.Ignore())
                  .ForMember(dest => dest.DesignScriptItem, opt => opt.Ignore())
       .ForMember(dest => dest.DesignScriptEntity, opt => opt.Ignore());

    }
}