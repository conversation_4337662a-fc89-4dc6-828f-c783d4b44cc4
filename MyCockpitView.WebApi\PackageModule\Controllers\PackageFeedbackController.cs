﻿
using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.WebApi.Responses;
using MyCockpitView.Utility.Common;
using MyCockpitView.WebApi.ActivityModule;
using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.ContactModule.Services;
using MyCockpitView.WebApi.PackageModule.Services;
using MyCockpitView.WebApi.PackageModule.Dtos;
using MyCockpitView.WebApi.PackageModule.Entities;
using DocumentFormat.OpenXml.Vml.Office;

namespace MyCockpitView.WebApi.PackageModule.Controllers;

[Authorize]
[ApiController]
[Route("[controller]")]
public class PackageFeedbackController : ControllerBase
{
    private readonly IPackageFeedbackService service;
    private readonly EntitiesContext db;
    private readonly IActivityService activityService;
    private readonly IMapper mapper;
    private readonly IContactService contactService;
    private readonly IPackageAssociationService packageAssociationService;

    public PackageFeedbackController(EntitiesContext db, IPackageFeedbackService service, IMapper mapper, IActivityService activityService, IContactService contactService, IPackageAssociationService packageAssociationService)
    {
        this.db = db;
        this.service = service;
        this.activityService = activityService;
        this.mapper = mapper;
        this.contactService = contactService;
        this.packageAssociationService = packageAssociationService;
    }

    [HttpGet]
    public async Task<ActionResult<IEnumerable<PackageFeedbackDto>>> Get(
        [FromQuery] string? filters = null,
        [FromQuery] string? search = null,
        [FromQuery] string? sort = null)
    {
        var query = service.Get(filters != null ? DataTools.GetObjectFromJsonString<APIFilter>(filters).Filters : null, search, sort)
            .Include(x=>x.Attachments);
        var results = mapper.Map<IEnumerable<PackageFeedbackDto>>(await query.ToListAsync());

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(PackageFeedback))
            .ToListAsync();

        foreach (var obj in results)
        {
            obj.Type = typeMasters.FirstOrDefault(x => x.Value == obj.TypeFlag)?.Title ?? "";
        }

        var statusMasters = await db.StatusMasters
          .AsNoTracking()
          .Where(x => x.Entity == nameof(PackageFeedback))
          .ToListAsync();

        foreach (var obj in results)
        {
            obj.Status = statusMasters.FirstOrDefault(x => x.Value == obj.StatusFlag)?.Title ?? "";
        }

        return Ok(results);
    }

    [HttpGet("Pages")]
    public async Task<ActionResult<PagedResponse<PackageFeedbackDto>>> GetPages(
        [FromQuery] int page = 0,
        [FromQuery] int pageSize = 50,
        [FromQuery] string? filters = null,
        [FromQuery] string? search = null,
        [FromQuery] string? sort = null)
    {
        var query = service.Get(filters != null ? DataTools.GetObjectFromJsonString<APIFilter>(filters).Filters : null, search, sort)
             .Include(x => x.Attachments);

        var totalCount = await query.CountAsync();
        var totalPages = (int)Math.Ceiling((decimal)totalCount / pageSize);
        var results = mapper.Map<IEnumerable<PackageFeedbackDto>>(await query
            .Skip(pageSize * page)
            .Take(pageSize)
            .ToListAsync());

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(PackageFeedback))
            .ToListAsync();

        foreach (var obj in results)
        {
            obj.Type = typeMasters.FirstOrDefault(x => x.Value == obj.TypeFlag)?.Title ?? "";
        }

        var statusMasters = await db.StatusMasters
           .AsNoTracking()
           .Where(x => x.Entity == nameof(PackageFeedback))
           .ToListAsync();

        foreach (var obj in results)
        {
            obj.Status = statusMasters.FirstOrDefault(x => x.Value == obj.StatusFlag)?.Title ?? "";
        }

        return Ok(new PagedResponse<PackageFeedbackDto>(results, totalCount, totalPages));
    }

    [HttpGet("{id:int}")]
    public async Task<ActionResult<PackageFeedbackDto>> GetByID(int id)
    {
        var responseDto = mapper.Map<PackageFeedbackDto>(await service.GetById(id));
        if (responseDto == null)
        {
            return NotFound();
        }

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(PackageFeedback))
            .ToListAsync();

        responseDto.Type = typeMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        var statusMasters = await db.StatusMasters
          .AsNoTracking()
          .Where(x => x.Entity == nameof(PackageFeedback))
          .ToListAsync();

        responseDto.Status = statusMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        return Ok(responseDto);
    }

    [AllowAnonymous]
    [HttpPost]
    public async Task<ActionResult<PackageFeedbackDto>> Post([FromForm] PackageFeedbackCreateDto dto)
    {
        if (!ModelState.IsValid)
        {
            var errors = ModelState
                .Where(e => e.Value?.Errors.Count > 0)
                .Select(e => new { Field = e.Key, Errors = e.Value?.Errors.Select(err => err.ErrorMessage) });
            return BadRequest(new { message = "Binding failed", errors });
        }

        var packageAssociates=await packageAssociationService.Get()
            .Include(x=>x.Contact)
            .Where(x=>x.PackageID== dto.PackageID)
            .Select(x => new
            {
                x.ContactID,
                Name = x.Contact!=null ? x.Contact.FirstName + " " + x.Contact.LastName : null,
                Email = x.Contact !=null ?  x.Contact.Email1 : null,
                x.TypeFlag
            })
            .Where(x=>x.Email!=null)
            .ToListAsync();
        var model = new PackageFeedback
        {
            PackageID = dto.PackageID.Value,
            ReviewerName = dto.ReviewerName,
            ReviewerEmail = dto.ReviewerEmail,
            Rating = dto.Rating.Value,
            Comment = dto.Comment,
        };
        if (packageAssociates.Any(x => x.TypeFlag == McvConstant.PACKAGE_ASSOCIATION_TYPEFLAG_ASSOCIATE))
        {
            model.PackageAssociateName = packageAssociates.FirstOrDefault(x => x.TypeFlag == McvConstant.PACKAGE_ASSOCIATION_TYPEFLAG_ASSOCIATE).Name;
            model.PackageAssociateEmail = packageAssociates.FirstOrDefault(x => x.TypeFlag == McvConstant.PACKAGE_ASSOCIATION_TYPEFLAG_ASSOCIATE).Email;
            model.PackageAssociateContactID = packageAssociates.FirstOrDefault(x => x.TypeFlag == McvConstant.PACKAGE_ASSOCIATION_TYPEFLAG_ASSOCIATE).ContactID;
        }
        if (packageAssociates.Any(x => x.TypeFlag == McvConstant.PACKAGE_ASSOCIATION_TYPEFLAG_PARTNER))
        {
            model.PackagePartnerName = packageAssociates.FirstOrDefault(x => x.TypeFlag == McvConstant.PACKAGE_ASSOCIATION_TYPEFLAG_PARTNER).Name;
            model.PackagePartnerEmail = packageAssociates.FirstOrDefault(x => x.TypeFlag == McvConstant.PACKAGE_ASSOCIATION_TYPEFLAG_PARTNER).Email;
            model.PackagePartnerContactID = packageAssociates.FirstOrDefault(x => x.TypeFlag == McvConstant.PACKAGE_ASSOCIATION_TYPEFLAG_PARTNER).ContactID;
        }

        var id = await service.Create(model);

        var Entity = await service.GetById(id);

        if (Entity == null)
            return BadRequest("Not Created");

        var filesBase64 = new List<PackageFeedbackFile>();

        if (dto.Files != null)
        {
            foreach (var file in dto.Files)
            {
                if (file.Length > 0)
                {

                    using var memoryStream = new MemoryStream();
                    await file.CopyToAsync(memoryStream);
                    var fileBytes = memoryStream.ToArray();
                    var base64String = Convert.ToBase64String(fileBytes);

                    filesBase64.Add(new PackageFeedbackFile
                    {
                        FileName = file.FileName,
                        ContentType = file.ContentType,
                        Base64Content = base64String
                    });
                }
            }
        }

        //SEND EMAIL
        await service.SendEmail(Entity,filesBase64);

        var responseDto = mapper.Map<PackageFeedbackDto>(Entity);


        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(PackageFeedback))
            .ToListAsync();

        responseDto.Type = typeMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        var statusMasters = await db.StatusMasters
  .AsNoTracking()
  .Where(x => x.Entity == nameof(PackageFeedback))
  .ToListAsync();

        responseDto.Status = statusMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        //if (!string.IsNullOrEmpty(User.Identity?.Name))
        //{
        //    var currentContact = await contactService.Get().FirstOrDefaultAsync(x => x.Username == User.Identity.Name);
        //    if (currentContact != null)
        //    {
        //        var parent = await contactService.Get().SingleOrDefaultAsync(x => x.ID == responseDto.ContactID);
        //        if (parent != null)
        //        {
        //            await activityService.LogUserActivity(currentContact, nameof(parent), parent.ID, $"{parent.Name}", $"{nameof(PackageFeedback).Replace(nameof(parent),"")} | {responseDto.Company.Title} | {responseDto.Designation} | {responseDto.ManValue}mV", "Created");
        //        }
        //    }
        //}



        return CreatedAtAction(nameof(GetByID), new { id = responseDto.ID }, responseDto);
    }

    [HttpPut("{id:int}")]
    public async Task<ActionResult<PackageFeedbackDto>> Put(int id, PackageFeedbackDto dto)
    {
        if (id != dto.ID)
        {
            return BadRequest();
        }

        await service.Update(mapper.Map<PackageFeedback>(dto));
        var responseDto = mapper.Map<PackageFeedbackDto>(await service.GetById(id));

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(PackageFeedback))
            .ToListAsync();

        responseDto.Type = typeMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        var statusMasters = await db.StatusMasters
  .AsNoTracking()
  .Where(x => x.Entity == nameof(PackageFeedback))
  .ToListAsync();

        responseDto.Status = statusMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        //if (!string.IsNullOrEmpty(User.Identity?.Name))
        //{
        //    var currentContact = await contactService.Get().FirstOrDefaultAsync(x => x.Username == User.Identity.Name);
        //    if (currentContact != null)
        //    {
        //        var parent = await contactService.Get().SingleOrDefaultAsync(x => x.ID == responseDto.ContactID);
        //        if (parent != null)
        //        {
        //            await activityService.LogUserActivity(currentContact, nameof(parent), parent.ID, $"{parent.Name}", $"{nameof(PackageFeedback).Replace(nameof(parent), "")} | {responseDto.Company.Title} | {responseDto.Designation} | {responseDto.ManValue}mV", "Updated");
        //        }
        //    }
        //}

        return Ok(responseDto);
    }

    [HttpDelete("{id:int}")]
    public async Task<IActionResult> Delete(int id)
    {
        var responseDto = mapper.Map<PackageFeedbackDto>(await service.GetById(id));
        if (responseDto == null) return BadRequest($"{nameof(PackageFeedback)} not found!");

        await service.Delete(id);

        //if (!string.IsNullOrEmpty(User.Identity?.Name))
        //{
        //    var currentContact = await contactService.Get().FirstOrDefaultAsync(x => x.Username == User.Identity.Name);
        //    if (currentContact != null)
        //    {
        //        var parent = await contactService.Get().SingleOrDefaultAsync(x => x.ID == responseDto.ContactID);
        //        if (parent != null)
        //        {
        //            await activityService.LogUserActivity(currentContact, nameof(parent), parent.ID, $"{parent.Name}", $"{nameof(PackageFeedback).Replace(nameof(parent), "")} | {responseDto.Company.Title} | {responseDto.Designation} | {responseDto.ManValue}mV", "Deleted");
        //        }
        //    }
        //}

        return NoContent();
    }


    [HttpGet("uid/{id:guid}")]
    [AllowAnonymous]
    public async Task<ActionResult<PackageFeedbackDto>> GetByGUID(Guid id)
    {
        var responseDto = mapper.Map<PackageFeedbackDto>(await service.GetById(id));
        if (responseDto == null)
        {
            return NotFound();
        }

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(PackageFeedback))
            .ToListAsync();

        responseDto.Type = typeMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        var statusMasters = await db.StatusMasters
                        .AsNoTracking()
                        .Where(x => x.Entity == nameof(PackageFeedback))
                        .ToListAsync();

        responseDto.Status = statusMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        return Ok(responseDto);
    }

    [Authorize]
    [HttpGet("SearchTagOptions")]
    public async Task<IActionResult> GetSearchTagOptions()
    {
        var results = await service.GetSearchTagOptions();
        return Ok(results);
    }

    [Authorize]
    [HttpGet("FieldOptions")]
    public async Task<IActionResult> GetFieldOptions(string field)
    {
        return Ok(await service.GetFieldOptions(field));
    }


}