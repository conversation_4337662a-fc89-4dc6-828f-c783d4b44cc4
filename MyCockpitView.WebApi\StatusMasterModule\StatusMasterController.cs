﻿
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.CoreModule;
using MyCockpitView.Utility.Common;
using MyCockpitView.WebApi.Exceptions;

namespace MyCockpitView.WebApi.StatusMasterModule;


[Authorize]
[Route("[controller]")]
[ApiController]
public class StatusMasterController : ControllerBase
{
    ILogger<StatusMasterController> _logger;
    private readonly IStatusMasterService service;
    // private readonly IMapper _mapper;
    private readonly EntitiesContext _entitiesContext;
    public StatusMasterController(
        ILogger<StatusMasterController> logger,
        EntitiesContext entitiesContext,
        IStatusMasterService StatusMasterService
        // IMapper mapper
        )
    {
        _logger = logger;
        _entitiesContext = entitiesContext;
        service = StatusMasterService;
        // _mapper = mapper;
    }


    [HttpGet]
    public async Task<IActionResult> Get(string? Filters = null, string? Search = null, string? Sort = null)
    {

        var query = service.Get(Filters != null ? DataTools.GetObjectFromJsonString<APIFilter>(Filters).Filters : null, Search, Sort);

        var results = await query
            .ToListAsync();

        return Ok(results);

    }

    [HttpGet("{id}")]
    public async Task<IActionResult> GetByID(int id)
    {

        var results = await service.Get().SingleOrDefaultAsync(x=>x.ID==id);

        if (results == null) throw new NotFoundException($"{nameof(StatusMaster)} not found!");

        return Ok(results);

    }

    [AllowAnonymous]
    [HttpGet("uid/{id:guid}")]
    public async Task<IActionResult> GetByGUID(Guid id)
    {

        var results = await service.Get().SingleOrDefaultAsync(x => x.UID == id);

        if (results == null) throw new NotFoundException($"{nameof(StatusMaster)} not found!");

        return Ok(results);

    }


    [HttpPost]
    public async Task<IActionResult> Post([FromBody] StatusMaster Dto)
    {
        var id = await service.Create(Dto);
        var results = await service.Get().SingleOrDefaultAsync(x => x.ID == id);

        if (results == null) throw new BadRequestException($"{nameof(StatusMaster)} could not be created!");

        return Ok(results);

    }


    [HttpPut("{id}")]
    //[ResponseType(typeof(int))]
    public async Task<IActionResult> Put(int id, [FromBody] StatusMaster Dto)
    {

        await service.Update(Dto);

        var results = await service.Get().SingleOrDefaultAsync(x => x.ID == id);
        if (results == null) throw new NotFoundException($"{nameof(StatusMaster)} not found!");

        return Ok(results);
    }

    [HttpDelete("{id}")]
    public async Task<IActionResult> Delete(int id)
    {
        await service.Delete(id);
        return Ok();

    }

    [HttpGet("{entity}")]
    public async Task<IActionResult> GetByEntity(string Entity)
    {

        var query = await service.Get().Where(x => x.Entity == Entity).ToListAsync();

        return Ok(query);

    }
}
