﻿using Microsoft.EntityFrameworkCore;
using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.ContactModule.Entities;
using MyCockpitView.WebApi.Services;

namespace MyCockpitView.WebApi.ContactModule.Services;

public class ContactGroupService : BaseEntityService<ContactGroup>, IContactGroupService
{
    public ContactGroupService(EntitiesContext db) : base(db) { }

    public IQueryable<ContactGroup> Get(IEnumerable<QueryFilter>? Filters = null, string? Search = null, string? Sort = null)
    {

        IQueryable<ContactGroup> _query = base.Get(Filters);

        //Apply filters
        if (Filters != null)
        {

            if (Filters.Where(x => x.Key.Equals("ContactID", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<ContactGroup>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("ContactID", StringComparison.OrdinalIgnoreCase)))
                {
                    var isNumeric = Convert.ToInt32(_item.Value);

                    predicate = predicate.Or(x => x.ContactID == isNumeric);
                }
                _query = _query.Where(predicate);
            }

        }



        return _query.OrderByDescending(x => x.Created);

    }
    public async Task<ContactGroup?> GetById(int Id)
    {

        return await db.ContactGroups.AsNoTracking()
             .Include(x => x.Contact)
              .Include(x => x.Members).ThenInclude(c => c.Contact)
               .SingleOrDefaultAsync(i => i.ID == Id);


    }

    public async Task<ContactGroup?> GetById(Guid Id)
    {

        return await db.ContactGroups.AsNoTracking()
             .Include(x => x.Contact)
              .Include(x => x.Members).ThenInclude(c => c.Contact)
               .SingleOrDefaultAsync(i => i.UID == Id);


    }

}