﻿
using MyCockpitView.WebApi.WFTaskModule.Services;

namespace MyCockpitView.WebApi.WFTaskModule.Extensions;

public static class ServiceExtensions
{

    public static IServiceCollection RegisterWFTaskServices(
     this IServiceCollection services)
    {
        services.AddScoped<IWFTaskService, WFTaskService>();
        services.AddScoped<IAssessmentService, AssessmentService>();
        services.AddScoped<ITimeEntryService, TimeEntryService>();
        services.AddScoped<ITaskRequestService, TaskRequestService>();
        services.AddScoped<IAssessmentMasterService, AssessmentMasterService>();
        services.AddScoped<IWFTaskAttachmentService, WFTaskAttachmentService>();
        return services;
    }
}
