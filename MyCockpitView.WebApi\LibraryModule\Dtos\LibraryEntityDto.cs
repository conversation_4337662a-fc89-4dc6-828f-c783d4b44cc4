﻿using AutoMapper;
using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.LeaveModule.Dtos;
using MyCockpitView.WebApi.LeaveModule.Entities;
using MyCockpitView.WebApi.LibraryModule.Entities;

namespace MyCockpitView.WebApi.LibraryModule.Dtos;

public class LibraryEntityDto : BaseEntityDto
{

    public string? Title { get; set; }
    public string? Subtitle { get; set; }

    public string? Code { get; set; }

    public int CodeFlag { get; set; }

    public virtual ICollection<LibraryEntityAttributeDto> Attributes { get; set; }
    public virtual ICollection<LibraryEntityAttachmentDto> Attachments { get; set; }
    public string? Category { get; set; }
    public virtual ICollection<LibraryEntityVendorDto> Vendors { get; set; }

}

public class LibraryEntityDtoMapperProfile : Profile
{
    public LibraryEntityDtoMapperProfile()
    {
        CreateMap<LibraryEntity, LibraryEntityDto>()
                     .ForMember(dest => dest.Attributes, opt => opt.MapFrom(src => src.Attributes))
                    .ForMember(dest => dest.Attachments, opt => opt.MapFrom(src => src.Attachments))
                     .ForMember(dest => dest.Vendors, opt => opt.MapFrom(src => src.Vendors))
                   .ReverseMap()
                    .ForMember(dest => dest.Vendors, opt => opt.Ignore())
                   //.ForMember(dest => dest.Attributes, opt => opt.Ignore()) 
                   .ForMember(dest => dest.Attachments, opt => opt.Ignore());

    }
}