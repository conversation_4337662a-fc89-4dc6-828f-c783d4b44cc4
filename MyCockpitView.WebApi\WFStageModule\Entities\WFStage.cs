﻿using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.CoreModule;
using System.ComponentModel.DataAnnotations;

namespace MyCockpitView.WebApi.WFStageModule.Entities;

public class WFStage : BaseEntity
{
    [StringLength(255)]
    public string? Code { get; set; }
    [StringLength(255)]
    public string? TaskTitle { get; set; }
    [StringLength(255)]
    public string? Entity { get; set; }
    [StringLength(255)]
    public string? EntityTypeFlag { get; set; }

    public bool IsSystem { get; set; }
    public bool IsStart { get; set; }

    [Precision(18, 2)] public decimal DueDays { get; set; }

    public bool IsAssignByRole { get; set; }

    public bool ShowAssessment { get; set; }
    [StringLength(255)]
    public string? AssessmentForStage { get; set; }
    [StringLength(255)]
    public string? AssignByProperty { get; set; }
    [StringLength(255)]
    public string? AssignByEntityProperty { get; set; }
    public bool ShowComment { get; set; }
    public bool ShowFollowUpDate { get; set; }
    public bool ShowAttachment { get; set; }
    [StringLength(255)]
    public string? ActionType { get; set; }

    public virtual ICollection<WFStageAction> Actions { get; set; } = new List<WFStageAction>();

    public bool IsCommentRequired { get; set; }

    public int InitialRevison { get; set; }
    public bool IsAssessmentRequired { get; set; }
    public bool IsPreAssignedTimeTask { get; set; }

}

public class WFStageConfiguration : BaseEntityConfiguration<WFStage>, IEntityTypeConfiguration<WFStage>
{
    public void Configure(EntityTypeBuilder<WFStage> builder)
    {
        base.Configure(builder);
        builder.HasIndex(e => e.Entity);
        builder.HasIndex(e => e.Code);
        builder.HasIndex(e => e.ShowAssessment);
        builder.HasIndex(e => e.IsAssignByRole);
        builder.HasIndex(e => e.IsAssessmentRequired);
        builder.HasIndex(e => e.ActionType);
        builder.HasIndex(e => e.AssessmentForStage);
        builder.HasIndex(e => e.EntityTypeFlag);
        builder.HasIndex(e => e.InitialRevison);
        builder.HasIndex(e => e.IsStart);
        builder.HasIndex(e => e.IsSystem);
        builder.HasIndex(e => e.TaskTitle);
    }
}