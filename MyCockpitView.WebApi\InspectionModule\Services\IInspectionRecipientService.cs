﻿
using MyCockpitView.WebApi.InspectionModule.Entities;
using MyCockpitView.WebApi.Services;

namespace MyCockpitView.WebApi.InspectionModule.Services;

public interface IInspectionRecipientService : IBaseEntityService<InspectionRecipient>
{
    Task LogRecipientTime(int ContactID, int InspectionID, DateTime Start, DateTime End, string StageCode);
    Task ScaffoldPendingRecipients(int InspectionID);
}