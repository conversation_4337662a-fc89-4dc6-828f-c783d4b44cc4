﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using MyCockpitView.CoreModule;
using System.ComponentModel.DataAnnotations;

namespace MyCockpitView.WebApi.AppSettingMasterModule;

public class AppSettingMaster : BaseEntity
{
    [StringLength(255)]
    public string? PresetKey { get; set; }

    public string? PresetValue { get; set; }
}

public class AppSettingMasterConfiguration : BaseEntityConfiguration<AppSettingMaster>, IEntityTypeConfiguration<AppSettingMaster>
{
    public void Configure(EntityTypeBuilder<AppSettingMaster> builder)
    {
        base.Configure(builder);
    }
}