﻿
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.CoreModule;

namespace MyCockpitView.WebApi.PackageModule.Entities;

public class PackageDesignIntent : BaseEntity
{
    public int PackageID { get; set; }
    public virtual Package? Package { get; set; }
    public int DesignScriptEntityID { get; set; }
    public string? Code { get; set; }
    public string? Title { get; set; }
    public int? ParentID { get; set; }
    public string? TypeValue { get; set; }
}
public class PackageDesignIntentConfiguration : BaseEntityConfiguration<PackageDesignIntent>, IEntityTypeConfiguration<PackageDesignIntent>
{
    public void Configure(EntityTypeBuilder<PackageDesignIntent> builder)
    {
        base.Configure(builder);
        builder.HasIndex(x => x.DesignScriptEntityID);
        builder.HasIndex(x => x.Code);
        builder.HasIndex(x => x.Title);
        builder.HasIndex(x => x.ParentID);
        builder.HasIndex(x => x.TypeValue);
    }
}
