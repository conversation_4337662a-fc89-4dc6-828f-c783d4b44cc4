﻿using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using MyCockpitView.CoreModule;

namespace MyCockpitView.WebApi.HabitModule.Entities;

public class HabitResponse : BaseEntity
{
    public string?  Value { get; set; }


    [Required]
    [StringLength(255)]
    public string?  Key { get; set; }


    [Required]
    [StringLength(255)]
    public string?  Label { get; set; }


    [StringLength(255)]
    public string?  PlaceHolder { get; set; }
    public string?  Hint { get; set; }


    public bool Required { get; set; }
    public bool Email { get; set; }
    public int? MinLength { get; set; }
    public int? Min { get; set; }
    public int? MaxLength { get; set; }
    public int? Max { get; set; }
    public int Order { get; set; }


    [Required]
    [StringLength(255)]
    public string?  ControlType { get; set; }

    [Column("Options")]
    public string?  _options { get; set; }

    [NotMapped]
    public string[] Options
    {
        get { return !string.IsNullOrEmpty(_options) ? _options.Split(','): new string[0]; }
        set
        {
            _options = string.Join(",", value);
        }
    }


    [Required]
    public int HabitID { get; set; }

    public virtual Habit? Habit { get; set; }
}

public class HabitResponseConfiguration : BaseEntityConfiguration<HabitResponse>, IEntityTypeConfiguration<HabitResponse>
{
    public void Configure(EntityTypeBuilder<HabitResponse> builder)
    {
      base.Configure(builder);
    }
}
