﻿

using System.Data;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.CoreModule;
using MyCockpitView.Utility.Common;
using MyCockpitView.Utility.RDLCClient;
using MyCockpitView.WebApi.Exceptions;
using MyCockpitView.WebApi.ProcessLibraryModule.Entities;
using MyCockpitView.WebApi.Services;


namespace MyCockpitView.WebApi.ProcessLibraryModule.Services;

public class ProcessLibraryEntityService : BaseEntityService<ProcessLibraryEntity>, IProcessLibraryEntityService
{
    public ProcessLibraryEntityService(EntitiesContext db) : base(db) { }

    public IQueryable<ProcessLibraryEntity> Get(IEnumerable<QueryFilter>? Filters = null, string? Search = null, string? Sort = null)
    {

        IQueryable<ProcessLibraryEntity> _query = base.Get(Filters);

        //Apply filters
        if (Filters != null)
        {
            if (Filters.Where(x => x.Key.Equals("deleted", StringComparison.OrdinalIgnoreCase)).Any())
            {

                _query = db.ProcessLibraryEntities
                        .AsNoTracking();
            }

            if (Filters.Where(x => x.Key.Equals("statusFlag", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<ProcessLibraryEntity>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("statusFlag", StringComparison.OrdinalIgnoreCase)))
                {
                    var isNumeric = Convert.ToInt32(_item.Value);

                    predicate = predicate.Or(x => x.StatusFlag == isNumeric);
                }
                _query = _query.Where(predicate);
            }

            if (Filters.Where(x => x.Key.Equals("typeFlag", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<ProcessLibraryEntity>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("typeFlag", StringComparison.OrdinalIgnoreCase)))
                {
                    var isNumeric = Convert.ToInt32(_item.Value);

                    predicate = predicate.Or(x => x.TypeFlag == isNumeric);
                }
                _query = _query.Where(predicate);
            }

            if (Filters.Where(x => x.Key.Equals("searchtag", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<ProcessLibraryEntity>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("searchtag", StringComparison.OrdinalIgnoreCase)))
                {
                    predicate = predicate.Or(x => x._searchTags.Contains(_item.Value));
                }
                _query = _query.Where(predicate);
            }

            if (Filters.Where(x => x.Key.ToLower() == "parentid").Any())
            {
                var predicate = PredicateBuilder.False<ProcessLibraryEntity>();
                foreach (var _item in Filters.Where(x => x.Key.ToLower() == "parentid"))
                {
                    var isNumeric = Convert.ToInt32(_item.Value);

                    predicate = predicate.Or(x => x.ParentID != null && x.ParentID == isNumeric);
                }
                _query = _query.Where(predicate);
            }

        }

        if (Search != null && Search != String.Empty)
        {
            var _keywords = Search.Split(' ');

            foreach (var _key in _keywords)
            {
                _query = _query
                     .Where(x => x.Title.ToLower().Contains(_key.ToLower())
                     || x.Code.ToLower().Contains(_key.ToLower())
                     || x._searchTags.ToLower().Contains(_key.ToLower())
                     || x._attributes.ToLower().Contains(_key.ToLower()));
            }
        }

        if (Sort != null && Sort != String.Empty)
        {
            switch (Sort.ToLower())
            {
                case "createddate":
                    return _query
                            .OrderBy(x => x.Created);

                case "modifieddate":
                    return _query
                            .OrderBy(x => x.Modified);

                case "createddate desc":
                    return _query
                            .OrderByDescending(x => x.Created);

                case "modifieddate desc":
                    return _query
                            .OrderByDescending(x => x.Modified);


            }
        }

        return _query.OrderBy(x => x.OrderFlag);

    }

    public async Task<ProcessLibraryEntity?> GetById(int Id)
    {

        return await db.ProcessLibraryEntities.AsNoTracking()
                      .Include(x => x.Attachments)
               .SingleOrDefaultAsync(i => i.ID == Id);


    }


    public async Task<int> Create(ProcessLibraryEntity Entity)
    {

        if (Entity.Title == null || Entity.Title == string.Empty)
            throw new EntityServiceException("Title is required. Please enter proper title!");

        return await base.Create(Entity);

    }

    public async Task<ReportDefinition> GetProcessListPDF(Guid ParentID, string RenderType = "PDF")
    {

        var parent = await db.ProcessLibraryEntities.AsNoTracking()
            
             .SingleOrDefaultAsync(x => x.UID == ParentID);
        if (parent == null) throw new EntityServiceException("Entity not found!");

        var parents = parent.ParentID != null ? await FetchRecursiveParentsAsync(db, parent.ParentID.Value) : new HashSet<ProcessLibraryEntity>();

        var path = parents.Any() ? string.Join(" > ", parents.OrderBy(x => x.TypeFlag).Select(x => x.Title)) : " ";
        var sharedService = new SharedService(db); ;
        var dateTimeFormat = await sharedService.GetPresetValue(McvConstant.DATE_FORMAT_WITH_TIME);

        var _reportProperties = new List<ReportProperties>
            {
                new ReportProperties() { PropertyName = "Path", PropertyValue = path },
                new ReportProperties() { PropertyName = "Title", PropertyValue = parent.Title },
                 new ReportProperties() { PropertyName = "CreatedBy", PropertyValue = parent.CreatedBy },
                  new ReportProperties() { PropertyName = "CreatedOn", PropertyValue = ClockTools.GetIST(parent.Created).ToString(dateTimeFormat) },
                   new ReportProperties() { PropertyName = "ModifiedBy", PropertyValue = parent.ModifiedBy },
                    new ReportProperties() { PropertyName = "ModifiedOn", PropertyValue = ClockTools.GetIST(parent.Modified).ToString(dateTimeFormat) }
            };

        var items = new List<ProcessLibraryEntity>
        {
            parent,
        };
        items = items.Concat(await FetchRecursiveChildrenAsync(db, parent.ID)).ToList();

        var _reportContainerUrl = await sharedService.GetPresetValue(McvConstant.RDLC_REPORT_CONTAINER_URL);

        var DEVMODE = Convert.ToBoolean((await sharedService.GetPresetValue(McvConstant.DEVMODE)));

        var _reportPath = $"ProcessList.rdlc";

        var _reportDef = new ReportDefinition()
        {
            ReportName = "ProcessList",
            ReportPath = DEVMODE ? $"{_reportContainerUrl}DEV/{_reportPath}" : $"{_reportContainerUrl}{_reportPath}",
            ReportDataSet = DataTools.ToDataTable(items.Select(x => new
            {
                Title = x.Title,
                Code = x.Code,
                OrderFlag = x.OrderFlag,
                ParentID = x.ParentID,
                TypeFlag = x.TypeFlag,
                TypeValue = "",
                x.ID,
                x.Description,
                x.Created,
                x.Modified,
                x.ModifiedBy,
                x.CreatedBy
            })),
            ReportProperties = _reportProperties,
            Filename = $"Process-{path.Replace(">", "-")}-{ClockTools.GetISTNow().ToString("yyMMddHHmm")}",
            RenderType = RenderType
        };

        var reportServiceApi = await sharedService.GetPresetValue(McvConstant.RDLC_PROCESSOR_API);
        return await ReportClient.GenerateReport(_reportDef, reportServiceApi);

    }

    // Recursive method to fetch all items based on parent ID
    public async Task<IEnumerable<ProcessLibraryEntity>> FetchRecursiveChildrenAsync(EntitiesContext context, int parentId)
    {
        var children = await context.ProcessLibraryEntities.AsNoTracking()
              
            .Where(i => i.ParentID == parentId).ToListAsync(); // Fetch the children items with the given parent ID asynchronously

        var allChildren = new List<ProcessLibraryEntity>();

        // Create a separate list to store items that need to be added to the collection
        var itemsToAdd = new List<ProcessLibraryEntity>();

        // Recursively fetch children items for each child
        foreach (var child in children)
        {
            itemsToAdd.Add(child); // Add the current child item to the list of items to add

            // Fetch the children items for the current child item asynchronously
            var grandchildren = await FetchRecursiveChildrenAsync(context, child.ID);

            itemsToAdd.AddRange(grandchildren); // Add the grandchildren items to the list of items to add
        }

        allChildren.AddRange(itemsToAdd); // Add all the items to the collection after the loop has finished

        return allChildren.OrderBy(x => x.OrderFlag);
    }

    // Recursive async method to fetch all parent items based on item ID
    public async Task<IEnumerable<ProcessLibraryEntity>> FetchRecursiveParentsAsync(EntitiesContext context, int itemId)
    {
        var item = await context.ProcessLibraryEntities.FindAsync(itemId); // Fetch the item with the given ID asynchronously

        if (item == null)
        {
            return new List<ProcessLibraryEntity>(); // Return an empty list if item not found
        }

        var parents = new List<ProcessLibraryEntity>();
        parents.Add(item); // Add the current item to the list of parents

        // Recursively fetch parent items for each level
        if (item.ParentID.HasValue)
        {
            parents.AddRange(await FetchRecursiveParentsAsync(context, item.ParentID.Value)); // Fetch the parent items for the current item's parent asynchronously
        }

        return parents.OrderBy(x => x.TypeFlag);
    }
}



public class ProcessReportData
{
    public ProcessReportData(
        string Title,
        string Code,
        int ParentID,
        string TypeValue,
        string Description)
    {
        this.Title = Title;
        this.Code = Code;
        this.ParentID = ParentID;
        this.TypeValue = TypeValue;
        this.Description = Description;
    }

    public string Title { get; }
    public string Code { get; }
    public int ParentID { get; }
    public string TypeValue { get; }
    public string Description { get; }
}