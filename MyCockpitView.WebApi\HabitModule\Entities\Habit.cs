﻿using Microsoft.EntityFrameworkCore;
using MyCockpitView.WebApi.ContactModule.Entities;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using MyCockpitView.CoreModule;

namespace MyCockpitView.WebApi.HabitModule.Entities;

public class Habit : BaseEntity
{
    [Required]
    [StringLength(255)]
    public string?  Title { get; set; }


    [StringLength(255)]
    public string?  Subtitle { get; set; }

    [Required]
    [Column(TypeName = "datetime2")]
    public DateTime DueDate { get; set; }


    [Required]
    [Column(TypeName = "datetime2")]
    public DateTime StartDate { get; set; }

    [Precision(14, 2)]
    public decimal RepeatInterval { get; set; } = 0;

    [Precision(14, 2)]
    public decimal TaskInterval { get; set; } = 0;


    public int RepeatCount { get; set; }
    public string?  Comment { get; set; }

    [Required]
    public int AssigneeContactID { get; set; }

    [Required]
    public int AssignerContactID { get; set; }

    public virtual Contact? AssigneeContact { get; set; }

    public virtual Contact? AssignerContact { get; set; }

    public virtual ICollection<HabitAttachment> Attachments { get; set; }=new List<HabitAttachment>();

    public virtual ICollection<HabitResponse> Responses { get; set; }= new HashSet<HabitResponse>();
    [Precision(14, 2)]
    public decimal MHrAssigned { get; set; } = 0;
    [Precision(14, 2)]
    public decimal MHrConsumed { get; set; } = 0;


    [StringLength(255)]
    public string?  Entity { get; set; }


    public int? EntityID { get; set; }

    [StringLength(255)]
    public string?  EntityTitle { get; set; }
}

public class HabitConfiguration : BaseEntityConfiguration<Habit>, IEntityTypeConfiguration<Habit>
{
    public void Configure(EntityTypeBuilder<Habit> builder)
    {
       base.Configure(builder);
        // Relationships
        builder.HasOne(t => t.AssigneeContact)
            .WithMany()
            .HasForeignKey(t => t.AssigneeContactID)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasOne(t => t.AssignerContact)
            .WithMany()
            .HasForeignKey(t => t.AssignerContactID)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasIndex(x => x.Title);
        builder.HasIndex(x => x.DueDate);
        builder.HasIndex(x => x.StartDate);
        builder.HasIndex(x => x.RepeatInterval);
        builder.HasIndex(x => x.TaskInterval);
        builder.HasIndex(x => x.RepeatCount);
        builder.HasIndex(x => x.EntityID);

    }
}
