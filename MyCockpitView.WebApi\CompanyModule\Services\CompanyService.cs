﻿using MyCockpitView.WebApi.Services;
using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.CompanyModule.Entities;

namespace MyCockpitView.WebApi.CompanyModule.Services;

public class CompanyService : BaseEntityService<Company>, ICompanyService
{

    public CompanyService(EntitiesContext db) : base(db)
    {
    }

    public IQueryable<Company> Get(IEnumerable<QueryFilter>? Filters = null, string? Search = null, string? Sort = null)
    {

        IQueryable<Company> _query = base.Get(Filters);


        if (Search != null && Search != string.Empty)
        {
            var _keywords = Search.ToLower().Split(' ');
            foreach (var _key in _keywords)
            {

                _query = _query.Where(x => x.Title.ToLower().Contains(_key.ToLower()));
            }
        }

        return _query.OrderBy(x => x.Title);

    }

}