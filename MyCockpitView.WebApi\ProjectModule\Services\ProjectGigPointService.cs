﻿


using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.ProjectModule.Entities;
using MyCockpitView.WebApi.Services;

namespace MyCockpitView.WebApi.ProjectModule.Services;

public class ProjectGigPointService : BaseEntityService<ProjectGigPoint>, IProjectGigPointService
{
    public ProjectGigPointService(EntitiesContext db) : base(db) { }
    public IQueryable<ProjectGigPoint> Get(IEnumerable<QueryFilter>? Filters = null, string? Search = null, string? Sort = null)
    {

        var _query = base.Get(Filters, Search);

        //Apply filters
        if (Filters != null)
        {

            if (Filters.Where(x => x.Key.Equals("ProjectID", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<ProjectGigPoint>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("ProjectID", StringComparison.OrdinalIgnoreCase)))
                {
                    var isNumeric = Convert.ToInt32(_item.Value);

                    predicate = predicate.Or(x => x.ProjectID == isNumeric);
                }
                _query = _query.Where(predicate);
            }

        }

        if (Sort != null && Sort != String.Empty)
        {
            switch (Sort.ToLower())
            {
                case "createddate":
                    return _query
                            .OrderByDescending(x => x.Created);

                case "modifieddate":
                    return _query
                            .OrderByDescending(x => x.Modified);

            }
        }

        return _query
          .OrderByDescending(x => x.RecordDate);

    }


}