﻿using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.CoreModule;


namespace MyCockpitView.WebApi.TodoModule.Entities;

public class TodoAttachment : BaseBlobEntity
{
    
    public int TodoID { get; set; }

    public virtual Todo? Todo { get; set; }
}

public class TodoAttachmentConfiguration : BaseBlobEntityConfiguration<TodoAttachment>, IEntityTypeConfiguration<TodoAttachment>
{
    public void Configure(EntityTypeBuilder<TodoAttachment> builder)
    {
        base.Configure(builder);

    }
}
