﻿using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.ExpenseModule.Entities;
using MyCockpitView.WebApi.Services;

namespace MyCockpitView.WebApi.ExpenseModule.Services;

public interface IExpenseService : IBaseEntityService<Expense>
{
    Task<byte[]> GetAnalysisExcel(IEnumerable<QueryFilter>? Filters = null, string? Search = null, string? Sort = null);
    Task<IEnumerable<int>> GetAssigneeContactIDs(int entityID, string stageCode, string propertyName);
    Task<dynamic> GetTaskByStage(string Entity, int EntityID, string StageCode, string StageTitle, decimal StageDuration, DateTime? FollowUpDate = null);
    Task RecalculateBalance(int CompanyID, int TypeFlag, DateTime? LastExpenseDate = null);
    Task TaskAction(int EntityID, string StageCode, int WFTaskID, string? taskComment = null);
}