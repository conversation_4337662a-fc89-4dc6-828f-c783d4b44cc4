﻿
using MyCockpitView.WebApi.DesignScriptModule.Services;

namespace MyCockpitView.WebApi.DesignScriptModule.Extensions;

public static class ServiceExtensions
{

    public static IServiceCollection RegisterDesignScriptServices(
     this IServiceCollection services)
    {
        services.AddScoped<IDesignScriptDataCardService, DesignScriptDataCardService>();
        services.AddScoped<IDesignScriptDataCardAttributeService, DesignScriptDataCardAttributeService>();
        services.AddScoped<IDesignScriptDataCardAttachmentService, DesignScriptDataCardAttachmentService>();

        services.AddScoped<IDesignScriptEntityItemMapService, DesignScriptEntityItemMapService>();
        services.AddScoped<IDesignScriptEntityService,DesignScriptEntityService>();

        services.AddScoped<IDesignScriptItemMasterService, DesignScriptItemMasterService>();
        services.AddScoped<IDesignScriptItemService, DesignScriptItemService>();
        services.AddScoped<IDesignScriptMeasurementGroupService, DesignScriptMeasurementGroupService>();
        services.AddScoped<IDesignScriptMeasurementService, DesignScriptMeasurementService>();
        return services;
    }
}
