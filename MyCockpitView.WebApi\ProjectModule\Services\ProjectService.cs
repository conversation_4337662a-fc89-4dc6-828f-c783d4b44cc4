﻿using AutoMapper;
using System.Data;
using System.Globalization;
using MyCockpitView.WebApi.Exceptions;
using System.Text;
using MyCockpitView.WebApi.DesignScriptModule.Services;
using MyCockpitView.WebApi.InspectionModule.Entities;
using MyCockpitView.WebApi.MeetingModule.Services;
using MyCockpitView.WebApi.MeetingModule.Entities;
using MyCockpitView.WebApi.PackageModule.Entities;
using MyCockpitView.WebApi.Services;
using MyCockpitView.WebApi.ProjectModule.Entities;
using MyCockpitView.WebApi.TodoModule.Entities;
using MyCockpitView.WebApi.WFTaskModule.Services;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.CoreModule;
using MyCockpitView.Utility.Common;
using MyCockpitView.Utility.Excel;
using MyCockpitView.Utility.RDLCClient;
using MyCockpitView.WebApi.ContactModule.Services;
using System.Linq;

namespace MyCockpitView.WebApi.ProjectModule.Services;

public class ProjectService : BaseEntityService<Project>, IProjectService
{
    public ProjectService(EntitiesContext db) : base(db) { }



    public IQueryable<Project> Get(IEnumerable<QueryFilter>? Filters = null, string? Search = null, string? Sort = null)
    {

        IQueryable<Project> _query = base.Get(Filters);

        //Apply filters
        if (Filters != null)
        {


            if (Filters.Where(x => x.Key.Equals("ProjectID", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<Project>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("ProjectID", StringComparison.OrdinalIgnoreCase)))
                {
                    var isNumeric = Convert.ToInt32(_item.Value);

                    predicate = predicate.Or(x => x.ID == isNumeric);
                }
                _query = _query.Where(predicate);
            }

            if (Filters.Where(x => x.Key.Equals("ProjectPartnerContactID", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<Project>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("ProjectPartnerContactID", StringComparison.OrdinalIgnoreCase)))
                {
                    var isNumeric = Convert.ToInt32(_item.Value);

                    predicate = predicate.Or(x => x.Associations.Where(a => a.TypeFlag == 0 && a.ContactID == isNumeric).Any());
                }
                _query = _query.Include(x => x.Associations).Where(predicate);
            }
            if (Filters.Where(x => x.Key.Equals("ProjectAssociateContactID", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<Project>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("ProjectAssociateContactID", StringComparison.OrdinalIgnoreCase)))
                {
                    var isNumeric = Convert.ToInt32(_item.Value);

                    predicate = predicate.Or(x => x.Associations.Where(a => a.TypeFlag == McvConstant.PROJECT_ASSOCIATION_TYPEFLAG_ASSOCIATE && a.ContactID == isNumeric).Any());
                }
                _query = _query.Include(x => x.Associations).Where(predicate);
            }
            if (Filters.Where(x => x.Key.Equals("ProjectPartnerOrAssociateContactID", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<Project>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("ProjectPartnerOrAssociateContactID", StringComparison.OrdinalIgnoreCase)))
                {
                    var isNumeric = Convert.ToInt32(_item.Value);

                    predicate = predicate.Or(x => x.Associations.Where(a => (a.TypeFlag == McvConstant.PROJECT_ASSOCIATION_TYPEFLAG_ASSOCIATE || a.TypeFlag == McvConstant.PROJECT_ASSOCIATION_TYPEFLAG_PARTNER) && a.ContactID == isNumeric).Any());
                }
                _query = _query.Include(x => x.Associations).Where(predicate);
            }

            if (Filters.Where(x => x.Key.Equals("DesignScriptManagerContactID", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<Project>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("DesignScriptManagerContactID", StringComparison.OrdinalIgnoreCase)))
                {
                    var isNumeric = Convert.ToInt32(_item.Value);

                    predicate = predicate.Or(x => x.Associations.Where(a => (a.TypeFlag == McvConstant.PROJECT_ASSOCIATION_TYPEFLAG_DESIGN_SCRIPT_MANAGER || a.TypeFlag == McvConstant.PROJECT_ASSOCIATION_TYPEFLAG_ASSOCIATE || a.TypeFlag == McvConstant.PROJECT_ASSOCIATION_TYPEFLAG_PARTNER) && a.ContactID == isNumeric).Any());
                }
                _query = _query.Include(x => x.Associations).Where(predicate);
            }

            if (Filters.Where(x => x.Key.Equals("companyID", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<Project>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("companyID", StringComparison.OrdinalIgnoreCase)))
                {
                    var isNumeric = Convert.ToInt32(_item.Value);

                    predicate = predicate.Or(x => x.CompanyID == isNumeric);
                }
                _query = _query.Where(predicate);
            }

            if (Filters.Where(x => x.Key.Equals("CreatedRangeStart", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var _item = Filters.First(x => x.Key.Equals("CreatedRangeStart", StringComparison.OrdinalIgnoreCase));

                DateTime result = Convert.ToDateTime(_item.Value);
                _query = _query.Where(x => x.Created >= result);
            }

            if (Filters.Where(x => x.Key.Equals("CreatedRangeEnd", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var _item = Filters.First(x => x.Key.Equals("CreatedRangeEnd", StringComparison.OrdinalIgnoreCase));

                DateTime result = Convert.ToDateTime(_item.Value);
                var end = result.AddDays(1);
                _query = _query.Where(x => x.Created < end);

            }


            if (Filters.Where(x => x.Key.Equals("ModifiedRangeStart", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var _item = Filters.First(x => x.Key.Equals("ModifiedRangeStart", StringComparison.OrdinalIgnoreCase));

                DateTime result = Convert.ToDateTime(_item.Value);
                _query = _query.Where(x => x.Modified >= result);
            }

            if (Filters.Where(x => x.Key.Equals("ModifiedRangeEnd", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var _item = Filters.First(x => x.Key.Equals("ModifiedRangeEnd", StringComparison.OrdinalIgnoreCase));

                DateTime result = Convert.ToDateTime(_item.Value);
                var end = result.AddDays(1);
                _query = _query.Where(x => x.Modified < end);

            }

            if (Filters.Where(x => x.Key.Equals("ConvertedRangeStart", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var _item = Filters.First(x => x.Key.Equals("ConvertedRangeStart", StringComparison.OrdinalIgnoreCase));

                DateTime result = Convert.ToDateTime(_item.Value);
                _query = _query.Where(x => x.InquiryConvertionDate != null && x.InquiryConvertionDate.Value >= result);
            }

            if (Filters.Where(x => x.Key.Equals("ConvertedRangeEnd", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var _item = Filters.First(x => x.Key.Equals("ConvertedRangeEnd", StringComparison.OrdinalIgnoreCase));

                DateTime result = Convert.ToDateTime(_item.Value);
                var end = result.AddDays(1);
                _query = _query.Where(x => x.InquiryConvertionDate != null && x.InquiryConvertionDate.Value < end);

            }
        }

        if (Search != null && Search != String.Empty)
        {
            _query = _query
            .Include(x => x.ClientContact)
          .Where(x => x.Title.ToLower().Contains(Search.ToLower())
                    || x.BillingTitle.ToLower().Contains(Search.ToLower())
                    || x.Code.ToLower().Contains(Search.ToLower())
                    || x.Location.ToLower().Contains(Search.ToLower())
                    || x.City.ToLower().Contains(Search.ToLower())
                    || x.State.ToLower().Contains(Search.ToLower())
                    || (x.ClientContact != null && x.ClientContact.FullName.ToLower().Contains(Search.ToLower()))
                    || x._searchTags.ToLower().Contains(Search.ToLower())
                    );
        }

        if (Sort != null && Sort != String.Empty)
        {
            var _orderedQuery = _query.OrderBy(l => 0);
            var keywords = Sort.Replace("asc", "").Split(',');

            foreach (var key in keywords)
            {
                if (key.Trim().Equals("created", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenBy(x => x.Created);

                else if (key.Trim().Equals("created desc", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenByDescending(x => x.Created);

                else if (key.Trim().Equals("modified", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenBy(x => x.Modified);

                else if (key.Trim().Equals("modified desc", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenByDescending(x => x.Modified);

                else if (key.Trim().Equals("ExpectedCompletionDate", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenBy(x => x.ExpectedCompletionDate);

                else if (key.Trim().Equals("ExpectedCompletionDate desc", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenByDescending(x => x.ExpectedCompletionDate);

                else if (key.Trim().Equals("ExpectedCompletionDate", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenBy(x => x.ExpectedCompletionDate);

                else if (key.Trim().Equals("ContractCompletionDate desc", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenByDescending(x => x.ContractCompletionDate);

                else if (key.Trim().Equals("ContractCompletionDate", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenBy(x => x.ContractCompletionDate);

                else if (key.Trim().Equals("InquiryConvertionDate desc", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenByDescending(x => x.InquiryConvertionDate);

                else if (key.Trim().Equals("InquiryConvertionDate", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenBy(x => x.InquiryConvertionDate);
            }

            return _orderedQuery;
        }

        return _query
                      .OrderByDescending(x => x.Modified);

    }

    public async Task<Project?> GetById(int Id)
    {

        return await db.Projects.AsNoTracking()
        .Include(x => x.ClientContact)
        .Include(x => x.ReferredByContact)
        .Include(x => x.Associations).ThenInclude(c => c.Contact)
        .Include(x => x.Consultants).ThenInclude(c => c.Contact)
        .Include(x => x.Attachments)
        .Include(x => x.CompanyAccount)
        .Include(x => x.Areas)
        .SingleOrDefaultAsync(x => x.ID == Id);

    }

    public async Task<Project?> GetById(Guid Id)
    {

        return await db.Projects.AsNoTracking()
        .Include(x => x.ClientContact)
        .Include(x => x.ReferredByContact)
        .Include(x => x.Associations).ThenInclude(c => c.Contact)
        .Include(x => x.Consultants).ThenInclude(c => c.Contact)
        .Include(x => x.Attachments)
        .Include(x => x.CompanyAccount)
        .Include(x => x.Areas)
        .SingleOrDefaultAsync(x => x.UID == Id);

    }


    public async Task<bool> Exist(string title)
    {

        return await Get().AnyAsync(x => x.Title==title )
            || await Get().AnyAsync(x => x.BillingTitle==title
            || x.Title==title);

    }

    public async Task ActivateProjectsByAgenda()
    {
        var sharedService = new SharedService(db); ;
        var _maxReminderCountForDiscard = Convert.ToInt32(await sharedService.GetPresetValue(McvConstant.AGENDA_MAX_REMINDER_COUNT_FOR_LOCK));

        var _projects = await db.Projects.AsNoTracking()
            //.Include(x => x.Inquiry)
            .Where(x => x.StatusFlag == 6) //LOCKED
                                           //.Where(x => x.TypeFlag == 0)
                             .ToListAsync();
        //var _marginDate = DateTime.UtcNow.AddMonths(-3);
        foreach (var _project in _projects)
        {
            var _pendingAgenda = await db.MeetingAgendas.AsNoTracking()
                .Where(x => !x.IsVersion)
                .Where(x => x.ProjectID != null && x.ProjectID == _project.ID
                && !x.IsForwarded && x.StatusFlag == 0)
                .ToListAsync();

            Console.WriteLine("PRoject Agendas :" + (_pendingAgenda.Any() ? _pendingAgenda.Max(x => x.ReminderCount) : 0));

            var _count = 0;
            if (_pendingAgenda.Any() && _pendingAgenda.All(x => x.ReminderCount < _maxReminderCountForDiscard))
            {
                Console.WriteLine("PRoject Activated :" + _project.Title);
                _project.StatusFlag = 2;
                //if (_project.Inquiry != null)
                //{
                //    _project.Inquiry.StatusFlag = 2;
                //    _db.Entry(_project.Inquiry).State = EntityState.Modified;
                //}
                db.Entry(_project).State = EntityState.Modified;
                await db.SaveChangesAsync();
                _count++;

            }

            Console.WriteLine("PRojects Activated :" + _count);
        }

    }

    public async Task LockProjects()
    {
        var sharedService = new SharedService(db); ;
        var _maxReminderCountForLock = Convert.ToInt32(await sharedService.GetPresetValue(McvConstant.AGENDA_MAX_REMINDER_COUNT_FOR_LOCK));

        var _lockedProjects = new List<dynamic>();

        var _projects = await db.Projects
        .Include(x => x.Associations).ThenInclude(c => c.Contact)
            .Where(x => x.StatusFlag == McvConstant.PROJECT_STATUSFLAG_INPROGRESS)
                             .ToListAsync();
        //var _marginDate = DateTime.UtcNow.AddMonths(-3);
        foreach (var _project in _projects)
        {
            var _pendingAgenda = await db.MeetingAgendas.AsNoTracking()
                .Where(x => !x.IsVersion)
                .Where(x => x.ProjectID != null
                && x.ProjectID == _project.ID
                && !x.IsForwarded
                && x.StatusFlag == McvConstant.MEETING_AGENDA_STATUSFLAG_PENDING
                && x.ReminderCount > _maxReminderCountForLock)
                .FirstOrDefaultAsync();
            if (_pendingAgenda != null)
            {
                _project.StatusFlag = McvConstant.PROJECT_STATUSFLAG_LOCKED;//LOCK
                _project.Description = DateTime.UtcNow.ToString("dd MMM yyyy") + " | Locked by agenda overdue.\n" + _project.Description;
                await ScaffoldAgendaTasks(_project.ID, _project.StatusFlag);
                _lockedProjects.Add(
                    new
                    {
                        Project = $"{_project.Code}-{_project.Title}",
                        Reason = $"Locked by agenda overdue (Duedate:{ClockTools.GetIST(_pendingAgenda.DueDate.Value).ToString("dd MMM yyyy")} | Reminders: {_pendingAgenda.ReminderCount})",
                        Partner =
                        _project.Associations.Where(x => x.TypeFlag == McvConstant.PROJECT_ASSOCIATION_TYPEFLAG_PARTNER).Any() ?
                        _project.Associations.Where(x => x.TypeFlag == McvConstant.PROJECT_ASSOCIATION_TYPEFLAG_PARTNER).FirstOrDefault().Contact.Name : null,
                        Associate =
                        _project.Associations.Where(x => x.TypeFlag == McvConstant.PROJECT_ASSOCIATION_TYPEFLAG_ASSOCIATE).Any() ?
                        _project.Associations.Where(x => x.TypeFlag == McvConstant.PROJECT_ASSOCIATION_TYPEFLAG_ASSOCIATE).FirstOrDefault().Contact.Name : null,
                    });
            }
            else if (_project.ContractCompletionDate < DateTime.UtcNow)
            {
                _project.StatusFlag = McvConstant.PROJECT_STATUSFLAG_LOCKED;//locked
                _project.Description = DateTime.UtcNow.ToString("dd MMM yyyy") + " | Locked by completion date overdue.\n" + _project.Description;
                await ScaffoldAgendaTasks(_project.ID, _project.StatusFlag);
                _lockedProjects.Add(
                   new
                   {
                       Project = $"{_project.Code}-{_project.Title}",
                       Reason = $"Locked by contract completion overdue (Contract Completion:{ClockTools.GetIST(_project.ContractCompletionDate.Value).ToString("dd MMM yyyy")})",
                       Partner =
                        _project.Associations.Where(x => x.TypeFlag == McvConstant.PROJECT_ASSOCIATION_TYPEFLAG_PARTNER).Any() ?
                        _project.Associations.Where(x => x.TypeFlag == McvConstant.PROJECT_ASSOCIATION_TYPEFLAG_PARTNER).FirstOrDefault().Contact.Name : null,
                       Associate =
                        _project.Associations.Where(x => x.TypeFlag == McvConstant.PROJECT_ASSOCIATION_TYPEFLAG_ASSOCIATE).Any() ?
                        _project.Associations.Where(x => x.TypeFlag == McvConstant.PROJECT_ASSOCIATION_TYPEFLAG_ASSOCIATE).FirstOrDefault().Contact.Name : null,
                   });
            }
            else if (_project.ExpectedCompletionDate < DateTime.UtcNow)
            {
                _project.StatusFlag = McvConstant.PROJECT_STATUSFLAG_LOCKED;//locked
                _project.Description = DateTime.UtcNow.ToString("dd MMM yyyy") + " | Locked by completion date overdue.\n" + _project.Description;
                await ScaffoldAgendaTasks(_project.ID, _project.StatusFlag);
                _lockedProjects.Add(
                   new
                   {
                       Project = $"{_project.Code}-{_project.Title}",
                       Reason = $"Locked by expected completion overdue (Expected Completion:{ClockTools.GetIST(_project.ExpectedCompletionDate.Value).ToString("dd MMM yyyy")})",
                       Partner =
                        _project.Associations.Where(x => x.TypeFlag == McvConstant.PROJECT_ASSOCIATION_TYPEFLAG_PARTNER).Any() ?
                        _project.Associations.Where(x => x.TypeFlag == McvConstant.PROJECT_ASSOCIATION_TYPEFLAG_PARTNER).FirstOrDefault().Contact.Name : null,
                       Associate =
                        _project.Associations.Where(x => x.TypeFlag == McvConstant.PROJECT_ASSOCIATION_TYPEFLAG_ASSOCIATE).Any() ?
                        _project.Associations.Where(x => x.TypeFlag == McvConstant.PROJECT_ASSOCIATION_TYPEFLAG_ASSOCIATE).FirstOrDefault().Contact.Name : null,
                   });
            }
            else
            {
                var _lastMeeting = await db.Meetings.AsNoTracking()
                   
                   .Where(x => !x.IsVersion)
                   .Where(x => x.ProjectID == _project.ID && x.StatusFlag == McvConstant.MEETING_STATUSFLAG_SENT)
                   .OrderByDescending(x => x.StartDate).FirstOrDefaultAsync();

                if (_lastMeeting != null && _lastMeeting.StartDate.AddDays(90) < DateTime.UtcNow)
                {
                    _project.StatusFlag = McvConstant.PROJECT_STATUSFLAG_LOCKED;//locked
                    _project.Description = DateTime.UtcNow.ToString("dd MMM yyyy") + " | Locked by meeting overdue.\n" + _project.Description;
                    await ScaffoldAgendaTasks(_project.ID, _project.StatusFlag);
                    _lockedProjects.Add(
                  new
                  {
                      Project = $"{_project.Code}-{_project.Title}",
                      Reason = $"Locked by meeting overdue (Last Meeting:{ClockTools.GetIST(_lastMeeting.StartDate).ToString("dd MMM yyyy")} | Days:{(DateTime.UtcNow - _lastMeeting.StartDate).Days})",
                      Partner =
                        _project.Associations.Where(x => x.TypeFlag == McvConstant.PROJECT_ASSOCIATION_TYPEFLAG_PARTNER).Any() ?
                        _project.Associations.Where(x => x.TypeFlag == McvConstant.PROJECT_ASSOCIATION_TYPEFLAG_PARTNER).FirstOrDefault().Contact.Name : null,
                      Associate =
                        _project.Associations.Where(x => x.TypeFlag == McvConstant.PROJECT_ASSOCIATION_TYPEFLAG_ASSOCIATE).Any() ?
                        _project.Associations.Where(x => x.TypeFlag == McvConstant.PROJECT_ASSOCIATION_TYPEFLAG_ASSOCIATE).FirstOrDefault().Contact.Name : null,
                  });
                }
            }
        }

        await db.SaveChangesAsync();

        if (!_lockedProjects.Any()) return;

        var _senderEmail = await sharedService.GetPresetValue(McvConstant.LOCK_PROJECT_EMAIL_SENDER_ID);
        var _senderName = await sharedService.GetPresetValue(McvConstant.LOCK_PROJECT_EMAIL_SENDER_NAME);

        var toList = new List<EmailContact>() {
                                                  new EmailContact {
                                                      Name = "Core | Newarch",
                                                      Email = "<EMAIL>"
                                                  }
                                                };

        var ccList = new List<EmailContact>() {
                                                    new EmailContact {
                                                        Name = "Backup | Newarch",
                                                        Email = "<EMAIL>"
                                                    }
                                                };

        var _reportTitle = $"Locked Projects | {ClockTools.GetISTNow().ToString("dd MMM yyyy")}";


        StringBuilder sb = new StringBuilder();
        sb.AppendLine("<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Transitional//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd\">");
        sb.AppendLine("<html xmlns=\"http://www.w3.org/1999/xhtml\">");
        sb.AppendLine("<head>");
        sb.AppendLine("    <meta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\" />");
        sb.AppendLine("    <title>Email Design</title>");
        sb.AppendLine("    <meta name=\"viewport\" content=\"width=device-width; initial-scale=1.0;\" />");
        sb.AppendLine("    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=9; IE=8; IE=7; IE=EDGE\" />");
        sb.AppendLine("    <meta name=\"format-detection\" content=\"telephone=no\" />");
        sb.AppendLine("    <!--[if gte mso 9]><xml>");
        sb.AppendLine("    <o:OfficeDocumentSettings>");
        sb.AppendLine("    <o:AllowPNG />");
        sb.AppendLine("    <o:PixelsPerInch>96</o:PixelsPerInch>");
        sb.AppendLine("    </o:OfficeDocumentSettings>");
        sb.AppendLine("    </xml><![endif]-->");
        sb.AppendLine("    <style type=\"text/css\">");
        sb.AppendLine("        /* Some resets and issue fixes */");
        sb.AppendLine("        #outlook a {");
        sb.AppendLine("            padding: 0;");
        sb.AppendLine("        }");
        sb.AppendLine("");
        sb.AppendLine("        body {");
        sb.AppendLine("            width: 100% !important;margin:0;");
        sb.AppendLine("            -webkit-text-size-adjust: 100%;");
        sb.AppendLine("            -ms-text-size-adjust: 100%;");
        sb.AppendLine("        }");

        sb.AppendLine("        table{");
        sb.AppendLine("            mso-table-lspace: 0px;");
        sb.AppendLine("            mso-table-rspace: 0px;");
        sb.AppendLine("        }");
        sb.AppendLine("");
        sb.AppendLine("        table td {");
        sb.AppendLine("            border-collapse: collapse;");
        sb.AppendLine("        }");
        sb.AppendLine("");
        sb.AppendLine("        .ExternalClass * {");
        sb.AppendLine("            line-height: 115%;");
        sb.AppendLine("        }");
        sb.AppendLine("        /* End reset */");

        sb.AppendLine("    </style>");
        sb.AppendLine("</head>");
        sb.AppendLine("");
        sb.AppendLine("<body>");
        sb.AppendLine("");

        sb.AppendLine("");
        sb.AppendLine("    <div style=\"margin: 0 auto;font-family:Calibri;font-size:14px;line-height:1.8;padding-left:5px;padding-right:5px; max-width:500px;\">");
        sb.AppendLine("");
        sb.AppendLine("        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; border-collapse: collapse;\">");
        sb.AppendLine("            <tr>");
        sb.AppendLine("                <td style=\"font-size:16px; font-weight:bold;\">");
        sb.AppendLine(_reportTitle.ToUpper());
        sb.AppendLine("                </td>");
        sb.AppendLine("            </tr>");
        sb.AppendLine("        </table>");
        sb.AppendLine("");

        sb.AppendLine("<div style=\"border-bottom: 1px solid #cccccc; margin-top: 15px; margin-bottom: 15px; \"></div>");

        sb.AppendLine("        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width: 100%; border-collapse: collapse\">");
        sb.AppendLine("            <tr>");
        sb.AppendLine("                <td valign=\"top\" width=\"120\" style=\"font-weight:bold;padding-bottom:5px;\">");
        sb.AppendLine("                    FollowUp Date:");
        sb.AppendLine("                </td>");
        sb.AppendLine("                <td valign=\"top\" style=\"padding-bottom: 5px\">");
        sb.AppendLine(ClockTools.GetISTNow().ToString("dd MMM yyyy"));
        sb.AppendLine("                </td>");
        sb.AppendLine("            </tr>");
        sb.AppendLine("            <tr>");
        sb.AppendLine("                <td valign=\"top\" width=\"120\" style=\"font-weight:bold;padding-bottom:5px;\">");
        sb.AppendLine("                    Projects Locked:");
        sb.AppendLine("                </td>");
        sb.AppendLine("                <td valign=\"top\" style=\"padding-bottom: 5px\">");
        sb.AppendLine($"{_lockedProjects.Count}");
        sb.AppendLine("                </td>");
        sb.AppendLine("            </tr>");


        sb.AppendLine("        </table>");
        sb.AppendLine("");
        sb.AppendLine("        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width: 100%; border-collapse: collapse\">");
        sb.AppendLine("            <tr>");
        sb.AppendLine("                <td valign=\"top\"  style=\"font-weight:bold;\">");
        sb.AppendLine("                    To:");
        sb.AppendLine("                </td>");
        sb.AppendLine("            </tr>");
        sb.AppendLine("            <tr>");
        sb.AppendLine("                <td valign=\"top\" style=\"padding-bottom: 5px;\">");
        sb.AppendLine("                    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width: 100%; border-collapse: collapse\">");
        foreach (var obj in toList)
        {
            sb.AppendLine("                        <tr>");
            sb.AppendLine("                            <td>");
            sb.AppendLine(obj.Name + " <i> (" + obj.Email + ")</i>");
            sb.AppendLine("                            </td>");
            sb.AppendLine("                        </tr>");
        }
        sb.AppendLine("                    </table>");
        sb.AppendLine("                </td>");
        sb.AppendLine("            </tr>");
        sb.AppendLine("            <tr>");
        sb.AppendLine("                <td valign=\"top\"  style=\"font-weight:bold;\">");
        sb.AppendLine("                    CC:");
        sb.AppendLine("                </td>");
        sb.AppendLine("            </tr>");
        sb.AppendLine("            <tr>");
        sb.AppendLine("                <td valign=\"top\" style=\"padding-bottom: 5px;\">");
        sb.AppendLine("                    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width: 100%; border-collapse: collapse\">");
        foreach (var obj in ccList)
        {
            sb.AppendLine("                        <tr>");
            sb.AppendLine("                            <td>");
            sb.AppendLine(obj.Name + " <i> (" + obj.Email + ")</i>");
            sb.AppendLine("                            </td>");
            sb.AppendLine("                        </tr>");
        }
        sb.AppendLine("                    </table>");
        sb.AppendLine("                </td>");
        sb.AppendLine("            </tr>");

        sb.AppendLine("        </table>");
        sb.AppendLine("<div style=\"border-bottom: 1px solid #cccccc; margin-top: 15px; margin-bottom: 15px; \"></div>");

        sb.AppendLine("        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width: 100%; border-collapse: collapse\">");
        sb.AppendLine("            <tr>");
        sb.AppendLine("                <td valign=\"top\" style=\"padding-top: 5px; padding-bottom: 5px;\">");
        sb.AppendLine("                    <pre style=\"font-family: Calibri;font-size: 14px;margin-top: 0;margin-bottom: 0;white-space: pre-wrap;white-space: -moz-pre-wrap;white-space: -pre-wrap;white-space: -o-pre-wrap;word-wrap: break-word;\">");
        sb.AppendLine("Dear Team,");
        sb.AppendLine(" Please find list of locked projects below.");
        sb.AppendLine("</pre>");
        sb.AppendLine("                </td>");
        sb.AppendLine("            </tr>");
        sb.AppendLine("        </table>");
        sb.AppendLine("");
        sb.AppendLine("");

        var _index = 1;
        foreach (var obj in _lockedProjects)
        {
            sb.AppendLine("        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width: 100%; border-collapse: collapse;\">");

            sb.AppendLine("                        <tr>");

            sb.AppendLine("                            <td style=\"font-weight:bold;font-size:14px;\">");
            sb.AppendLine($"{obj.Project}");
            sb.AppendLine("                            </td>");

            sb.AppendLine("                            <td style=\"text-align:end;\">");
            sb.AppendLine($"{(obj.Partner != null ? obj.Partner : "NA")}");
            sb.AppendLine("                            </td>");
            sb.AppendLine("                        </tr>");

            sb.AppendLine("                        <tr>");
            sb.AppendLine(" <td colspan=\"2\" valign=\"top\" style=\"padding-bottom: 5px; font-style: italic;\">");
            sb.AppendLine($"{obj.Reason}");
            sb.AppendLine("                            </td>");
            sb.AppendLine("                        </tr>");

            sb.AppendLine("        </table>");

            _index++;

        }

        //FOOTER
        sb.AppendLine("<div style=\"border-bottom: 1px solid #cccccc; margin-top: 15px; margin-bottom: 15px; \"></div>");
        sb.AppendLine("        <table align=\"center\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; border-collapse: collapse;font-size:11px;\">");

        sb.AppendLine("");
        sb.AppendLine("            <tr>");
        sb.AppendLine("");
        sb.AppendLine("                <td align=\"center\" >");
        sb.AppendLine("This is a <b>MyCockpitView<sup>&copy;</sup></b> & <b>DesignScript<sup>&copy;</sup></b> generated e-mail for your information and necessary action.");
        sb.AppendLine("</td>");
        sb.AppendLine("            </tr>");
        sb.AppendLine("");
        sb.AppendLine("            <tr>");
        sb.AppendLine("");
        sb.AppendLine("                <td align=\"center\" >");
        sb.AppendLine("");
        sb.AppendLine("                    Powered by <b>Newarch<sup>&reg;</sup> Infotech LLP</b>");
        sb.AppendLine("");
        sb.AppendLine("                </td>");
        sb.AppendLine("            </tr>");
        sb.AppendLine("        </table>");
        sb.AppendLine("    </div>");

        sb.AppendLine("</body>");
        sb.AppendLine("");
        sb.AppendLine("</html>");

        var _emailBody = sb.ToString();

        var emailTo = new List<(string name, string email)>();
        foreach (var obj in toList)
            emailTo.Add((obj.Name, obj.Email));

        var emailCC = new List<(string name, string email)>();
        foreach (var obj in ccList)
            emailCC.Add((obj.Name, obj.Email));




        await sharedService.SendMail(_reportTitle, _senderName, _senderEmail, _emailBody, emailTo, emailCC);


    }



    public async Task<int> GetNewCodeOrder()
    {

        return await db.Projects.AsNoTracking()
            .Where(x => x.OrderFlag > 0)
            .MaxAsync(x => x.OrderFlag) + 1;

    }

    public async Task<int> Create(Project Entity, IEnumerable<ProjectArea> Areas = null)
    {

        var _exist = await db.Projects.AsNoTracking().Where(x => x.Title == Entity.Title).SingleOrDefaultAsync();
        if (_exist != null) return _exist.ID;

        Entity.BillingTitle = Entity.BillingTitle == null || Entity.BillingTitle == String.Empty ? Entity.Title : Entity.BillingTitle;

        Entity.Title = Entity.Title.ToUpper().Trim();
        Entity.BillingTitle = Entity.BillingTitle.ToUpper().Trim();

        Entity.OrderFlag = await GetNewCodeOrder();
        Entity.Code = Entity.OrderFlag.ToString("0000");
        //Entity.CompanyID = 1;

        if (Areas != null && Areas.Any())
            Entity.Areas = Areas.ToList();

        return await base.Create(Entity);

    }
    public async Task Update(Project UpdatedEntity, IEnumerable<ProjectArea>? Areas = null)
    {

        var Entity = await db.Projects.AsNoTracking()
            .SingleOrDefaultAsync(x => x.ID == UpdatedEntity.ID);

        if (Entity == null) throw new EntityServiceException("Project not found");

        Entity = UpdatedEntity;

        if (Entity.StatusFlag != McvConstant.PROJECT_STATUSFLAG_INPROGRESS && Entity.StatusFlag != McvConstant.PROJECT_STATUSFLAG_PREPOPOSAL)
        {
            var _activePackages = await db.Packages.AsNoTracking()
                .Where(x => x.ProjectID == Entity.ID)
                .Where(x => x.StatusFlag == McvConstant.PACKAGE_STATUSFLAG_ACTIVE)
                .AnyAsync();

            if (_activePackages) throw new EntityServiceException("This project has an active package. The status cannot be changed.");
        }

        if (Areas != null)
        {
            foreach (var _area in Areas)
            {
                var _exist = await db.ProjectAreas.AsNoTracking()
                    .AnyAsync(x => x.ProjectID == _area.ProjectID && x.Title.ToUpper().Trim() == _area.Title.ToUpper().Trim());

                if (!_exist)
                {
                    _area.ProjectID = Entity.ID;
                    db.ProjectAreas.Add(_area);

                }
            }


            var _areas = await db.ProjectAreas.Where(x => x.ProjectID == Entity.ID).ToListAsync();
            if (Areas == null || !Areas.Any())
            {
                db.ProjectAreas.RemoveRange(_areas);
            }
            else
            {
                foreach (var _type in _areas)
                {
                    if (!Areas.Any(x => x.Title.ToUpper().Trim() == _type.Title.ToUpper().Trim()))
                        db.ProjectAreas.Remove(_type);
                }
            }
        }


       await base.Update(Entity);

    }

    public async Task ScaffoldAgendaTasks(int projectID, int statusFlag)
    {
        var _pendingAgendaIDs = await db.MeetingAgendas.AsNoTracking()
               .Where(x => x.ProjectID == projectID)
               .Where(x => !x.IsVersion)
              .Where(x => x.StatusFlag == 0 && !x.IsForwarded && x.ProjectID != null && x.TodoID == null && x.PackageID == null)
              .Select(x => x.ID)
              .ToListAsync();

        if (statusFlag == McvConstant.PROJECT_STATUSFLAG_INQUIRY
            || statusFlag == McvConstant.PROJECT_STATUSFLAG_PREPOPOSAL
            || statusFlag == McvConstant.PROJECT_STATUSFLAG_INPROGRESS)
        {

            var meetingAgendaService = new MeetingAgendaService(db);
            foreach (var _agenda in _pendingAgendaIDs)
            {
                await meetingAgendaService.AssignAgendaTasks(_agenda);
            }

        }
        else
        {

            var taskService = new WFTaskService(db);
            foreach (var _agenda in _pendingAgendaIDs)
            {
                await taskService.PurgePendingTasks(nameof(MeetingAgenda), _agenda);
            }

        }
    }

    public async Task<String> GetCode(int ID)
    {

        var entity = await db.Projects.AsNoTracking().SingleOrDefaultAsync(x => x.ID == ID);
        if (entity == null) return null;

        return entity.Code;

    }


    public async Task<byte[]> GetProjectAnalysisExcel(IEnumerable<QueryFilter>? Filters = null, string? Search = null, string? Sort = null)
    {

        var _dataSet = new DataSet();

        var _data = await GetProjectAnalysisData(Filters, Search, Sort);

        var _IstData = new List<ProjectAnalysis>();
        var _validDate = new DateTime(2000, 1, 1);
        foreach (var item in _data)
        {
            if (item.CreatedDate < _validDate)
            {
                item.CreatedDate = DateTime.UtcNow;
            }
            if (item.ContractCompletionDate < _validDate)
            {
                item.ContractCompletionDate = item.CreatedDate;
            }
            if (item.LastRecievedDate < _validDate)
            {
                item.LastRecievedDate = (DateTime?)null;
            }
            if (item.OfferDueDate < _validDate)
            {
                item.OfferDueDate = DateTime.UtcNow;
            }
            if (item.InquiryConvertionDate < _validDate)
            {
                item.InquiryConvertionDate = (DateTime?)null;
            }
            if (item.InquiryLastActionDate < _validDate)
            {
                item.InquiryLastActionDate = (DateTime?)null;
            }
            item.CreatedDate = item.CreatedDate.AddMinutes(330);
            item.ContractCompletionDate = item.ContractCompletionDate != null ? item.ContractCompletionDate.Value.AddMinutes(330) : (DateTime?)null;
            item.ExpectedCompletionDate = item.ExpectedCompletionDate != null ? item.ExpectedCompletionDate.Value.AddMinutes(330) : (DateTime?)null;

            item.LastRecievedDate = item.LastRecievedDate != null ? (item.LastRecievedDate.Value.AddMinutes(330)) : (DateTime?)null;

            item.OfferDueDate = item.OfferDueDate != null ? item.OfferDueDate.Value.AddMinutes(330) : (DateTime?)null;

            item.InquiryConvertionDate = item.InquiryConvertionDate != null ? item.InquiryConvertionDate.Value.AddMinutes(330) : (DateTime?)null;

            item.InquiryLastActionDate = item.InquiryLastActionDate != null ? item.InquiryLastActionDate.Value.AddMinutes(330) : (DateTime?)null;

            _IstData.Add(item);
        }

        _dataSet.Tables.Add(DataTools.ToDataTable(_IstData));

        return ExcelUtility.ExportExcel(_dataSet);

    }
    public async Task<IEnumerable<ProjectAnalysis>> GetProjectAnalysisData(IEnumerable<QueryFilter>? Filters = null, string? Search = null, string? Sort = null)
    {

        var _lastActivities = db.Activities.AsNoTracking()
           .Where(x => x.Entity != null
           && x.Entity==nameof(Project))
           .Where(a => a.WFTaskID != null)
           .GroupBy(x => x.EntityID)
               .Select(x => new
               {
                   EntityID = x.Key.Value,
                   LastActivity = x.Where(a => a.WFTaskID != null)
                                   .OrderByDescending(a => a.Created)
                                   .FirstOrDefault()
               });



        var query = await Get(Filters, Search, Sort)
                         .Include(x => x.Associations).ThenInclude(c => c.Contact)
                        .Include(x => x.ClientContact)
                        .Include(x => x.Bills)
                        .Include(x => x.CompanyAccount)
                        .Where(x => x.Code!="N/A")
                        //.Where(x => x.StatusFlag != -1)//DISCARD
                        //.Where(x => x.StatusFlag != 4)//COMPLETED
                        .Select(x => new
                        {
                            ID = x.ID,
                            Company = x.CompanyAccount.Title,
                            Code = x.Code,
                            Title = x.Title,
                            BillingTitle = x.BillingTitle,
                            Partner = x.Associations.Where(a => a.TypeFlag == 0).Any() ? x.Associations.Where(a => a.TypeFlag == 0).FirstOrDefault().Contact.FullName : "Not Assigned",
                            Associate = x.Associations.Where(a => a.TypeFlag == 1).Any() ? x.Associations.Where(a => a.TypeFlag == 1).FirstOrDefault().Contact.FullName : "Not Assigned",
                            Client = x.ClientContact != null ? x.ClientContact.FullName : "Not Assigned",
                            Location = (x.Location + ", " + x.City + " " + x.State + " " + x.Country).Trim(),
                            Created = x.Created,
                            x.ContractCompletionDate,
                            x.ExpectedCompletionDate,
                            x.InquiryConvertionDate,
                            OfferDue = x.OfferDue != null ? x.OfferDue : x.Created,
                            Fee = x.CompanyFee,
                            RecievedPercentage = x.Bills.Where(b => !b.IsDeleted)
                                .Where(b => b.TypeFlag == 1)
                                .Any() ?
                                x.Bills.Where(b => !b.IsDeleted)
                                .Where(b => b.TypeFlag == 1)
                                .OrderByDescending(b => b.BillDate)
                                .Select(b => b.WorkCompletion)
                                .FirstOrDefault() :
                                0,
                            RecievedAmount = x.Bills.Where(b => !b.IsDeleted)
                                .Where(b => b.TypeFlag == 1)
                                .Any() ?
                                x.Bills.Where(b => !b.IsDeleted)
                                .Where(b => b.TypeFlag == 1)
                                .OrderByDescending(b => b.BillDate)
                                .Select(b => b.Amount)
                                .FirstOrDefault() :
                                0,
                            LastRecievedDate = x.Bills.Where(b => !b.IsDeleted)
                                .Where(b => b.TypeFlag == 1)
                                .Any() ? x.Bills.Where(b => !b.IsDeleted)
                                .Where(b => b.TypeFlag == 1)
                                .OrderByDescending(b => b.BillDate)
                                .Select(b => b.BillDate)
                                .FirstOrDefault() :
                                (DateTime?)null,
                            PendingPercentage = 100 - (x.Bills.Where(b => !b.IsDeleted)
                                .Where(b => b.TypeFlag == 1)
                                .Any() ?
                                x.Bills.Where(b => !b.IsDeleted)
                                .Where(b => b.TypeFlag == 1)
                                .OrderByDescending(b => b.BillDate)
                                .Select(b => b.WorkCompletion)
                                .FirstOrDefault() :
                                0),
                            PendingAmount = x.CompanyFee - (x.Bills.Where(b => !b.IsDeleted)
                                .Where(b => b.TypeFlag == 1)
                                .Any() ?
                                x.Bills.Where(b => !b.IsDeleted)
                                .Where(b => b.TypeFlag == 1)
                                .OrderByDescending(b => b.BillDate)
                                .Select(b => b.Amount)
                                .FirstOrDefault() :
                                0),
                            ProformaPercentage = x.Bills.Where(b => !b.IsDeleted)
                                .Where(b => b.TypeFlag == 0)
                                .Any() ?
                                x.Bills.Where(b => !b.IsDeleted)
                                .Where(b => b.TypeFlag == 0)
                                .OrderByDescending(b => b.BillDate)
                                .Select(b => b.WorkCompletion)
                                .FirstOrDefault() :
                                0,
                            ProformaAmount = x.Bills.Where(b => !b.IsDeleted)
                                .Where(b => b.TypeFlag == 0)
                                .Any() ?
                                x.Bills.Where(b => !b.IsDeleted)
                                .Where(b => b.TypeFlag == 0)
                                .OrderByDescending(b => b.BillDate)
                                .Select(b => b.Amount)
                                .FirstOrDefault() :
                                0,
                            Status = db.StatusMasters.Where(s => s.Entity==nameof(Project) && s.Value == x.StatusFlag).Any() ? db.StatusMasters.Where(s => s.Entity==nameof(Project) && s.Value == x.StatusFlag).Select(s => s.Title).FirstOrDefault() : "",
                            ClientType = db.TypeMasters.Where(s => s.Entity==nameof(Project) && s.Value == x.TypeFlag).Any() ? db.TypeMasters.Where(s => s.Entity==nameof(Project) && s.Value == x.TypeFlag).Select(s => s.Title).FirstOrDefault() : "",

                            InquiryLastActionDate = _lastActivities.Where(a => a.EntityID == x.ID).Any() ?
                            _lastActivities.Where(a => a.EntityID == x.ID)
                            .FirstOrDefault().LastActivity.Created : x.Created,
                            InquiryLastAction = _lastActivities.Where(a => a.EntityID == x.ID).Any() ? _lastActivities.Where(a => a.EntityID == x.ID).FirstOrDefault().LastActivity.Action : null,
                            InquiryLastComments = _lastActivities.Where(a => a.EntityID == x.ID).Any() ? _lastActivities.Where(a => a.EntityID == x.ID).FirstOrDefault().LastActivity.Comments : null,
                            InquiryLastStatus = _lastActivities.Where(a => a.EntityID == x.ID).Any() ? _lastActivities.Where(a => a.EntityID == x.ID).FirstOrDefault().LastActivity.Status : null,
                            InquiryLastActionBy = _lastActivities.Where(a => a.EntityID == x.ID).Any() ? _lastActivities.Where(a => a.EntityID == x.ID).FirstOrDefault().LastActivity.CreatedBy : null,

                            x.IsRepeatClient
                        })
                        .ToListAsync();


        var _result = query.Select(x => new ProjectAnalysis
        {
            ID = x.ID,
            Company = x.Company,
            Title = x.Title,
            BillingTitle = x.BillingTitle,
            Code = x.Code,
            Status = x.Status,
            Partner = x.Partner,
            Associate = x.Associate,
            Client = x.Client,
            ClientType = x.ClientType,
            IsRepeatClient = x.IsRepeatClient,
            Location = x.Location,
            CreatedDate = x.Created,
            ContractCompletionDate = x.ContractCompletionDate,
            ExpectedCompletionDate = x.ExpectedCompletionDate,
            OfferDueDate = x.OfferDue,
            Fee = x.Fee,
            LastRecievedDate = x.LastRecievedDate,
            RecievedPercentage = x.RecievedPercentage,
            RecievedAmount = x.RecievedAmount,
            PendingPercentage = x.PendingPercentage,
            PendingAmount = x.PendingAmount,
            ProformaPercentage = x.ProformaPercentage,
            ProformaAmount = x.ProformaAmount,

            InquiryConvertionDate = x.InquiryConvertionDate,
            InquiryLastActionDate = x.InquiryLastActionDate,
            InquiryLastActionBy = x.InquiryLastActionBy,
            InquiryLastComments = x.InquiryLastComments,
            InquiryLastStatus = x.InquiryLastStatus


        });

        return _result.OrderByDescending(x => x.Code);


    }



    public async Task<IEnumerable<ProjectSummary>> GetCashflowData(
     IEnumerable<QueryFilter>? Filters = null,
     string? Search = null,
     string? Sort = null,
     bool ISTDates = false)
    {

        var _projects =await Get(Filters, Search, Sort)
           .Include(x => x.Associations)
           .ThenInclude(c => c.Contact)
           .Select(x => new
           {
               ProjectID = x.ID,
               ProjectUID = x.UID,
               Project = x.Title,
               Code = x.Code,
               Partner = x.Associations
                   .Where(t => t.TypeFlag == McvConstant.PROJECT_ASSOCIATION_TYPEFLAG_PARTNER)
                   .Select(t => t.Contact.FullName)
                   .FirstOrDefault() ?? "N/A",
               x.CompanyFee,
               x.TotalFee,
               x.Discount,
               x.StatusFlag,
           }).ToListAsync();

        var _filteredProjectIDs = _projects.Select(x => x.ProjectID).ToList();

        var _billPayments =await db.ProjectBillPayments.AsNoTracking()
            
            .Include(b => b.ProjectBill)
            .Where(x => !x.ProjectBill.IsDeleted)
            .Where(x=> _filteredProjectIDs.Contains(x.ProjectBill.ProjectID))
            .Select(x => new
            {
                ProjectID = x.ProjectBill.ProjectID,
                TransactionDate = x.TransactionDate,
                BillAmount = x.ProjectBill.BillAmount,
                RecievedAmountPreTax = x.BillAmountReceived,
                ReceivedAmountPostTax = x.Amount,
            })
            .ToListAsync();

        var _bills =await db.ProjectBills.AsNoTracking()
            .Include(x => x.Payments)
            
               .Where(x => _filteredProjectIDs.Contains(x.ProjectID))
            .Select(x => new
            {
                x.ProjectID,
                Amount = x.BillAmount,
                x.Created,
                Percentage = x.WorkCompletion,
                IsReceived = x.Payments.Any()
            })
            .ToListAsync();

        var _stageServices =await db.ProjectScopeServices.AsNoTracking()
            .Include(x => x.ProjectScope)
               .Where(x => _filteredProjectIDs.Contains(x.ProjectID))
            .Select(x => new
            {
                x.ProjectID,
                x.ProjectScopeID,
                x.Abbreviation,
                ScopePercentage = x.ProjectScope.SharePercentage,
                ServicePercentage = x.SharePercentage
            })
            .ToListAsync();

        var _dsSpaces =await db.DesignScriptEntities.AsNoTracking()
               .Where(x => _filteredProjectIDs.Contains(x.ProjectID))
            .GroupJoin(db.ProjectScopes, x => x.ProjectScopeID, y => y.ID,
                (a, b) => new { dse = a, scope = b.DefaultIfEmpty() })
            .SelectMany(join => join.scope.DefaultIfEmpty(), (a, b) => new
            {
                a.dse.ID,
                a.dse.SharePercentage,
                a.dse.ProjectScopeID,
                Scope = b != null ? b.Title : null,
                ScopePercentage = b != null ? b.SharePercentage : 0m,
            })
            .ToListAsync();

        var _packages = await db.Packages.AsNoTracking()
    
    .Where(x => _filteredProjectIDs.Contains(x.ProjectID))
    .Select(x=> new
    {
        x.ProjectID,
        x.TypeFlag,
        x.StatusFlag,
        x.Created,
        x.Stage,
        x.PhaseTitle,
        x.DesignScriptEntityID,
    })
    .ToListAsync(); // Execute the query here to get the results in memory

        // Then perform the grouping, ordering, and selection in memory
        var _latestPackages = _packages
            .GroupBy(x => new
            {
                x.ProjectID,
                x.DesignScriptEntityID,
                x.Stage
            })
            .Select(g => g.OrderByDescending(p => p.Created).FirstOrDefault())
            .Select(x => new
            {
                x.ProjectID,
                x.TypeFlag,
                x.StatusFlag,
                x.Created,
                x.Stage,
                x.PhaseTitle,
                x.DesignScriptEntityID,
            })
            .ToList();



        var _meetings =await db.Meetings.AsNoTracking()
            .Where(x => !x.IsVersion)
            
                 .Where(x =>x.ProjectID!=null && _filteredProjectIDs.Contains(x.ProjectID.Value))
            .Where(x => x.StatusFlag == McvConstant.MEETING_STATUSFLAG_SENT)
            .Where(x => x.ProjectID != null)
            .Select(x => new
            {
                ProjectID = x.ProjectID.Value,
                MeetingDate = x.StartDate
            })
            .ToListAsync();

        var _projectStatusMasters=await db.StatusMasters
                    .Where(s => s.Entity != null && s.Entity == nameof(Project))
                    .Select(s =>new { s.Title,s.Value })
                    .ToListAsync();

        var sharedService = new SharedService(db);
        var _partnerShare = Convert.ToDecimal(await sharedService.GetPresetValue(McvConstant.PROJECT_PARTNER_SHARE));
        var _partnerShareBeforeApr21 = Convert.ToDecimal(await sharedService.GetPresetValue(McvConstant.PROJECT_PARTNER_SHARE_BEFORE_APR21));
        var _partnerSharePolicyStart = new DateTime(2021, 04, 01);

        var _packageData = _latestPackages
     .GroupJoin(
         _dsSpaces,
         x => x.DesignScriptEntityID,
         p => p.ID,
         (package, spaces) => new { package, spaces = spaces.FirstOrDefault() }
     )
     .Select(joined => {
         var package = joined.package;
         var space = joined.spaces;

         var projectScopeId = space?.ProjectScopeID;

         // Get the stage service once if we have a valid project scope ID
         var stageService = projectScopeId.HasValue
             ? _stageServices.FirstOrDefault(s => s.ProjectScopeID == projectScopeId && s.Abbreviation == package.Stage)
             : null;

         // Get any stage service with matching project scope ID (for ScopePercentage)
         var anyStageService = projectScopeId.HasValue
             ? _stageServices.FirstOrDefault(s => s.ProjectScopeID == projectScopeId)
             : null;

         return new
         {
             package.ProjectID,
             package.TypeFlag,
             package.StatusFlag,
             package.Created,
             package.Stage,
             package.PhaseTitle,
             Scope = space?.Scope,
             ScopePercentage = anyStageService?.ScopePercentage ?? 0m,
             PhasePercentage = space?.SharePercentage ?? 0m,
             ServicePercentage = stageService?.ServicePercentage ?? 0m
         };
     })
     .ToList();

        var _projectData = _projects
            .Select(x => new
            {
                x.ProjectID,
                x.ProjectUID,
                x.Project,
                x.Code,
                x.Partner,
                x.CompanyFee,
                x.TotalFee,
                x.Discount,
                x.StatusFlag,
                Status = _projectStatusMasters.FirstOrDefault(s=>s.Value==x.StatusFlag).Title ?? "UNDEFINED",
                Payments = _billPayments.Where(b => b.ProjectID == x.ProjectID).ToList(),
                IsPostAPR21 = !_billPayments
                    .Where(b => b.ProjectID == x.ProjectID)
                    .Where(p => p.TransactionDate < _partnerSharePolicyStart)
                    .Any(),
                BillPercentage = _bills
                    .Where(b => b.ProjectID == x.ProjectID && b.IsReceived)
                    .OrderByDescending(p => p.Created)
                    .Select(p => p.Percentage)
                    .FirstOrDefault(),
                ProformaAmount = _bills
                    .Where(b => b.ProjectID == x.ProjectID && !b.IsReceived)
                    .OrderByDescending(p => p.Created)
                    .Select(p => p.Amount)
                    .FirstOrDefault(),
                ProformaPercentage = _bills
                    .Where(b => b.ProjectID == x.ProjectID && !b.IsReceived)
                    .OrderByDescending(p => p.Created)
                    .Select(p => p.Percentage)
                    .FirstOrDefault(),
                ProformaDate = _bills
                    .Where(b => b.ProjectID == x.ProjectID && !b.IsReceived)
                    .OrderByDescending(p => p.Created)
                    .Select(p => p.Created)
                    .FirstOrDefault(),
                CompletedPackages = _packageData
                    .Where(b => b.ProjectID == x.ProjectID)
                    .Where(p => p.TypeFlag == McvConstant.PACKAGE_TYPEFLAG_ACTIVE || p.TypeFlag == McvConstant.PACKAGE_TYPEFLAG_EMPTY)
                    .Where(p => p.StatusFlag == McvConstant.PACKAGE_STATUSFLAG_SENT)
                    .ToList(),
                ActivePackages = _packageData
                    .Where(b => b.ProjectID == x.ProjectID)
                    .Where(p => p.TypeFlag == McvConstant.PACKAGE_TYPEFLAG_ACTIVE)
                    .Where(p => p.StatusFlag == McvConstant.PACKAGE_STATUSFLAG_ACTIVE)
                    .ToList(),
                ProposedPackages = _packageData
                    .Where(b => b.ProjectID == x.ProjectID)
                    .Where(p => p.TypeFlag == McvConstant.PACKAGE_TYPEFLAG_PROPOSED)
                    .ToList(),
            }).ToList();


        var _results = _projectData.Select(x => new ProjectSummary
        {
            ProjectID = x.ProjectID,
            ProjectUID = x.ProjectUID,
            Project = x.Project,
            Code = x.Code,
            StatusFlag = x.StatusFlag,
            Status = x.Status,
            Partner = x.Partner,
            CompanyFee = x.CompanyFee,
            TotalFee = x.TotalFee,
            LastPaymentDate = x.Payments.Any() ?
                           x.Payments.OrderByDescending(p => p.TransactionDate)
                                        .FirstOrDefault().TransactionDate :
                            (DateTime?)null,
            LastPaymentAmount = x.Payments.Any() ?
                           x.Payments.OrderByDescending(p => p.TransactionDate)
                                        .FirstOrDefault().RecievedAmountPreTax :
                            0m,
            IsPostAPR21 = x.IsPostAPR21,
            TotalPaymentAmount = x.Payments.Any() ?
                           x.Payments.Sum(p => p.ReceivedAmountPostTax) :
                            0m,
            TotalBillAmount = x.Payments.Any() ?
                           x.Payments.Sum(p => p.RecievedAmountPreTax) :
                            0m,

            ProformaAmount = x.ProformaAmount,
            ProformaDate = x.ProformaDate,
            CompletedPercentage = x.CompletedPackages.Any() ? x.CompletedPackages.Sum(p => (p.ScopePercentage) * (p.PhasePercentage / 100m) * (p.ServicePercentage / 100m)) : 0m,

            ActivePercentage = x.ActivePackages.Any() ? x.ActivePackages.Sum(p => (p.ScopePercentage) * (p.PhasePercentage / 100m) * (p.ServicePercentage / 100m)) : 0m,

            ProposedPercentage = x.ProposedPackages.Any() ? x.ProposedPackages.Sum(p => (p.ScopePercentage) * (p.PhasePercentage / 100m) * (p.ServicePercentage / 100m)) : 0m,

            LastMeetingDate = _meetings.Where(b => b.ProjectID == x.ProjectID).Any() ?
                                               _meetings.Where(b => b.ProjectID == x.ProjectID)
                                                            .OrderByDescending(p => p.MeetingDate)
                                                            .FirstOrDefault().MeetingDate :
                                                (DateTime?)null

        })
            .OrderByDescending(x => x.LastPaymentDate)
        .ThenBy(x => x.Project)
            .ToList();

        var _xCostFactor = Convert.ToInt32(await sharedService.GetPresetValue(McvConstant.XCOST_FACTOR));

        foreach (var obj in _results)
        {
            var _currentPeriodFilters = new List<QueryFilter>();
            _currentPeriodFilters.Add(new QueryFilter { Key = "ProjectID", Value = obj.ProjectID.ToString() });

            var _pkgs = await GetPackageVHrData(_currentPeriodFilters, Search, Sort);
            var _meets = await GetMeetingVHrData(_currentPeriodFilters, Search, Sort);
            var _insp = await GetInspectionVHrData(_currentPeriodFilters, Search, Sort);
            var _todos = await GetTodoVHrData(_currentPeriodFilters, Search, Sort);
            obj.VHr = (_pkgs.Any() ? _pkgs.FirstOrDefault().VHr : 0m)
            + (_meets.Any() ? _meets.FirstOrDefault().VHr : 0m)
             + (_insp.Any() ? _insp.FirstOrDefault().VHr : 0m)
            + (_todos.Any() ? _todos.FirstOrDefault().VHr : 0m);

            obj.VHrCost = (_pkgs.Any() ? _pkgs.FirstOrDefault().VHrCost : 0m)
            + (_meets.Any() ? _meets.FirstOrDefault().VHrCost : 0m)
              + (_insp.Any() ? _insp.FirstOrDefault().VHrCost : 0m)
            + (_todos.Any() ? _todos.FirstOrDefault().VHrCost : 0m);

            if (obj.LastPaymentDate != null)
                _currentPeriodFilters.Add(new QueryFilter { Key = "rangeStart", Value = obj.LastPaymentDate.ToString() });

            _currentPeriodFilters.Add(new QueryFilter { Key = "rangeEnd", Value = DateTime.UtcNow.ToString() });

            var _pkgsAfterLastPayment = await GetPackageVHrData(_currentPeriodFilters, Search, Sort);
            var _meetsAfterLastPayment = await GetMeetingVHrData(_currentPeriodFilters, Search, Sort);
            var _inspAfterLastPayment = await GetInspectionVHrData(_currentPeriodFilters, Search, Sort);
            var _todosAfterLastPayment = await GetTodoVHrData(_currentPeriodFilters, Search, Sort);
            obj.VHrAfterLastPayment = (_pkgsAfterLastPayment.Any() ? _pkgsAfterLastPayment.FirstOrDefault().VHr : 0m)
            + (_meetsAfterLastPayment.Any() ? _meetsAfterLastPayment.FirstOrDefault().VHr : 0m)
             + (_inspAfterLastPayment.Any() ? _inspAfterLastPayment.FirstOrDefault().VHr : 0m)
            + (_todosAfterLastPayment.Any() ? _todosAfterLastPayment.FirstOrDefault().VHr : 0m);

            obj.VHrCostAfterLastPayment = (_pkgsAfterLastPayment.Any() ? _pkgsAfterLastPayment.FirstOrDefault().VHrCost : 0m)
            + (_meetsAfterLastPayment.Any() ? _meetsAfterLastPayment.FirstOrDefault().VHrCost : 0m)
             + (_inspAfterLastPayment.Any() ? _inspAfterLastPayment.FirstOrDefault().VHrCost : 0m)
            + (_todosAfterLastPayment.Any() ? _todosAfterLastPayment.FirstOrDefault().VHrCost : 0m);

            if (obj.IsPostAPR21) //POST APRIL 2021
            {
                obj.PartnerShare = _partnerShare;

            }
            else
            {
                //Logic to keep Lastbite Upto10% only
                //Difference between PartnerShare(PostApr21)(40%) - VHrCostShare(x)
                var _difference = _partnerShare - obj.VHrCostShare;

                if (_difference >= _partnerShareBeforeApr21)
                {
                    obj.PartnerShare = _partnerShareBeforeApr21 + obj.VHrCostShare;
                }
                else
                {
                    obj.PartnerShare = _difference + obj.VHrCostShare;
                }

            }

            obj.XCost = (obj.VHrCost * _xCostFactor);
            obj.XCostPercentage = obj.CompanyFee != 0 ? Math.Ceiling(obj.VHrCost * _xCostFactor / obj.CompanyFee * 100.0m) : 100m;
        }

        if (Sort != null && Sort != String.Empty)
        {
            var _orderedQuery = _results.OrderBy(l => 0);
            var keywords = Sort.Replace("asc", "").Split(',');

            foreach (var key in keywords)
            {
                if (key.Trim().Equals("LastPaymentDate", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenBy(x => x.LastPaymentDate);
                else if (key.Trim().Equals("LastPaymentDate desc", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenByDescending(x => x.LastPaymentDate);
                else if (key.Trim().Equals("VHrCost", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenBy(x => x.VHrCost);
                else if (key.Trim().Equals("VHrCost desc", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenByDescending(x => x.VHrCost);

                else if (key.Trim().Equals("VHrCostAfterLastPayment", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenBy(x => x.VHrCostAfterLastPayment);

                else if (key.Trim().Equals("VHrCostAfterLastPayment desc", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenByDescending(x => x.VHrCostAfterLastPayment);

            }

            return _orderedQuery;
        }

        return _results;
    }



    public async Task<byte[]> GetCashflowExcel(IEnumerable<QueryFilter>? Filters = null, string? Search = null, string? Sort = null)
    {

        var _dataSet = new DataSet();

        var _data = await GetCashflowData(Filters, Search, Sort, true);
        foreach (var item in _data)
        {
            item.LastPaymentDate = item.LastPaymentDate != null ? ClockTools.GetIST(item.LastPaymentDate.Value) : (DateTime?)null;
            item.ProformaDate = item.ProformaDate != null ? ClockTools.GetIST(item.ProformaDate.Value) : (DateTime?)null;
        }
        _dataSet.Tables.Add(DataTools.ToDataTable(_data));

        return ExcelUtility.ExportExcel(_dataSet);


    }

    public async Task<IEnumerable<ProjectEstimation>> GetProjectEstimationData(
      IEnumerable<QueryFilter>? Filters = null,
      string? Search = null,
      string? Sort = null,
      bool ISTDates = false
      )
    {

        var _projects = await Get(Filters, Search, Sort)
                           .Include(x => x.Associations).ThenInclude(c => c.Contact)
                           .Select(x=> new
                           {
                               x.ID,
                               x.UID,
                               x.Title,
                               x.Code,
                               x.CompanyFee,
                               x.StatusFlag,
                               Partner = x.Associations.Any(t => t.TypeFlag == McvConstant.PROJECT_ASSOCIATION_TYPEFLAG_PARTNER) ? 
                               x.Associations.FirstOrDefault(t => t.TypeFlag == McvConstant.PROJECT_ASSOCIATION_TYPEFLAG_PARTNER).Contact.FullName : "N/A",
                               Status = db.StatusMasters.Where(s => s.Entity != null && s.Entity == nameof(Project) && s.Value == x.StatusFlag).Any() ? db.StatusMasters.Where(s => s.Entity != null && s.Entity == nameof(Project) && s.Value == x.StatusFlag).Select(s => s.Title).FirstOrDefault() : "",
                           })
                           .ToListAsync();

        var _filteredProjectIDs = _projects.Select(x => x.ID).ToList();

        var designScriptEntities= await db.DesignScriptEntities.AsNoTracking()
            .Where(x => x.TypeFlag == McvConstant.DESIGN_SCRIPT_ENTITY_TYPEFLAG_SPACE || x.TypeFlag == McvConstant.DESIGN_SCRIPT_ENTITY_TYPEFLAG_ELEMENT)
            .Where(x => _filteredProjectIDs.Contains(x.ProjectID))
            .Select(x=> new
            {
                x.ID,
                x.ProjectID,
                x.TypeFlag,
                //x.CostingQuantity,
                x.CostingAmount
            })
            .ToListAsync();

        var _filteredDesignScriptEntityIDs = designScriptEntities.Select(x => x.ID).ToList();

        var _spaceData = designScriptEntities
           .Where(x => x.TypeFlag == McvConstant.DESIGN_SCRIPT_ENTITY_TYPEFLAG_SPACE)
           .GroupBy(x => x.ProjectID)
           .Select(x => new
           {
               ProjectID = x.Key,
               //Quantity= x.Sum(d => d.CostingQuantity),
               Amount = x.Sum(d => d.CostingAmount),
           }).ToList();

        var _elementData = designScriptEntities
           .Where(x => x.TypeFlag == McvConstant.DESIGN_SCRIPT_ENTITY_TYPEFLAG_ELEMENT)
           .GroupBy(x => x.ProjectID)
           .Select(x => new
           {
               ProjectID = x.Key,
               //Quantity = x.Sum(d => d.CostingQuantity),
               Amount = x.Sum(d => d.CostingAmount),
           }).ToList();

        var _designScriptEntityItemMaps = await db.DesignScriptEntityItemMaps.AsNoTracking()
            .Where(x => _filteredProjectIDs.Contains(x.ProjectID) && _filteredDesignScriptEntityIDs.Contains(x.DesignScriptEntityID))
            .Select(x=> new
            {
                x.ProjectID,
                x.DesignScriptEntityID,
                //x.CostingQuantity,
                x.CostingAmount
            })
            .ToListAsync();

        var _boqData = _designScriptEntityItemMaps
          .GroupBy(x => x.ProjectID)
          .Select(x => new
          {
              ProjectID = x.Key,
              //Quantity = x.Sum(d => d.CostingQuantity),
              Amount = x.Sum(d => d.CostingAmount),
          }).ToList();

        //var _projectStatusMasters = await db.StatusMasters
        //            .Where(s => s.Entity != null && s.Entity == nameof(Project))
        //            .Select(s => new { s.Title, s.Value })
        //            .ToListAsync();

        var result = _projects
                             
                               .Select(x => new ProjectEstimation
                               {
                                   ID = x.ID,
                                   UID = x.UID.ToString(),
                                   Title = x.Title,
                                   Code = x.Code,
                                   Partner = x.Partner,
                                   CompanyFee = x.CompanyFee,
                                   StatusFlag = x.StatusFlag,
                                   Status = x.Status,
                                   SpaceAmount = _spaceData.Any(a => a.ProjectID == x.ID) ? _spaceData.FirstOrDefault(a => a.ProjectID == x.ID).Amount : 0m,
                                   ElementAmount = _elementData.Any(a=>a.ProjectID==x.ID) ? _elementData.FirstOrDefault(a => a.ProjectID == x.ID).Amount : 0m,
                                   BOQAmount = _boqData.Any(b=>b.ProjectID==x.ID)? _boqData.FirstOrDefault(b => b.ProjectID == x.ID).Amount : 0m
                               }).ToList();



        return result;
    }

    public async Task<byte[]> GetProjectEstimationExcel(IEnumerable<QueryFilter>? Filters = null, string? Search = null, string? Sort = null)
    {
        var _data = await GetProjectEstimationData(Filters, Search, Sort);

        return _data.ExportExcel();
    }

    public async Task<IEnumerable<ProjectSummary>> GetPackageVHrData(IEnumerable<QueryFilter>? Filters = null, string? Search = null, string? Sort = null)
    {

        //var _previousPeriodFilterDate = new DateTime(2021, 4, 1);

        var _packages = db.Packages.AsNoTracking()
             
             .Where(x => x.TypeFlag == McvConstant.PACKAGE_TYPEFLAG_ACTIVE)
             .Where(x => x.StatusFlag == McvConstant.PACKAGE_STATUSFLAG_SENT);
        //.Where(x => x.SubmissionDate != null && x.SubmissionDate.Value > _previousPeriodFilterDate);

        //Apply filters
        if (Filters != null)
        {
            if (Filters.Where(x => x.Key.Equals("ProjectID", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<Package>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("ProjectID", StringComparison.OrdinalIgnoreCase)))
                {
                    var isNumeric = Convert.ToInt32(_item.Value);

                    predicate = predicate.Or(x => x.ProjectID == isNumeric);
                }
                _packages = _packages.Where(predicate);
            }
            if (Filters.Where(x => x.Key.Equals("rangestart", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var _item = Filters.First(x => x.Key.Equals("rangestart", StringComparison.OrdinalIgnoreCase));

                DateTime result = Convert.ToDateTime(_item.Value);
                _packages = _packages.Where(x => x.SubmissionDate != null && x.SubmissionDate.Value >= result);
            }

            if (Filters.Where(x => x.Key.Equals("rangeend", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var _item = Filters.First(x => x.Key.Equals("rangeend", StringComparison.OrdinalIgnoreCase));

                DateTime result = Convert.ToDateTime(_item.Value);
                var end = result.AddDays(1);
                _packages = _packages.Where(x => x.SubmissionDate != null && x.SubmissionDate.Value < end);
            }
        }

        var _queryResult = await _packages
            .Select(x => new
            {
                Entity = nameof(Package),
                EntityID = x.ID,
                ProjectID = x.ProjectID,
                VHr = x.VHrAssigned,
                VHrCost = x.VHrAssignedCost,
                VHrRate = x.VHrRate,
            })
            .ToListAsync();

        return _queryResult
            .GroupBy(x => x.ProjectID)
            .Select(x => new ProjectSummary
            {
                ProjectID = x.Key,
                VHr = x.Any(t => t != null) ? x.Sum(t => t.VHr) : 0,
                VHrCost = x.Any(t => t != null) ? x.Sum(t => t.VHrCost) : 0,
            });

    }

    public async Task<IEnumerable<ProjectSummary>> GetMeetingVHrData(IEnumerable<QueryFilter>? Filters = null, string? Search = null, string? Sort = null)
    {

        //var _previousPeriodFilterDate = new DateTime(2021, 4, 1);

        var _meetings = db.Meetings.AsNoTracking()
            .Where(x => !x.IsVersion)
                        
                        .Where(x => x.StatusFlag != 0)
                        .Where(x => x.ProjectID != null);
        //.Where(x => x.FromDate > _previousPeriodFilterDate);

        //Apply filters
        if (Filters != null)
        {
            if (Filters.Where(x => x.Key.Equals("ProjectID", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<Meeting>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("ProjectID", StringComparison.OrdinalIgnoreCase)))
                {
                    var isNumeric = Convert.ToInt32(_item.Value);

                    predicate = predicate.Or(x => x.ProjectID == isNumeric);
                }
                _meetings = _meetings.Where(predicate);
            }
            if (Filters.Where(x => x.Key.Equals("rangestart", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var _item = Filters.First(x => x.Key.Equals("rangestart", StringComparison.OrdinalIgnoreCase));

                DateTime result = Convert.ToDateTime(_item.Value);
                _meetings = _meetings.Where(x => x.StartDate >= result);
            }

            if (Filters.Where(x => x.Key.Equals("rangeend", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var _item = Filters.First(x => x.Key.Equals("rangeend", StringComparison.OrdinalIgnoreCase));

                DateTime result = Convert.ToDateTime(_item.Value);
                var end = result.AddDays(1);
                _meetings = _meetings.Where(x => x.StartDate < end);
            }
        }

        var _meetingTasks = db.WFTasks.AsNoTracking()
        .Where(x => x.Entity==nameof(Meeting))
        .Where(x => x.StatusFlag == 1);

        var _queryResult = await _meetings
             .Join(_meetingTasks,
                        meet => meet.ID,
                        task => task.EntityID,
                        (meet, task) => new { meet, task })
                        .Select(x => new
                        {
                            Entity = nameof(Meeting),
                            EntityID = x.meet.ID,
                            ProjectID = x.meet.ProjectID.Value,
                            VHr = x.task.VHrAssessed,
                            VHrCost = x.task.VHrAssessedCost,
                            VHrRate = x.task.VHrRate,
                        })
            .ToListAsync();

        return _queryResult
            .GroupBy(x => x.ProjectID)
            .Select(x => new ProjectSummary
            {
                ProjectID = x.Key,
                VHr = x.Any(t => t != null) ? x.Sum(t => t.VHr) : 0,
                VHrCost = x.Any(t => t != null) ? x.Sum(t => t.VHrCost) : 0,
            });

    }

    public async Task<IEnumerable<ProjectSummary>> GetInspectionVHrData(IEnumerable<QueryFilter>? Filters = null, string? Search = null, string? Sort = null)
    {

        //var _previousPeriodFilterDate = new DateTime(2021, 4, 1);

        var _meetings = db.Inspections.AsNoTracking()
            .Where(x => !x.IsVersion)
                        
                        .Where(x => x.StatusFlag != 0)
                        .Where(x => x.ProjectID != null);
        //.Where(x => x.FromDate > _previousPeriodFilterDate);

        //Apply filters
        if (Filters != null)
        {
            if (Filters.Where(x => x.Key.Equals("ProjectID", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<Inspection>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("ProjectID", StringComparison.OrdinalIgnoreCase)))
                {
                    var isNumeric = Convert.ToInt32(_item.Value);

                    predicate = predicate.Or(x => x.ProjectID == isNumeric);
                }
                _meetings = _meetings.Where(predicate);
            }
            if (Filters.Where(x => x.Key.Equals("rangestart", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var _item = Filters.First(x => x.Key.Equals("rangestart", StringComparison.OrdinalIgnoreCase));

                DateTime result = Convert.ToDateTime(_item.Value);
                _meetings = _meetings.Where(x => x.StartDate >= result);
            }

            if (Filters.Where(x => x.Key.Equals("rangeend", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var _item = Filters.First(x => x.Key.Equals("rangeend", StringComparison.OrdinalIgnoreCase));

                DateTime result = Convert.ToDateTime(_item.Value);
                var end = result.AddDays(1);
                _meetings = _meetings.Where(x => x.StartDate < end);
            }
        }

        var _meetingTasks = db.WFTasks.AsNoTracking()
        .Where(x => x.Entity==nameof(Inspection))
        .Where(x => x.StatusFlag == 1);

        var _queryResult = await _meetings
             .Join(_meetingTasks,
                        meet => meet.ID,
                        task => task.EntityID,
                        (meet, task) => new { meet, task })
                        .Select(x => new
                        {
                            Entity = nameof(Inspection),
                            EntityID = x.meet.ID,
                            ProjectID = x.meet.ProjectID.Value,
                            VHr = x.task.VHrAssessed,
                            VHrCost = x.task.VHrAssessedCost,
                            VHrRate = x.task.VHrRate,
                        })
            .ToListAsync();

        return _queryResult
            .GroupBy(x => x.ProjectID)
            .Select(x => new ProjectSummary
            {
                ProjectID = x.Key,
                VHr = x.Any(t => t != null) ? x.Sum(t => t.VHr) : 0,
                VHrCost = x.Any(t => t != null) ? x.Sum(t => t.VHrCost) : 0,
            });

    }

    public async Task<IEnumerable<ProjectSummary>> GetTodoVHrData(IEnumerable<QueryFilter>? Filters = null, string? Search = null, string? Sort = null)
    {

        // var _previousPeriodFilterDate = new DateTime(2021, 4, 1);

        var _todos = db.Todos.AsNoTracking()
                        
                        .Where(x => x.StatusFlag == 1)
                        .Where(x => x.ProjectID != null);
        //  .Where(x => x.FromDate > _previousPeriodFilterDate);

        //Apply filters
        if (Filters != null)
        {
            if (Filters.Where(x => x.Key.Equals("ProjectID", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<Todo>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("ProjectID", StringComparison.OrdinalIgnoreCase)))
                {
                    var isNumeric = Convert.ToInt32(_item.Value);

                    predicate = predicate.Or(x => x.ProjectID == isNumeric);
                }
                _todos = _todos.Where(predicate);
            }
            if (Filters.Where(x => x.Key.Equals("rangestart", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var _item = Filters.First(x => x.Key.Equals("rangestart", StringComparison.OrdinalIgnoreCase));

                DateTime result = Convert.ToDateTime(_item.Value);
                _todos = _todos.Where(x => x.Created >= result);
            }

            if (Filters.Where(x => x.Key.Equals("rangeend", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var _item = Filters.First(x => x.Key.Equals("rangeend", StringComparison.OrdinalIgnoreCase));

                DateTime result = Convert.ToDateTime(_item.Value);
                var end = result.AddDays(1);
                _todos = _todos.Where(x => x.Created < end);
            }
        }

        var _todoTasks = db.WFTasks.AsNoTracking()
        .Where(x => x.Entity==nameof(Todo))
        .Where(x => x.StatusFlag == 1);

        var _queryResult = await _todos
             .Join(_todoTasks,
                        meet => meet.ID,
                        task => task.EntityID,
                        (meet, task) => new { meet, task })
                        .Select(x => new
                        {
                            Entity = nameof(Todo),
                            EntityID = x.meet.ID,
                            ProjectID = x.meet.ProjectID.Value,
                            VHr = x.task.VHrAssessed,
                            VHrCost = x.task.VHrAssessedCost,
                            VHrRate = x.task.VHrRate,
                        })
            .ToListAsync();

        return _queryResult
            .GroupBy(x => x.ProjectID)
            .Select(x => new ProjectSummary
            {
                ProjectID = x.Key,
                VHr = x.Any(t => t != null) ? x.Sum(t => t.VHr) : 0,
                VHrCost = x.Any(t => t != null) ? x.Sum(t => t.VHrCost) : 0,
            });

    }


    public async Task<object> GetProjectConsumedVHr(int ProjectID, DateTime From, DateTime To)
    {
        var _filters = new List<QueryFilter>
        {
            new QueryFilter { Key = "ProjectID", Value = ProjectID.ToString() },
            new QueryFilter { Key = "rangeStart", Value = From.ToString() },
            new QueryFilter { Key = "rangeEnd", Value = To.ToString() }
        };

        var _pkgs = await GetPackageVHrData(_filters);
        var _meets = await GetMeetingVHrData(_filters);
        var _insp = await GetInspectionVHrData(_filters);
        var _todos = await GetTodoVHrData(_filters);

        var PackageVHr = _pkgs.Any() ? _pkgs.FirstOrDefault().VHr : 0;
        var PackageVHrCost = _pkgs.Any() ? _pkgs.FirstOrDefault().VHrCost : 0;

        var MeetingVHr = _meets.Any() ? _meets.FirstOrDefault().VHr : 0;
        var MeetingVHrCost = _meets.Any() ? _meets.FirstOrDefault().VHrCost : 0;

        var InspectionVHr = _insp.Any() ? _insp.FirstOrDefault().VHr : 0;
        var InspectionVHrCost = _insp.Any() ? _insp.FirstOrDefault().VHrCost : 0;

        var TodoVHr = _todos.Any() ? _todos.FirstOrDefault().VHr : 0;
        var TodoVHrCost = _todos.Any() ? _todos.FirstOrDefault().VHrCost : 0;

        return new { VHr = PackageVHr + MeetingVHr + TodoVHr, VHrCost = PackageVHrCost + MeetingVHrCost + TodoVHrCost };

    }

    #region LastBite
    public async Task<IEnumerable<ProjectLastBite>> GetLastBiteData(IEnumerable<QueryFilter>? Filters = null, string? Search = null, string? Sort = null, bool ISTDates = false)
    {
        var sharedService = new SharedService(db); ;
        var _partnerShare = Convert.ToDecimal(await sharedService.GetPresetValue(McvConstant.PROJECT_PARTNER_SHARE));
        var _partnerShareBeforeApr21 = Convert.ToDecimal(await sharedService.GetPresetValue(McvConstant.PROJECT_PARTNER_SHARE_BEFORE_APR21));
        var _partnerSharePolicyStart = new DateTime(2021, 04, 01);

        var statusMasters = await db.StatusMasters.Where(s => s.Entity != null && s.Entity == nameof(Project))
          .Select(x => new
          {
              x.Title,
              x.Value
          })
          .ToListAsync();

        var _projects = await db.Projects.AsNoTracking()
               .Include(x => x.Associations).ThenInclude(c => c.Contact)
             .Select(x => new
             {
                 x.ID,
                 x.Title,
                 x.Code,
                 x.CompanyFee,
                 x.StatusFlag,
                 PartnerContactID = x.Associations.Where(t => t.TypeFlag == McvConstant.PROJECT_ASSOCIATION_TYPEFLAG_PARTNER).Any() ? x.Associations.Where(t => t.TypeFlag == 0).FirstOrDefault().ContactID : (int?)null,
                 Partner = x.Associations.Where(t => t.TypeFlag == McvConstant.PROJECT_ASSOCIATION_TYPEFLAG_PARTNER).Any() ? x.Associations.Where(t => t.TypeFlag == 0).FirstOrDefault().Contact.FullName : "N/A",
             })
               .ToListAsync();
        //.Where(x =>
        //x.StatusFlag != 4 //completed
        //&&
        //x.StatusFlag != -1)//discarded
        ;

        var _billPayments = await db.ProjectBillPayments.AsNoTracking()
                
            .Include(b => b.ProjectBill)
            .Where(x => !x.ProjectBill.IsDeleted)
            .Where(x => x.BillAmountReceived > 0)
            .Select(x => new
            {
                x.ProjectBill.ProjectID,
                PayableAmountPostTax = x.ProjectBill.Payable,
                BillAmount = x.ProjectBill.BillAmount,
                RecievedAmountPreTax = x.BillAmountReceived, //(x.Amount) * 100.0m / (x.Bill.TaxRate + 100.0m-(x.TDS/x.Bill.BillAmount)/100.0m),
                ReceivedAmountPostTax = x.Amount,
                x.TransactionDate,
                x.ProjectBill.BillNo,
                //x.Bill.BillDate,
                //BillAmount = x.Bill.BillAmount,
            }).ToListAsync();



        var _projectPayments = _billPayments
             .Join(_projects,
                 payment => payment.ProjectID,
                 project => project.ID,
                (payment, project) => new { payment, project })
             .Select(x => new
             {
                 ProjectID = x.project.ID,
                 Project = x.project.Title,
                 Code = x.project.Code,
                 x.project.PartnerContactID,
                 x.project.Partner,
                 TotalFee = x.project.CompanyFee,
                 Status = statusMasters.Where(s => s.Value == x.project.StatusFlag).Any() ?
                  statusMasters.Where(s => s.Value == x.project.StatusFlag).FirstOrDefault().Title : "UNDEFINED",
                 LastPayment = x.payment,
                 PreviousPayment = _billPayments.Where(p => p.ProjectID == x.project.ID
                                                       && p.TransactionDate < x.payment.TransactionDate)
                                                .Any() ?
                _billPayments.Where(p => p.ProjectID == x.project.ID
                                                      && p.TransactionDate < x.payment.TransactionDate)
                            .OrderByDescending(b => b.TransactionDate)
                            .FirstOrDefault() :
                 null,
                 IsPostAPR21 = !_billPayments.Where(p => p.ProjectID == x.project.ID
                                                       && p.TransactionDate < _partnerSharePolicyStart)
                                                .Any(),
             });
        //Apply filters

        //Apply filters
        if (Filters != null)
        {
            if (Filters.Where(x => x.Key.Equals("rangestart", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var _item = Filters.First(x => x.Key.Equals("rangestart", StringComparison.OrdinalIgnoreCase));

                DateTime result = Convert.ToDateTime(_item.Value);
                _projectPayments = _projectPayments.Where(x => x.LastPayment.TransactionDate >= result).ToList();
            }

            if (Filters.Where(x => x.Key.Equals("rangeend", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var _item = Filters.First(x => x.Key.Equals("rangeend", StringComparison.OrdinalIgnoreCase));

                DateTime result = Convert.ToDateTime(_item.Value);
                var end = result.AddDays(1);
                _projectPayments = _projectPayments.Where(x => x.LastPayment.TransactionDate < end).ToList();
            }

            if (Filters.Where(x => x.Key.Equals("contactID", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var _item = Filters.First(x => x.Key.Equals("contactID", StringComparison.OrdinalIgnoreCase));

                int result = Convert.ToInt32(_item.Value);
                _projectPayments = _projectPayments.Where(x => x.PartnerContactID != null && x.PartnerContactID == result).ToList();
            }
        }

        var _results = _projectPayments
            .Select(x => new ProjectLastBite
            {
                ProjectID = x.ProjectID,
                Project = x.Project,
                Code = x.Code,
                Status = x.Status,
                Partner = x.Partner,
                TotalFee = x.TotalFee,
                IsPostAPR21 = x.IsPostAPR21,
                LastReceivedPaymentDate = ISTDates ?
                  x.LastPayment.TransactionDate.AddMinutes(330).Date : x.LastPayment.TransactionDate,

                LastBillNo = x.LastPayment.BillNo,

                LastReceivedPaymentAmount = x.LastPayment.RecievedAmountPreTax,
                LastBillAmount = x.LastPayment.BillAmount,
                PayableAmountPostTax = x.LastPayment.PayableAmountPostTax,

                PreviousPaymentDate = x.PreviousPayment != null ? (ISTDates ?
                  x.PreviousPayment.TransactionDate.AddMinutes(330).Date : x.PreviousPayment.TransactionDate) : (DateTime?)null,
                PreviousPaymentAmount = x.PreviousPayment != null ? x.PreviousPayment.RecievedAmountPreTax : 0,
            }).ToList();



        if (Search != null && Search != String.Empty)
        {
            _results = _results
                  .Where(x => x.Project.ToLower().Contains(Search.ToLower())
                                      || x.Partner.ToLower().Contains(Search.ToLower())
                                    ).OrderByDescending(x => x.Project).ToList();
        }


        foreach (var obj in _results)
        {
            var _currentPeriodFilters = new List<QueryFilter>();
            _currentPeriodFilters.Add(new QueryFilter { Key = "ProjectID", Value = obj.ProjectID.ToString() });

            if (obj.PreviousPaymentDate != null)
            {
                _currentPeriodFilters.Add(new QueryFilter { Key = "rangeStart", Value = obj.PreviousPaymentDate.ToString() });
            }

            _currentPeriodFilters.Add(new QueryFilter { Key = "rangeEnd", Value = obj.LastReceivedPaymentDate.ToString() });

            var _pkgs = await GetPackageVHrData(_currentPeriodFilters, Search, Sort);
            var _meets = await GetMeetingVHrData(_currentPeriodFilters, Search, Sort);
            var _insp = await GetInspectionVHrData(_currentPeriodFilters, Search, Sort);
            var _todos = await GetTodoVHrData(_currentPeriodFilters, Search, Sort);

            obj.PackageVHr = _pkgs.Any() ? _pkgs.FirstOrDefault().VHr : 0;
            obj.PackageVHrCost = _pkgs.Any() ? _pkgs.FirstOrDefault().VHrCost : 0;

            obj.MeetingVHr = _meets.Any() ? _meets.FirstOrDefault().VHr : 0;
            obj.MeetingVHrCost = _meets.Any() ? _meets.FirstOrDefault().VHrCost : 0;

            obj.InspectionVHr = _insp.Any() ? _insp.FirstOrDefault().VHr : 0;
            obj.InspectionVHrCost = _insp.Any() ? _insp.FirstOrDefault().VHrCost : 0;

            obj.TodoVHr = _todos.Any() ? _todos.FirstOrDefault().VHr : 0;
            obj.TodoVHrCost = _todos.Any() ? _todos.FirstOrDefault().VHrCost : 0;

            //obj.VHr = (_pkgs.Any()?_pkgs.FirstOrDefault().VHr:0)
            //+ (_meets.Any()?_meets.FirstOrDefault().VHr:0)
            //+ (_todos.Any()?_todos.FirstOrDefault().VHr:0);

            //obj.VHrCost = (_pkgs.Any() ? _pkgs.FirstOrDefault().VHrCost : 0)
            //+ (_meets.Any() ? _meets.FirstOrDefault().VHrCost : 0)
            //+ (_todos.Any() ? _todos.FirstOrDefault().VHrCost : 0);

            if (obj.IsPostAPR21) //POST APRIL 2021
            {
                obj.PartnerShare = _partnerShare;
            }
            else
            {
                //Logic to keep Lastbite Upto10% only
                //Difference between PartnerShare(PostApr21)(40%) - VHrCostShare(x)
                // Logic verified on 10 Mar 2023 with HP
                var _difference = _partnerShare - obj.VHrShare;

                if (_difference >= _partnerShareBeforeApr21) // more than 10%
                {
                    obj.PartnerShare = _partnerShareBeforeApr21 + obj.VHrShare;
                }
                else //less than 10%
                {
                    obj.PartnerShare = _difference + obj.VHrShare;
                }
            }
        }

        return _results.OrderByDescending(x => x.LastReceivedPaymentDate);

    }


    public async Task<byte[]> GetLastBiteExcel(IEnumerable<QueryFilter>? Filters = null, string? Search = null, string? Sort = null)
    {

        var _dataSet = new DataSet();

        var _data = await GetLastBiteData(Filters, Search, Sort, true);
        _dataSet.Tables.Add(DataTools.ToDataTable(_data));
        //_dataSet.Tables.Add(DataTools.ToDataTable(_data.Select(x => new
        //{
        //    x.LastReceivedPaymentDate,
        //    x.PreviousPaymentDate,
        //    x.Project,
        //    x.Code,
        //    x.Status,
        //    x.Partner,
        //    x.TotalFee,
        //    x.LastReceivedPaymentAmount,
        //    x.CompanyShare,
        //    x.CompanyShareAmount,
        //    x.PartnerShare,
        //    x.PartnerShareAmount,
        //    x.VHrCost,
        //    x.VHrCostShare,
        //    x.LastBite,
        //    x.LastBiteShare,
        //    x.IsPostAPR21
        //})));

        return ExcelUtility.ExportExcel(_dataSet);

    }

    #endregion LastBite

    #region Report

    public async Task<ReportDefinition> GetProjectActivityReport(int ProjectID)
    {

        var _project = await db.Projects.AsNoTracking()
           .SingleOrDefaultAsync(x => x.ID == ProjectID);

        if (_project == null) throw new EntityServiceException("Project not found!");

        var textInfo = new CultureInfo("en-IN", false).TextInfo;
        var _projectName = _project.Code + "-" + textInfo.ToTitleCase(_project.Title.ToLower());
        var _reportProperties = new List<ReportProperties>();
        _reportProperties.Add(new ReportProperties() { PropertyName = "Project", PropertyValue = _projectName });
        _reportProperties.Add(new ReportProperties() { PropertyName = "Title", PropertyValue = "Project Activity" });
        _reportProperties.Add(new ReportProperties() { PropertyName = "Date", PropertyValue = ClockTools.GetISTNow().ToString("dd MMM yy HH:mm") });

        var _reportDef = new ReportDefinition()
        {
            ReportName = "ProjectActivity",
            ReportPath = @"https://nhub.blob.core.windows.net/rdlc/ProjectActivity.rdlc",
            ReportDataSet = DataTools.ToDataTable<ProjectActivity>(await GetProjectActivityData(ProjectID)),
            ReportProperties = _reportProperties,
            Filename = _projectName + "-" + "Project Activity",
        };
        var sharedService = new SharedService(db);

        var reportServiceApi = await sharedService.GetPresetValue(McvConstant.RDLC_PROCESSOR_API);
        return await ReportClient.GenerateReport(_reportDef, reportServiceApi);

    }

    public async Task<IEnumerable<ProjectActivity>> GetProjectActivityData(int ProjectID, IEnumerable<QueryFilter> Filters = null)
    {

        var _project = await Get()
          .SingleOrDefaultAsync(x => x.ID == ProjectID);

        if (_project == null) return null;

        IEnumerable<ProjectActivity> queryResult = new List<ProjectActivity>();
        TextInfo textInfo = new CultureInfo("en-IN", false).TextInfo;
        var _projectName = _project.Code.ToString() + "-" + textInfo.ToTitleCase(_project.Title.ToLower());

        var _bills = await db.ProjectBills.AsNoTracking()
            .Include(x => x.Payments)
            .Where(x => x.ProjectID == ProjectID
            && x.TypeFlag == 1)
            .Select(x => new ProjectActivity()
            {
                Head = "Tax Invoice",
                Date = x.BillDate,
                Title = x.BillNo,
                Person = x.ModifiedBy,
                Project = _projectName,
                DownloadUrl = "https://mycockpitview.in/newarch-Bills/Report/" + x.UID
            }).ToListAsync();

        var _proformas = await db.ProjectBills.AsNoTracking()
            .Where(x => x.ProjectID == ProjectID
            && x.TypeFlag == 0)
            .Select(x => new ProjectActivity()
            {
                Head = "Proforma",
                Date = x.BillDate,
                Title = Math.Round(x.WorkCompletion, 1).ToString() + "%", //+ | " + i.Payable.ToString("C", new CultureInfo("en-IN", false)),
                Person = x.ModifiedBy,
                Project = _projectName,
                DownloadUrl = "https://mycockpitview.in/newarch-Bills/Report/" + x.UID
            })
            .ToListAsync();

        queryResult = queryResult.Concat(_bills).Concat(_proformas);
        var sharedService = new SharedService(db); ;
        var _reportRoolUrl = await sharedService.GetPresetValue(McvConstant.MEETING_MINUTES_URL_ROOT);
        var _minutes = await db.Meetings.AsNoTracking()
            .Where(x => !x.IsVersion)
          .Include(x => x.Contact)
          .Where(x => x.ProjectID == ProjectID
          && x.StatusFlag == McvConstant.MEETING_AGENDA_STATUSFLAG_RESOLVED)
          .Select(x => new ProjectActivity()
          {
              Head = "Minutes",
              Date = x.StartDate,
              Title = x.Title,
              Person = x.Contact.FullName,
              Project = _projectName,
              DownloadUrl = _reportRoolUrl + x.UID.ToString()
          }).ToListAsync();

        queryResult = queryResult.Concat(_minutes);

        var _reportRootUrl = await sharedService.GetPresetValue(McvConstant.PACKAGE_SUBMISSION_URL_ROOT);
        var _submissions = await db.Packages.AsNoTracking()
           .Include(x => x.Associations)
           .Where(x => x.StatusFlag == McvConstant.PACKAGE_STATUSFLAG_SENT && x.TypeFlag == McvConstant.PACKAGE_TYPEFLAG_ACTIVE
           && x.ProjectID == ProjectID)
           .Select(x => new ProjectActivity()
           {
               Head = "Submission",
               Date = x.SubmissionDate.Value,
               Title = x.Title,
               Person = x.Associations.Where(c => !c.IsDeleted).Where(a => a.TypeFlag == McvConstant.PROJECT_ASSOCIATION_TYPEFLAG_PARTNER).FirstOrDefault().Contact.FullName,
               Project = _projectName,
               DownloadUrl = _reportRootUrl + x.ID.ToString(),
           }).ToListAsync();

        queryResult = queryResult.Concat(_submissions);

        if (Filters != null)
        {
            if (Filters.Where(x => x.Key.Equals("rangestart", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var _item = Filters.First(x => x.Key.Equals("rangestart", StringComparison.OrdinalIgnoreCase));
                DateTime result = Convert.ToDateTime(_item.Value);
                queryResult = queryResult.Where(x => x.Date >= result);
            }

            if (Filters.Where(x => x.Key.Equals("rangeend", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var _item = Filters.First(x => x.Key.Equals("rangeend", StringComparison.OrdinalIgnoreCase));

                DateTime result = Convert.ToDateTime(_item.Value);
                var end = result.AddDays(1);
                queryResult = queryResult.Where(x => x.Date < end);
            }
        }

        return queryResult.OrderByDescending(x => x.Date);

    }

    public async Task<ReportDefinition> GetProjectActivityWeeklyReport(IEnumerable<QueryFilter> Filters = null)
    {

        DateTime? _start = null;
        DateTime? _end = null;
        //Apply filters
        if (Filters != null)
        {
            if (Filters.Where(x => x.Key == "start").Any())
            {
                _start = Convert.ToDateTime(Filters.Where(x => x.Key == "start").FirstOrDefault().Value);
            }

            if (Filters.Where(x => x.Key == "end").Any())
            {
                _end = Convert.ToDateTime(Filters.Where(x => x.Key == "end").FirstOrDefault().Value);
            }
        }
        var _reportProperties = new List<ReportProperties>();
        _reportProperties.Add(new ReportProperties() { PropertyName = "Project", PropertyValue = "All" });
        _reportProperties.Add(new ReportProperties() { PropertyName = "Title", PropertyValue = "Project Activity" });
        _reportProperties.Add(new ReportProperties() { PropertyName = "Date", PropertyValue = _start.Value.ToString("dd MMM yy") + " - " + _end.Value.ToString("dd MMM yy") });

        var _reportDef = new ReportDefinition()
        {
            ReportName = "ProjectActivity",
            ReportPath = @"https://nhub.blob.core.windows.net/rdlc/ProjectActivity.rdlc",
            ReportDataSet = DataTools.ToDataTable<ProjectActivity>(await GetProjectActivityWeeklyData(Filters)),
            ReportProperties = _reportProperties,
            Filename = "Project Activity",
        };
        var sharedService = new SharedService(db);

        var reportServiceApi = await sharedService.GetPresetValue(McvConstant.RDLC_PROCESSOR_API);
        return await ReportClient.GenerateReport(_reportDef, reportServiceApi);

    }

    public async Task<byte[]> GetProjectActivityWeeklyExcel(IEnumerable<QueryFilter> Filters = null)
    {

        var _dataSet = new DataSet();
        _dataSet.Tables.Add(DataTools.ToDataTable<ProjectActivity>(await GetProjectActivityWeeklyData(Filters, true)));
        return ExcelUtility.ExportExcel(_dataSet);

    }

    public async Task<IEnumerable<ProjectActivity>> GetProjectActivityWeeklyData(IEnumerable<QueryFilter> Filters = null, bool ISTDates = false)
    {


        DateTime? _start = null;
        DateTime? _end = null;
        //Apply filters
        if (Filters != null)
        {
            if (Filters.Where(x => x.Key == "start").Any())
            {
                _start = Convert.ToDateTime(Filters.Where(x => x.Key == "start").FirstOrDefault().Value);
            }

            if (Filters.Where(x => x.Key == "end").Any())
            {
                _end = Convert.ToDateTime(Filters.Where(x => x.Key == "end").FirstOrDefault().Value);
            }
        }

        var result = new List<ProjectActivity>();

        var _submissions = db.Packages.AsNoTracking()
            .Where(x => x.StatusFlag != 0);

        if (_start != null)
        {
            var _dateValue = _start.Value.Date;
            _submissions = _submissions.Where(x => x.SubmissionDate >= _dateValue);
        }

        if (_end != null)
        {
            var _dateValue = _end.Value.Date.AddDays(1);
            _submissions = _submissions.Where(x => x.SubmissionDate < _dateValue);
        }

        TextInfo textInfo = new CultureInfo("en-IN", false).TextInfo;

        foreach (var i in await _submissions.ToListAsync())
        {
            var _project = await db.Projects.AsNoTracking()
        .SingleOrDefaultAsync(x => x.ID == i.ProjectID);

            var _projectName = textInfo.ToTitleCase(_project.Title.ToLower());

            result.Add(new ProjectActivity()
            {
                Head = "Submission",
                Date = ISTDates ? ClockTools.GetIST(i.SubmissionDate.Value) : i.SubmissionDate.Value,
                Title = i.Title,
                Person = i.ModifiedBy,
                Project = _projectName,
                DownloadUrl = ""
            });
        }

        var _meetings = db.Meetings.AsNoTracking()
        .Where(x => !x.IsVersion)
            .Where(x => x.StatusFlag == 3 && x.ProjectID != null);

        if (_start != null)
        {
            var _dateValue = _start.Value.Date;
            _meetings = _meetings.Where(x => x.StartDate >= _dateValue);
        }

        if (_end != null)
        {
            var _dateValue = _end.Value.Date.AddDays(1);
            _meetings = _meetings.Where(x => x.StartDate < _dateValue);
        }

        foreach (var i in await _meetings.ToListAsync())
        {
            var _project = await db.Projects.AsNoTracking()
           .SingleOrDefaultAsync(x => x.ID == i.ProjectID);

            var _projectName = textInfo.ToTitleCase(_project.Title.ToLower());

            result.Add(new ProjectActivity()
            {
                Head = nameof(Meeting),
                Date = ISTDates ? i.FinalizedOn.Value.AddMinutes(330) : i.FinalizedOn.Value,
                Title = i.Code,
                Person = i.ModifiedBy,
                Project = _projectName,
                DownloadUrl = ""
            });
        }

        var _bills = db.ProjectBills.AsNoTracking().Include(x => x.Payments)
            .Where(x => x.TypeFlag == 1 && x.Payments.Any());

        if (_start != null)
        {
            var _dateValue = _start.Value.Date;
            _bills = _bills.Where(x => x.BillDate >= _dateValue);
        }

        if (_end != null)
        {
            var _dateValue = _end.Value.Date.AddDays(1);
            _bills = _bills.Where(x => x.BillDate < _dateValue);
        }

        foreach (var i in await _bills.ToListAsync())
        {
            var _project = await db.Projects.AsNoTracking()
           .SingleOrDefaultAsync(x => x.ID == i.ProjectID);

            var _projectName = textInfo.ToTitleCase(_project.Title.ToLower());
            result.Add(new ProjectActivity()
            {
                Head = nameof(ProjectBill),
                Date = i.BillDate.Date,
                Title = i.BillNo,
                Person = i.ModifiedBy,
                Project = _projectName,
                DownloadUrl = ""
            });
        }

        var _proformas = db.ProjectBills.AsNoTracking()
            .Where(x => x.TypeFlag == 0);

        if (_start != null)
        {
            var _dateValue = _start.Value.Date;
            _proformas = _proformas.Where(x => x.BillDate >= _dateValue);
        }

        if (_end != null)
        {
            var _dateValue = _end.Value.Date.AddDays(1);
            _proformas = _proformas.Where(x => x.BillDate < _dateValue);
        }

        foreach (var i in await _proformas.ToListAsync())
        {
            var _project = await db.Projects.AsNoTracking()
           .SingleOrDefaultAsync(x => x.ID == i.ProjectID);

            var _projectName = textInfo.ToTitleCase(_project.Title.ToLower());
            result.Add(new ProjectActivity()
            {
                Head = "Proforma",
                Date = i.BillDate.Date,
                Title = Math.Round(i.WorkCompletion, 1).ToString() + "%",
                Person = i.ModifiedBy,
                Project = _projectName,
                DownloadUrl = ""
            });
        }

        return result;


    }

    #endregion Report



    #region CRM

    public async Task<IEnumerable<ProjectSummary>> GetCRMData(IEnumerable<QueryFilter>? Filters = null, string? Search = null, string? Sort = null)
    {

        var _meetings = db.Meetings.AsNoTracking()
            .Where(x => !x.IsVersion)
                                            
                                            .Where(x => x.StatusFlag > 1)
                                            .Where(x => x.ProjectID != null)
                                            .Select(x => new
                                            {
                                                ProjectID = x.ProjectID.Value,
                                                MeetingDate = x.StartDate
                                            });

        var _projectData = Get(Filters, Search, Sort)
                            .Include(x => x.Associations).ThenInclude(c => c.Contact)
                            .Where(x => x.StatusFlag != McvConstant.PROJECT_STATUSFLAG_COMPLETED //completed
                                     && x.StatusFlag != McvConstant.PROJECT_STATUSFLAG_INQUIRY //inquiry
                                     && x.StatusFlag != McvConstant.PROJECT_STATUSFLAG_DISCARD)//discarded
                            .Select(x => new
                            {
                                ProjectID = x.ID,
                                ProjectUID = x.UID,
                                Project = x.Title,
                                Code = x.Code,

                                Partner = x.Associations.Where(t => t.TypeFlag == 0).Any() ? x.Associations.Where(t => t.TypeFlag == 0).FirstOrDefault().Contact.FullName : "N/A",

                                TotalFee = x.CompanyFee,
                                StatusFlag = x.StatusFlag,
                                Status = db.StatusMasters.Where(s => s.Entity != null && s.Entity==nameof(Project) && s.Value == x.StatusFlag).Any() ?
                              db.StatusMasters.Where(s => s.Entity != null && s.Entity==nameof(Project) && s.Value == x.StatusFlag).FirstOrDefault().Title : "UNDEFINED",

                                LastMeetingDate = _meetings.Where(b => b.ProjectID == x.ID).Any() ?
                                               _meetings.Where(b => b.ProjectID == x.ID)
                                                            .OrderByDescending(p => p.MeetingDate)
                                                            .FirstOrDefault().MeetingDate :
                                                x.Created,
                            });

        var _query = await _projectData
            .OrderBy(x => x.LastMeetingDate)
            .ThenBy(x => x.Project)
            .ToListAsync();

        var _result = _query.Select(x => new ProjectSummary
        {
            ProjectID = x.ProjectID,
            ProjectUID = x.ProjectUID,
            Project = x.Project,
            Code = x.Code,
            StatusFlag = x.StatusFlag,
            Status = x.Status,
            Partner = x.Partner,
            CompanyFee = x.TotalFee,
            LastMeetingDate = x.LastMeetingDate,
        });

        if (Sort != null && Sort != String.Empty)
        {
            var _orderedQuery = _result.OrderBy(l => 0);
            var keywords = Sort.Replace("asc", "").Split(',');

            foreach (var key in keywords)
            {
                if (key.Trim().Equals("LastMeetingDate", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenBy(x => x.LastMeetingDate);
                else if (key.Trim().Equals("LastMeetingDate desc", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenByDescending(x => x.LastMeetingDate);
                else if (key.Trim().Equals("Interval", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenBy(x => x.LastMeetingIntervalInDays);
                else if (key.Trim().Equals("Interval desc", StringComparison.OrdinalIgnoreCase))
                    _orderedQuery = _orderedQuery
                            .ThenByDescending(x => x.LastMeetingIntervalInDays);
            }

            return _orderedQuery;
        }

        return _result;

    }

    public async Task<byte[]> GetCRMExcel(IEnumerable<QueryFilter>? Filters = null, string? Search = null, string? Sort = null)
    {
        var _dataSet = new DataSet();

        var _data = await GetCRMData(Filters, Search, Sort);

        _dataSet.Tables.Add(DataTools.ToDataTable(_data.Select(x => new
        {
            x.ProjectID,
            x.Project,
            x.ProjectUID,
            x.Code,
            x.Status,
            x.Partner,
            x.CompanyFee,
            LastMeetingDate = ClockTools.GetIST(x.LastMeetingDate.Value),
            x.LastMeetingIntervalInDays
        })));

        return ExcelUtility.ExportExcel(_dataSet);

    }

    #endregion CRM


    public async Task<dynamic> GetTaskByStage(string Entity, int EntityID, string StageCode, string StageTitle, decimal StageDuration, DateTime? FollowUpDate = null)
    {

        //if (StageCode == "INQUIRY_DRAFT_OFFER" ||StageCode == "INQUIRY_DRAFT_REVIEW" ||StageCode == "INQUIRY_FINAL_REVIEW" ||StageCode == "INQUIRY_FINAL_OFFER" ||StageCode == "INQUIRY_OFFER_SUBMISSION" ||StageCode == "INQUIRY_OFFER_FOLLOW_UP")

        var sharedService = new SharedService(db); ;

        var _entity = await Get()
     .SingleOrDefaultAsync(x => x.ID == EntityID);

        if (_entity == null) throw new EntityServiceException($"{nameof(Project)} not found!");
        var _startTimeSpan = TimeSpan.Parse(await sharedService.GetPresetValue(McvConstant.OFFICE_OPEN_TIME));

        var _endTimeSpan = TimeSpan.Parse(await sharedService.GetPresetValue(McvConstant.OFFICE_CLOSE_TIME));

        var _nextDue = DateTime.UtcNow.AddDays(Decimal.ToDouble(StageDuration)).Date;

        if (FollowUpDate != null)
            _nextDue = FollowUpDate.Value.Date;

        return new
        {
            Title = StageTitle,
            Entity = Entity,
            EntityID = EntityID,
            Subtitle = $"{_entity.Code}-{_entity.Title}",
            WFStageCode = StageCode,
            StartDate = ClockTools.GetUTC(ClockTools.GetISTNow().Date
                    .AddMinutes(_startTimeSpan.TotalMinutes)),
            DueDate = ClockTools.GetUTC(ClockTools.GetIST(_nextDue).Date
                .AddMinutes(_endTimeSpan.TotalMinutes)),
            MHrAssigned = 0,
            IsPreAssignedTimeTask = false
        };

    }

    public async Task TaskAction(int EntityID, string StageCode, string taskComment = null)
    {
        var Entity = await Get()
                      .SingleOrDefaultAsync(x => x.ID == EntityID);

        if (Entity == null) throw new EntityServiceException($"{nameof(Project)} not found!");

        if (StageCode == "SYS_INQUIRY_PROJECT")
        {
            Entity.StatusFlag = McvConstant.PROJECT_STATUSFLAG_INPROGRESS; //project
            Entity.InquiryConvertionDate = DateTime.UtcNow;
        }
        else if (StageCode == "SYS_INQUIRY_DISCARD")
        {
            Entity.StatusFlag = McvConstant.PROJECT_STATUSFLAG_DISCARD; //discard
            Entity.InquiryConvertionDate = DateTime.UtcNow;
        }
        else if (StageCode == "SYS_INQUIRY_LOST")
        {
            Entity.StatusFlag = McvConstant.PROJECT_STATUSFLAG_LOST;
            Entity.InquiryConvertionDate = DateTime.UtcNow;

        }
        else if (StageCode == "SYS_INQUIRY_PRE_PROPOSAL")
        {

            Entity.StatusFlag = McvConstant.PROJECT_STATUSFLAG_PREPOPOSAL; //preproposal

        }

        Entity.Comment = taskComment;
        db.Entry(Entity).State = EntityState.Modified;
    }


    public async Task ResumeTaskFlow()
    {

        var _inquires = await Get()
             .Where(x => x.StatusFlag == McvConstant.PROJECT_STATUSFLAG_INQUIRY).ToListAsync();


        var taskService = new WFTaskService(db);
        foreach (var Entity in _inquires)
        {
            var _task = await taskService.Get()
                .Where(x => x.Entity == nameof(Project) && x.EntityID == Entity.ID).AnyAsync();

            if (!_task)
            {
                await taskService.StartFlow(nameof(Project), 0, Entity.ID);

            }
        }

    }

    public async Task RecordHistory(int ProjectID)
    {
        var filters = new List<QueryFilter> {
            new QueryFilter { Key="ProjectID", Value= ProjectID.ToString() }
        };

        var summary = await GetCashflowData(filters);
        if (!summary.Any())
        {
            return;
        }

        var histories = summary.Select(x => new ProjectHistory
        {
            ProjectID = x.ProjectID,
            ProjectUID = x.ProjectUID,
            Project = x.Project,
            Code = x.Code,
            StatusFlag = x.StatusFlag,
            Status = x.Status,
            Partner = x.Partner,
            TotalFee = x.CompanyFee,
            VHr = x.VHr,
            VHrCost = x.VHrCost,
            VHrAfterLastPayment = x.VHrAfterLastPayment,
            VHrCostAfterLastPayment = x.VHrCostAfterLastPayment,
            LastPaymentDate = x.LastPaymentDate,
            LastBillAmount = x.LastBillAmount,
            LastPaymentAmount = x.LastPaymentAmount,
            TotalBillAmount = x.TotalBillAmount,
            TotalPaymentAmount = x.TotalPaymentAmount,
            TotalBillPercentage = x.TotalBillPercentage,

            TotalPaymentPercentage = x.TotalPaymentPercentage,
            IsPostAPR21 = x.IsPostAPR21,
            PartnerShare = x.PartnerShare,
            PartnerShareAmount = x.PartnerShareAmount,

            CompanyShare = x.CompanyShare,
            CompanyShareAmount = x.CompanyShareAmount,

            LastBite = x.LastBite,
            VHrCostShare = x.VHrCostShare,
            LastBiteShare = x.LastBiteShare,
            VHrCostPercentage = x.VHrCostPercentage,
            KPI = x.KPI,

            XCost = x.XCost,
            XCostPercentage = x.XCostPercentage,
            ProformaPercentage = x.ProformaPercentage,
            ProformaAmount = x.ProformaAmount,
            ProformaDate = x.ProformaDate,

            CompletedPercentage = x.CompletedPercentage,
            ActivePercentage = x.ActivePercentage,
            ProposedPercentage = x.ProformaPercentage,

            CompletedAmount = x.CompletedPercentage,
            ActiveAmount = x.ActiveAmount,
            ProposedAmount = x.ProformaAmount,
            LastMeetingDate = x.LastMeetingDate,
            LastMeetingIntervalInDays = x.LastMeetingIntervalInDays,
            RecordedDate=DateTime.UtcNow

        }).ToList();

        db.ProjectHistories.Add(histories.First());

        await db.SaveChangesAsync();
    }

}

public class ProjectAnalysis
{
    public int ID { get; set; }
    public string? Company { get; set; }
    public string? Code { get; set; }
    public string? Title { get; set; }
    public string? BillingTitle { get; set; }
    public string? Partner { get; set; }
    public string? Associate { get; set; }
    public string? Client { get; set; }
    public string? ClientType { get; set; }
    public bool IsRepeatClient { get; set; } = false;
    public string? Location { get; set; }
    public string? Status { get; set; }
    public DateTime CreatedDate { get; set; }
    public DateTime? InquiryConvertionDate { get; set; }
    public DateTime? InquiryLastActionDate { get; set; }
    public string? InquiryLastComments { get; set; }
    public string? InquiryLastStatus { get; set; }
    public string? InquiryLastActionBy { get; set; }
    public DateTime? ContractCompletionDate { get; set; }
    public DateTime? ExpectedCompletionDate { get; set; }
    public DateTime? OfferDueDate { get; set; }
    public decimal Fee { get; set; }
    public decimal RecievedPercentage { get; set; }
    public decimal RecievedAmount { get; set; }
    public DateTime? LastRecievedDate { get; set; }
    public decimal PendingPercentage { get; set; }
    public decimal PendingAmount { get; set; }

    public decimal ProformaPercentage { get; set; }
    public decimal ProformaAmount { get; set; }
    public decimal VHr { get; set; }
    public decimal VHrCost { get; set; }
    public int ConvertedInDays { get { return InquiryConvertionDate != null ? (InquiryConvertionDate.Value - CreatedDate).Days : 0; } }
}

public class ProjectSummary
{
    public int ProjectID { get; set; }
    public Guid ProjectUID { get; set; }
    public string? Project { get; set; }
    public string? Code { get; set; }
    public int StatusFlag { get; set; }
    public string? Status { get; set; }
    public string? Partner { get; set; }
    public decimal TotalFee { get; set; }
    public decimal CompanyFee { get; set; }
    public decimal VHr { get; set; } = 0;
    public decimal VHrCost { get; set; } = 0;
    public decimal VHrAfterLastPayment { get; set; } = 0;
    public decimal VHrCostAfterLastPayment { get; set; } = 0;
    public DateTime? LastPaymentDate { get; set; }
    public decimal LastBillAmount { get; set; }
    public decimal LastPaymentAmount { get; set; }
    public decimal TotalBillAmount { get; set; }
    public decimal TotalPaymentAmount { get; set; }
    public decimal TotalBillPercentage
    {
        get
        {
            return CompanyFee != 0 ? TotalBillAmount / CompanyFee * 100.0m : 100.0m;

        }
    }

    public decimal TotalPaymentPercentage
    {
        get
        {
            return CompanyFee != 0 ? TotalPaymentAmount / CompanyFee * 100.0m : 100.0m;

        }
    }
    public bool IsPostAPR21 { get; set; }
    public decimal PartnerShare { get; set; }
    public decimal PartnerShareAmount
    {
        get
        {
            return TotalBillAmount * PartnerShare / 100.0m;
        }
    }

    public decimal CompanyShare
    {
        get
        {
            return 100 - PartnerShare;
        }
    }
    public decimal CompanyShareAmount
    {
        get
        {
            return TotalBillAmount * CompanyShare / 100.0m;
        }
    }

    public decimal LastBite
    {
        get
        {
            return PartnerShareAmount - VHrCost;
        }
    }
    public decimal VHrCostShare
    {
        get
        {
            return TotalBillAmount != 0 ? VHrCost / TotalBillAmount * 100.0m : 100m;
        }
    }
    public decimal LastBiteShare
    {
        get
        {
            return TotalBillAmount != 0 ? LastBite / TotalBillAmount * 100.0m : 100m;
        }
    }



    public decimal VHrCostPercentage
    {
        get
        {
            return CompanyFee != 0 ? VHrCost / CompanyFee * 100.0m : 100m;

        }
    }

    public decimal KPI
    {
        get
        {
            return VHrCostPercentage != 0 ? TotalPaymentPercentage / VHrCostPercentage : 1;

        }
    }

    public decimal XCost { get; set; }
    public decimal XCostPercentage { get; set; }
    public decimal ProformaPercentage
    {
        get
        {
            return CompanyFee != 0 ? ProformaAmount / CompanyFee * 100.0m : 100.0m;

        }
    }
    public decimal ProformaAmount { get; set; }
    public DateTime? ProformaDate { get; set; }

    public decimal CompletedPercentage { get; set; }
    public decimal ActivePercentage { get; set; }
    public decimal ProposedPercentage { get; set; }

    public decimal CompletedAmount
    {
        get
        {
            return CompletedPercentage / 100 * CompanyFee;
        }
    }
    public decimal ActiveAmount
    {
        get
        {
            return ActivePercentage / 100 * CompanyFee;
        }
    }
    public decimal ProposedAmount
    {
        get
        {
            return ProposedPercentage / 100 * CompanyFee;
        }
    }
    public DateTime? LastMeetingDate { get; set; }
    public decimal LastMeetingIntervalInDays
    {
        get
        {
            return LastMeetingDate != null ? Math.Floor(Convert.ToDecimal((DateTime.UtcNow - LastMeetingDate.Value).TotalDays)) : 0;
        }
    }
}

public class ProjectActivity
{
    public string? Project { get; set; }
    public string? Head { get; set; }
    public string? Title { get; set; }
    public DateTime Date { get; set; }
    public string? Person { get; set; }
    public string? DownloadUrl { get; set; }
}

public class ProjectLastBite
{
    public int ProjectID { get; set; }
    public string? Project { get; set; }
    public string? Code { get; set; }
    public string? Status { get; set; }
    public string? Partner { get; set; }
    public decimal TotalFee { get; set; }
    public DateTime? PreviousPaymentDate { get; set; }
    public decimal PreviousPaymentAmount { get; set; } = 0;

    public DateTime LastReceivedPaymentDate { get; set; }
    public decimal LastReceivedPaymentAmount { get; set; } = 0;
    public string? LastBillNo { get; set; }
    public decimal LastBillAmount { get; set; }
    public decimal PayableAmountPostTax { get; set; }
    public bool IsPostAPR21 { get; set; }
    public decimal PartnerShare { get; set; } = 0;
    public decimal PartnerAmount
    {
        get
        {
            return LastReceivedPaymentAmount * PartnerShare / 100.0m;
        }
    }

    public decimal CompanyShare
    {
        get
        {
            return 100 - PartnerShare;
        }
    }
    public decimal CompanyAmount
    {
        get
        {
            return LastReceivedPaymentAmount * CompanyShare / 100.0m;
        }
    }
    public decimal PackageVHr { get; set; }

    public decimal PackageVHrCost { get; set; }

    public decimal MeetingVHr { get; set; }

    public decimal MeetingVHrCost { get; set; }
    public decimal InspectionVHr { get; set; }

    public decimal InspectionVHrCost { get; set; }
    public decimal TodoVHr { get; set; }

    public decimal TodoVHrCost { get; set; }
    public decimal VHr
    {
        get
        {
            return PackageVHr + MeetingVHr + TodoVHr;
        }
    }

    public decimal VHrShare
    {
        get
        {
            return LastReceivedPaymentAmount != 0 ? VHrCost / LastReceivedPaymentAmount * 100.0m : 0;
        }
    }
    public decimal VHrCost
    {
        get
        {
            return PackageVHrCost + MeetingVHrCost + TodoVHrCost;
        }
    }

    public decimal LastBiteShare
    {
        get
        {
            return LastReceivedPaymentAmount != 0 ? LastBiteAmount / LastReceivedPaymentAmount * 100.0m : 0;
        }
    }
    public decimal LastBiteAmount
    {
        get
        {
            return PartnerAmount - VHrCost;
        }
    }





}

public class ProjectEstimation
{
    public int ID { get; set; }
    public string UID { get; set; }
    public string Title { get; set; }
    public string Code { get; set; }
    public string Partner { get; set; }
    public decimal CompanyFee { get; set; }
    public int StatusFlag { get; set; }
    public string Status { get; set; }
    public decimal SpaceAmount { get; set; }
    public decimal ElementAmount { get; set; }
    public decimal BOQAmount { get; set; }
}