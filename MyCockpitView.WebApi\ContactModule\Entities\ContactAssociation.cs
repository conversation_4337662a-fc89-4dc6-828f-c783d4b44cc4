﻿using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.CoreModule;

using System.ComponentModel.DataAnnotations;

namespace MyCockpitView.WebApi.ContactModule.Entities;

public class ContactAssociation : BaseEntity
{
    public int PersonContactID { get; set; }
    public int CompanyContactID { get; set; }
    public virtual Contact? Person { get; set; }
    public virtual Contact? Company { get; set; }

    [StringLength(255)]
    public string? Department { get; set; }
    [StringLength(255)]
    public string? Designation { get; set; }
}

public class ContactAssociationConfiguration : BaseEntityConfiguration<ContactAssociation>, IEntityTypeConfiguration<ContactAssociation>
{
public void Configure(EntityTypeBuilder<ContactAssociation> builder)
{
        base.Configure(builder);

        builder
           .HasOne(u => u.Person)
            .WithMany(c => c.AssociatedCompanies)
            .HasForeignKey(x => x.PersonContactID)
             .OnDelete(DeleteBehavior.Restrict).IsRequired();

        builder
              .HasOne(u => u.Company)
              .WithMany(c => c.AssociatedContacts)
              .HasForeignKey(x => x.CompanyContactID)
               .OnDelete(DeleteBehavior.Restrict).IsRequired();

    }
}