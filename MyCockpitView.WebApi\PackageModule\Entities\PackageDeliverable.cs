﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using MyCockpitView.CoreModule;
using System.ComponentModel.DataAnnotations;

namespace MyCockpitView.WebApi.PackageModule.Entities
{
    public class PackageDeliverable : BaseEntity
    {
        [StringLength(255)]
        public string?  Title { get; set; }

        [StringLength(255)]
        public string? Category { get; set; }


        [StringLength(255)]
        public string? Code { get; set; }


        [StringLength(255)]
        public string? StageService { get; set; }
        public int PackageID { get; set; }
        public virtual Package? Package { get; set; }

        public virtual ICollection<PackageDeliverableTaskMap> PackageDeliverableTaskMaps { get; set; } = new List<PackageDeliverableTaskMap>();
    }

    public class PackageDeliverableConfiguration : BaseEntityConfiguration<PackageDeliverable>, IEntityTypeConfiguration<PackageDeliverable>
    {
        public void Configure(EntityTypeBuilder<PackageDeliverable> builder)
        {
            base.Configure(builder);
            builder.HasIndex(x => x.Title);
            builder.HasIndex(x => x.Category);
            builder.HasIndex(x => x.Code);
            builder.HasIndex(x => x.StageService);
        }
    }
}
