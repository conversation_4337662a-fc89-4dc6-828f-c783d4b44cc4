﻿using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.CoreModule;

namespace MyCockpitView.WebApi.WFTaskModule.Entities;

public class TaskRequest : BaseEntity
{
    public decimal OriginalMHr { get; set; } = 0;

    public decimal RequestedMHr { get; set; } = 0;

    public DateTime? OriginalDueDate { get; set; }

    public DateTime? RequestedDueDate { get; set; }

    public string? Comment { get; set; }
    public string? RequestMessage { get; set; }

    public int WFTaskID { get; set; }
    public virtual WFTask? WFTask { get; set; }
}


public class TaskRequestConfiguration : BaseEntityConfiguration<TaskRequest>, IEntityTypeConfiguration<TaskRequest>
{
    public void Configure(EntityTypeBuilder<TaskRequest> builder)
    {
        base.Configure(builder);

    }
}