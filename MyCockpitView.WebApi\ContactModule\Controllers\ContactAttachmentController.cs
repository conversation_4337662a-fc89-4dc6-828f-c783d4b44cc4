﻿
using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.WebApi.Responses;
using MyCockpitView.WebApi.ContactModule.Dtos;
using MyCockpitView.WebApi.ContactModule.Entities;
using MyCockpitView.WebApi.ContactModule.Services;
using MyCockpitView.Utility.Common;
using MyCockpitView.WebApi.ActivityModule;
using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.Services;

namespace MyCockpitView.WebApi.ContactModule.Controllers;

[Authorize]
[ApiController]
[Route("[controller]")]
public class ContactAttachmentController : ControllerBase
{
    private readonly IContactAttachmentService service;
    private readonly EntitiesContext db;
    private readonly IActivityService activityService;
    private readonly IMapper mapper;
    private readonly IContactService contactService;
    private readonly ICurrentUserService currentUserService;

    public ContactAttachmentController(
        EntitiesContext db,
        IContactAttachmentService service,
        IMapper mapper,
        IActivityService activityService,
        IContactService contactService,
        ICurrentUserService currentUserService)
    {
        this.db = db;
        this.service = service;
        this.activityService = activityService;
        this.mapper = mapper;
        this.contactService = contactService;
        this.currentUserService = currentUserService;
    }

    [HttpGet]
    public async Task<ActionResult<IEnumerable<ContactAttachmentDto>>> Get(
        [FromQuery] string? filters = null,
        [FromQuery] string? search = null,
        [FromQuery] string? sort = null)
    {
        var query = service.Get(filters != null ? DataTools.GetObjectFromJsonString<APIFilter>(filters).Filters : null, search, sort);
        var results = mapper.Map<IEnumerable<ContactAttachmentDto>>(await query.ToListAsync());

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(ContactAttachment))
            .ToListAsync();

        foreach (var obj in results)
        {
            obj.Type = typeMasters.FirstOrDefault(x => x.Value == obj.TypeFlag)?.Title ?? "";
        }

        var statusMasters = await db.StatusMasters
          .AsNoTracking()
          .Where(x => x.Entity == nameof(ContactAttachment))
          .ToListAsync();

        foreach (var obj in results)
        {
            obj.Status = statusMasters.FirstOrDefault(x => x.Value == obj.StatusFlag)?.Title ?? "";
        }

        return Ok(results);
    }

    [HttpGet("Pages")]
    public async Task<ActionResult<PagedResponse<ContactAttachmentDto>>> GetPages(
        [FromQuery] int page = 0,
        [FromQuery] int pageSize = 50,
        [FromQuery] string? filters = null,
        [FromQuery] string? search = null,
        [FromQuery] string? sort = null)
    {
        var query = service.Get(filters != null ? DataTools.GetObjectFromJsonString<APIFilter>(filters).Filters : null, search, sort);

        var totalCount = await query.CountAsync();
        var totalPages = (int)Math.Ceiling((decimal)totalCount / pageSize);
        var results = mapper.Map<IEnumerable<ContactAttachmentDto>>(await query
            .Skip(pageSize * page)
            .Take(pageSize)
            .ToListAsync());

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(ContactAttachment))
            .ToListAsync();

        foreach (var obj in results)
        {
            obj.Type = typeMasters.FirstOrDefault(x => x.Value == obj.TypeFlag)?.Title ?? "";
        }

        var statusMasters = await db.StatusMasters
           .AsNoTracking()
           .Where(x => x.Entity == nameof(ContactAttachment))
           .ToListAsync();

        foreach (var obj in results)
        {
            obj.Status = statusMasters.FirstOrDefault(x => x.Value == obj.StatusFlag)?.Title ?? "";
        }

        return Ok(new PagedResponse<ContactAttachmentDto>(results, totalCount, totalPages));
    }

    [HttpGet("{id:int}")]
    public async Task<ActionResult<ContactAttachmentDto>> GetByID(int id)
    {
        var responseDto = mapper.Map<ContactAttachmentDto>(await service.GetById(id));
        if (responseDto == null)
        {
            return NotFound();
        }

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(ContactAttachment))
            .ToListAsync();

        responseDto.Type = typeMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        var statusMasters = await db.StatusMasters
          .AsNoTracking()
          .Where(x => x.Entity == nameof(ContactAttachment))
          .ToListAsync();

        responseDto.Status = statusMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        return Ok(responseDto);
    }

    [HttpPost]
    public async Task<ActionResult<ContactAttachmentDto>> Post(ContactAttachmentDto dto)
    {
        var id = await service.Create(mapper.Map<ContactAttachment>(dto));
        var responseDto = mapper.Map<ContactAttachmentDto>(await service.GetById(id));

        if (responseDto == null)
            return BadRequest("Not Created");

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(ContactAttachment))
            .ToListAsync();

        responseDto.Type = typeMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        var statusMasters = await db.StatusMasters
  .AsNoTracking()
  .Where(x => x.Entity == nameof(ContactAttachment))
  .ToListAsync();

        responseDto.Status = statusMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        var username = currentUserService.GetCurrentUsername();
        if (!string.IsNullOrEmpty(username))
        {
            var currentContact = await contactService.Get().FirstOrDefaultAsync(x => x.Username == username);
            if (currentContact != null)
            {
                var parent = await contactService.Get().SingleOrDefaultAsync(x => x.ID == responseDto.ContactID);
                if (parent != null)
                {
                    await activityService.LogUserActivity(currentContact, nameof(parent), parent.ID, $"{parent.Name}", $"{nameof(ContactAttachment).Replace(nameof(parent),"")} | {responseDto.Filename}", "Uploaded");
                }
            }
        }

        return CreatedAtAction(nameof(GetByID), new { id = responseDto.ID }, responseDto);
    }

    [HttpPut("{id:int}")]
    public async Task<ActionResult<ContactAttachmentDto>> Put(int id, ContactAttachmentDto dto)
    {
        if (id != dto.ID)
        {
            return BadRequest();
        }

        await service.Update(mapper.Map<ContactAttachment>(dto));
        var responseDto = mapper.Map<ContactAttachmentDto>(await service.GetById(id));

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(ContactAttachment))
            .ToListAsync();

        responseDto.Type = typeMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        var statusMasters = await db.StatusMasters
  .AsNoTracking()
  .Where(x => x.Entity == nameof(ContactAttachment))
  .ToListAsync();

        responseDto.Status = statusMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        var username = currentUserService.GetCurrentUsername();
        if (!string.IsNullOrEmpty(username))
        {
            var currentContact = await contactService.Get().FirstOrDefaultAsync(x => x.Username == username);
            if (currentContact != null)
            {
                var parent = await contactService.Get().SingleOrDefaultAsync(x => x.ID == responseDto.ContactID);
                if (parent != null)
                {
                    await activityService.LogUserActivity(currentContact, nameof(parent), parent.ID, $"{parent.Name}", $"{nameof(ContactAttachment).Replace(nameof(parent), "")} | {responseDto.Filename}", "Updated");
                }
            }
        }

        return Ok(responseDto);
    }

    [HttpDelete("{id:int}")]
    public async Task<IActionResult> Delete(int id)
    {
        var responseDto = mapper.Map<ContactAttachmentDto>(await service.GetById(id));
        if (responseDto == null) return BadRequest($"{nameof(ContactAttachment)} not found!");

        await service.Delete(id);

        var username = currentUserService.GetCurrentUsername();
        if (!string.IsNullOrEmpty(username))
        {
            var currentContact = await contactService.Get().FirstOrDefaultAsync(x => x.Username == username);
            if (currentContact != null)
            {
                var parent = await contactService.Get().SingleOrDefaultAsync(x => x.ID == responseDto.ContactID);
                if (parent != null)
                {
                    await activityService.LogUserActivity(currentContact, nameof(parent), parent.ID, $"{parent.Name}", $"{nameof(ContactAttachment).Replace(nameof(parent), "")} | {responseDto.Filename}", "Deleted");
                }
            }
        }

        return NoContent();
    }


    [HttpGet("uid/{id:guid}")]
    [AllowAnonymous]
    public async Task<ActionResult<ContactAttachmentDto>> GetByGUID(Guid id)
    {
        var responseDto = mapper.Map<ContactAttachmentDto>(await service.GetById(id));
        if (responseDto == null)
        {
            return NotFound();
        }

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(ContactAttachment))
            .ToListAsync();

        responseDto.Type = typeMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        var statusMasters = await db.StatusMasters
                        .AsNoTracking()
                        .Where(x => x.Entity == nameof(ContactAttachment))
                        .ToListAsync();

        responseDto.Status = statusMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        return Ok(responseDto);
    }


    [HttpGet("SearchTagOptions")]
    public async Task<IActionResult> GetSearchTagOptions()
    {
        var results = await service.GetSearchTagOptions();
        return Ok(results);
    }


    [HttpGet("FieldOptions")]
    public async Task<IActionResult> GetFieldOptions(string field)
    {
        return Ok(await service.GetFieldOptions(field));
    }


}