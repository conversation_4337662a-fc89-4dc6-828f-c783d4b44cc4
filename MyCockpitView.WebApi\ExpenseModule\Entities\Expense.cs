﻿using Microsoft.EntityFrameworkCore;
using MyCockpitView.WebApi.ContactModule.Entities;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.CompanyModule.Entities;
using MyCockpitView.Utility.Common;
using MyCockpitView.WebApi.ProjectModule.Entities;

namespace MyCockpitView.WebApi.ExpenseModule.Entities;

public class Expense : BaseEntity
{
    [Required]
    public DateTime ExpenseDate { get; set; }

    [Required]
    [StringLength(50)]
    public string? Code { get; set; }

    [Required]
    [StringLength(255)]
    public string? Particulars { get; set; }

    public string? Narration { get; set; }


    [Required]
    [StringLength(255)]
    public string? ExpenseHead { get; set; }

    public DateTime? TransactionDate { get; set; }
    [StringLength(255)]
    public string? TransactionMode { get; set; }

    [StringLength(255)]
    public string? TransactionRefNo { get; set; }

    public string? TransactionDetails { get; set; }

    [StringLength(255)]
    public string? PayTo { get; set; }

    public int? PayToContactID { get; set; }


    [StringLength(255)]
    public string? ApprovedBy { get; set; }

    public int? ApprovedByContactID { get; set; }
    public virtual Contact? ApprovedByContact { get; set; }

    public int? ProjectID { get; set; }


    [Required]
    public int CompanyID { get; set; }
    public virtual Company? Company { get; set; }

    [StringLength(255)]
    public string? PAN { get; set; }

    [StringLength(255)]
    public string? TAN { get; set; }

    [StringLength(255)]
    public string? GST { get; set; }


    [StringLength(255)]
    public string? InvoiceNumber { get; set; }

    public virtual ICollection<ExpenseAttachment> Attachments { get; set; }=new List<ExpenseAttachment>();


    [StringLength(255)]

    public string? Entity { get; set; }


    public int? EntityID { get; set; }

    [StringLength(255)]
    public string? EntityTitle { get; set; }
    [Precision(14, 2)]
    public decimal ExpenseAmount { get; set; } = 0;
    [Precision(14, 2)]
    public decimal AmountAfterTax { get; set; } = 0;
    [Precision(14, 2)]
    public decimal TDSAmount { get; set; } = 0;
    [Precision(14, 2)]
    public decimal TDSPercentage { get; set; } = 0;
    [Precision(14, 2)]

    public decimal AmountDr { get; set; } = 0;
    [Precision(14, 2)]

    public decimal AmountCr { get; set; } = 0;
    [Precision(14, 2)]
    public decimal AmountBalance { get; set; } = 0;

    [Column("Taxes")]
    public string? _taxes { get; set; }

    [NotMapped]
    public ICollection<Tax> Taxes
    {
        get
        {
            return _taxes != null && _taxes != string.Empty ?
                DataTools.GetObjectFromJsonString<ICollection<Tax>>(_taxes) :
                new List<Tax>();
        }
        set
        {
            _taxes = DataTools.GetJsonStringFromObject(value);
        }
    }

}

public class Tax
{
    public decimal Amount { get; set; }
    public decimal Percentage { get; set; }
    public string? TaxTitle { get; set; }
}

public class ExpenseConfiguration : BaseEntityConfiguration<Expense>, IEntityTypeConfiguration<Expense>
{
    public void Configure(EntityTypeBuilder<Expense> builder)
    {
      base.Configure(builder);

        builder.HasIndex(x => x.ExpenseDate);
        builder.HasIndex(x => x.Code);
        builder.HasIndex(x => x.Particulars);
        builder.HasIndex(x => x.ExpenseHead);
        builder.HasIndex(x => x.TransactionMode);
        builder.HasIndex(x => x.TransactionDate);
        builder.HasIndex(x => x.TransactionRefNo);
        builder.HasIndex(x => x.PayTo);
        builder.HasIndex(x => x.PayToContactID);
        builder.HasIndex(x => x.ApprovedBy);
        builder.HasIndex(x => x.InvoiceNumber);
        builder.HasIndex(x => x.Entity);
        builder.HasIndex(x => x.EntityID);

    }
}
