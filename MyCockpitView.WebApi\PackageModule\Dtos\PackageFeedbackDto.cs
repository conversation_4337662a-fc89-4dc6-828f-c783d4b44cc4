﻿using AutoMapper;
using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.PackageModule.Entities;
using System.ComponentModel.DataAnnotations;

namespace MyCockpitView.WebApi.PackageModule.Dtos;

public class PackageFeedbackDto : BaseEntityDto
{
    public int PackageID { get; set; }

    [StringLength(255)]
    public string? PackageTitle { get; set; }

    [StringLength(255)]
    public string? PackagePartnerName { get; set; }
    [StringLength(255)]
    public string? PackagePartnerEmail { get; set; }
    public int? PackagePartnerContactID { get; set; }

    [StringLength(255)]
    public string? PackageAssociateName { get; set; }
    [StringLength(255)]
    public string? PackageAssociateEmail { get; set; }
    public int? PackageAssociateContactID { get; set; }

    [StringLength(255)]
    public string? ReviewerName { get; set; }

    [StringLength(255)]
    public string? ReviewerEmail { get; set; }
    public int? ReviewerContactID { get; set; }
    public int Rating { get; set; }
    public string? Comment { get; set; }

    public virtual ICollection<PackageFeedbackAttachmentDto> Attachments { get; set; } = new List<PackageFeedbackAttachmentDto>();

    public List<IFormFile>? Files { get; set; }
}

public class PackageFeedbackDtoMapperProfile : Profile
{
    public PackageFeedbackDtoMapperProfile()
    {
        CreateMap<PackageFeedback, PackageFeedbackDto>()
        .ReverseMap()
             .ForMember(dest => dest.Attachments, opt => opt.Ignore())
     .ForMember(dest => dest.Package, opt => opt.Ignore());


    }
}

public class PackageFeedbackCreateDto
{
    public int? PackageID { get; set; }
    public string? ReviewerName { get; set; }
    public string? ReviewerEmail { get; set; }
    public int? Rating { get; set; }
    public string? Comment { get; set; }
    public List<IFormFile>? Files { get; set; }
}

