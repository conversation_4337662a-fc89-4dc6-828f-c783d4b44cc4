﻿

using AutoMapper;
using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.PackageModule.Entities;

namespace MyCockpitView.WebApi.PackageModule.Dtos;

public class PackageDeliverableTaskMapDto : BaseEntityDto
{
    public int PackageID { get; set; }
    public int PackageDeliverableID { get; set; }
    public int WFTaskID { get; set; }
}

public class PackageDeliverableTaskMapDtoMapperProfile : Profile
{
    public PackageDeliverableTaskMapDtoMapperProfile()
    {
        CreateMap<PackageDeliverableTaskMap, PackageDeliverableTaskMapDto>()
        .ReverseMap()
     .ForMember(dest => dest.PackageDeliverable, opt => opt.Ignore());


    }
}