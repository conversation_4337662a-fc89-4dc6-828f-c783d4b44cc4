﻿
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using MyCockpitView.CoreModule;

namespace MyCockpitView.WebApi.DesignScriptModule.Entities;

public class DesignScriptDataCardAttachment : BaseBlobEntity
{
    [Required]
    public int DesignScriptDataCardID { get; set; }

    public virtual DesignScriptDataCard? DesignScriptDataCard { get; set; }

    public bool IsHidden { get; set; }
}

public class DesignScriptDataCardAttachmentConfiguration : BaseBlobEntityConfiguration<DesignScriptDataCardAttachment>, IEntityTypeConfiguration<DesignScriptDataCardAttachment>
{
    public void Configure(EntityTypeBuilder<DesignScriptDataCardAttachment> builder)
    {
       base.Configure(builder);

        builder.HasIndex(e => e.IsHidden);
    }
}