﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.CompanyModule.Entities;
using MyCockpitView.WebApi.ContactModule.Dtos;
using MyCockpitView.WebApi.ContactModule.Entities;
using MyCockpitView.WebApi.ProjectModule.Entities;
using System.ComponentModel.DataAnnotations;

namespace MyCockpitView.WebApi.ProjectModule.Dtos;

public class ProjectListDto : BaseEntityDto
{

    public string? Code { get; set; }
    public string? Title { get; set; }
    public string? BillingTitle { get; set; }

    public int? InquiryCode { get; set; }
    public DateTime ContractCompletionDate { get; set; }
    public DateTime? InquiryConvertionDate { get; set; }
    public DateTime? ExpectedCompletionDate { get; set; }
    public string? Location { get; set; }
    public string? City { get; set; }
    public string? State { get; set; }
    public string? Country { get; set; }
    public string? StateCode { get; set; }
    public int CompanyID { get; set; }
    public virtual Company? CompanyAccount { get; set; }

    public string? ImageUrl { get; set; }


}
public class ProjectDto : ProjectListDto
{
   
    public bool IsRepeatClient { get; set; }
    public int? ClientContactID { get; set; }
    public virtual ContactListDto? ClientContact { get; set; }

    public int? ReferredByContactID { get; set; }
    public virtual ContactListDto? ReferredByContact { get; set; }
    public string? Comment { get; set; }
    public decimal TotalFee { get; set; }
    public decimal CompanyFee { get; set; }
    public decimal Discount { get; set; }
    public int? ExpMHr { get; set; }
    public string? OfferType { get; set; }
    public DateTime? OfferDue { get; set; }
    public ICollection<ProjectAttachmentDto> Attachments { get; set; }=new List<ProjectAttachmentDto>();

    public virtual ICollection<ProjectAssociationDto> Associations { get; set; } = new List<ProjectAssociationDto>();
    public virtual ICollection<ProjectConsultantDto> Consultants { get; set; } = new List<ProjectConsultantDto>();
    public virtual ICollection<ProjectBillDto> Bills { get; set; } = new List<ProjectBillDto>();


    public virtual ICollection<ProjectOutwardDto> Outwards { get; set; } = new List<ProjectOutwardDto>();
    public virtual ICollection<ProjectScopeDto> Scopes { get; set; }= new List<ProjectScopeDto>();

    public virtual ICollection<ProjectInwardDto> Inwards { get; set; } = new List<ProjectInwardDto>();
    public virtual ICollection<ProjectNoteDto> Notes { get; set; } = new List<ProjectNoteDto>();

    public virtual ICollection<ProjectGigPointDto> GigPoints { get; set; } = new HashSet<ProjectGigPointDto>();
    public virtual ICollection<ProjectAreaDto> Areas { get; set; } = new HashSet<ProjectAreaDto>();

    public string? Segment { get; set; }
    public decimal? ExpectedMHr { get; set; } = 0;
    public string? HSNCode { get; set; }
}

public class ProjectDtoMapperProfile : Profile
{
    public ProjectDtoMapperProfile()
    {

        CreateMap<Project, ProjectDto>()
           
              .ReverseMap()
                .ForMember(dest => dest.CompanyAccount, opt => opt.Ignore())
               .ForMember(dest => dest.ClientContact, opt => opt.Ignore())
           .ForMember(dest => dest.ReferredByContact, opt => opt.Ignore())
   .ForMember(dest => dest.Inwards, opt => opt.Ignore())
    .ForMember(dest => dest.Outwards, opt => opt.Ignore())
    .ForMember(dest => dest.Notes, opt => opt.Ignore())
    .ForMember(dest => dest.Areas, opt => opt.Ignore())
                .ForMember(dest => dest.Attachments, opt => opt.Ignore())
                .ForMember(dest => dest.Associations, opt => opt.Ignore())
                                .ForMember(dest => dest.GigPoints, opt => opt.Ignore())
                .ForMember(dest => dest.Consultants, opt => opt.Ignore());

        CreateMap<Project, ProjectListDto>();
    }
}