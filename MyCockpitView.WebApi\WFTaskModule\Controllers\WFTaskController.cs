﻿
using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.WebApi.Responses;
using MyCockpitView.Utility.Common;
using MyCockpitView.WebApi.ActivityModule;
using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.ContactModule.Services;
using MyCockpitView.WebApi.WFTaskModule.Services;
using MyCockpitView.WebApi.WFTaskModule.Dtos;
using MyCockpitView.WebApi.WFTaskModule.Entities;
using MyCockpitView.WebApi.PackageModule.Services;
using MyCockpitView.WebApi.PackageModule.Entities;
using MyCockpitView.WebApi.WFStageModule.Dtos;
using MyCockpitView.WebApi.WFStageModule.Services;
using MyCockpitView.WebApi.Exceptions;
using MyCockpitView.WebApi.WFStageModule.Entities;
using Microsoft.AspNetCore.Http;
using MyCockpitView.WebApi.Services;

namespace MyCockpitView.WebApi.WFTaskModule.Controllers;

[Authorize]
[ApiController]
[Route("[controller]")]
public class WFTaskController : ControllerBase
{
    private readonly IWFTaskService service;
    private readonly EntitiesContext db;
    private readonly IActivityService activityService;
    private readonly IMapper mapper;
    private readonly IContactService contactService;
    private readonly IPackageService packageService;
    private readonly IWFStageService wFStageService;
    private readonly ICurrentUserService currentUserService;

    public WFTaskController(
        EntitiesContext db,
        IWFTaskService service,
        IMapper mapper,
        IActivityService activityService,
        IContactService contactService,
        IPackageService packageService,
        IWFStageService wFStageService,
        ICurrentUserService currentUserService)
    {
        this.db = db;
        this.service = service;
        this.activityService = activityService;
        this.mapper = mapper;
        this.contactService = contactService;
        this.packageService = packageService;
        this.wFStageService = wFStageService;
        this.currentUserService = currentUserService;
    }

    [HttpGet]
    public async Task<ActionResult<IEnumerable<WFTaskDto>>> Get(
        [FromQuery] string? filters = null,
        [FromQuery] string? search = null,
        [FromQuery] string? sort = null)
    {
        var _filters = filters != null ? DataTools.GetObjectFromJsonString<APIFilter>(filters).Filters.ToList() : new List<QueryFilter>();

        if (_filters
            .Where(x => x.Key.Equals("PackagePartnerOrAssociateContactID", StringComparison.OrdinalIgnoreCase))
            .Any())
        {
            var packageFilters = _filters
                .Where(x => x.Key.Equals("PackagePartnerOrAssociateContactID", StringComparison.OrdinalIgnoreCase))
                .Select(x => new QueryFilter { Key = "PartnerOrAssociateContactID", Value = x.Value })
                .ToList();

            var packages = await packageService.Get(packageFilters)
                .Where(x => x.TypeFlag == McvConstant.PACKAGE_TYPEFLAG_ACTIVE && x.StatusFlag == McvConstant.PACKAGE_STATUSFLAG_ACTIVE)
                .Select(x => x.ID)
                .ToListAsync();

            if (!packages.Any()) return Ok(new List<WFTaskDto>());

            _filters.Add(new QueryFilter { Key = "Entity", Value = nameof(Package) });
            foreach (var id in packages)
            {
                _filters.Add(new QueryFilter { Key = "EntityID", Value = id.ToString() });
            }

        }


        var query = service.Get(_filters, search, sort)
            .Include(x=>x.Contact)
            .Include(x=>x.Assigner)
            .Include(x=>x.Assessments)
            .Include(x=>x.TimeEntries)
            ;
        var results = mapper.Map<IEnumerable<WFTaskDto>>(await query.ToListAsync());

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(WFTask))
            .ToListAsync();

        foreach (var obj in results)
        {
            obj.Type = typeMasters.FirstOrDefault(x => x.Value == obj.TypeFlag)?.Title ?? "";
        }

        var statusMasters = await db.StatusMasters
          .AsNoTracking()
          .Where(x => x.Entity == nameof(WFTask))
          .ToListAsync();

        foreach (var obj in results)
        {
            obj.Status = statusMasters.FirstOrDefault(x => x.Value == obj.StatusFlag)?.Title ?? "";
        }

        var _wftaskStages = await wFStageService.Get()
                  .Include(x => x.Actions)
                  .ToListAsync();

        foreach (var obj in results)
        {

            if (obj.Entity == nameof(Package)
                && obj.StageIndex == 5)
            {
                var _pendingAssessmentCount = await service.GetPendingAssessmentCount(obj.ID);
                if (_pendingAssessmentCount != 0)
                {
                    obj.IsShowAlert = true;
                    obj.AlertMessage = _pendingAssessmentCount.ToString("00") + " Assessments pending";
                }
            }

            if (obj.WFStageCode != null)
            {
                obj.WFStage = mapper.Map<WFStageDto>(_wftaskStages.SingleOrDefault(x => x.Code.Equals(obj.WFStageCode)));
            }
        }

        return Ok(results);
    }

    [HttpGet("Pages")]
    public async Task<ActionResult<PagedResponse<WFTaskDto>>> GetPages(
        [FromQuery] int page = 0,
        [FromQuery] int pageSize = 50,
        [FromQuery] string? filters = null,
        [FromQuery] string? search = null,
        [FromQuery] string? sort = null)
    {
        var _filters = filters != null ? DataTools.GetObjectFromJsonString<APIFilter>(filters).Filters.ToList() : new List<QueryFilter>();

        if (_filters
            .Where(x => x.Key.Equals("PackagePartnerOrAssociateContactID", StringComparison.OrdinalIgnoreCase))
            .Any())
        {
            var packageFilters = _filters
                .Where(x => x.Key.Equals("PackagePartnerOrAssociateContactID", StringComparison.OrdinalIgnoreCase))
                .Select(x => new QueryFilter { Key = "PartnerOrAssociateContactID", Value = x.Value })
                .ToList();

            var packages = await packageService.Get(packageFilters)
                .Where(x => x.TypeFlag == McvConstant.PACKAGE_TYPEFLAG_ACTIVE && x.StatusFlag == McvConstant.PACKAGE_STATUSFLAG_ACTIVE)
                .Select(x => x.ID)
                .ToListAsync();

            if (!packages.Any()) return Ok(new List<WFTaskDto>());

            _filters.Add(new QueryFilter { Key = "Entity", Value = nameof(Package) });
            foreach (var id in packages)
            {
                _filters.Add(new QueryFilter { Key = "EntityID", Value = id.ToString() });
            }

        }

        var query = service.Get(_filters, search, sort)
            .Include(x => x.Contact).Include(x => x.Assigner);

        var totalCount = await query.CountAsync();
        var totalPages = (int)Math.Ceiling((decimal)totalCount / pageSize);
        var results = mapper.Map<IEnumerable<WFTaskDto>>(await query
            .Skip(pageSize * page)
            .Take(pageSize)
            .ToListAsync());

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(WFTask))
            .ToListAsync();

        foreach (var obj in results)
        {
            obj.Type = typeMasters.FirstOrDefault(x => x.Value == obj.TypeFlag)?.Title ?? "";
        }

        var statusMasters = await db.StatusMasters
           .AsNoTracking()
           .Where(x => x.Entity == nameof(WFTask))
           .ToListAsync();

        foreach (var obj in results)
        {
            obj.Status = statusMasters.FirstOrDefault(x => x.Value == obj.StatusFlag)?.Title ?? "";
        }

        foreach (var obj in results)
        {
            obj.IsDelayed = obj.StatusFlag != -1 && obj.StatusFlag != 1 ? obj.DueDate < DateTime.UtcNow : (obj.CompletedDate != null ? obj.DueDate < obj.CompletedDate.Value : obj.DueDate < DateTime.UtcNow);
        }
        var _wftaskStages = await wFStageService.Get()
                  .Include(x => x.Actions)
                  .ToListAsync();

        foreach (var obj in results)
        {

            if (obj.Entity == nameof(Package)
                && obj.StageIndex == 5)
            {
                var _pendingAssessmentCount = await service.GetPendingAssessmentCount(obj.ID);
                if (_pendingAssessmentCount != 0)
                {
                    obj.IsShowAlert = true;
                    obj.AlertMessage = _pendingAssessmentCount.ToString("00") + " Assessments pending";
                }
            }

            if (obj.WFStageCode != null)
            {
                obj.WFStage = mapper.Map<WFStageDto>(_wftaskStages.SingleOrDefault(x => x.Code.Equals(obj.WFStageCode)));
            }
        }
        return Ok(new PagedResponse<WFTaskDto>(results, totalCount, totalPages));
    }

    [HttpGet("{id:int}")]
    public async Task<ActionResult<WFTaskDto>> GetByID(int id)
    {
        var responseDto = mapper.Map<WFTaskDto>(await service.GetById(id));
        if (responseDto == null)
        {
            return NotFound();
        }

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(WFTask))
            .ToListAsync();

        responseDto.Type = typeMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        var statusMasters = await db.StatusMasters
          .AsNoTracking()
          .Where(x => x.Entity == nameof(WFTask))
          .ToListAsync();

        responseDto.Status = statusMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        var _wftaskStages = await wFStageService.Get()
                  .Include(x => x.Actions)
                  .ToListAsync();


            if (responseDto.Entity == nameof(Package)
                && responseDto.StageIndex == 5)
            {
                var _pendingAssessmentCount = await service.GetPendingAssessmentCount(responseDto.ID);
                if (_pendingAssessmentCount != 0)
                {
                    responseDto.IsShowAlert = true;
                    responseDto.AlertMessage = _pendingAssessmentCount.ToString("00") + " Assessments pending";
                }
            }

            if (responseDto.WFStageCode != null)
            {
                responseDto.WFStage = mapper.Map<WFStageDto>(_wftaskStages.SingleOrDefault(x => x.Code.Equals(responseDto.WFStageCode)));
            }


        return Ok(responseDto);
    }

    [HttpPost]
    public async Task<ActionResult<WFTaskDto>> Post(WFTaskDto dto)
    {
        var Entity = mapper.Map<WFTask>(dto);
        if (Entity.Entity == nameof(Package))
        {
            var package = await packageService.Get().FirstOrDefaultAsync(x => x.ID == Entity.EntityID);
            if (package == null) return BadRequest("Package not found!");

            if (package.ActiveStage.ToUpper() != McvConstant.PACKAGE_TASK_STAGE_5.ToUpper()) //PACKAGE
            {
                return BadRequest($"{McvConstant.PACKAGE_TASK_STAGE_3} Task can only be assigned at {McvConstant.PACKAGE_TASK_STAGE_5} stage.");
            }

            var packageAssociates = await packageService.Get()
                .Include(x => x.Associations)
                .Where(x => x.ID == Entity.EntityID)
                .SelectMany(x => x.Associations.Where(a => !a.IsDeleted).Where(a => a.TypeFlag == McvConstant.PACKAGE_ASSOCIATION_TYPEFLAG_PARTNER || a.TypeFlag == McvConstant.PACKAGE_ASSOCIATION_TYPEFLAG_ASSOCIATE), (a, b) => new
                {
                    b.ContactID,
                    b.TypeFlag
                })
                .ToListAsync();

            if (packageAssociates.Any())
            {
                var username = currentUserService.GetCurrentUsername();
                var contact = !string.IsNullOrEmpty(username)
                    ? await contactService.Get().SingleOrDefaultAsync(x => x.Username == username)
                    : null;

                if (contact != null && packageAssociates.Any(x => x.ContactID == contact.ID))
                {
                    Entity.AssignerContactID = packageAssociates.FirstOrDefault(x => x.ContactID == contact.ID).ContactID;
                }
                else
                {
                    // Prioritize Associate, if not available, fallback to Partner
                    var associate = packageAssociates.FirstOrDefault(x => x.TypeFlag == McvConstant.PACKAGE_ASSOCIATION_TYPEFLAG_ASSOCIATE);
                    var partner = packageAssociates.FirstOrDefault(x => x.TypeFlag == McvConstant.PACKAGE_ASSOCIATION_TYPEFLAG_PARTNER);

                    Entity.AssignerContactID = (associate != null) ? associate.ContactID
                                            : (partner != null) ? partner.ContactID
                                            : (int?)null; // Default value, update as needed
                }
            }
        }
        else
        {
            var username = currentUserService.GetCurrentUsername();
            var contact = !string.IsNullOrEmpty(username)
                ? await contactService.Get().SingleOrDefaultAsync(x => x.Username == username)
                : null;

            if (contact != null)
                Entity.AssignerContactID = contact.ID;
        }

        var id = await service.Create(Entity);
        var responseDto = mapper.Map<WFTaskDto>(await service.GetById(id));

        if (responseDto == null)
            return BadRequest("Not Created");

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(WFTask))
            .ToListAsync();

        responseDto.Type = typeMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        var statusMasters = await db.StatusMasters
  .AsNoTracking()
  .Where(x => x.Entity == nameof(WFTask))
  .ToListAsync();

        responseDto.Status = statusMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        var _wftaskStages = await wFStageService.Get()
          .Include(x => x.Actions)
          .ToListAsync();


        if (responseDto.Entity == nameof(Package)
            && responseDto.StageIndex == 5)
        {
            var _pendingAssessmentCount = await service.GetPendingAssessmentCount(responseDto.ID);
            if (_pendingAssessmentCount != 0)
            {
                responseDto.IsShowAlert = true;
                responseDto.AlertMessage = _pendingAssessmentCount.ToString("00") + " Assessments pending";
            }
        }

        if (responseDto.WFStageCode != null)
        {
            responseDto.WFStage = mapper.Map<WFStageDto>(_wftaskStages.SingleOrDefault(x => x.Code.Equals(responseDto.WFStageCode)));
        }

        //if (!string.IsNullOrEmpty(User.Identity?.Name))
        //{
        //    var currentContact = await contactService.Get().FirstOrDefaultAsync(x => x.Username == User.Identity.Name);
        //    if (currentContact != null)
        //    {
        //        var parent = await contactService.Get().SingleOrDefaultAsync(x => x.ID == responseDto.ContactID);
        //        if (parent != null)
        //        {
        //            await activityService.LogUserActivity(currentContact, nameof(parent), parent.ID, $"{parent.Name}", $"{nameof(WFTask).Replace(nameof(parent), "")} | {responseDto.Company.Title} | {responseDto.Designation} | {responseDto.ManValue}mV", "Created");
        //        }
        //    }
        //}

        return CreatedAtAction(nameof(GetByID), new { id = responseDto.ID }, responseDto);
    }

    [HttpPut("{id:int}")]
    public async Task<ActionResult<WFTaskDto>> Put(int id, WFTaskDto dto)
    {
        if (id != dto.ID)
        {
            return BadRequest();
        }

        await service.Update(mapper.Map<WFTask>(dto), mapper.Map<IEnumerable<Assessment>>(dto.Assessments));
        var responseDto = mapper.Map<WFTaskDto>(await service.GetById(id));

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(WFTask))
            .ToListAsync();

        responseDto.Type = typeMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        var statusMasters = await db.StatusMasters
  .AsNoTracking()
  .Where(x => x.Entity == nameof(WFTask))
  .ToListAsync();

        responseDto.Status = statusMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        var _wftaskStages = await wFStageService.Get()
          .Include(x => x.Actions)
          .ToListAsync();


        if (responseDto.Entity == nameof(Package)
            && responseDto.StageIndex == 5)
        {
            var _pendingAssessmentCount = await service.GetPendingAssessmentCount(responseDto.ID);
            if (_pendingAssessmentCount != 0)
            {
                responseDto.IsShowAlert = true;
                responseDto.AlertMessage = _pendingAssessmentCount.ToString("00") + " Assessments pending";
            }
        }

        if (responseDto.WFStageCode != null)
        {
            responseDto.WFStage = mapper.Map<WFStageDto>(_wftaskStages.SingleOrDefault(x => x.Code.Equals(responseDto.WFStageCode)));
        }



        return Ok(responseDto);
    }

    [HttpDelete("{id:int}")]
    public async Task<IActionResult> Delete(int id)
    {
        var responseDto = mapper.Map<WFTaskDto>(await service.GetById(id));
        if (responseDto == null) return BadRequest($"{nameof(WFTask)} not found!");

        await service.Delete(id);

        //if (!string.IsNullOrEmpty(User.Identity?.Name))
        //{
        //    var currentContact = await contactService.Get().FirstOrDefaultAsync(x => x.Username == User.Identity.Name);
        //    if (currentContact != null)
        //    {
        //        var parent = await contactService.Get().SingleOrDefaultAsync(x => x.ID == responseDto.ContactID);
        //        if (parent != null)
        //        {
        //            await activityService.LogUserActivity(currentContact, nameof(parent), parent.ID, $"{parent.Name}", $"{nameof(WFTask).Replace(nameof(parent), "")} | {responseDto.Company.Title} | {responseDto.Designation} | {responseDto.ManValue}mV", "Deleted");
        //        }
        //    }
        //}

        return NoContent();
    }


    [HttpGet("uid/{id:guid}")]
    [AllowAnonymous]
    public async Task<ActionResult<WFTaskDto>> GetByGUID(Guid id)
    {
        var responseDto = mapper.Map<WFTaskDto>(await service.GetById(id));
        if (responseDto == null)
        {
            return NotFound();
        }

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(WFTask))
            .ToListAsync();

        responseDto.Type = typeMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        var statusMasters = await db.StatusMasters
                        .AsNoTracking()
                        .Where(x => x.Entity == nameof(WFTask))
                        .ToListAsync();

        responseDto.Status = statusMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        return Ok(responseDto);
    }


    [HttpGet("SearchTagOptions")]
    public async Task<IActionResult> GetSearchTagOptions()
    {
        var results = await service.GetSearchTagOptions();
        return Ok(results);
    }

    [HttpGet("FieldOptions")]
    public async Task<IActionResult> GetFieldOptions(string field)
    {
        return Ok(await service.GetFieldOptions(field));
    }

    [HttpGet("TimeLineTasks")]
    public async Task<IActionResult> GetTimeLineTasks(string? filters = null, string? search = null, string? sort = null)
    {
        var query = await service.Get(filters != null ? DataTools.GetObjectFromJsonString<APIFilter>(filters).Filters : null, search, sort)
        .Include(x => x.Contact)
        .Include(x => x.Assigner)
            .ToListAsync();

        var packageIDs = query
            .Where(x => x.Entity == nameof(Package) && x.EntityID != null)
            .Select(x => x.EntityID)
            .ToList();

        var packages = await packageService.Get()
                      .Where(x => !x.IsVersion && x.TypeFlag == McvConstant.PACKAGE_TYPEFLAG_ACTIVE)
                      .Where(x => packageIDs.Contains(x.ID))
                      .Include(x => x.Associations).ThenInclude(c => c.Contact)
                      .ToListAsync();

        var results = query.Select(x =>
        {
            var package = (x.Entity == nameof(Package))
                ? packages.FirstOrDefault(p => p.ID == x.EntityID)
                : null;

            return new
            {
                x.ID,
                x.Entity,
                x.EntityID,
                x.Title,
                x.Subtitle,
                x.Description,
                x.DueDate,
                x.StartDate,
                x.CompletedDate,
                x.StatusFlag,
                x.StageIndex,
                x.StageRevision,
                x.WFStageCode,
                x.ManValue,
                x.MHrAssigned,
                x.MHrConsumed,
                x.MHrAssessed,
                x.VHrAssigned,
                x.VHrAssessed,
                x.VHrConsumed,
                x.Created,
                x.CreatedBy,
                x.CreatedByContactID,
                x.AssignerContactID,
                Assigner = x.Assigner != null ? x.Assigner.Name : null,
                Assignee = x.Contact?.Name,
                AssigneeContactID = x.Contact?.ID,
                AssigneePhotoUrl = x.Contact?.PhotoUrl,
                IsDelayed = x.StatusFlag != McvConstant.WFTASK_STATUSFLAG_UNATTENDED && x.StatusFlag != McvConstant.WFTASK_STATUSFLAG_COMPLETED
                            ? x.DueDate < DateTime.UtcNow
                            : (x.CompletedDate != null ? x.DueDate < x.CompletedDate.Value : x.DueDate < DateTime.UtcNow),

                // Package details included only if x.Entity == nameof(Package)
                Package = package != null ? new
                {
                    package.ID,
                    package.Code,
                    package.ProjectCode,
                    package.ProjectTitle,
                    Partner = package.Associations.Any(a => !a.IsDeleted && a.TypeFlag == McvConstant.PACKAGE_ASSOCIATION_TYPEFLAG_PARTNER) ? package.Associations.Where(a => !a.IsDeleted && a.TypeFlag == McvConstant.PACKAGE_ASSOCIATION_TYPEFLAG_PARTNER).Select(c => new
                    {
                        c.Contact.ID,
                        c.Contact.Name
                    }) : null,
                    Associate = package.Associations.Any(a => !a.IsDeleted && a.TypeFlag == McvConstant.PACKAGE_ASSOCIATION_TYPEFLAG_ASSOCIATE) ? package.Associations.Where(a => !a.IsDeleted && a.TypeFlag == McvConstant.PACKAGE_ASSOCIATION_TYPEFLAG_ASSOCIATE).Select(c => new
                    {
                        c.Contact.ID,
                        c.Contact.Name
                    }) : null,
                } : null
            };
        });

        return Ok(results);
    }

    [HttpGet("Count")]
    public async Task<IActionResult> GetCount(string? filters = null, string? search = null, string? sort = null)
    {

        var query = service.Get(filters != null ? DataTools.GetObjectFromJsonString<APIFilter>(filters).Filters :null, search, sort);

        return Ok(await query.CountAsync());

    }

    [HttpGet("EntityOptions")]
    public async Task<IActionResult> GetEntityOptions()
    {

        var query = await service.GetEntityOptions();

        return Ok(query);

    }

    [AllowAnonymous]
    [HttpGet("Analysis/Excel")]
    public async Task<IActionResult> GetAnalysisExcel(
    [FromQuery] string? filters = null,
    [FromQuery] string? search = null,
    [FromQuery] string? sort = null)
    {
        byte[] report = await service.GetAnalysisExcel(filters != null ? DataTools.GetObjectFromJsonString<APIFilter>(filters).Filters : null, search, sort);

        if (report != null)
        {
            var filename = $"TaskAnalysis-{DateTimeOffset.Now:dd-MMM-yyyy}.xlsx";
            return File(
                report,
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                filename);
        }
        else
        {
            return BadRequest("Report cannot be generated");
        }
    }


    [HttpGet("Analysis")]
    public async Task<IActionResult> GetAnalysis(
        [FromQuery] string? filters = null,
        [FromQuery] string? search = null,
        [FromQuery] string? sort = null)
    {
        var results = await service.GetAnalysisData(filters != null ? DataTools.GetObjectFromJsonString<APIFilter>(filters).Filters :null, search, sort);
        return Ok(results);
    }

    [HttpGet("Analysis/Total")]
    public async Task<IActionResult> GetAnalysisTotal(
        [FromQuery] string? filters = null,
        [FromQuery] string? search = null,
        [FromQuery] string? sort = null)
    {
        var results = await service.GetAnalysisDataTotal(filters != null ? DataTools.GetObjectFromJsonString<APIFilter>(filters).Filters :null, search, sort);
        return Ok(results);
    }

    [HttpGet("Analysis/vhr")]
    public async Task<IActionResult> GetVHrAnalysis(
        [FromQuery] string? filters = null,
        [FromQuery] string? search = null,
        [FromQuery] string? sort = null)
    {
        var results = await service.GetVHrAnalysisData(filters != null ? DataTools.GetObjectFromJsonString<APIFilter>(filters).Filters :null, search, sort);
        return Ok(results);
    }


    [HttpGet("Analysis/vhr/{period}")]
    public async Task<IActionResult> GetVHrAnalysisByPeriod(
        string period,
        [FromQuery] string? filters = null,
        [FromQuery] string? search = null,
        [FromQuery] string? sort = null)
    {
        var results = await service.GetVHrAnalysisDatabyPeriod(period, filters != null ? DataTools.GetObjectFromJsonString<APIFilter>(filters).Filters :null, search, sort);
        return Ok(results);
    }

    [AllowAnonymous]
    [HttpGet("Analysis/vhr/excel")]
    public async Task<IActionResult> GetVHrAnalysisExcel(
        [FromQuery] string? filters = null,
        [FromQuery] string? search = null,
        [FromQuery] string? sort = null)
    {

        var _filters = filters != null ? DataTools.GetObjectFromJsonString<APIFilter>(filters).Filters : null;
        if (_filters == null
          || !_filters.Where(x => x.Key.Equals("rangestart", StringComparison.OrdinalIgnoreCase)).Any()
          || !_filters.Where(x => x.Key.Equals("rangeend", StringComparison.OrdinalIgnoreCase)).Any()
          )
            throw new EntityServiceException("Range filters are required!");

        DateTime _rangeStart = Convert.ToDateTime(_filters.First(x => x.Key.Equals("rangestart", StringComparison.OrdinalIgnoreCase)).Value);
        DateTime _rangeEnd = Convert.ToDateTime(_filters.First(x => x.Key.Equals("rangeend", StringComparison.OrdinalIgnoreCase)).Value);

        var analysisData = await service.GetVHrAnalysisData(filters != null ? DataTools.GetObjectFromJsonString<APIFilter>(filters).Filters :null, search, sort);
        byte[] report = await service.GetVHrAnalysisExcel(analysisData);

        if (report != null)
        {
            var filename = $"VHrAnalysis-{ClockTools.GetIST(_rangeStart).ToString("dd-MMM-yyyy")}-{ClockTools.GetIST(_rangeEnd).ToString("dd-MMM-yyyy")}.xlsx";
            return File(
                report,
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                filename);
        }
        else
        {
            return BadRequest("Report cannot be generated");
        }
    }

    [AllowAnonymous]
    [HttpGet("Analysis/vhr/{period}/excel")]
    public async Task<IActionResult> GetVHrAnalysisExcelByPeriod(
        string period,
        [FromQuery] string? filters = null,
        [FromQuery] string? search = null,
        [FromQuery] string? sort = null)
    {
        var analysisData = await service.GetVHrAnalysisDatabyPeriod(period, filters != null ? DataTools.GetObjectFromJsonString<APIFilter>(filters).Filters :null, search, sort);
        byte[] report = await service.GetVHrAnalysisExcel(analysisData);

        if (report != null)
        {
            var filename = $"VHrAnalysis-{period}-{DateTimeOffset.Now:dd-MMM-yyyy}.xlsx";
            return File(
                report,
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                filename);
        }
        else
        {
            return BadRequest("Report cannot be generated");
        }
    }

    [AllowAnonymous]
    [HttpGet("AutoPause")]
    public async Task<IActionResult> AutoPause()
    {
        await service.AutoPauseTasks();
        return Ok();
    }

    [AllowAnonymous]
    [HttpGet("StartFlow/{Entity}/{EntityID}")]
    public async Task<IActionResult> AutoPause(string Entity, int EntityID, int EntityTypeFlag, WFStage? Stage = null)
    {
        await service.StartFlow(Entity,EntityTypeFlag,EntityID,Stage);
        return Ok();
    }

    [AllowAnonymous]
    [HttpGet("CheckTaskCompletedDate")]
    public async Task<IActionResult> CheckTaskCompletedDate()
    {
        await service.CheckTaskCompletedDate();
        return Ok();
    }

    [AllowAnonymous]
    [HttpGet("ClosePendingAssessments")]
    public async Task<IActionResult> ClosePendingAssessments()
    {
        var tasks = await service.Get()
            .Include(x => x.Assessments)
            .Where(x => x.OutcomeFlag != -1) // REJECTED
            .Where(x => x.StatusFlag == McvConstant.WFTASK_STATUSFLAG_COMPLETED)
            .Where(x => x.IsAssessmentRequired && !x.Assessments.Any())
            .ToListAsync();

        foreach (var obj in tasks)
        {
            try
            {
                obj.IsAssessmentRequired = false;
                obj.AssessmentPoints = 10;
                obj.AssessmentRemark = "System Assessed";
                obj.MHrAssessed = (obj.MHrAssigned * obj.AssessmentPoints / 10.0m);
                obj.VHrAssessed = obj.MHrAssessed * obj.ManValue;
                obj.VHrAssessedCost = obj.VHrAssessed * obj.VHrRate;

                db.Entry(obj).State = EntityState.Modified;
                await db.SaveChangesAsync();
            }
            catch (Exception)
            {
                // Consider logging the exception or handling it more explicitly
            }
        }

        return Ok();
    }

    [AllowAnonymous]
    [HttpGet("RecalculateVHrData")]
    public async Task<IActionResult> RecalculateVHrData()
    {
        var date= new DateTime(2025, 4, 1);
        var tasks = await service.Get()
            .Where(x => x.OutcomeFlag != -1) // REJECTED
            .Where(x => x.StatusFlag == McvConstant.WFTASK_STATUSFLAG_COMPLETED)
            .Where(x=>x.MHrAssigned ==0)
            .Where(x => x.CompletedDate > date)
            .ToListAsync();

        var updatedTasks = new List<WFTask>();
        foreach (var obj in tasks)
        {
            try
            {
                await service.RecalculateVHrData(obj);
                updatedTasks.Add(obj);
            }
            catch (Exception)
            {
                // Consider logging the exception or handling it more explicitly
            }
        }

        return Ok(updatedTasks);
    }
}