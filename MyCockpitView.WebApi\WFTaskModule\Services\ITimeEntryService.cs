﻿using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.Services;
using MyCockpitView.WebApi.WFTaskModule.Entities;

namespace MyCockpitView.WebApi.WFTaskModule.Services;

public interface ITimeEntryService: IBaseEntityService<TimeEntry>
{
    Task<int> Create(TimeEntry Entity, bool AllowOverlapp = false);
    Task EndTimeLog(int WFTaskID, bool IsPaused = false);
    IQueryable<TimeEntryAnalysis> GetAnalysisData(IQueryable<TimeEntry> TimeEntries, bool ISTDates = false);
    Task<TimeEntryAnalysis> GetAnalysisDataTotal(IQueryable<TimeEntry> TimeEntries);
    Task<byte[]> GetAnalysisExcel(IEnumerable<QueryFilter>? Filters = null, string? Search = null, string? Sort = null);
    Task StartTimeLog(int WFTaskID);
    Task StartTimeLog(TimeEntry Entity);
    Task ValidateTime(TimeEntry Entity, bool isUpdate = false, bool allowOverlapp = false);
}