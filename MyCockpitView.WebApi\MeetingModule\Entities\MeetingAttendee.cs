﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using MyCockpitView.CoreModule;

using System.ComponentModel.DataAnnotations;

namespace MyCockpitView.WebApi.MeetingModule.Entities;

public class MeetingAttendee : BaseEntity
{
    [Required]
    public int MeetingID { get; set; }

  
    [Required]
    [StringLength(255)]
    public string? Name { get; set; }

    [StringLength(255)]
    public string? Email { get; set; }

    [StringLength(255)]
    public string? Company { get; set; }

    public int? ContactID { get; set; }

    public virtual Meeting? Meeting { get; set; }

}
public class MeetingAttendeeConfiguration : BaseEntityConfiguration<MeetingAttendee>, IEntityTypeConfiguration<MeetingAttendee>
{
    public void Configure(EntityTypeBuilder<MeetingAttendee> builder)
    {
        base.Configure(builder);
        builder.HasIndex(e => e.Name);
        builder.HasIndex(e => e.Email);
        builder.HasIndex(e => e.Company);
        builder.HasIndex(e => e.ContactID);
       

    }
}