﻿
using MyCockpitView.WebApi.LeaveModule.Services;

namespace MyCockpitView.WebApi.LeaveModule.Extensions;

public static class ServiceExtensions
{

    public static IServiceCollection RegisterLeaveServices(
     this IServiceCollection services)
    {
        services.AddScoped<ILeaveService,LeaveService>();
        services.AddScoped<ILeaveAttachmentService, LeaveAttachmentService>();
        services.AddScoped<IHolidayMasterService, HolidayMasterService>();
        return services;
    }
}
