﻿using AutoMapper;
using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.ProjectModule.Entities;
using System.ComponentModel.DataAnnotations;

namespace MyCockpitView.WebApi.ProjectModule.Dtos;
public class ProjectNoteDto : BaseEntityDto
{

    public string? Notes { get; set; }


    public int ProjectID { get; set; }

}

public class ProjectNoteDtoMapperProfile : Profile
{
    public ProjectNoteDtoMapperProfile()
    {
        CreateMap<ProjectNote, ProjectNoteDto>()
      .ReverseMap()
   .ForMember(dest => dest.Project, opt => opt.Ignore());

    }
}
