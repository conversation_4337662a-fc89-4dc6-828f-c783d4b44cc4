﻿using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.PackageModule.Entities;

namespace MyCockpitView.WebApi.ProcessLibraryModule.Entities;

public class ProcessLibraryEntityAttachment : BaseBlobEntity
{
    public int ProcessLibraryEntityID { get; set; }

    public virtual ProcessLibraryEntity? ProcessLibraryEntity { get; set; }


    [Column("Categories")]
    public string? _categories { get; set; }

    [NotMapped]
    public ICollection<string> Categories
    {
        get
        {
            return _categories != null && _categories != string.Empty ?
                _categories.Split(',').ToList() :
                new List<string>() { };
        }
        set
        {
            _categories = string.Join(",", value);
        }
    }

}

public class ProcessLibraryEntityAttachmentConfiguration : BaseBlobEntityConfiguration<ProcessLibraryEntityAttachment>, IEntityTypeConfiguration<ProcessLibraryEntityAttachment>
{
    public void Configure(EntityTypeBuilder<ProcessLibraryEntityAttachment> builder)
    {
       base.Configure(builder);
    }
}
