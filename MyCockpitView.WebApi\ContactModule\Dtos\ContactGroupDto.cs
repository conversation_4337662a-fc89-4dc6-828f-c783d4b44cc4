﻿using AutoMapper;
using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.CompanyModule;
using MyCockpitView.WebApi.ContactModule.Entities;
using System.ComponentModel.DataAnnotations;

namespace MyCockpitView.WebApi.ContactModule.Dtos;

public class ContactGroupDto : BaseEntityDto
{

    [Required]
    public int ContactID { get; set; }
    [StringLength(255)]
    public string? Title { get; set; }
    public bool IsDefault { get; set; }
    public virtual ICollection<ContactGroupMemberDto> Members { get; set; } = new List<ContactGroupMemberDto>();

}

public class ContactGroupMapperProfile : Profile
{
    public ContactGroupMapperProfile()
    {

        CreateMap<ContactGroup, ContactGroupDto>()
         .ReverseMap()
          .ForMember(dest => dest.Members, opt => opt.Ignore());

    }
}
