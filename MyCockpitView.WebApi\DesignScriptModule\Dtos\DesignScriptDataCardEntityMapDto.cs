﻿using AutoMapper;
using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.DesignScriptModule.Entities;

namespace MyCockpitView.WebApi.DesignScriptModule.Dtos;

public class DesignScriptDataCardEntityMapDto : BaseEntityDto
{
    public int DesignScriptDataCardID { get; set; }
    public int DesignScriptEntityID { get; set; }
}

public class DesignScriptDataCardEntityMapDtoMapperProfile : Profile
{
    public DesignScriptDataCardEntityMapDtoMapperProfile()
    {
        CreateMap<DesignScriptDataCardEntityMap, DesignScriptDataCardEntityMapDto>()
      .ReverseMap()
       .ForMember(dest => dest.DataCard, opt => opt.Ignore())
       .ForMember(dest => dest.Entity, opt => opt.Ignore());
    }
}