﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace MyCockpitView.WebApi.Migrations
{
    /// <inheritdoc />
    public partial class AK015 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_ProcessLibraryEntities_ProcessLibraryEntities_ParentID",
                table: "ProcessLibraryEntities");

            migrationBuilder.DropIndex(
                name: "IX_RequestTickets_IsReadOnly",
                table: "RequestTickets");

            migrationBuilder.DropIndex(
                name: "IX_ProcessLibraryEntities_ParentID",
                table: "ProcessLibraryEntities");

            migrationBuilder.Sql(
                @"UPDATE RequestTickets SET IsVersion = IsReadOnly");

            migrationBuilder.DropColumn(
                name: "IsReadOnly",
                table: "RequestTickets");

            migrationBuilder.AddColumn<bool>(
                name: "IsReadOnly",
                table: "ProcessLibraryEntities",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<int>(
                name: "ProcessLibraryEntityID",
                table: "ProcessLibraryEntities",
                type: "int",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_ProcessLibraryEntities_ProcessLibraryEntityID",
                table: "ProcessLibraryEntities",
                column: "ProcessLibraryEntityID");

            migrationBuilder.AddForeignKey(
                name: "FK_ProcessLibraryEntities_ProcessLibraryEntities_ProcessLibraryEntityID",
                table: "ProcessLibraryEntities",
                column: "ProcessLibraryEntityID",
                principalTable: "ProcessLibraryEntities",
                principalColumn: "ID");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_ProcessLibraryEntities_ProcessLibraryEntities_ProcessLibraryEntityID",
                table: "ProcessLibraryEntities");

            migrationBuilder.DropIndex(
                name: "IX_ProcessLibraryEntities_ProcessLibraryEntityID",
                table: "ProcessLibraryEntities");

            migrationBuilder.DropColumn(
                name: "IsReadOnly",
                table: "ProcessLibraryEntities");

            migrationBuilder.DropColumn(
                name: "ProcessLibraryEntityID",
                table: "ProcessLibraryEntities");

            migrationBuilder.AddColumn<bool>(
                name: "IsReadOnly",
                table: "RequestTickets",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.CreateIndex(
                name: "IX_RequestTickets_IsReadOnly",
                table: "RequestTickets",
                column: "IsReadOnly");

            migrationBuilder.CreateIndex(
                name: "IX_ProcessLibraryEntities_ParentID",
                table: "ProcessLibraryEntities",
                column: "ParentID");

            migrationBuilder.AddForeignKey(
                name: "FK_ProcessLibraryEntities_ProcessLibraryEntities_ParentID",
                table: "ProcessLibraryEntities",
                column: "ParentID",
                principalTable: "ProcessLibraryEntities",
                principalColumn: "ID");
        }
    }
}
