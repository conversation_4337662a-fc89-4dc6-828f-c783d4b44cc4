﻿using System.ComponentModel.DataAnnotations;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.WFTaskModule.Entities;

namespace MyCockpitView.WebApi.StatusMasterModule;

public class StatusMaster : BaseEntity
{

    [StringLength(255)]
    public string? Entity { get; set; }

    [StringLength(50)]
    public string? Title { get; set; }


    public int Value { get; set; }
}

public class StatusMasterConfiguration : BaseEntityConfiguration<StatusMaster>, IEntityTypeConfiguration<StatusMaster>
{
    public void Configure(EntityTypeBuilder<StatusMaster> builder)
    {
        base.Configure(builder);
    }
}