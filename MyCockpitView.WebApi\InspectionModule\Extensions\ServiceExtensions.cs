﻿
using MyCockpitView.WebApi.InspectionModule.Services;

namespace MyCockpitView.WebApi.InspectionModule.Extensions;

public static class ServiceExtensions
{

    public static IServiceCollection RegisterInspectionServices(
     this IServiceCollection services)
    {
        services.AddScoped<IInspectionService,InspectionService>();
        services.AddScoped<IInspectionItemService, InspectionItemService>();
        services.AddScoped<IInspectionItemAttachmentService, InspectionItemAttachmentService>();
        services.AddScoped<IInspectionRecipientService, InspectionRecipientService>();
        return services;
    }
}
