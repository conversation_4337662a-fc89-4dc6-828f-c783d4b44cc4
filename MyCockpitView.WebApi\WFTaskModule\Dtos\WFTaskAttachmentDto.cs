﻿using AutoMapper;
using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.WFTaskModule.Entities;


namespace MyCockpitView.WebApi.WFTaskModule.Dtos;

public class WFTaskAttachmentDto : BaseBlobEntityDto
{

    public int WFTaskID { get; set; }

}

public class WFTaskAttachmentDtoMapperProfile : Profile
{
    public WFTaskAttachmentDtoMapperProfile()
    {


        CreateMap<WFTaskAttachment, WFTaskAttachmentDto>()
                    .ReverseMap()
                    .ForMember(dest => dest.WFTask, opt => opt.Ignore());
    }
}
