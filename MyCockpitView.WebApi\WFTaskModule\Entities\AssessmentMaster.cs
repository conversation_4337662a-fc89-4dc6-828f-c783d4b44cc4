﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using MyCockpitView.CoreModule;

using System.ComponentModel.DataAnnotations;

namespace MyCockpitView.WebApi.WFTaskModule.Entities;

public class AssessmentMaster : BaseEntity
{


    [StringLength(255)]
    
    public string? Category { get; set; }

    
     [Precision(18,2)] public decimal Points { get; set; }
}


public class AssessmentMasterConfiguration : BaseEntityConfiguration<AssessmentMaster>, IEntityTypeConfiguration<AssessmentMaster>
{
    public void Configure(EntityTypeBuilder<AssessmentMaster> builder)
    {
        base.Configure(builder);

        builder
            .HasIndex(x => x.Category).IsUnique();


    }
}
