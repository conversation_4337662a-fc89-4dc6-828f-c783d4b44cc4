﻿using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.CoreModule;

namespace MyCockpitView.WebApi.ProjectModule.Entities;

public class ProjectAttachment : BaseBlobEntity
{
    
    public int ProjectID { get; set; }

    public virtual Project? Project { get; set; }


}


public class ProjectAttachmentConfiguration : BaseBlobEntityConfiguration<ProjectAttachment>, IEntityTypeConfiguration<ProjectAttachment>
{
    public void Configure(EntityTypeBuilder<ProjectAttachment> builder)
    {
        base.Configure(builder);
      
    }
}