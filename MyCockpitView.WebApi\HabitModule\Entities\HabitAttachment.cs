﻿
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.CoreModule;
using System.ComponentModel.DataAnnotations;

namespace MyCockpitView.WebApi.HabitModule.Entities;

public class HabitAttachment : BaseBlobEntity
{
    [Required]
    public int HabitID { get; set; }

    public virtual Habit? Habit { get; set; }
}

public class HabitAttachmentConfiguration : BaseBlobEntityConfiguration<HabitAttachment>, IEntityTypeConfiguration<HabitAttachment>
{
    public void Configure(EntityTypeBuilder<HabitAttachment> builder)
    {
      base.Configure(builder);
    }
}

