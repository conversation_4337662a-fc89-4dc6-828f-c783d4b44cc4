﻿using MyCockpitView.Utility.RDLCClient;
using MyCockpitView.WebApi.ProcessLibraryModule.Entities;
using MyCockpitView.WebApi.Services;

namespace MyCockpitView.WebApi.ProcessLibraryModule.Services;

public interface IProcessLibraryEntityService:IBaseEntityService<ProcessLibraryEntity>
{
    Task<IEnumerable<ProcessLibraryEntity>> FetchRecursiveChildrenAsync(EntitiesContext context, int parentId);
    Task<IEnumerable<ProcessLibraryEntity>> FetchRecursiveParentsAsync(EntitiesContext context, int itemId);
    Task<ReportDefinition> GetProcessListPDF(Guid ParentID, string RenderType = "PDF");
}