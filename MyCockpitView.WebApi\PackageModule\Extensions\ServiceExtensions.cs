﻿
using MyCockpitView.WebApi.PackageModule.Services;

namespace MyCockpitView.WebApi.PackageModule.Extensions;

public static class ServiceExtensions
{

    public static IServiceCollection RegisterPackageServices(
     this IServiceCollection services)
    {
        services.AddScoped<IPackageService,PackageService>();
        services.AddScoped<IPackageAttachmentService, PackageAttachmentService>();
        services.AddScoped<IPackageDeliverableMasterService, PackageDeliverableMasterService>();
        services.AddScoped<IPackageDeliverableService, PackageDeliverableService>();
        services.AddScoped<IPackageDeliverableTaskMapService, PackageDeliverableTaskMapService>();
        services.AddScoped<IPackageAssociationService, PackageAssociationService>();
        services.AddScoped<IPackageFeedbackService, PackageFeedbackService>();
        return services;
    }
}
