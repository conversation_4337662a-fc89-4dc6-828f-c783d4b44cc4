﻿using AutoMapper;
using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.ContactModule.Dtos;
using MyCockpitView.WebApi.ProjectModule.Entities;
using System.ComponentModel.DataAnnotations;

namespace MyCockpitView.WebApi.ProjectModule.Dtos;

public class ProjectConsultantDto : BaseEntityDto
{

    [StringLength(255)]
    public string? Title { get; set; }

    [Required]
    public int ProjectID { get; set; }

    [Required]
    public int ContactID { get; set; }
    public virtual ContactListDto? Contact { get; set; }

    public decimal Fee { get; set; }
}

public class ProjectConsultantDtoMapperProfile : Profile
{
    public ProjectConsultantDtoMapperProfile()
    {
        CreateMap<ProjectConsultant, ProjectConsultantDto>()
                   .ReverseMap();

    }
}