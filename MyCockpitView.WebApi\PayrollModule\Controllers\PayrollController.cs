﻿
using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.WebApi.Responses;
using MyCockpitView.Utility.Common;
using MyCockpitView.WebApi.ActivityModule;
using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.ContactModule.Services;
using MyCockpitView.WebApi.PayrollModule.Services;
using MyCockpitView.WebApi.PayrollModule.Dtos;
using MyCockpitView.WebApi.PayrollModule.Entities;

namespace MyCockpitView.WebApi.PayrollModule.Controllers;

[Authorize]
[ApiController]
[Route("[controller]")]
public class PayrollController : ControllerBase
{
    private readonly IPayrollService service;
    private readonly EntitiesContext db;
    private readonly IActivityService activityService;
    private readonly IMapper mapper;
    private readonly IContactService contactService;
    private readonly IContactAppointmentService contactAppointmentService;

    public PayrollController(EntitiesContext db, IPayrollService service, IMapper mapper, IActivityService activityService, IContactService contactService,IContactAppointmentService contactAppointmentService   )
    {
        this.db = db;
        this.service = service;
        this.activityService = activityService;
        this.mapper = mapper;
        this.contactService = contactService;
        this.contactAppointmentService = contactAppointmentService;
    }

    [HttpGet]
    public async Task<ActionResult<IEnumerable<PayrollDto>>> Get(
        [FromQuery] string? filters = null,
        [FromQuery] string? search = null,
        [FromQuery] string? sort = null)
    {
        var query = service.Get(filters != null ? DataTools.GetObjectFromJsonString<APIFilter>(filters).Filters : null, search, sort);
        var results = mapper.Map<IEnumerable<PayrollDto>>(await query.ToListAsync());

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(Payroll))
            .ToListAsync();

        foreach (var obj in results)
        {
            obj.Type = typeMasters.FirstOrDefault(x => x.Value == obj.TypeFlag)?.Title ?? "";
        }

        var statusMasters = await db.StatusMasters
          .AsNoTracking()
          .Where(x => x.Entity == nameof(Payroll))
          .ToListAsync();

        foreach (var obj in results)
        {
            obj.Status = statusMasters.FirstOrDefault(x => x.Value == obj.StatusFlag)?.Title ?? "";
        }

        return Ok(results);
    }

    [HttpGet("Pages")]
    public async Task<ActionResult<PagedResponse<PayrollDto>>> GetPages(
        [FromQuery] int page = 0,
        [FromQuery] int pageSize = 50,
        [FromQuery] string? filters = null,
        [FromQuery] string? search = null,
        [FromQuery] string? sort = null)
    {
        var query = service.Get(filters != null ? DataTools.GetObjectFromJsonString<APIFilter>(filters).Filters : null, search, sort);

        var totalCount = await query.CountAsync();
        var totalPages = (int)Math.Ceiling((decimal)totalCount / pageSize);
        var results = mapper.Map<IEnumerable<PayrollDto>>(await query
            .Skip(pageSize * page)
            .Take(pageSize)
            .ToListAsync());

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(Payroll))
            .ToListAsync();

        foreach (var obj in results)
        {
            obj.Type = typeMasters.FirstOrDefault(x => x.Value == obj.TypeFlag)?.Title ?? "";
        }

        var statusMasters = await db.StatusMasters
           .AsNoTracking()
           .Where(x => x.Entity == nameof(Payroll))
           .ToListAsync();

        foreach (var obj in results)
        {
            obj.Status = statusMasters.FirstOrDefault(x => x.Value == obj.StatusFlag)?.Title ?? "";
        }

        return Ok(new PagedResponse<PayrollDto>(results, totalCount, totalPages));
    }

    [HttpGet("{id:int}")]
    public async Task<ActionResult<PayrollDto>> GetByID(int id)
    {
        var responseDto = mapper.Map<PayrollDto>(await service.GetById(id));
        if (responseDto == null)
        {
            return NotFound();
        }

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(Payroll))
            .ToListAsync();

        responseDto.Type = typeMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        var statusMasters = await db.StatusMasters
          .AsNoTracking()
          .Where(x => x.Entity == nameof(Payroll))
          .ToListAsync();

        responseDto.Status = statusMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        return Ok(responseDto);
    }

    [HttpPost]
    public async Task<ActionResult<PayrollDto>> Post(PayrollDto dto)
    {
        var id = await service.Create(mapper.Map<Payroll>(dto));
        var responseDto = mapper.Map<PayrollDto>(await service.GetById(id));

        if (responseDto == null)
            return BadRequest("Not Created");

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(Payroll))
            .ToListAsync();

        responseDto.Type = typeMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        var statusMasters = await db.StatusMasters
  .AsNoTracking()
  .Where(x => x.Entity == nameof(Payroll))
  .ToListAsync();

        responseDto.Status = statusMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        //if (!string.IsNullOrEmpty(User.Identity?.Name))
        //{
        //    var currentContact = await contactService.Get().FirstOrDefaultAsync(x => x.Username == User.Identity.Name);
        //    if (currentContact != null)
        //    {
        //        var parent = await contactService.Get().SingleOrDefaultAsync(x => x.ID == responseDto.ContactID);
        //        if (parent != null)
        //        {
        //            await activityService.LogUserActivity(currentContact, nameof(parent), parent.ID, $"{parent.Name}", $"{nameof(Payroll).Replace(nameof(parent),"")} | {responseDto.Company.Title} | {responseDto.Designation} | {responseDto.ManValue}mV", "Created");
        //        }
        //    }
        //}

        return CreatedAtAction(nameof(GetByID), new { id = responseDto.ID }, responseDto);
    }

    [HttpPut("{id:int}")]
    public async Task<ActionResult<PayrollDto>> Put(int id, PayrollDto dto)
    {
        if (id != dto.ID)
        {
            return BadRequest();
        }

        await service.Update(mapper.Map<Payroll>(dto));
        var responseDto = mapper.Map<PayrollDto>(await service.GetById(id));

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(Payroll))
            .ToListAsync();

        responseDto.Type = typeMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        var statusMasters = await db.StatusMasters
  .AsNoTracking()
  .Where(x => x.Entity == nameof(Payroll))
  .ToListAsync();

        responseDto.Status = statusMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        //if (!string.IsNullOrEmpty(User.Identity?.Name))
        //{
        //    var currentContact = await contactService.Get().FirstOrDefaultAsync(x => x.Username == User.Identity.Name);
        //    if (currentContact != null)
        //    {
        //        var parent = await contactService.Get().SingleOrDefaultAsync(x => x.ID == responseDto.ContactID);
        //        if (parent != null)
        //        {
        //            await activityService.LogUserActivity(currentContact, nameof(parent), parent.ID, $"{parent.Name}", $"{nameof(Payroll).Replace(nameof(parent), "")} | {responseDto.Company.Title} | {responseDto.Designation} | {responseDto.ManValue}mV", "Updated");
        //        }
        //    }
        //}

        return Ok(responseDto);
    }

    [HttpDelete("{id:int}")]
    public async Task<IActionResult> Delete(int id)
    {
        var responseDto = mapper.Map<PayrollDto>(await service.GetById(id));
        if (responseDto == null) return BadRequest($"{nameof(Payroll)} not found!");

        await service.Delete(id);

        //if (!string.IsNullOrEmpty(User.Identity?.Name))
        //{
        //    var currentContact = await contactService.Get().FirstOrDefaultAsync(x => x.Username == User.Identity.Name);
        //    if (currentContact != null)
        //    {
        //        var parent = await contactService.Get().SingleOrDefaultAsync(x => x.ID == responseDto.ContactID);
        //        if (parent != null)
        //        {
        //            await activityService.LogUserActivity(currentContact, nameof(parent), parent.ID, $"{parent.Name}", $"{nameof(Payroll).Replace(nameof(parent), "")} | {responseDto.Company.Title} | {responseDto.Designation} | {responseDto.ManValue}mV", "Deleted");
        //        }
        //    }
        //}

        return NoContent();
    }


    [HttpGet("uid/{id:guid}")]
    [AllowAnonymous]
    public async Task<ActionResult<PayrollDto>> GetByGUID(Guid id)
    {
        var responseDto = mapper.Map<PayrollDto>(await service.GetById(id));
        if (responseDto == null)
        {
            return NotFound();
        }

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(Payroll))
            .ToListAsync();

        responseDto.Type = typeMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        var statusMasters = await db.StatusMasters
                        .AsNoTracking()
                        .Where(x => x.Entity == nameof(Payroll))
                        .ToListAsync();

        responseDto.Status = statusMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        return Ok(responseDto);
    }


    [HttpGet("SearchTagOptions")]
    public async Task<IActionResult> GetSearchTagOptions()
    {
        var results = await service.GetSearchTagOptions();
        return Ok(results);
    }


    [HttpGet("FieldOptions")]
    public async Task<IActionResult> GetFieldOptions(string field)
    {
        return Ok(await service.GetFieldOptions(field));
    }

    [AllowAnonymous]
    [HttpGet("Generate")]
    public async Task<IActionResult> GeneratePayrolls(
         [FromQuery] int year = 0,
         [FromQuery] int month = 0)
    {
        var lastMonth = ClockTools.GetISTNow().AddMonths(-1);
        var _selectedMonthIST = new DateTime(year > 0 ? year : lastMonth.Year, month > 0 ? month : lastMonth.Month, 1);
        var selectedMonthUTC = ClockTools.GetUTC(_selectedMonthIST);

        var _appointedContactIDs = await contactAppointmentService.Get()
            .Where(x => x.StatusFlag == McvConstant.APPOINTMENT_STATUSFLAG_APPOINTED)
            .Select(x => x.ContactID).Distinct()
            .ToListAsync();

        var _resignedContactIDs = await contactAppointmentService.Get()
           .Where(x => x.StatusFlag == McvConstant.APPOINTMENT_STATUSFLAG_RESIGNED && x.ResignationDate != null && x.ResignationDate > selectedMonthUTC)
           .Select(x => x.ContactID).Distinct()
           .ToListAsync();


        var _active = await db.Payrolls.AsNoTracking()
            .Where(x => x.StatusFlag == McvConstant.PAYROLL_STATUSFLAG_PENDING)
            .Where(x => x.StartDate < selectedMonthUTC)
            .ToListAsync();
        foreach (var obj in _active)
        {
            obj.StatusFlag = McvConstant.PAYROLL_STATUSFLAG_PAID;
            db.Entry(obj).State = EntityState.Modified;
        }
        await db.SaveChangesAsync();

        foreach (var id in _appointedContactIDs)
        {
            await service.GenerateByContact(id, _selectedMonthIST.Year, _selectedMonthIST.Month);
        }
        foreach (var id in _resignedContactIDs)
        {
            await service.GenerateByContact(id, _selectedMonthIST.Year, _selectedMonthIST.Month);
        }

        return Ok();
    }


    [HttpGet("ReGenerate/{id:int}")]
    public async Task<ActionResult<PayrollDto>> GeneratePayrolls(
        int id,
        [FromQuery] int year = 0,
        [FromQuery] int month = 0)
    {
        var payrollId = await service.GenerateByContact(id, year, month);
        var payroll = await service.GetById(payrollId);

        var result = mapper.Map<PayrollDto>(payroll);
        if (result is null)
            return BadRequest("Not Modified");

        // Get status value
        var statusMasters = await db.StatusMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(Payroll))
            .ToListAsync();

        result.Status = statusMasters
            .FirstOrDefault(x => x.Value == result.StatusFlag)?.Title ?? string.Empty;

        // Get type value
        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(Payroll))
            .ToListAsync();

        result.Type = typeMasters
            .FirstOrDefault(x => x.Value == result.TypeFlag)?.Title ?? string.Empty;

        return Ok(result);
    }

    [HttpGet("Send/{id:int}")]
    public async Task<IActionResult> SendPaySlip(int id)
    {
        await service.SendPaySlip(id);
        return Ok();
    }

    [AllowAnonymous]
    [HttpGet("Report/{reportName}/{size}/{id:guid}")]
    public async Task<IActionResult> Report(
        string reportName,
        string size,
        Guid id,
        [FromQuery] string? filters = null,
        [FromQuery] string? parameters = null,
        [FromQuery] string output = "PDF")
    {
        var deserializedFilters = filters != null ? DataTools.GetObjectFromJsonString<APIFilter>(filters).Filters : null;

        var deserializedParameters = filters != null ? DataTools.GetObjectFromJsonString<dynamic>(filters).Filters : null;

        if (!reportName.Equals("month", StringComparison.OrdinalIgnoreCase))
            return BadRequest("No matching reportname found!");

        var reportDef = await service.GetMonthSlipPDF(id, size, deserializedFilters);

        if (reportDef?.FileContent is null)
            return BadRequest("Report not generated!");

        return File(
            fileContents: reportDef.FileContent,
            contentType: reportDef.FileContentType,
            fileDownloadName: reportDef.Filename + reportDef.FileExtension,
            enableRangeProcessing: true);
    }

    [AllowAnonymous]
    [HttpGet("Analysis/{dataType}/excel")]
    [Produces("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")]
    public async Task<IActionResult> GetVHrAnalysisExcel(
        string dataType,
        [FromQuery] string? filters = null,
        [FromQuery] string? search = null,
        [FromQuery] string? sort = null)
    {
        if (!dataType.Equals("full", StringComparison.OrdinalIgnoreCase))
            return BadRequest();

        var report = await service.GetAnalysisExcel(
           filters != null ? DataTools.GetObjectFromJsonString<APIFilter>(filters).Filters : null,
            search,
            sort);

        if (report is null)
            return BadRequest("Report cannot be generated");

        var fileName = $"{dataType.ToUpper()}-{TimeProvider.System.GetUtcNow():dd-MMM-yyyy}.xlsx";

        return File(
            fileContents: report,
            contentType: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            fileDownloadName: fileName,
            enableRangeProcessing: true);
    }
}