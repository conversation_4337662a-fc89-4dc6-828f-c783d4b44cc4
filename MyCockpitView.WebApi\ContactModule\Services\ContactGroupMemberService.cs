﻿




using Microsoft.EntityFrameworkCore;
using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.ContactModule.Entities;
using MyCockpitView.WebApi.Services;

namespace MyCockpitView.WebApi.ContactModule.Services;

public class ContactGroupMemberService : BaseEntityService<ContactGroupMember>, IContactGroupMemberService
{
    public ContactGroupMemberService(EntitiesContext db) : base(db) { }

    public IQueryable<ContactGroupMember> Get(IEnumerable<QueryFilter>? Filters = null, string? Search = null, string? Sort = null)
    {

        IQueryable<ContactGroupMember> _query = base.Get(Filters);

        //Apply filters
        if (Filters != null)
        {

            if (Filters.Where(x => x.Key.Equals("ContactID", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<ContactGroupMember>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("ContactID", StringComparison.OrdinalIgnoreCase)))
                {
                    var isNumeric = Convert.ToInt32(_item.Value);

                    predicate = predicate.Or(x => x.ContactID == isNumeric);
                }
                _query = _query.Where(predicate);
            }

            if (Filters.Where(x => x.Key.Equals("ContactGroupID", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<ContactGroupMember>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("ContactGroupID", StringComparison.OrdinalIgnoreCase)))
                {
                    var isNumeric = Convert.ToInt32(_item.Value);

                    predicate = predicate.Or(x => x.ContactGroupID == isNumeric);
                }
                _query = _query.Where(predicate);
            }

        }



        return _query.OrderByDescending(x => x.Created);

    }
    public async Task<ContactGroupMember?> GetById(int Id)
    {

        return await db.ContactGroupMembers.AsNoTracking()
           .Include(x => x.Contact)
             .SingleOrDefaultAsync(i => i.ID == Id);


    }

    public async Task<ContactGroupMember?> GetById(Guid Id)
    {

        return await db.ContactGroupMembers.AsNoTracking()
           .Include(x => x.Contact)
             .SingleOrDefaultAsync(i => i.UID == Id);


    }

}