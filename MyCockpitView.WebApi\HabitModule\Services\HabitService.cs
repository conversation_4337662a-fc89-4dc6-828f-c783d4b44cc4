﻿



using System.Data;



using MyCockpitView.WebApi.Exceptions;
using MyCockpitView.WebApi.ContactModule.Entities;
using MyCockpitView.WebApi.Services;
using MyCockpitView.WebApi.HabitModule.Entities;
using MyCockpitView.WebApi.WFTaskModule.Services;
using MyCockpitView.CoreModule;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.Utility.Common;


namespace MyCockpitView.WebApi.HabitModule.Services;

public class HabitService : BaseEntityService<Habit>, IHabitService
{
    public HabitService(EntitiesContext db) : base(db)
    { }


    public IQueryable<Habit> Get(IEnumerable<QueryFilter>? Filters = null, string? Search = null, string? Sort = null)
    {

        IQueryable<Habit> _query = base.Get(Filters);

        //Apply filters
        if (Filters != null)
        {

            if (Filters.Where(x => x.Key.Equals("assignerContactID", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<Habit>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("assignerContactID", StringComparison.OrdinalIgnoreCase)))
                {
                    var isNumeric = Convert.ToInt32(_item.Value);

                    predicate = predicate.Or(x => x.AssignerContactID == isNumeric);
                }
                _query = _query.Where(predicate);
            }
            if (Filters.Where(x => x.Key.Equals("assigneeContactID", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<Habit>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("assigneeContactID", StringComparison.OrdinalIgnoreCase)))
                {
                    var isNumeric = Convert.ToInt32(_item.Value);

                    predicate = predicate.Or(x => x.AssigneeContactID == isNumeric);
                }
                _query = _query.Where(predicate);
            }
            if (Filters.Where(x => x.Key.Equals("entity", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<Habit>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("entity", StringComparison.OrdinalIgnoreCase)))
                {
                    predicate = predicate.Or(x => x.Entity != null && x.Entity == _item.Value);
                }
                _query = _query.Where(predicate);
            }

            if (Filters.Where(x => x.Key.Equals("entityID", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<Habit>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("entityID", StringComparison.OrdinalIgnoreCase)))
                {
                    predicate = predicate.Or(x => x.EntityID != null && x.EntityID.ToString() == _item.Value);
                }
                _query = _query.Where(predicate);
            }
        }

        if (Search != null && Search != String.Empty)
        {
            Search = Search.ToLower();
            _query = _query
                .Include(x => x.AssigneeContact)
                 .Where(x => x.Title.ToLower().Contains(Search.ToLower())
                 || x.Subtitle.ToLower().Contains(Search.ToLower())
                                            || (x.AssigneeContact.FirstName + " " + x.AssigneeContact.LastName).ToLower().Contains(Search.ToLower())
                                            || x._searchTags.ToLower().Contains(Search.ToLower())
                                           );

        }

        if (Sort != null && Sort != String.Empty)
        {
            switch (Sort.ToLower())
            {
                case "createddate":
                    return _query
                            .OrderBy(x => x.Created);

                case "modifieddate":
                    return _query
                            .OrderBy(x => x.Modified);

                case "createddate desc":
                    return _query
                            .OrderByDescending(x => x.Created);

                case "modifieddate desc":
                    return _query
                            .OrderByDescending(x => x.Modified);

                case "duedate":
                    return _query
                            .OrderBy(x => x.DueDate);

                case "duedate desc":
                    return _query
                            .OrderByDescending(x => x.DueDate);
            }
        }

        return _query.OrderBy(x => x.DueDate);

    }

    public async Task<Habit?> GetById(int Id)
    {

     return await db.Habits.AsNoTracking()
          .Include(x => x.AssigneeContact)
                    .Include(x => x.AssignerContact)
                    .Include(x => x.Responses)
                    .Include(x => x.Attachments)
             .SingleOrDefaultAsync(i => i.ID == Id);

    }

    public async Task<int> Create(Habit Entity, ICollection<HabitResponse>? Respones = null)
    {

        var sharedService = new SharedService(db);

        var _startDate = Entity.DueDate.AddDays(-Decimal.ToDouble(Entity.TaskInterval));
        Entity.StartDate = _startDate > DateTime.UtcNow ? _startDate : DateTime.UtcNow;
        Entity.StartDate = ClockTools.GetUTC(ClockTools.GetIST(Entity.StartDate).Date
                    .AddMinutes(await sharedService.GetBusinessStartMinutesIST()));


        if (Respones != null)
        {
            Entity.Responses = Respones.Where(x => !string.IsNullOrEmpty(x.Key)
            && !string.IsNullOrEmpty(x.Key)).ToList();
        }

        var _lastorder = 0;
        var _query = db.Habits.AsNoTracking()
            .Where(x => x.TypeFlag == Entity.TypeFlag);


        if (await _query.AnyAsync())
        {
            _lastorder = await _query
               .MaxAsync(x => x.OrderFlag);
        }

        _lastorder++;

        Entity.OrderFlag = _lastorder;

await base.Create(Entity);


       

        return Entity.ID;

    }

    public async Task<bool> Update(Habit UpdatedEntity, ICollection<HabitResponse>? Responses = null)
    {

        var Entity = await db.Habits.Where(x => x.ID == UpdatedEntity.ID)
            .Include(x => x.Responses)
            .SingleOrDefaultAsync();
        if (Entity == null) return false;

        // Update parent
        //Entity = UpdatedEntity;
        db.Entry(Entity).CurrentValues.SetValues(UpdatedEntity);

        if (Responses != null)
        {

            // Delete children
            foreach (var existingChild in Entity.Responses.ToList())
            {
                if (!Responses.Any(c => c.ID == existingChild.ID))
                    db.HabitResponses.Remove(existingChild);
            }

            // Update and Insert children
            foreach (var childModel in Responses.Where(x => !string.IsNullOrEmpty(x.Key)
                && !string.IsNullOrEmpty(x.Key)).ToList())
            {
                var existingChild = Entity.Responses
                    .Where(c => c.ID == childModel.ID && c.ID != default(int))
                    .SingleOrDefault();

                if (existingChild != null)
                    // Update child
                    db.Entry(existingChild).CurrentValues.SetValues(childModel);
                else
                {
                    childModel.HabitID = Entity.ID;
                    Entity.Responses.Add(childModel);
                }
            }
        }

        await db.SaveChangesAsync();

       


        return true;

    }




    public async Task<IEnumerable<int>> GetAssigneeContactIDs(int entityID, string stageCode, string propertyName)
    {
        var Entity = await Get()
             .Include(x => x.AssigneeContact)
             .Include(x => x.AssignerContact)
             .Where(x => x.ID == entityID).SingleOrDefaultAsync();

        if (Entity == null) throw new EntityServiceException($"{nameof(Habit)} not found!");

        var _list = new List<int>();

        if (stageCode.Equals("HABIT_WORK", StringComparison.OrdinalIgnoreCase) //Work
        || stageCode.Equals("HABIT_REVIEW", StringComparison.OrdinalIgnoreCase)//Review
                                                                           )
        {
            var _type = DataTools.GetPropertyType(Entity, propertyName);
            if (_type == typeof(Contact))
            {
                _list.Add(((Contact)DataTools.GetPropertyValue(Entity, propertyName)).ID);
            }
        }
        else
        {
            throw new EntityServiceException($"{nameof(Habit)} Task assignee not found for stage {stageCode}!");
        }

        return _list;
    }

    public async Task<dynamic> GetTaskByStage(string Entity, int EntityID, string StageCode, string StageTitle, decimal StageDuration, DateTime? FollowUpDate = null)
    {


        var sharedService = new SharedService(db); ;

        var _entity = await Get().SingleOrDefaultAsync(x => x.ID == EntityID);

        if (_entity == null) throw new EntityServiceException($"{nameof(Habit)} not found!");

        var _startTimeSpan = TimeSpan.Parse(await sharedService.GetPresetValue(McvConstant.OFFICE_OPEN_TIME));
        var _endTimeSpan = TimeSpan.Parse(await sharedService.GetPresetValue(McvConstant.OFFICE_CLOSE_TIME));

        var _nextDue = DateTime.UtcNow.AddDays(Decimal.ToDouble(StageDuration)).Date;

        if (FollowUpDate != null)
            _nextDue = FollowUpDate.Value.Date;
        if (StageCode == "HABIT_WORK")//Work
        {
            _nextDue = _entity.DueDate.Date;

            return new
            {
                Title = StageTitle,
                Entity = Entity,
                EntityID = EntityID,
                Subtitle = $"{_entity.Title}-{_entity.Subtitle}",
                WFStageCode = StageCode,
                StartDate = ClockTools.GetUTC(ClockTools.GetIST(_entity.DueDate.AddHours(0 - Decimal.ToDouble(_entity.MHrAssigned))).Date.AddMinutes(_startTimeSpan.TotalMinutes)),
                DueDate = ClockTools.GetUTC(ClockTools.GetIST(_nextDue).Date
                  .AddMinutes(_endTimeSpan.TotalMinutes)),
                MHrAssigned = _entity.MHrAssigned,
                IsPreAssignedTimeTask = false
            };
        }
        return new
        {
            Title = StageTitle,
            Entity = Entity,
            EntityID = EntityID,
            Subtitle = $"#{_entity.OrderFlag.ToString("0000")} | {_entity.Title} | {_entity.Subtitle}",
            WFStageCode = StageCode,
            StartDate = ClockTools.GetUTC(ClockTools.GetISTNow().Date
              .AddMinutes(_startTimeSpan.TotalMinutes)),
            DueDate = ClockTools.GetUTC(ClockTools.GetIST(_nextDue).Date
              .AddMinutes(_endTimeSpan.TotalMinutes)),
            MHrAssigned = 0,
            IsPreAssignedTimeTask = false
        };
    }

    public async Task TaskAction(int EntityID, string StageCode, int WFTaskID, string taskComment = null)
    {
        var Entity = await Get()
            .Include(x => x.Responses)
                      .SingleOrDefaultAsync(x => x.ID == EntityID);

        if (Entity == null) throw new EntityServiceException($"{nameof(Habit)} not found!");

        if (StageCode == "SYS_HABIT_COMPLETE") //Close McvHabit
        {
           
            Entity.StatusFlag = 1;
            db.Entry(Entity).State = EntityState.Modified;
            await db.SaveChangesAsync();


        }
        else if (StageCode == "SYS_HABIT_CONTINUE") //Close McvHabit
        {


            var _newResponses = new List<HabitResponse>();
            foreach (var _action in Entity.Responses.Where(x => x.StatusFlag == 0))
            {
                _newResponses.Add(new HabitResponse
                {
                    Key = _action.Key,
                    ControlType = _action.ControlType,
                    Required = _action.Required,
                    Label = _action.Label,
                    PlaceHolder = _action.PlaceHolder,
                    Hint = _action.Hint,
                    Email = _action.Email,
                    Min = _action.Min,
                    Max = _action.Max,
                    MinLength = _action.MinLength,
                    MaxLength = _action.MaxLength,
                    Options = _action.Options,
                    Order = _action.Order,
                    HabitID = Entity.ID
                });

            }

            foreach (var _action in Entity.Responses.Where(x => x.StatusFlag == 0))
            {

                _action.StatusFlag = 1;
                db.Entry(_action).State = EntityState.Modified;
            }

            foreach (var _action in _newResponses)
            {
                Entity.Responses.Add(_action);
            }


            Entity.DueDate = Entity.DueDate.AddDays(Decimal.ToDouble(Entity.RepeatInterval));
            Entity.RepeatCount++;
            Entity.StatusFlag = 0;
            db.Entry(Entity).State = EntityState.Modified;
            await db.SaveChangesAsync();

        }
        else if (StageCode == "SYS_HABIT_START_TIME") //Start Time
        {
            var timeEntryService = new TimeEntryService(db);
            await timeEntryService.StartTimeLog(WFTaskID);
        }
        else if (StageCode == "SYS_HABIT_PAUSE_TIME") //Pause Time
        {
            var timeEntryService = new TimeEntryService(db);
            await timeEntryService.EndTimeLog(WFTaskID, true);
        }
        else if (StageCode == "SYS_HABIT_STOP_TIME") //Stop Time
        {
            var timeEntryService = new TimeEntryService(db);
            await timeEntryService.EndTimeLog(WFTaskID);
        }

    }
}