﻿using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.CoreModule;
using System.ComponentModel.DataAnnotations;

namespace MyCockpitView.WebApi.LibraryModule.Entities;

public class LibraryEntityVendor : BaseEntity
{
    public int LibraryEntityID { get; set; }
    public virtual LibraryEntity? LibraryEntity { get; set; }

    public int? ContactID { get; set; }

    [StringLength(255)]
    public string? Name { get; set; }
    [StringLength(255)]
    public string? Phone { get; set; }
    [StringLength(255)]
    public string? Email { get; set; }

}
public class LibraryEntityVendorConfiguration : BaseEntityConfiguration<LibraryEntityVendor>, IEntityTypeConfiguration<LibraryEntityVendor>
{
    public void Configure(EntityTypeBuilder<LibraryEntityVendor> builder)
    {
      base.Configure(builder);

        builder.HasIndex(e => e.ContactID);
        builder.HasIndex(e => e.Name);
        builder.HasIndex(e => e.Phone);
        builder.HasIndex(e => e.Email);
    }
}