﻿




using Microsoft.EntityFrameworkCore;
using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.Services;
using MyCockpitView.WebApi.WFTaskModule.Entities;

namespace MyCockpitView.WebApi.WFTaskModule.Services;

public class AssessmentService : BaseEntityService<Assessment>, IAssessmentService
{
    public AssessmentService(EntitiesContext db) : base(db) { }
    public IQueryable<Assessment> Get(IEnumerable<QueryFilter>? Filters = null, string? Search = null, string? Sort = null)
    {

        var _query = base.Get(Filters);


        if (Search != null && Search != String.Empty)
        {
            _query = _query.Include(x => x.Contact);

            _query = _query.Where(x => x.TaskTitle.ToLower().Contains(Search.ToLower())
                                    || x.Entity.ToLower().Contains(Search.ToLower())
                                    || x.EntityTitle.ToLower().Contains(Search.ToLower())
                                           || (x.Contact.FirstName + " " + x.Contact.LastName).ToLower().Contains(Search.ToLower())
                                       );

        }

        //Apply filters
        if (Filters != null)
        {


            if (Filters.Where(x => x.Key.Equals("entity", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<Assessment>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("entity", StringComparison.OrdinalIgnoreCase)))
                {
                    predicate = predicate.Or(x => x.Entity != null && x.Entity == _item.Value);
                }
                _query = _query.Where(predicate);
            }

            if (Filters.Where(x => x.Key.Equals("EntityID", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<Assessment>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("EntityID", StringComparison.OrdinalIgnoreCase)))
                {
                    predicate = predicate.Or(x => x.EntityID != null && x.EntityID.ToString() == _item.Value);
                }
                _query = _query.Where(predicate);
            }

            if (Filters.Where(x => x.Key.Equals("ContactID", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<Assessment>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("ContactID", StringComparison.OrdinalIgnoreCase)))
                {
                    var isNumeric = Convert.ToInt32(_item.Value);

                    predicate = predicate.Or(x => x.ContactID == isNumeric);
                }
                _query = _query.Where(predicate);
            }

            if (Filters.Where(x => x.Key.Equals("WftaskID", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<Assessment>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("WFtaskID", StringComparison.OrdinalIgnoreCase)))
                {
                    var isNumeric = Convert.ToInt32(_item.Value);

                    predicate = predicate.Or(x => x.WFTaskID != null && x.WFTaskID == isNumeric);
                }
                _query = _query.Where(predicate);
            }

        }

        if (Sort != null && Sort != String.Empty)
        {
            switch (Sort.ToLower())
            {
                case "createddate":
                    return _query
                            .OrderByDescending(x => x.Created);

                case "modifieddate":
                    return _query
                            .OrderByDescending(x => x.Modified);

            }
        }

        return _query
          .OrderByDescending(x => x.Created);

    }

    public async Task<Assessment?> GetById(int Id)
    {

        return
            await db.Assessments.AsNoTracking()
            .Include(x => x.Contact)
            .SingleOrDefaultAsync(x => x.ID == Id);

    }

    public async Task Delete(int Id)
    {

        var _entity = await Get()
             .SingleOrDefaultAsync(i => i.ID == Id);
        if (_entity == null)
        {
            return;
        }

        await base.Delete(Id);

        if (_entity.WFTaskID != null)
        {

            var wfTask = await db.WFTasks.AsNoTracking()
            .Include(x => x.Assessments)
                .Where(x => x.ID == _entity.WFTaskID)
                .SingleOrDefaultAsync();
            if (wfTask != null)
            {

                wfTask.AssessmentPoints = wfTask.Assessments.Where(x => x.ID != Id).Any() ? wfTask.Assessments.Where(x => x.ID != Id).Select(x => x.ScoredPoints).Sum() : 0;

                wfTask.MHrAssessed = Math.Round(
                                        wfTask.MHrAssigned * wfTask.AssessmentPoints / 10.0m, 2);


                wfTask.VHrAssessed = Math.Round(wfTask.MHrAssessed * wfTask.ManValue, 2);
                wfTask.VHrAssessedCost = Math.Round(wfTask.VHrAssessed * wfTask.VHrRate, 2);

                db.Entry(wfTask).State = EntityState.Modified;
            }
        }


    }



}
