﻿


using System.Data;



using MyCockpitView.WebApi.Exceptions;
using System.Text.RegularExpressions;
using System.Text;

using MyCockpitView.WebApi.ContactModule.Entities;
using MyCockpitView.WebApi.ProjectModule.Entities;
using MyCockpitView.WebApi.Services;
using MyCockpitView.WebApi.RequestTicketModule.Entities;
using MyCockpitView.CoreModule;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.Utility.Common;
using MyCockpitView.WebApi.WFTaskModule.Services;
using MyCockpitView.WebApi.ContactModule.Services;

namespace MyCockpitView.WebApi.RequestTicketModule.Services;

public class RequestTicketService : BaseEntityService<RequestTicket>, IRequestTicketService
{
    public RequestTicketService(EntitiesContext db) : base(db) { }

    public IQueryable<RequestTicket> Get(IEnumerable<QueryFilter>? Filters = null, string? Search = null, string? Sort = null)
    {
        IQueryable<RequestTicket> _query = base.Get(Filters);

        //Apply filters
        if (Filters != null)
        {

            if (Filters.Where(x => x.Key.Equals("assignerContactID", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<RequestTicket>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("assignerContactID", StringComparison.OrdinalIgnoreCase)))
                {
                    var isNumeric = Convert.ToInt32(_item.Value);

                    predicate = predicate.Or(x => x.AssignerContactID == isNumeric);
                }
                _query = _query.Where(predicate);
            }

            if (Filters.Where(x => x.Key.Equals("entity", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<RequestTicket>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("entity", StringComparison.OrdinalIgnoreCase)))
                {
                    predicate = predicate.Or(x => x.Entity != null && x.Entity == _item.Value);
                }
                _query = _query.Where(predicate);
            }

            if (Filters.Where(x => x.Key.Equals("entityID", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<RequestTicket>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("entityID", StringComparison.OrdinalIgnoreCase)))
                {
                    predicate = predicate.Or(x => x.EntityID != null && x.EntityID.ToString() == _item.Value);
                }
                _query = _query.Where(predicate);
            }

            if (Filters.Where(x => x.Key.Equals("projectid", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<RequestTicket>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("projectid", StringComparison.OrdinalIgnoreCase)))
                {
                    predicate = predicate.Or(x => x.ProjectID != null && x.ProjectID.ToString() == _item.Value);
                }
                _query = _query.Where(predicate);
            }

            if (Filters.Where(x => x.Key.Equals("IsVersion", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<RequestTicket>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("IsVersion", StringComparison.OrdinalIgnoreCase)))
                {
                    var IsVersion = Convert.ToBoolean(_item.Value);

                    predicate = predicate.Or(x => x.IsVersion == IsVersion);
                }
                _query = _query.Where(predicate);
            }

            if (Filters.Where(x => x.Key.Equals("parentID", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<RequestTicket>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("parentID", StringComparison.OrdinalIgnoreCase)))
                {
                    var isNumeric = Convert.ToInt32(_item.Value);

                    predicate = predicate.Or(x => x.ParentID != null && x.ParentID == isNumeric);
                }
                _query = _query.Where(predicate);
            }
        }

        if (Search != null && Search != String.Empty)
        {
            _query = _query.Include(x => x.AssignerContact)
                 .Where(x => x.Title.ToLower().Contains(Search.ToLower())
                 || x.Code.ToLower().Contains(Search.ToLower())
                                            || (x.AssignerContact.FirstName + " " + x.AssignerContact.LastName).ToLower().Contains(Search.ToLower())
                                            || x._searchTags.ToLower().Contains(Search.ToLower())
                                           );

        }

        if (Sort != null && Sort != String.Empty)
        {
            switch (Sort.ToLower())
            {
                case "createddate":
                    return _query
                            .OrderBy(x => x.Created);

                case "modifieddate":
                    return _query
                            .OrderBy(x => x.Modified);

                case "createddate desc":
                    return _query
                            .OrderByDescending(x => x.Created);

                case "modifieddate desc":
                    return _query
                            .OrderByDescending(x => x.Modified);

                case "nextReminderDate":
                    return _query
                            .OrderBy(x => x.NextReminderDate);

                case "nextReminderDate desc":
                    return _query
                            .OrderByDescending(x => x.NextReminderDate);
            }
        }

        return _query.OrderBy(x => x.NextReminderDate);

    }

    public async Task<RequestTicket?> GetById(int Id)
    {
        return await db.RequestTickets.AsNoTracking()
            .Include(x => x.Assignees)
                      .Include(x => x.AssignerContact)
                       .Include(x => x.Attachments)
               .SingleOrDefaultAsync(i => i.ID == Id);
    }


    public async Task<int> Create(RequestTicket Entity)
    {

        if (Entity.ProjectID != null)
        {
            var _project = await db.Projects.AsNoTracking().SingleOrDefaultAsync(x => x.ID == Entity.ProjectID);
            if (_project != null)
            {
                Entity.Title = _project.Code + "-" + _project.Title;
            }
        }

        if (Entity.Title == null || Entity.Title == string.Empty || Entity.Subtitle == null || Entity.Subtitle == string.Empty)
            throw new EntityServiceException("Title and subtitle is required. Please enter proper title and subtitle!");

        var _lastOrder = await Get().AnyAsync() ? (await Get().OrderByDescending(x => x.OrderFlag).FirstOrDefaultAsync()).OrderFlag : 0;

        Entity.OrderFlag = _lastOrder + 1;
        Entity.Code = Entity.OrderFlag.ToString("0000");

        if (Entity.Entity != null && Entity.Entity == nameof(Project) && Entity.EntityID != null)
        {
            var _project = await db.Projects.AsNoTracking().SingleOrDefaultAsync(x => x.ID == Entity.EntityID);
            if (_project != null)
            {
                Entity.Title = _project.Code + "-" + _project.Title;
                Entity.EntityTitle = _project.Code + "-" + _project.Title;
            }
        }

        await base.Create(Entity);

        return Entity.ID;

    }

    public async Task Update(RequestTicket Entity, bool IsSendUpdate = false)
    {
        if (Entity == null) throw new EntityServiceException("Object is null!");

        if (IsSendUpdate)
        {
            //await RecordRequestTicketVersion(Entity.ID);
            await RecordReadonly(Entity.ID);
        }

        if (Entity.Entity != null && Entity.Entity == nameof(Project) && Entity.EntityID != null)
        {
            var _project = await db.Projects.AsNoTracking().SingleOrDefaultAsync(x => x.ID == Entity.EntityID);
            if (_project != null)
            {
                Entity.Title = _project.Code + "-" + _project.Title;
                Entity.EntityTitle = _project.Code + "-" + _project.Title;
            }
        }

        await base.Update(Entity);

        if (IsSendUpdate)
            await SendRequestTicket(Entity.ID); //SEND Updated EMAIL


    }

    public async Task Delete(int Id)
    {

        var taskService = new WFTaskService(db);
        if (await taskService.Get()
          .Where(x => x.Entity != null && x.EntityID == Id && x.Entity == nameof(RequestTicket))
         .Where(x => x.StatusFlag == 2).AnyAsync())
            throw new Exception("One of the tasks is InProgress. Please complete the task and try again!");

        var _taskIDs = await taskService.Get()
          .Where(x => x.Entity != null && x.EntityID == Id && x.Entity == nameof(RequestTicket))
         .Where(x => x.StatusFlag != McvConstant.WFTASK_STATUSFLAG_COMPLETED && x.StatusFlag != McvConstant.WFTASK_STATUSFLAG_UNATTENDED)
         .Select(x => x.ID)
          .ToListAsync();


        foreach (var _task in _taskIDs)
        {
            await taskService.Delete(_task);
        }

        await base.Delete(Id);



    }


    public async Task SendRequestTicket(int ID)
    {

        var _requestTicket = await db.RequestTickets

                        .AsNoTracking()
                                         .Where(x => !x.IsVersion)
            .Include(x => x.Assignees)
            .Include(x => x.AssignerContact)
            .Include(x => x.Attachments)
            .SingleOrDefaultAsync(x => x.ID == ID);

        if (_requestTicket == null) throw new EntityServiceException("Request Ticket not Found!");
        var sharedService = new SharedService(db); ;
        var _emailSenderName = await sharedService.GetPresetValue(McvConstant.REQUEST_TICKET_EMAIL_SENDER_NAME);
        var _emailSenderAddress = await sharedService.GetPresetValue(McvConstant.REQUEST_TICKET_EMAIL_SENDER_ID);
        var _defaultCCList = await sharedService.GetPresetValue(McvConstant.REQUEST_TICKET_EMAIL_CC);


        var _emailSubject = $"Request For {_requestTicket.Purpose} | {_requestTicket.Title} | {_requestTicket.Subtitle}";

        if (_requestTicket.StatusFlag == 0 && _requestTicket.RepeatCount > 0)
        {
            _emailSubject = $"Request For {_requestTicket.Purpose} | {_requestTicket.Title} | {_requestTicket.Subtitle} | Gentle Reminder {_requestTicket.RepeatCount.ToString("00")}";
        }
        else if (_requestTicket.StatusFlag == McvConstant.REQUEST_TICKET_STATUSFLAG_CLOSED)
        {
            _emailSubject = $"Request Resolved & Closed | {_requestTicket.Purpose} | {_requestTicket.Title} | {_requestTicket.Subtitle}";
        }

        var _emailMessage = _requestTicket.RequestMessage;
        if (_requestTicket.StatusFlag == McvConstant.REQUEST_TICKET_STATUSFLAG_CLOSED)
        {
            _emailMessage = _requestTicket.ResolutionMessage == null ? @"Dear Sir/Madam,

Thank you for completing the request." :
    _requestTicket.ResolutionMessage;
        }

        var _toAddresses = new List<EmailContact>();
        foreach (var obj in _requestTicket.Assignees.Where(x => x.TypeFlag == 0))
        {
            _toAddresses.Add(new EmailContact { Name = obj.FullName, Email = obj.Email });
        }

        var _ccAddresses = new List<EmailContact>();
        foreach (var obj in _requestTicket.Assignees.Where(x => x.TypeFlag == 1).Where(x => !_toAddresses.Any(a => a.Equals(x.Email))))
        {
            _ccAddresses.Add(new EmailContact { Name = obj.FullName, Email = obj.Email });
        }
        if (!_toAddresses.Any(a => a.Equals(_requestTicket.AssignerContact.Email1)) && !_ccAddresses.Any(a => a.Equals(_requestTicket.AssignerContact.Email1)))
            _ccAddresses.Add(new EmailContact { Name = _requestTicket.AssignerContact.Name, Email = _requestTicket.AssignerContact.Email1 });


        //default CC
        if (_defaultCCList != null)
        {
            Regex myRegex = new Regex(McvConstant.EMAIL_REGEX, RegexOptions.None);
            foreach (Match myMatch in myRegex.Matches(_defaultCCList))
            {
                if (!_toAddresses.Any(a => a.Equals(myMatch.Value.Trim())) && !_ccAddresses.Any(a => a.Equals(myMatch.Value.Trim())))
                    _ccAddresses.Add(new EmailContact { Name = "", Email = myMatch.Value.Trim() });

            }
        }

        var _replyToEmail = _requestTicket.AssignerContact.Email1;
        var _replyParameter = $"mailto:{_replyToEmail}?cc=";

        foreach (var email in _toAddresses)
        {
            if (!email.Equals(_replyToEmail))
                _replyParameter = _replyParameter + email + ";";
        }
        foreach (var email in _ccAddresses)
        {
            if (!email.Equals(_replyToEmail))
                _replyParameter = _replyParameter + email + ";";
        }


        var historyList = await db.RequestTickets

                      .AsNoTracking()
                      .Where(x => x.IsVersion)
          .Include(x => x.Assignees)
          .Include(x => x.AssignerContact)
          .Include(x => x.Attachments)
          .Where(x => x.ParentID == ID)
               .OrderByDescending(x => x.Created)
            .Take(5)
            .ToListAsync();

        _replyParameter = _replyParameter + $"&subject=RE:{_emailSubject.ToUpper()}";


        var sb = new StringBuilder();

        sb.AppendLine("<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Transitional//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd\">");
        sb.AppendLine("<html xmlns=\"http://www.w3.org/1999/xhtml\">");
        sb.AppendLine("<head>");
        sb.AppendLine("    <meta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\" />");
        sb.AppendLine("    <title>Email Design</title>");
        sb.AppendLine("    <meta name=\"viewport\" content=\"width=device-width; initial-scale=1.0;\" />");
        sb.AppendLine("    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=9; IE=8; IE=7; IE=EDGE\" />");
        sb.AppendLine("    <meta name=\"format-detection\" content=\"telephone=no\" />");
        sb.AppendLine("    <!--[if gte mso 9]><xml>");
        sb.AppendLine("    <o:OfficeDocumentSettings>");
        sb.AppendLine("    <o:AllowPNG />");
        sb.AppendLine("    <o:PixelsPerInch>96</o:PixelsPerInch>");
        sb.AppendLine("    </o:OfficeDocumentSettings>");
        sb.AppendLine("    </xml><![endif]-->");
        sb.AppendLine("    <style type=\"text/css\">");
        sb.AppendLine("        /* Some resets and issue fixes */");
        sb.AppendLine("        #outlook a {");
        sb.AppendLine("            padding: 0;");
        sb.AppendLine("        }");
        sb.AppendLine("");
        sb.AppendLine("        body {");
        sb.AppendLine("            width: 100% !important;");
        sb.AppendLine("            margin: 0;");
        sb.AppendLine("            -webkit-text-size-adjust: 100%;");
        sb.AppendLine("            -ms-text-size-adjust: 100%;");
        sb.AppendLine("        }");
        sb.AppendLine("");
        sb.AppendLine("        table {");
        sb.AppendLine("            mso-table-lspace: 0px;");
        sb.AppendLine("            mso-table-rspace: 0px;");
        sb.AppendLine("        }");
        sb.AppendLine("");
        sb.AppendLine("            table td {");
        sb.AppendLine("                border-collapse: collapse;");
        sb.AppendLine("            }");
        sb.AppendLine("");
        sb.AppendLine("        .ExternalClass * {");
        sb.AppendLine("            line-height: 115%;");
        sb.AppendLine("        }");
        sb.AppendLine("        /* End reset */");

        sb.AppendLine("    </style>");
        sb.AppendLine("</head>");
        sb.AppendLine("");
        sb.AppendLine("<body>");
        sb.AppendLine("");
        sb.AppendLine("");
        sb.AppendLine("    <div style=\"margin: 0 auto;font-family:Calibri;font-size:14px;line-height:1.8;padding-left:5px;padding-right:5px; max-width:500px;\">");
        sb.AppendLine("");
        sb.AppendLine("        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; border-collapse: collapse;\">");
        sb.AppendLine("            <tr>");
        sb.AppendLine("                <td style=\"font-size:16px; font-weight:bold;\">");
        sb.AppendLine(_emailSubject.ToUpper());
        sb.AppendLine("                </td>");
        sb.AppendLine("            </tr>");
        sb.AppendLine("        </table>");
        sb.AppendLine("");
        sb.AppendLine("<div style=\"border-bottom: 1px solid #cccccc; margin-top: 15px; margin-bottom: 15px; \"></div>");

        sb.AppendLine("        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; border-collapse: collapse;\">");
        if (_requestTicket.Entity != null)
        {
            sb.AppendLine("            <tr>");
            sb.AppendLine("                <td valign=\"top\" width=\"150\" style=\"padding-bottom:5px;font-weight:bold;\">");
            sb.AppendLine(_requestTicket.Entity + ":");
            sb.AppendLine("                </td>");
            sb.AppendLine("                <td valign=\"top\" style=\"padding-bottom:5px;\">");
            sb.AppendLine(_requestTicket.EntityTitle);
            sb.AppendLine("                </td>");
            sb.AppendLine("            </tr>");
        }
        sb.AppendLine("            <tr>");
        sb.AppendLine("                <td valign=\"top\" width=\"150\" style=\"padding-bottom:5px;font-weight:bold;\">");
        sb.AppendLine("                    Request For:");
        sb.AppendLine("                </td>");
        sb.AppendLine("                <td valign=\"top\" style=\"padding-bottom:5px;\">");
        sb.AppendLine(_requestTicket.Purpose);
        sb.AppendLine("                </td>");
        sb.AppendLine("            </tr>");

        sb.AppendLine("            <tr>");
        sb.AppendLine("                <td valign=\"top\" width=\"150\" style=\"padding-bottom:5px;font-weight:bold;\">");
        sb.AppendLine("                    Request Date:");
        sb.AppendLine("                </td>");
        sb.AppendLine("                <td valign=\"top\" style=\"padding-bottom:5px;\">");
        sb.AppendLine(ClockTools.GetIST(_requestTicket.Created).ToString("dd MMM yyyy"));
        sb.AppendLine("                </td>");
        sb.AppendLine("            </tr>");
        sb.AppendLine("            <tr>");
        sb.AppendLine("                <td valign=\"top\" width=\"150\" style=\"padding-bottom:5px;font-weight:bold;\">");
        sb.AppendLine("                   Request Code:");
        sb.AppendLine("                </td>");
        sb.AppendLine("                <td valign=\"top\" style=\"padding-bottom:5px;\">");
        sb.AppendLine(_requestTicket.Code);
        sb.AppendLine("                </td>");
        sb.AppendLine("            </tr>");
        sb.AppendLine("            <tr>");
        sb.AppendLine("                <td valign=\"top\" width=\"150\" style=\"padding-bottom:5px;font-weight:bold;\">");
        sb.AppendLine("                    Requested By:");
        sb.AppendLine("                </td>");
        sb.AppendLine("                <td valign=\"top\" style=\"padding-bottom:5px;\">");
        sb.AppendLine(_requestTicket.AssignerContact.FullName + " (" + _requestTicket.AssignerContact.Email1 + ")");

        sb.AppendLine("                </td>");
        sb.AppendLine("            </tr>");
        if (_requestTicket.StatusFlag != 1)
        {
            sb.AppendLine("            <tr>");
            sb.AppendLine("                <td valign=\"top\" width=\"150\" style=\"padding-bottom:5px;font-weight:bold;\">");
            sb.AppendLine("                    Next Reminder on:");
            sb.AppendLine("                </td>");
            sb.AppendLine("                <td valign=\"top\" style=\"padding-bottom:5px;\">");
            sb.AppendLine(ClockTools.GetIST(_requestTicket.NextReminderDate).ToString("dd MMM yyyy"));
            sb.AppendLine("                </td>");
            sb.AppendLine("            </tr>");
        }
        sb.AppendLine("        </table>");
        sb.AppendLine("");
        sb.AppendLine("        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; border-collapse: collapse;\">");
        sb.AppendLine("            <tr>");
        sb.AppendLine("                <td valign=\"top\" style=\"font-weight:bold;\">");
        sb.AppendLine("                    To:");
        sb.AppendLine("                </td>");
        sb.AppendLine("            </tr>");
        sb.AppendLine("            <tr>");
        sb.AppendLine("                <td valign=\"top\" style=\"padding-bottom:5px;\">");
        sb.AppendLine("                    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; border-collapse: collapse;\">");

        foreach (var obj in _requestTicket.Assignees.Where(x => x.TypeFlag == 0))
        {
            sb.AppendLine("                        <tr>");
            sb.AppendLine("                            <td >");
            sb.AppendLine(obj.FullName + " <i> (" + obj.Email + ")</i>");
            sb.AppendLine("                            </td>");
            sb.AppendLine("                        </tr>");
        }

        sb.AppendLine("                    </table>");
        sb.AppendLine("                </td>");
        sb.AppendLine("            </tr>");
        sb.AppendLine("            <tr>");
        sb.AppendLine("                <td valign=\"top\" style=\"font-weight:bold;\">");
        sb.AppendLine("                    CC:");
        sb.AppendLine("                </td>");
        sb.AppendLine("            </tr>");
        sb.AppendLine("            <tr>");
        sb.AppendLine("                <td valign=\"top\" style=\"padding-bottom:5px;\">");
        sb.AppendLine("                    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; border-collapse: collapse;\">");
        foreach (var obj in _requestTicket.Assignees.Where(x => x.TypeFlag == 1))
        {
            sb.AppendLine("                        <tr>");
            sb.AppendLine("                            <td >");
            sb.AppendLine(obj.FullName + " <i> (" + obj.Email + ")</i>");
            sb.AppendLine("                            </td>");
            sb.AppendLine("                        </tr>");
        }
        sb.AppendLine("                    </table>");
        sb.AppendLine("                </td>");
        sb.AppendLine("            </tr>");

        if (_requestTicket.Attachments.Any())
        {

            sb.AppendLine("            <tr>");
            sb.AppendLine("                 <td valign=\"top\" style=\"font-weight:bold;\">");
            sb.AppendLine(" Kindly click the below links to download: ");
            sb.AppendLine("                </td>");
            sb.AppendLine("            </tr>");

            var _index = 1;
            foreach (var attachment in _requestTicket.Attachments)
            {
                sb.AppendLine("            <tr>");
                sb.AppendLine("                <td valign=\"top\" style=\"padding-bottom:5px;\">");
                sb.AppendLine("                    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; border-collapse: collapse;\">");

                sb.AppendLine("            <tr>");
                sb.AppendLine("                <td >");
                sb.AppendLine(_index.ToString("D2"));
                sb.AppendLine("                </td>");
                sb.AppendLine("                <td >");
                sb.AppendLine($"                    <a href=\"{attachment.Url}\">{attachment.Filename}</a>");

                sb.AppendLine("                </td>");
                sb.AppendLine("            </tr>");
                sb.AppendLine("                    </table>");
                sb.AppendLine("                </td>");
                sb.AppendLine("            </tr>");
                _index++;
            }
        }

        sb.AppendLine("        </table>");
        sb.AppendLine("<div style=\"border-bottom: 1px solid #cccccc; margin-top: 15px; margin-bottom: 15px; \"></div>");

        sb.AppendLine("");
        sb.AppendLine("        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; border-collapse: collapse;\">");
        sb.AppendLine("            <tr>");
        sb.AppendLine("                <td valign=\"top\" style=\"padding-top:5px;padding-bottom:5px;\">");
        sb.AppendLine("                    <pre style=\"font-family:Calibri;line-height:1.5; font-size: 14px;margin-top:0;margin-bottom:0; white-space: pre-wrap; white-space: -moz-pre-wrap;");
        sb.AppendLine("                                white-space: -pre-wrap; white-space: -o-pre-wrap; word-wrap: break-word;\">");

        sb.AppendLine(_emailMessage);

        sb.AppendLine("</pre>");
        sb.AppendLine("                </td>");
        sb.AppendLine("            </tr>");
        sb.AppendLine("        </table>");
        sb.AppendLine("");


        sb.AppendLine(" <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; border-collapse: collapse;\">");
        sb.AppendLine("            <tr>");
        sb.AppendLine("                <td valign=\"top\" style=\"font-weight:bold;\">");
        sb.AppendLine("                    History:");
        sb.AppendLine("                </td>");
        sb.AppendLine("            </tr>");
        sb.AppendLine("        </table>");

        var _count = 0;
        foreach (var obj in historyList)
        {
            sb.AppendLine(" <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%;font-size: 11px; border-collapse: collapse;\">");
            sb.AppendLine("            <tr>");
            sb.AppendLine("                <td valign=\"top\" style=\"padding-bottom:5px;\">");
            sb.AppendLine($"{ClockTools.GetIST(obj.Modified).ToString("dd MMM yyyy HH:mm")}");
            sb.AppendLine("                </td>");
            sb.AppendLine("                <td valign=\"top\" style=\"padding-bottom:5px;\" align=\"right\">");
            sb.AppendLine(obj.RepeatCount != 0 ? "Reminder " + obj.RepeatCount.ToString("00") : "Requested");
            sb.AppendLine("                </td>");
            sb.AppendLine("            </tr>");
            sb.AppendLine("            <tr>");
            //sb.AppendLine("                <td width=\"25%\" valign=\"top\" style=\"padding-bottom:5px;\">");
            //sb.AppendLine(obj.RepeatCount.ToString("00"));
            //sb.AppendLine("                </td>");
            sb.AppendLine("                <td colspan=\"2\" valign=\"top\" style=\"padding-bottom:5px;\">");
            sb.AppendLine("                    <pre style=\"font-family:Calibri;line-height:1.1; font-size: 12px;margin-top:0;margin-bottom:0; white-space: pre-wrap; white-space: -moz-pre-wrap;font-style: italic;");
            sb.AppendLine("                                white-space: -pre-wrap; white-space: -o-pre-wrap; word-wrap: break-word;\">");

            sb.AppendLine(obj.RequestMessage);

            sb.AppendLine("</pre>");
            sb.AppendLine("                </td>");
            sb.AppendLine("            </tr>");
            sb.AppendLine("        </table>");
            _count++;

            if (_count < historyList.Count())
            {
                sb.AppendLine("<div style=\"border-bottom: 1px solid #cccccc; margin-bottom: 10px; \"></div>");
            }
        }

        if (historyList.Count() > 5)
        {
            sb.AppendLine("<div style=\"border-bottom: 1px solid #cccccc; margin-bottom: 10px; \"></div>");

            sb.AppendLine(" <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; border-collapse: collapse;\">");
            sb.AppendLine("                        <tr>");
            sb.AppendLine("                            <td valign=\"top\" align=\"center\" colspan=\"3\" style=\"padding-bottom:5px; \"> ");
            sb.AppendLine((historyList.Count() - 5).ToString("00") + " more items.");
            sb.AppendLine("                            </td>");
            sb.AppendLine("                        </tr>");


            sb.AppendLine("        </table>");
        }


        //FOOTER
        sb.AppendLine("<div style=\"border-bottom: 1px solid #cccccc; margin-top: 15px; margin-bottom: 15px; \"></div>");
        sb.AppendLine("        <table align=\"center\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; border-collapse: collapse;font-size:11px;\">");

        sb.AppendLine("");
        sb.AppendLine("            <tr>");
        sb.AppendLine("");
        sb.AppendLine("                <td align=\"center\" >");
        sb.AppendLine("This is a <b>MyCockpitView<sup>&copy;</sup></b> & <b>DesignScript<sup>&copy;</sup></b> generated e-mail for your information and necessary action.");
        sb.AppendLine("</td>");
        sb.AppendLine("            </tr>");
        sb.AppendLine("");
        sb.AppendLine("            <tr>");
        sb.AppendLine("");
        sb.AppendLine("                <td align=\"center\" >");
        sb.AppendLine("");
        sb.AppendLine("                    Powered by <b>Newarch<sup>&reg;</sup> Infotech LLP</b>");
        sb.AppendLine("");
        sb.AppendLine("                </td>");
        sb.AppendLine("            </tr>");
        sb.AppendLine("        </table>");
        sb.AppendLine("    </div>");

        sb.AppendLine("</body>");
        sb.AppendLine("");
        sb.AppendLine("</html>");

        var emailTo = new List<(string name, string email)>();
        foreach (var obj in _toAddresses)
            emailTo.Add((obj.Name, obj.Email));

        var emailCC = new List<(string name, string email)>();
        foreach (var obj in _ccAddresses)
            emailCC.Add((obj.Name, obj.Email));



        await sharedService.SendMail(
                _emailSubject.ToUpper(),
                _emailSenderName,
                _emailSenderAddress,
                sb.ToString(),
                emailTo,
                emailCC);


    }



    //public async Task RecordRequestTicketVersion(int ID)
    //{

    //    //RECORD ENTITY HISTORY
    //    var Entity = await db.RequestTickets
    //            
    //                    .AsNoTracking()
    //      .Include(x => x.Assignees)
    //                .Include(x => x.Assigner)
    //                 .Include(x => x.Attachments)
    //         .SingleOrDefaultAsync(i => i.ID == ID);

    //    db.RequestTicketVersions.Add(new RequestTicketVersion
    //    {
    //        RequestTicketID = Entity.ID,
    //        JsonValue = JsonConvert.SerializeObject(Entity, Formatting.Indented, new JsonSerializerSettings
    //        {
    //            ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
    //            //PreserveReferencesHandling = PreserveReferencesHandling.All
    //        }),
    //        Created = DateTime.UtcNow
    //    });

    //    await db.SaveChangesAsync();

    //}

    public async Task<Guid> RecordReadonly(int ID)
    {

        //RECORD ENTITY HISTORY
        var entity = await Get()
            .Where(x => !x.IsVersion)
             .Include(x => x.AssignerContact)
                    .Include(x => x.Assignees)
                     .Include(x => x.Attachments)
                    .SingleOrDefaultAsync(x => x.ID == ID);

        if (entity == null) throw new EntityServiceException("RequestTicket not found!");

        var newEntity = new RequestTicket();
        // Set values from the original entity to the new entity
        db.Entry(newEntity).CurrentValues.SetValues(entity);
        // Detach the newAttendee if it's already being tracked
        if (db.Entry(newEntity).State != EntityState.Detached)
        {
            db.Entry(newEntity).State = EntityState.Detached;
        }
        newEntity.ID = default(int);
        newEntity.UID = default(Guid);
        newEntity.Created = DateTime.UtcNow;
        newEntity.Modified = DateTime.UtcNow;
        // Create a new entity with a deep copy of the original entity's properties

        db.Entry(newEntity).State = EntityState.Added;

        // Add the new entity to the context
        db.RequestTickets.Add(newEntity);


        // Reset the ID property
        newEntity.ID = 0;
        newEntity.ParentID = entity.ID;
        newEntity.IsVersion = true;


        foreach (var item in entity.Assignees)
        {
            // Create a new instance and copy the values
            var newAttendee = new RequestTicketAssignee();
            db.Entry(newAttendee).CurrentValues.SetValues(item);

            // Detach the newAttendee if it's already being tracked
            if (db.Entry(newAttendee).State != EntityState.Detached)
            {
                db.Entry(newAttendee).State = EntityState.Detached;
            }

            // Set ID to default (assuming ID is an int)
            newAttendee.ID = default(int);
            newAttendee.UID = default(Guid);
            // Attach the new entity to the context
            db.Entry(newAttendee).State = EntityState.Added;

            // Add the new entity to the context
            db.RequestTicketAssignees.Add(newAttendee);

            // Add the new entity to the collection
            newEntity.Assignees.Add(newAttendee);

        }

        foreach (var item in entity.Attachments)
        {
            // Create a new instance and copy the values
            var newAttachment = new RequestTicketAttachment();
            db.Entry(newAttachment).CurrentValues.SetValues(item);

            // Detach the newAttachment if it's already being tracked
            if (db.Entry(newAttachment).State != EntityState.Detached)
            {
                db.Entry(newAttachment).State = EntityState.Detached;
            }

            // Set ID to default (assuming ID is an int)
            newAttachment.ID = default(int);
            newAttachment.UID = default(Guid);
            // Attach the new entity to the context
            db.Entry(newAttachment).State = EntityState.Added;
            // Add the new entity to the context
            db.RequestTicketAttachments.Add(newAttachment);

            newEntity.Attachments.Add(newAttachment);
        }

        await db.SaveChangesAsync();
        return newEntity.UID;

    }

    public async Task<IEnumerable<int>> GetAssigneeContactIDs(int entityID, string stageCode, string propertyName)
    {
        var Entity = await Get()
               .Where(x => !x.IsVersion)
          //.Include(x => x.Assignee)
          .Include(x => x.AssignerContact)
          .Where(x => x.ID == entityID).SingleOrDefaultAsync();

        if (Entity == null) throw new EntityServiceException($"{nameof(RequestTicket)} not found!");

        var _list = new List<int>();

        if (stageCode.Equals("REQUEST_TICKET_FOLLOW_UP", StringComparison.OrdinalIgnoreCase)) //Accept RequestTicket
        {
            var _type = DataTools.GetPropertyType(Entity, propertyName);
            if (_type == typeof(Contact))
            {
                _list.Add(((Contact)DataTools.GetPropertyValue(Entity, propertyName)).ID);
            }
        }
        else
        {
            throw new EntityServiceException($"{nameof(RequestTicket)} Task assignee not found for stage {stageCode}!");
        }

        return _list;
    }

    public async Task<dynamic> GetTaskByStage(string Entity, int EntityID, string StageCode, string StageTitle, decimal StageDuration, DateTime? FollowUpDate = null)
    {


        var sharedService = new SharedService(db); ;

        var _entity = await Get().SingleOrDefaultAsync(x => x.ID == EntityID);

        if (_entity == null) throw new EntityServiceException($"{nameof(RequestTicket)} not found!");

        var _startTimeSpan = TimeSpan.Parse(await sharedService.GetPresetValue(McvConstant.OFFICE_OPEN_TIME));
        var _endTimeSpan = TimeSpan.Parse(await sharedService.GetPresetValue(McvConstant.OFFICE_CLOSE_TIME));

        var _nextDue = _entity.NextReminderDate.Date;

        if (FollowUpDate != null)
            _nextDue = FollowUpDate.Value.Date;

        var start = DateTime.UtcNow;
        if (start > _nextDue)
            start = _nextDue;

        return new
        {
            Title = StageTitle,
            Entity = Entity,
            EntityID = EntityID,
            Subtitle = $"{_entity.Purpose} | {_entity.Title} | {_entity.Subtitle}",
            WFStageCode = StageCode,
            StartDate = ClockTools.GetUTC(ClockTools.GetIST(start).Date
              .AddMinutes(_startTimeSpan.TotalMinutes)),
            DueDate = ClockTools.GetUTC(ClockTools.GetIST(_nextDue).Date
              .AddMinutes(_endTimeSpan.TotalMinutes)),
            MHrAssigned = 0,
            IsPreAssignedTimeTask = false
        };
    }

    public async Task TaskAction(int EntityID, string StageCode, int WFTaskID, string taskComment = null)
    {
        var Entity = await Get()
                      .SingleOrDefaultAsync(x => x.ID == EntityID);

        if (Entity == null) throw new EntityServiceException($"{nameof(RequestTicket)} not found!");

        if (StageCode == "SYS_REQUEST_TICKET_COMPLETE") //Close RequestTicket
        {
            Entity.StatusFlag = McvConstant.REQUEST_TICKET_STATUSFLAG_CLOSED;
            Entity.ResolutionMessage = taskComment;
            db.Entry(Entity).State = EntityState.Modified;
            await db.SaveChangesAsync();

            await SendRequestTicket(Entity.ID); //SEND CLOSURE EMAIL
        }

    }
}