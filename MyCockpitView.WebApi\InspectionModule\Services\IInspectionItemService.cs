﻿using MyCockpitView.WebApi.InspectionModule.Entities;
using MyCockpitView.WebApi.Services;

namespace MyCockpitView.WebApi.InspectionModule.Services;

public interface IInspectionItemService : IBaseEntityService<InspectionItem>
{
    Task AssignItemTasks(int itemID);
    Task CompleteItemTasks(int itemID);
    Task GenerateTasks();
    Task<IEnumerable<int>> GetAssigneeContactIDs(int entityID, string stageCode, string propertyName);
    IQueryable<InspectionItem> GetByInspection(int InspectionID);
    IQueryable<InspectionItemGroup> GetGroups(IQueryable<InspectionItem> ItemQuery, string[]? GroupBy = null);
    Task<IEnumerable<InspectionItem>> GetPreviousItem(int ItemID);
    Task<dynamic> GetTaskByStage(string Entity, int EntityID, string StageCode, string StageTitle, decimal StageDuration, DateTime? FollowUpDate = null);
}