﻿
using MyCockpitView.WebApi.RequestTicketModule.Services;

namespace MyCockpitView.WebApi.RequestTicketModule.Extensions;

public static class ServiceExtensions
{

    public static IServiceCollection RegisterRequestTicketServices(
     this IServiceCollection services)
    {
        services.AddScoped<IRequestTicketService, RequestTicketService>();
        services.AddScoped<IRequestTicketAttachmentService, RequestTicketAttachmentService>();
        services.AddScoped<IRequestTicketAssigneeService, RequestTicketAssigneeService>();
        return services;
    }
}
