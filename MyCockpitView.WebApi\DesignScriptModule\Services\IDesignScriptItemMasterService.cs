﻿using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.DesignScriptModule.Entities;
using MyCockpitView.WebApi.Services;

namespace MyCockpitView.WebApi.DesignScriptModule.Services;

public interface IDesignScriptItemMasterService : IBaseEntityService<DesignScriptItemMaster>
{
    Task<string> GetListReport(IEnumerable<QueryFilter> filters = null, string Search = null, string Sort = null, string RenderType = "PDF");
}