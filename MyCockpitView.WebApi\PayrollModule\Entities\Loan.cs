﻿
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using MyCockpitView.CoreModule;
using MyCockpitView.Utility.Common;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace MyCockpitView.WebApi.PayrollModule.Entities;

public class Loan : BaseEntity
{
    [Required]
    
    public int PersonContactID { get; set; }

    
    [StringLength(50)]
    public string? PersonName { get; set; }

    
    [StringLength(50)]
    public string? PersonBankAccountNo { get; set; }

    [Required]
    
    public int CompanyID { get; set; }

    
    [StringLength(50)]
    public string? CompanyName { get; set; }

    
    [StringLength(50)]
    public string? CompanyBankAccountNo { get; set; }

    public decimal LoanAmount { get; set; }

    public decimal InstallmentAmount { get; set; }


    [Column("Installments")]
    public string? _installments { get; set; }

    [NotMapped]
    public ICollection<LoanInstallment> Installments
    {
        get
        {
            return _installments != null && _installments != string.Empty ?
                DataTools.GetObjectFromJsonString<ICollection<LoanInstallment>>(_installments) :
                new List<LoanInstallment>();
        }
        set
        {
            _installments = value.Count != 0 ? DataTools.GetJsonStringFromObject(value) : null;
        }
    }

}
public class LoanInstallment
{
    [Column(TypeName = "datetime2")]
    public DateTime PaymentDate { get; set; }
    public decimal Amount { get; set; } = 0.0m;
    public string? Remark { get; set; }

    public DateTime? PayrollDate { get; set; }
}


public class LoanConfiguration : BaseEntityConfiguration<Loan>, IEntityTypeConfiguration<Loan>
{
    public void Configure(EntityTypeBuilder<Loan> builder)
    {

     base.Configure(builder);

        builder.HasIndex(x => x.PersonContactID);
    }

}
