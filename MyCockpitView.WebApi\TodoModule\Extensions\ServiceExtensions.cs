﻿
using MyCockpitView.WebApi.TodoModule.Services;

namespace MyCockpitView.WebApi.TodoModule.Extensions;

public static class ServiceExtensions
{

    public static IServiceCollection RegisterTodoServices(
     this IServiceCollection services)
    {
        services.AddScoped<ITodoService, TodoService>();
        services.AddScoped<ITodoAttachmentService, TodoAttachmentService>();
        services.AddScoped<ITodoAgendaService, TodoAgendaService>();
        return services;
    }
}
