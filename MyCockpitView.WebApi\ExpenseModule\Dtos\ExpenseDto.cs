﻿using AutoMapper;
using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.CompanyModule.Dtos;
using MyCockpitView.WebApi.ContactModule.Dtos;
using MyCockpitView.WebApi.DesignScriptModule.Dtos;
using MyCockpitView.WebApi.DesignScriptModule.Entities;
using MyCockpitView.WebApi.ExpenseModule.Entities;
using MyCockpitView.WebApi.ProjectModule.Dtos;

namespace MyCockpitView.WebApi.ExpenseModule.Dtos;

public class ExpenseListDto : BaseEntityDto
{
    public string? Code { get; set; }
    public string? Particulars { get; set; }
    public DateTime ExpenseDate { get; set; }

    public decimal AmountDr { get; set; }

    public decimal AmountCr { get; set; }

    public string? ExpenseHead { get; set; }

    public string? PaymentMode { get; set; }
    public string? PayTo { get; set; }
    public string? ApprovedBy { get; set; }

    public decimal AmountBalance { get; set; }
    public int? EntityID { get; set; }
    public string? Entity { get; set; }
    public string? EntityTitle { get; set; }



}
public class ExpenseDto : ExpenseListDto
{


    public string? Narration { get; set; }



    public DateTime? TransactionDate { get; set; }



    public string? TransactionMode { get; set; }



    public string? TransactionRefNo { get; set; }

    public string? TransactionDetails { get; set; }



    public int? PayToContactID { get; set; }


    public int? ApprovedByContactID { get; set; }
    public virtual ContactListDto? ApprovedByContact { get; set; }


    public string? PAN { get; set; }


    public string? TAN { get; set; }


    public string? GST { get; set; }

    public virtual ICollection<ExpenseAttachmentDto> Attachments { get; set; }=new List<ExpenseAttachmentDto>();

    public virtual ICollection<Tax> Taxes { get; set; } = new List<Tax>();

    public int? ProjectID { get; set; }

    public virtual ProjectListDto? Project { get; set; }

    public int CompanyID { get; set; }
    public virtual CompanyDto? CompanyAccount { get; set; }

    public decimal ExpenseAmount { get; set; }
    public decimal AmountAfterTax { get; set; }
    public decimal TDSAmount { get; set; }
    public decimal TDSPercentage { get; set; }

}

public class ExpenseDtoMapperProfile : Profile
{
    public ExpenseDtoMapperProfile()
    {


        CreateMap<Expense, ExpenseDto>()
             .ReverseMap()
               .ForMember(dest => dest.Company, opt => opt.Ignore())
             //.ForMember(dest => dest.Project, opt => opt.Ignore())
             .ForMember(dest => dest.Attachments, opt => opt.Ignore())
             .ForMember(dest => dest.ApprovedByContact, opt => opt.Ignore());

        CreateMap<Expense, ExpenseListDto>();
    }
}
