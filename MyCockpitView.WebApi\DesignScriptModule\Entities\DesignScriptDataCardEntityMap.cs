﻿using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.CoreModule;

namespace MyCockpitView.WebApi.DesignScriptModule.Entities;

public class DesignScriptDataCardEntityMap : BaseEntity
{
    public int DesignScriptDataCardID { get; set; }
    public virtual DesignScriptDataCard? DataCard { get; set; }
    public int DesignScriptEntityID { get; set; }
    public virtual DesignScriptEntity? Entity { get; set; }
}
public class DesignScriptDataCardEntityMapConfiguration : BaseEntityConfiguration<DesignScriptDataCardEntityMap>, IEntityTypeConfiguration<DesignScriptDataCardEntityMap>
{
    public void Configure(EntityTypeBuilder<DesignScriptDataCardEntityMap> builder)
    {
       base.Configure(builder);

    }
}