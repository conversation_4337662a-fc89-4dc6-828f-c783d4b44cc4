﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using MyCockpitView.CoreModule;
using System.ComponentModel.DataAnnotations;

namespace MyCockpitView.WebApi.PackageModule.Entities
{
    public class PackageDeliverableMaster : BaseEntity
    {
        [StringLength(255)]
        public string?  Title { get; set; }

        
        [StringLength(255)]
        public string? Category { get; set; }

        
        [StringLength(255)]
        public string? Code { get; set; }

        
        [StringLength(255)]
        public string? StageService { get; set; }
    }

    public class PackageDeliverableMasterConfiguration : BaseEntityConfiguration<PackageDeliverableMaster>, IEntityTypeConfiguration<PackageDeliverableMaster>
    {
        public void Configure(EntityTypeBuilder<PackageDeliverableMaster> builder)
        {
            base.Configure(builder);
            builder.HasIndex(x => x.Title);
            builder.HasIndex(x => x.Category);
            builder.HasIndex(x => x.Code);
            builder.HasIndex(x => x.StageService);
        }
    }
}
