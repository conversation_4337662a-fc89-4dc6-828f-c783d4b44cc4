﻿using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.CoreModule;
using System.ComponentModel.DataAnnotations;


namespace MyCockpitView.WebApi.InspectionModule.Entities;

public class InspectionItemAttachment : BaseBlobEntity
{
    [Required]
    public int InspectionItemID { get; set; }

    public virtual InspectionItem? InspectionItem { get; set; }
}

public class InspectionItemAttachmentConfiguration : BaseBlobEntityConfiguration<InspectionItemAttachment>, IEntityTypeConfiguration<InspectionItemAttachment>
{
    public void Configure(EntityTypeBuilder<InspectionItemAttachment> builder)
    {
     base.Configure(builder);
    }
}