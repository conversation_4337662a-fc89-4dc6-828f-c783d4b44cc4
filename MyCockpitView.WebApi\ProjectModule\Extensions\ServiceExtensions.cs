﻿
using MyCockpitView.WebApi.ProjectModule.Services;

namespace MyCockpitView.WebApi.ProjectModule.Extensions;

public static class ServiceExtensions
{

    public static IServiceCollection RegisterProjectServices(
     this IServiceCollection services)
    {
        services.AddScoped<IProjectService, ProjectService>();
        services.AddScoped<IProjectAttachmentService, ProjectAttachmentService>();
        services.AddScoped<IProjectAssociationService, ProjectAssociationService>();
        services.AddScoped<IProjectConsultantService, ProjectConsultantService>();
        services.AddScoped<IProjectGigPointService, ProjectGigPointService>();
        services.AddScoped<IProjectBillService, ProjectBillService>();
        services.AddScoped<IProjectInwardService, ProjectInwardService>();
        services.AddScoped<IProjectInwardAttachmentService, ProjectInwardAttachmentService>();
        services.AddScoped<IProjectOutwardService, ProjectOutwardService>();
        services.AddScoped<IProjectOutwardAttachmentService, ProjectOutwardAttachmentService>();
        services.AddScoped<IProjectScope_Service, ProjectScope_Service>();
        services.AddScoped<IProjectScopeServiceService, ProjectScopeServiceService>();
        services.AddScoped<IProjectScopeVersionService, ProjectScopeVersionService>();
        services.AddScoped<IProjectNoteService, ProjectNoteService>();
        services.AddScoped<IProjectScopeServiceMasterService, ProjectScopeServiceMasterService>();
        return services;
    }
}
