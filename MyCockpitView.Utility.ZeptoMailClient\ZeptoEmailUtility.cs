﻿using System.Diagnostics;
using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;

namespace MyCockpitView.Utility.ZeptoMailClient;

/// <summary>
/// Custom exception for Zepto email service errors
/// </summary>
public class ZeptoEmailException : Exception
{
    public ZeptoEmailException(string message) : base(message) { }
    public ZeptoEmailException(string message, Exception innerException) : base(message, innerException) { }
}
public class ZeptoEmailUtility
{
    // Maximum attachment size in bytes (25MB)
    private const long MaxAttachmentSizeBytes = 25 * 1024 * 1024;

    // Static JsonSerializerOptions to avoid creating new instances for each call
    private static readonly JsonSerializerOptions JsonOptions = new()
    {
        WriteIndented = false,
        DefaultIgnoreCondition = System.Text.Json.Serialization.JsonIgnoreCondition.WhenWritingNull,
        Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
    };

    // Static HttpClient to reuse connections
    private static readonly HttpClient HttpClient = new();

    static ZeptoEmailUtility()
    {
        // Configure the static HttpClient
        HttpClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
        // Timeout can be adjusted as needed
        HttpClient.Timeout = TimeSpan.FromSeconds(30);
    }

    /// <summary>
    /// Sends an email using the Zepto API with optional attachments
    /// </summary>
    public static async Task SendEmailAsync(
        string apiAddress,
        string apiKey,
        string subject,
        string senderName,
        string senderAddress,
        string htmlBody,
        List<(string name, string email)> toAddresses,
        List<(string name, string email)>? ccAddresses = null,
        List<(string name, string email)>? bccAddresses = null,
        string? replyAddress = null,
        string? replyName = null,
        List<ZeptoEmailAttachment>? attachments = null)
    {
        // Validate required parameters
        if (string.IsNullOrWhiteSpace(apiAddress))
            throw new ArgumentException("API address cannot be null or empty", nameof(apiAddress));

        if (string.IsNullOrWhiteSpace(apiKey))
            throw new ArgumentException("API key cannot be null or empty", nameof(apiKey));

        if (string.IsNullOrWhiteSpace(subject))
            throw new ArgumentException("Subject cannot be null or empty", nameof(subject));

        if (string.IsNullOrWhiteSpace(senderAddress))
            throw new ArgumentException("Sender address cannot be null or empty", nameof(senderAddress));

        if (toAddresses == null || toAddresses.Count == 0)
            throw new ArgumentException("At least one recipient address is required", nameof(toAddresses));

        // Validate attachment size
        if (attachments != null && attachments.Count > 0)
        {
            long totalSize = attachments.Sum(a => a.Content.Length);
            if (totalSize > MaxAttachmentSizeBytes)
            {
                throw new ArgumentException($"Total attachment size ({FormatFileSize(totalSize)}) exceeds maximum allowed size of 25MB.");
            }
        }

        // Process HTML body to properly handle special characters
        string processedHtmlBody = PrepareHtmlBody(htmlBody);

        // Create a copy of the default headers to avoid modifying the static HttpClient
        using var requestMessage = new HttpRequestMessage(HttpMethod.Post, apiAddress);
        requestMessage.Headers.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
        requestMessage.Headers.Add("Authorization", apiKey);

        var emailPayload = new
        {
            from = new { address = senderAddress, name = senderName },
            to = toAddresses.ConvertAll(item => new { email_address = new { address = item.email, name = item.name } }),
            cc = ccAddresses?.ConvertAll(item => new { email_address = new { address = item.email, name = item.name } }),
            bcc = bccAddresses?.ConvertAll(item => new { email_address = new { address = item.email, name = item.name } }),
            reply_to = !string.IsNullOrEmpty(replyAddress) && !string.IsNullOrEmpty(replyName)
                ? new { address = replyAddress, name = replyName }
                : null,
            subject = subject,
            htmlbody = processedHtmlBody,
            attachments = (attachments != null && attachments.Count > 0)
                ? attachments.Select(att => new
                {
                    filename = att.FileName,
                    content = Convert.ToBase64String(att.Content),
                    content_type = att.ContentType
                }).ToArray()
                : null
        };

        // Use the static JsonOptions
        string jsonPayload = JsonSerializer.Serialize(emailPayload, JsonOptions);

        try
        {
            // Set the content of the request
            requestMessage.Content = new StringContent(jsonPayload, Encoding.UTF8, "application/json");

            // Send the request using the static HttpClient
            var response = await HttpClient.SendAsync(requestMessage);

            if (!response.IsSuccessStatusCode)
            {
                var errorResponse = await response.Content.ReadAsStringAsync();
                throw new ZeptoEmailException($"Email service error! Status: {response.StatusCode}, Response: {errorResponse}");
            }
        }
        catch (ZeptoEmailException)
        {
            // Re-throw ZeptoEmailException without wrapping to preserve the original error details
            throw;
        }
        catch (HttpRequestException e)
        {
            Debug.WriteLine(e.Message + " \n" + e.StackTrace);
            throw new ZeptoEmailException("Email service connection error", e);
        }
        catch (TaskCanceledException e)
        {
            Debug.WriteLine(e.Message + " \n" + e.StackTrace);
            throw new ZeptoEmailException("Email service request timed out", e);
        }
        catch (Exception e)
        {
            Debug.WriteLine(e.Message + " \n" + e.StackTrace);
            throw new ZeptoEmailException("Email service error", e);
        }
    }

    /// <summary>
    /// Prepares HTML body content for JSON serialization by handling special characters
    /// </summary>
    private static string PrepareHtmlBody(string htmlBody)
    {
        if (string.IsNullOrEmpty(htmlBody))
            return string.Empty;

        // The JSON serializer will handle escaping of special characters
        // We don't need to manually escape newlines as the JsonSerializerOptions
        // with UnsafeRelaxedJsonEscaping will handle this correctly

        return htmlBody;
    }

    /// <summary>
    /// Synchronous version of SendEmailAsync for backward compatibility
    /// </summary>
    /// <remarks>
    /// This method uses Task.Run to avoid potential deadlocks that can occur with GetAwaiter().GetResult()
    /// in synchronization contexts like ASP.NET.
    /// </remarks>
    public static void SendEmail(
        string apiAddress,
        string apiKey,
        string subject,
        string senderName,
        string senderAddress,
        string htmlBody,
        List<(string name, string email)> toAddresses,
        List<(string name, string email)>? ccAddresses = null,
        List<(string name, string email)>? bccAddresses = null,
        string? replyAddress = null,
        string? replyName = null,
        List<ZeptoEmailAttachment>? attachments = null)
    {
        try
        {
            // Use Task.Run to avoid deadlocks in synchronization contexts
            Task.Run(async () =>
            {
                await SendEmailAsync(apiAddress, apiKey, subject, senderName, senderAddress, htmlBody,
                    toAddresses, ccAddresses, bccAddresses, replyAddress, replyName, attachments);
            }).GetAwaiter().GetResult();
        }
        catch (AggregateException ae)
        {
            // Unwrap AggregateException to get the original exception
            if (ae.InnerException != null)
                throw ae.InnerException;
            throw;
        }
    }

    /// <summary>
    /// Formats file size in bytes to a human-readable format
    /// </summary>
    private static string FormatFileSize(long bytes)
    {
        string[] suffixes = ["B", "KB", "MB", "GB", "TB"];
        int counter = 0;
        decimal number = bytes;

        while (Math.Round(number / 1024) >= 1)
        {
            number /= 1024;
            counter++;
        }

        return $"{number:n2} {suffixes[counter]}";
    }
}


/// <summary>
/// Represents an email attachment
/// </summary>
public class ZeptoEmailAttachment
{
    public string FileName { get; set; } = string.Empty;
    public byte[] Content { get; set; } = [];
    public string ContentType { get; set; } = "application/octet-stream";

    /// <summary>
    /// Creates an attachment from a file path
    /// </summary>
    public static ZeptoEmailAttachment FromFile(string filePath)
    {
        return new ZeptoEmailAttachment
        {
            FileName = Path.GetFileName(filePath),
            Content = File.ReadAllBytes(filePath),
            ContentType = GetMimeTypeFromExtension(Path.GetExtension(filePath))
        };
    }

    public static ZeptoEmailAttachment FromBase64(string fileName, string base64Content)
    {
        return new ZeptoEmailAttachment
        {
            FileName = fileName,
            Content = Convert.FromBase64String(base64Content),
            ContentType = GetMimeTypeFromExtension(Path.GetExtension(fileName))
        };
    }

    /// <summary>
    /// Helper method to determine MIME type from file extension
    /// </summary>
    private static string GetMimeTypeFromExtension(string extension)
    {
        return extension.ToLower() switch
        {
            ".pdf" => "application/pdf",
            ".doc" => "application/msword",
            ".docx" => "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            ".xls" => "application/vnd.ms-excel",
            ".xlsx" => "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            ".png" => "image/png",
            ".jpg" => "image/jpeg",
            ".jpeg" => "image/jpeg",
            ".gif" => "image/gif",
            ".txt" => "text/plain",
            _ => "application/octet-stream"
        };
    }
}