{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": ".NET Core Launch (web)",
            "type": "coreclr",
            "request": "launch",
            "preLaunchTask": "build",
            "program": "${workspaceFolder}/MyCockpitView.WebApi/bin/Debug/net8.0/MyCockpitView.WebApi.dll",
            "args": [],
            "cwd": "${workspaceFolder}/MyCockpitView.WebApi",
            "stopAtEntry": false,
            "serverReadyAction": {
                "action": "openExternally",
                "pattern": "\\bNow listening on:\\s+(https?://\\S+)"
            },
            "env": {
                "ASPNETCORE_ENVIRONMENT": "Development"
            },
            "sourceFileMap": {
                "/Views": "${workspaceFolder}/Views"
            },
            "logging": {
                "moduleLoad": false
            }
        },
        {
            "name": ".NET Core Attach",
            "type": "coreclr",
            "request": "attach",
            "processId": "${command:pickProcess}"
        },
        {
            "name": ".NET Core Watch",
            "type": "coreclr",
            "request": "launch",
            "preLaunchTask": "watch",
            "program": "dotnet",
            "args": [
                "watch",
                "--project",
                "${workspaceFolder}/MyCockpitView.WebApi/MyCockpitView.WebApi.csproj"
            ],
            "cwd": "${workspaceFolder}/MyCockpitView.WebApi",
            "env": {
                "ASPNETCORE_ENVIRONMENT": "Development"
            },
            "console": "integratedTerminal"
        }
    ]
}