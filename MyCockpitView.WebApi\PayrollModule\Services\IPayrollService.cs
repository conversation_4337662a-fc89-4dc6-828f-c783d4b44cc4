﻿using MyCockpitView.CoreModule;
using MyCockpitView.Utility.RDLCClient;
using MyCockpitView.WebApi.PayrollModule.Entities;
using MyCockpitView.WebApi.Services;

namespace MyCockpitView.WebApi.PayrollModule.Services;

public interface IPayrollService:IBaseEntityService<Payroll>
{
    Task<int> GenerateByContact(int contactID, int year, int month);
    Task<byte[]> GetAnalysisExcel(IEnumerable<QueryFilter>? Filters = null, string? Search = null, string? Sort = null);
    Task<ReportDefinition> GetMonthSlipPDF(Guid id, string reportSize = "a4", IEnumerable<QueryFilter>? filters = null);
    Task SendPaySlip(int ID);
}