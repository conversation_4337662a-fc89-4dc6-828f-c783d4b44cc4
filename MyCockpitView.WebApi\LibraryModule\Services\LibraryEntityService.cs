﻿

using System.Data;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.Exceptions;
using MyCockpitView.WebApi.LibraryModule.Entities;
using MyCockpitView.WebApi.Services;

namespace MyCockpitView.WebApi.LibraryModule.Services;

public class LibraryEntityService : BaseEntityService<LibraryEntity>, ILibraryEntityService
{
    public LibraryEntityService(EntitiesContext db) : base(db) { }

    public IQueryable<LibraryEntity> Get(IEnumerable<QueryFilter>? Filters = null, string? Search = null, string? Sort = null)
    {

        IQueryable<LibraryEntity> _query = base.Get(Filters);

        //Apply filters
        if (Filters != null)
        {
            if (Filters.Where(x => x.Key.Equals("deleted", StringComparison.OrdinalIgnoreCase)).Any())
            {

                _query = db.LibraryEntities
                        .AsNoTracking();
            }


            if (Filters.Where(x => x.Key.Equals("category", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<LibraryEntity>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("category", StringComparison.OrdinalIgnoreCase)))
                {
                    predicate = predicate.Or(x => x.Category.Contains(_item.Value));
                }
                _query = _query.Where(predicate);
            }

        }

        if (Search != null && Search != String.Empty)
        {
            var _keywords = Search.Split(' ');

            foreach (var _key in _keywords)
            {
                _query = _query
                .Include(x => x.Attributes)
                     .Where(x => x.Title.ToLower().Contains(_key.ToLower())
                     || x.Subtitle.ToLower().Contains(_key.ToLower())
                     || x.Code.ToLower().Contains(_key.ToLower())
                     || x._searchTags.ToLower().Contains(_key.ToLower())
                     || x.Attributes.Any(a => a.AttributeValue.ToLower().Contains(_key.ToLower()))
                     );
            }
        }

        if (Sort != null && Sort != String.Empty)
        {
            switch (Sort.ToLower())
            {
                case "createddate":
                    return _query
                            .OrderBy(x => x.Created);

                case "modifieddate":
                    return _query
                            .OrderBy(x => x.Modified);

                case "createddate desc":
                    return _query
                            .OrderByDescending(x => x.Created);

                case "modifieddate desc":
                    return _query
                            .OrderByDescending(x => x.Modified);


            }
        }

        return _query.OrderBy(x => x.OrderFlag);

    }

    public async Task<LibraryEntity?> GetById(int Id)
    {

        var query = await db.LibraryEntities.AsNoTracking()
                    .Include(x => x.Attachments)
                     .Include(x => x.Vendors)
                      .Include(x => x.Attributes)
             .SingleOrDefaultAsync(i => i.ID == Id);

        return query;

    }

    public async Task<LibraryEntity?> GetById(Guid Id)
    {

        var query = await db.LibraryEntities.AsNoTracking()
                    .Include(x => x.Attachments)
                     .Include(x => x.Vendors)
                     .Include(x => x.Attributes)
             .SingleOrDefaultAsync(i => i.UID == Id);

        return query;

    }

    public async Task<int> Create(LibraryEntity Entity)
    {

        if (Entity.Title == null || Entity.Title == string.Empty)
            throw new EntityServiceException("Title is required. Please enter proper title!");

        var _currentYearStart = new DateTime(DateTime.UtcNow.Year, 1, 1);
        var _count = await db.LibraryEntities.AsNoTracking().Where(x => x.Created > _currentYearStart && x.TypeFlag == Entity.TypeFlag).AnyAsync() ?
              await db.LibraryEntities.AsNoTracking().Where(x => x.Created > _currentYearStart && x.TypeFlag == Entity.TypeFlag).MaxAsync(x => x.CodeFlag) : 0;

        var _typeValue = await db.TypeMasters.AsNoTracking()
            .SingleOrDefaultAsync(x => x.Entity==nameof(LibraryEntity) && x.Value == Entity.TypeFlag);

        Entity.CodeFlag = _count + 1;
        Entity.Code = _typeValue.Title.Substring(0, 2) + DateTime.UtcNow.ToString("yy") + Entity.CodeFlag.ToString("D4");


        Entity.OrderFlag = Entity.CodeFlag;

        if (await db.LibraryEntities.AsNoTracking()
            .Where(x => x.Code==Entity.Code)
            .AnyAsync())
            throw new EntityServiceException("Entity already exists!");

        Entity.ID = await base.Create(Entity);



        return Entity.ID;

    }

    public async Task Update(LibraryEntity UpdatedEntity)
    {

        if (UpdatedEntity == null) throw new EntityServiceException("Object is null!");

        var _entity = await db.LibraryEntities.Include(x => x.Attributes)
            .SingleOrDefaultAsync(x => x.ID == UpdatedEntity.ID);

        if (_entity == null) throw new EntityServiceException("Object is null!");

        //db.Entry(_entity).CurrentValues.SetValues(UpdatedEntity);

        // Update or Add attributes
        foreach (var updatedAttr in UpdatedEntity.Attributes)
        {
            var existingAttr = _entity.Attributes.FirstOrDefault(attr =>
                attr.AttributeKey == updatedAttr.AttributeKey);

            if (existingAttr != null)
            {
                // Update the existing attribute
                //db.Entry(existingAttr).CurrentValues.SetValues(updatedAttr);
                existingAttr.AttributeValue = updatedAttr.AttributeValue;
                existingAttr.OrderFlag = updatedAttr.OrderFlag;
            }
            else
            {
                // Add new attribute
                _entity.Attributes.Add(updatedAttr);
            }
        }

        // Remove attributes not present in the updated entity
        foreach (var existingAttr in _entity.Attributes.ToList())
        {
            if (!UpdatedEntity.Attributes.Any(attr =>
                 attr.AttributeKey == existingAttr.AttributeKey))
            {
                _entity.Attributes.Remove(existingAttr);
                db.Entry(existingAttr).State = EntityState.Deleted;
            }
        }

        db.Entry(_entity).CurrentValues.SetValues(UpdatedEntity);

        await db.SaveChangesAsync();

    }

    public async Task Delete(int Id)
    {


        var attributes = await db.LibraryEntityAttributes.AsNoTracking()
          .Where(x => x.LibraryEntityID == Id)
          .Select(x => x.ID)
          .ToListAsync();

        var attributeService = new BaseEntityService<LibraryEntityAttribute>(db);
        foreach (var child in attributes)
            await attributeService.Delete(child);

        var _attachments = await db.LibraryEntityAttachments.AsNoTracking()
          .Where(x => x.LibraryEntityID == Id)
          .Select(x => x.ID)
          .ToListAsync();

        var attachmentService = new BaseAttachmentService<LibraryEntityAttachment>(db);
        foreach (var child in _attachments)
            await attachmentService.Delete(child);




        await base.Delete(Id);


    }



    public async Task<string> GetNextCode(int TypeFlag)
    {


        var _typeValue = await db.TypeMasters.AsNoTracking()
        .SingleOrDefaultAsync(x => x.Entity==nameof(LibraryEntity) && x.Value == TypeFlag);

        if (_typeValue == null)

            throw new EntityServiceException($"Typeflag {TypeFlag} not defined for getting next code!");

        var _currentYearStart = new DateTime(DateTime.UtcNow.Year, 1, 1);
        var _count = await db.LibraryEntities.AsNoTracking().Where(x => x.Created > _currentYearStart && x.TypeFlag == TypeFlag).AnyAsync() ?
              await db.LibraryEntities.AsNoTracking().Where(x => x.Created > _currentYearStart && x.TypeFlag == TypeFlag).MaxAsync(x => x.CodeFlag) : 0;

        var _CodeFlag = _count + 1;
        return _typeValue.Title.Substring(0, 2) + DateTime.UtcNow.ToString("yy") + _CodeFlag.ToString("D4");


    }

    public async Task<IEnumerable<string>> GetAttibuteValueOptions(string attributeKey)
    {

        return await db.LibraryEntityAttributes
            
            .Where(x => x.AttributeValue != null && x.AttributeValue != string.Empty
            && x.AttributeKey == attributeKey)
            .Select(x => x.AttributeValue)
        .Where(x => x != null)
        .Distinct()
        .ToListAsync();


    }

    //public async Task TransferAttributes()
    //{
    //    try
    //    {
    //        var datalist = await _db.LibraryEntities.AsNoTracking()
    //            .Include(x=>x.LibraryEntityAttributes)
    //           .Where(x=>x._attributes!=null && x._attributes!=string.Empty)
    //          .ToListAsync();

    //        var count = 0;
    //        var _failed = 0;
    //        var _uploaded = 0;
    //        var _exist = 0;
    //        foreach (var _obj in datalist)
    //        {

    //            Console.WriteLine($"Entity: {_obj.Title} | {_obj.Attributes.Count} attributes");

    //            try
    //            {

    //                foreach (var attr in _obj.Attributes)
    //                {
    //                    if (!_obj.LibraryEntityAttributes.Any() || !_obj.LibraryEntityAttributes.Any(x => x.Key == attr.AttributeKey))
    //                    {
    //                        Console.WriteLine($"Key: {attr.AttributeKey} | Value:   {attr.AttributeValue}");
    //                        _db.LibraryEntityAttributes.Add(new LibraryEntityAttribute
    //                        {
    //                            LibraryEntityID = _obj.ID,
    //                            Key = attr.AttributeKey,
    //                            Value = attr.AttributeValue
    //                        });
    //                    }

    //                }

    //                await _db.SaveChangesAsync();
    //                count++;
    //            }
    //            catch (Exception e)
    //            {
    //                Console.WriteLine($"Failed: {e.Message} | {e.StackTrace}");
    //                _failed++;
    //            }

    //            Console.WriteLine(count + " of " + datalist.Count() + " processed");
    //            Console.WriteLine(_failed + " of " + datalist.Count() + " Failed");
    //            Console.WriteLine("========================");
    //        }

    //        await _db.SaveChangesAsync();
    //    }
    //    catch (Exception)
    //    {

    //        throw;
    //    }
    //}

}