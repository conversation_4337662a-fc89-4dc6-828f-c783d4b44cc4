﻿using AutoMapper;




using System.Data;


using System.Text;
using System.Text.RegularExpressions;
using MyCockpitView.WebApi.Exceptions;
using MyCockpitView.WebApi.ContactModule.Entities;
using MyCockpitView.WebApi.ExpenseModule.Services;
using MyCockpitView.WebApi.DesignScriptModule.Entities;
using MyCockpitView.WebApi.DesignScriptModule.Services;
using MyCockpitView.WebApi.Services;
using MyCockpitView.WebApi.InspectionModule.Entities;
using MyCockpitView.WebApi.LeaveModule.Services;
using MyCockpitView.WebApi.ProjectModule.Services;
using MyCockpitView.WebApi.WFTaskModule.Services;
using MyCockpitView.CoreModule;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.Utility.Common;
using MyCockpitView.Utility.RDLCClient;
using MyCockpitView.WebApi.InspectionModule.Entities;

namespace MyCockpitView.WebApi.InspectionModule.Services;

public class InspectionService : BaseEntityService<Inspection>, IInspectionService
{
    public InspectionService(EntitiesContext db) : base(db) { }

    public IQueryable<Inspection> Get(IEnumerable<QueryFilter>? Filters = null, string? Search = null, string? Sort = null)
    {

        IQueryable<Inspection> _query = base.Get(Filters);


        //Apply filters
        if (Filters != null)
        {


            if (Filters.Where(x => x.Key.Equals("ProjectID", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<Inspection>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("ProjectID", StringComparison.OrdinalIgnoreCase)))
                {
                    var isNumeric = Convert.ToInt32(_item.Value);

                    predicate = predicate.Or(x => x.ProjectID != null && x.ProjectID == isNumeric);
                }
                _query = _query.Where(predicate);
            }

            if (Filters.Where(x => x.Key.Equals("contactID", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<Inspection>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("contactID", StringComparison.OrdinalIgnoreCase)))
                {
                    var isNumeric = Convert.ToInt32(_item.Value);

                    predicate = predicate.Or(x => x.ContactID == isNumeric);
                }
                _query = _query.Where(predicate);
            }

            if (Filters.Where(x => x.Key.Equals("recipientContactID", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<Inspection>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("recipientContactID", StringComparison.OrdinalIgnoreCase)))
                {
                    var isNumeric = Convert.ToInt32(_item.Value);

                    predicate = predicate.Or(x => x.ContactID == isNumeric || x.Recipients.Any(c => c.TypeFlag == 0 && c.ContactID == isNumeric));
                }
                _query = _query.Include(x => x.Recipients).Where(predicate);
            }

            if (Filters.Where(x => x.Key.Equals("startDate", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var _item = Filters.First(x => x.Key.Equals("startDate", StringComparison.OrdinalIgnoreCase));
                DateTime result = Convert.ToDateTime(_item.Value);
                _query = _query.Where(x => x.StartDate.Date == result);
            }

            if (Filters.Where(x => x.Key.Equals("endDate", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var _item = Filters.First(x => x.Key.Equals("endDate", StringComparison.OrdinalIgnoreCase));

                DateTime result = Convert.ToDateTime(_item.Value);
                _query = _query.Where(x => x.EndDate.Date == result);
            }

            if (Filters.Where(x => x.Key.Equals("sentDate", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var _item = Filters.First(x => x.Key.Equals("sentDate", StringComparison.OrdinalIgnoreCase));

                DateTime result = Convert.ToDateTime(_item.Value);
                _query = _query.Where(x => x.FinalizedOn != null && x.FinalizedOn.Value.Date == result);
            }

            if (Filters.Where(x => x.Key.Equals("rangestart", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var _item = Filters.First(x => x.Key.Equals("rangestart", StringComparison.OrdinalIgnoreCase));

                DateTime result = Convert.ToDateTime(_item.Value);
                _query = _query.Where(x => x.StartDate >= result || x.EndDate >= result);
            }

            if (Filters.Where(x => x.Key.Equals("rangeend", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var _item = Filters.First(x => x.Key.Equals("rangeend", StringComparison.OrdinalIgnoreCase));

                DateTime result = Convert.ToDateTime(_item.Value);
                var end = result.AddDays(1);
                _query = _query.Where(x => x.StartDate < end || x.EndDate < end);

            }

            if (Filters.Where(x => x.Key.Equals("IsVersion", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<Inspection>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("IsVersion", StringComparison.OrdinalIgnoreCase)))
                {
                    var IsVersion = Convert.ToBoolean(_item.Value);

                    predicate = predicate.Or(x => x.IsVersion == IsVersion);
                }
                _query = _query.Where(predicate);
            }

            if (Filters.Where(x => x.Key.Equals("parentID", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<Inspection>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("parentID", StringComparison.OrdinalIgnoreCase)))
                {
                    var isNumeric = Convert.ToInt32(_item.Value);

                    predicate = predicate.Or(x => x.ParentID != null && x.ParentID == isNumeric);
                }
                _query = _query.Where(predicate);
            }
        }

        if (Search != null && Search != string.Empty)
        {
            var _keywords = Search.Split(' ');

            foreach (var _key in _keywords)
            {
                _query = _query.Include(x => x.Contact)
                     .Where(x => x.Title.ToLower().Contains(_key.ToLower())
                                   || (x.Contact.FirstName + " " + x.Contact.LastName).ToLower().Contains(_key.ToLower())
                                        );
            }
        }

        if (Sort != null && Sort != String.Empty)
        {
            switch (Sort.ToLower())
            {
                case "sentdate":
                    return _query
                            .OrderBy(x => x.FinalizedOn);

                case "sentdate desc":
                    return _query
                            .OrderByDescending(x => x.FinalizedOn);
            }
        }

        return _query
                .OrderByDescending(x => x.StartDate);

    }

    public async Task<int> Create(Inspection Entity, IEnumerable<InspectionRecipient> Recipients = null)
    {

        if (Entity.ProjectID != null)
        {
            var _project = await db.Projects.AsNoTracking().SingleOrDefaultAsync(x => x.ID == Entity.ProjectID);
            if (_project != null)
            {
                Entity.Title = _project.Code + "-" + _project.Title;
            }
        }

        if (Entity.Title == null || Entity.Title == String.Empty)
            throw new EntityServiceException("Subject is required. Please enter proper subject and try again!");


        var _lastInspection = await GetLastPending(Entity);
        if (_lastInspection != null)
        {
            var _typeFlagMasters = await db.TypeMasters.AsNoTracking().FirstOrDefaultAsync(x => x.Entity == nameof(Inspection) && x.Value == _lastInspection.TypeFlag);

            throw new EntityServiceException($"Last {(_typeFlagMasters != null ? _typeFlagMasters.Title : nameof(Inspection))} for this subject {_lastInspection.Code} is pending. Please close & send it before creating new!");

        }

        if (Recipients != null)
        {
            foreach (var _person in Recipients)
            {
                if (_person.Name != null)
                {
                    if (!Entity.Recipients.Any(x => x.Name == _person.Name && x.Email == _person.Email))
                    {
                        var _attModel = new InspectionRecipient
                        {
                            Name = _person.Name,
                            Email = _person.Email,
                            Company = _person.Company,
                            TypeFlag = _person.TypeFlag,
                            ContactID = _person.ContactID,
                        };
                        Entity.Recipients.Add(_attModel);
                    }
                }
            }
        }


        var _lastorder = 0;
        var _query = db.Inspections.AsNoTracking()
            
            .Where(x => !x.IsVersion)
            .Where(x => x.TypeFlag == Entity.TypeFlag);
        if (Entity.ProjectID != null)
        {
            _query = _query.Where(x => x.ProjectID == Entity.ProjectID);
        }
        else
        {
            _query = _query.Where(x => x.Title==Entity.Title);
        }

        if (await _query.AnyAsync())
        {
            _lastorder = await _query
               .MaxAsync(x => x.OrderFlag);
        }

        _lastorder++;

        Entity.OrderFlag = _lastorder;


        Entity.Code = GetCode(Entity);
        db.Inspections.Add(Entity);
        await db.SaveChangesAsync();



        var taskService = new WFTaskService(db);
        await taskService.StartFlow(nameof(Inspection), Entity.TypeFlag, Entity.ID);



        return Entity.ID;

    }

    public async Task Update(Inspection Entity)
    {

        var inspection = await db.Inspections.AsNoTracking()
        .Include(x => x.Recipients)
        .SingleOrDefaultAsync(x => x.ID == Entity.ID);

        if (inspection == null) throw new EntityServiceException($"{nameof(Inspection)} not found!");

        //var _oldStatus = _entity.StatusFlag;
        var _oldStart = inspection.StartDate;
        var _oldEnd = inspection.EndDate;
        var _recipients = inspection.Recipients;


        //increment version if status is sent
        if (Entity.StatusFlag >= McvConstant.INSPECTION_STATUSFLAG_SENT)
        {
            Entity.Version++;
        }
        Entity.Code = GetCode(Entity);

        if (Entity.StatusFlag != McvConstant.INSPECTION_STATUSFLAG_SCHEDULED
            && (_oldStart != Entity.StartDate || _oldEnd != Entity.EndDate))
        {
            var InspectionRecipientService = new InspectionRecipientService(db);
            foreach (var _obj in _recipients.Where(x => x.TypeFlag == 0))
            {
                await InspectionRecipientService.LogRecipientTime(_obj.ContactID.Value, Entity.ID, Entity.StartDate, Entity.EndDate, "INSPECTION_CLOSE");
            }
            await InspectionRecipientService.LogRecipientTime(Entity.ContactID, Entity.ID, Entity.StartDate, Entity.EndDate, "INSPECTION_CLOSE");

        }

        await base.Update(Entity);


        var taskService = new WFTaskService(db);
        await taskService.UpdateTaskDue(nameof(Inspection), Entity.ID);


    }

    private string GetCode(Inspection Entity)
    {
        var _docKey = "INSP";

        var _docCode = $"{Entity.Title.Replace(" | ", "-")}-{_docKey}-#{Entity.OrderFlag:F1}";

        return _docCode.ToUpper().Trim();
    }

    public async Task<bool> IsInspectionEditable(int ID, int ContactID)
    {

        var inspection = await db.Inspections.AsNoTracking()
            .Where(x => x.ID == ID)
            .SingleOrDefaultAsync();

        if (inspection == null) throw new EntityServiceException("Inspection not found!");

        if (inspection.IsVersion) return false;

        if (inspection.ContactID != ContactID) return false;

        if (inspection.StatusFlag != McvConstant.INSPECTION_STATUSFLAG_SENT) return true;


        if (inspection.StatusFlag == McvConstant.INSPECTION_STATUSFLAG_SENT)
        {

            var _nextInspection = await Get()
                .Where(x => x.ID != inspection.ID)
                .Where(x => !x.IsVersion)
                .Where(x => x.Title == inspection.Title)
                .Where(x => x.StartDate > inspection.StartDate)

                .OrderByDescending(x => x.StartDate)
                .FirstOrDefaultAsync();

            if (_nextInspection == null)
            {
                var sharedService = new SharedService(db);
                var INSPECTION_UPDATE_LIMIT = Convert.ToInt32((await sharedService.GetPresetValue(McvConstant.INSPECTION_UPDATE_ALLOW_DURATION)));

                return ClockTools.GetDaysDifference(inspection.FinalizedOn != null ? inspection.FinalizedOn.Value : inspection.Modified, DateTime.UtcNow) < INSPECTION_UPDATE_LIMIT;
            }

        }

        return false;

    }

  
    public async Task<Inspection?> GetById(int Id)
    {

        return
            await db.Inspections.AsNoTracking()
                    .Include(x => x.Contact)
                    .Include(x => x.Recipients)
                     .Include(x => x.Items).ThenInclude(a => a.Attachments)
                    .SingleOrDefaultAsync(x => x.ID == Id);

    }

    public async Task<Inspection?> GetById(Guid Id)
    {

        var _entity = await db.Inspections.AsNoTracking()
                    .Include(x => x.Contact)
                    .Include(x => x.Recipients)
                     .Include(x => x.Items).ThenInclude(a => a.Attachments)
                    .SingleOrDefaultAsync(x => x.UID == Id);
        if (_entity == null) throw new EntityServiceException("Inspection Not found!");
        return _entity;

    }

    //public async Task<InspectionRecipientDto> GetRecipientById(int Id)
    //{

    //        return Mapper.Map<InspectionRecipientDto>(
    //            await db.InspectionRecipients
    //            .AsNoTracking()
    //                    .SingleOrDefaultAsync(x => x.ID == Id)
    //                    );

    //}

    #region RDLC
    public async Task<ReportDefinition> GetItemReport(string reportSize, Guid uid, string? sort = null)
    {

        var _Inspection = await Get()
                    .Include(x => x.Contact)
                    .Include(x => x.Recipients)
                     .Include(x => x.Items).ThenInclude(a => a.Attachments)
                     .Where(x => x.UID == uid)
                     .SingleOrDefaultAsync();

        if (_Inspection == null) throw new EntityServiceException("Inspection not Found!");

        var _dsEntities = new List<DesignScriptEntity>();

        if (_Inspection.ProjectID != null)
        {
            var designScriptEntityService = new DesignScriptEntityService(db);

            _dsEntities = await designScriptEntityService.Get()
                                .Where(x => x.ProjectID == _Inspection.ProjectID)
                                .ToListAsync();
        }


        var reportTitle = "Item List";

        var _InspectionDate = ClockTools.GetIST(_Inspection.StartDate).ToString("dd MMM yyyy HH:mm");
        var _reportProperties = new List<ReportProperties>
            {
                new ReportProperties() { PropertyName = "ReportTitle", PropertyValue = reportTitle.ToString() },
                new ReportProperties() { PropertyName = "Title", PropertyValue =_Inspection.Title.ToString() },
                new ReportProperties() { PropertyName = "Number", PropertyValue = $"#{_Inspection.OrderFlag.ToString("000")}" },
                new ReportProperties() { PropertyName = "StartDate", PropertyValue = ClockTools.GetIST(_Inspection.StartDate).ToString("dd MMM yyyy HH:mm") },
                                    new ReportProperties() { PropertyName = "EndDate", PropertyValue = ClockTools.GetIST(_Inspection.EndDate).ToString("HH:mm") },
                                     new ReportProperties() { PropertyName = "Code", PropertyValue = _Inspection.Code },
                new ReportProperties() { PropertyName = "PreparedBy", PropertyValue = _Inspection.Contact.FullName.ToString() },
                new ReportProperties() { PropertyName = "Location", PropertyValue = _Inspection.Location },

            };


        var today = DateTime.UtcNow;
        var inspectionItems = _Inspection.Items;
        if (sort != null)
        {
            if (sort.ToLower() == "duedate")
                inspectionItems = inspectionItems.OrderBy(x => x.DueDate).ToList();
            else if (sort.ToLower() == "actionby")
                inspectionItems = inspectionItems.OrderBy(x => x.ActionBy).ToList();
            else if (sort.ToLower() == "inspectionItem")
                inspectionItems = inspectionItems.OrderBy(x => x.Title).ThenBy(x => x.Subtitle).ToList();
        }
        var _minutesItems = inspectionItems.Select(x => new
        {
            x.ID,
            Title = x.Title,
            Subtitle = x.Subtitle,
            Reminder = x.ReminderCount,
            Comment = x.Comment,
            DueDate = x.DueDate != null ? ClockTools.GetIST(x.DueDate.Value).ToString("dd MMM yyyy") : "",
            ActionBy = x.ActionBy,
            History = x.PreviousHistory,
            Breadcrumb = x.DesignScriptEntityID != null ? getDSBreadcrumb(_dsEntities, x.DesignScriptEntityID.Value) : string.Empty,
            //IsDelayed = x.StatusFlag == McvConstant.INSPECTION_AGENDA_STATUSFLAG_PENDING && x.DueDate != null && x.DueDate.Value < today
        });


        var sharedService = new SharedService(db); ;
        var _reportContainerUrl = await sharedService.GetPresetValue(McvConstant.RDLC_REPORT_CONTAINER_URL);
        var DEVMODE = Convert.ToBoolean((await sharedService.GetPresetValue(McvConstant.DEVMODE)));

        var _reportPath = $"InspectionItem-{reportSize}.rdlc";

        var _reportDef = new ReportDefinition()
        {
            ReportName = "InspectionItem",
            ReportPath = DEVMODE ? $"{_reportContainerUrl}DEV/{_reportPath}" : $"{_reportContainerUrl}{_reportPath}",
            ReportDataSet = DataTools.ToDataTable(_minutesItems),
            ReportProperties = _reportProperties,
            Filename = $"{reportTitle}-{_Inspection.Code}-{ClockTools.GetISTNow().ToString("yyMMddHHmm")}",
            RenderType = "PDF"
        };

        _reportDef.SubReports.Add(new ReportDefinition()
        {
            ReportName = "InspectionMinutesRecipient",
            ReportPath = DEVMODE ? $"{_reportContainerUrl}DEV/InspectionMinutesRecipient.rdlc" : $"{_reportContainerUrl}InspectionMinutesRecipient.rdlc",
            ReportDataSet = DataTools.ToDataTable(_Inspection.Recipients.Select(x => new
            {
                Name = x.Name,
                Email = x.Email,
                Company = x.Company,
                TypeFlag = x.TypeFlag,
                ItemCount = _Inspection.Items.Where(m => m.ActionByContactID != null && m.ActionByContactID == x.ContactID).Count(),
                x.OrderFlag
            })),
        });

        var reportServiceApi = await sharedService.GetPresetValue(McvConstant.RDLC_PROCESSOR_API);
        return await ReportClient.GenerateReport(_reportDef, reportServiceApi);

    }
    public async Task<ReportDefinition> GetMinutesReport(string reportSize, Guid uid, string sort = null)
    {

        var _Inspection = await Get()
                    .Include(x => x.Contact)
                    .Include(x => x.Recipients)
                     .Include(x => x.Items).ThenInclude(a => a.Attachments)
                     .Where(x => x.UID == uid)
                     .SingleOrDefaultAsync();

        if (_Inspection == null) throw new EntityServiceException("Inspection not Found!");

        var reportTitle = "Inspection Report";

        if (!_Inspection.IsVersion && _Inspection.StatusFlag != McvConstant.INSPECTION_STATUSFLAG_SENT)
            return await GetItemReport(reportSize, uid, sort);


        var _dsEntities = new List<DesignScriptEntity>();

        if (_Inspection.ProjectID != null)
        {
            var designScriptEntityService = new DesignScriptEntityService(db);

            _dsEntities = await designScriptEntityService.Get()
                                .Where(x => x.ProjectID == _Inspection.ProjectID)
                                .ToListAsync();
        }




        var _InspectionDate = ClockTools.GetIST(_Inspection.StartDate).ToString("dd MMM yyyy HH:mm");
        var _reportProperties = new List<ReportProperties>
            {
                new ReportProperties() { PropertyName = "ReportTitle", PropertyValue = reportTitle.ToString() },
                new ReportProperties() { PropertyName = "Title", PropertyValue =_Inspection.Title.ToString() },
                new ReportProperties() { PropertyName = "Number", PropertyValue = $"#{_Inspection.OrderFlag.ToString("000")}" },
                new ReportProperties() { PropertyName = "StartDate", PropertyValue = ClockTools.GetIST(_Inspection.StartDate).ToString("dd MMM yyyy HH:mm") },
                                    new ReportProperties() { PropertyName = "EndDate", PropertyValue = ClockTools.GetIST(_Inspection.EndDate).ToString("HH:mm") },
                                     new ReportProperties() { PropertyName = "Code", PropertyValue = _Inspection.Code },
                new ReportProperties() { PropertyName = "PreparedBy", PropertyValue = _Inspection.Contact.FullName.ToString() },
                new ReportProperties() { PropertyName = "Location", PropertyValue = _Inspection.Location },

            };


        //var _consolidatedAttachments = new List<InspectionMinutesAttachment>();
        //var _fileIndex = 1;
        //foreach (var item in _Inspection.Items)
        //{
        //    if (item.Attachments.Any())
        //    {
        //        foreach (var attachment in item.Attachments)
        //        {
        //            var _extension = DataTools.GetFileExtension(attachment.Filename);
        //            if (!_consolidatedAttachments.Any(x => x.Filename == attachment.Filename))
        //                _consolidatedAttachments.Add(new InspectionMinutesAttachment()
        //                {
        //                    InspectionItemID = attachment.InspectionItemID,
        //                    Guidname = attachment.Guidname,
        //                    Reference = _fileIndex.ToString("00"),
        //                    Filename = attachment.Filename,
        //                    Url = attachment.ThumbUrl,
        //                    IsImage = _extension.Equals(".jpg", StringComparison.OrdinalIgnoreCase)
        //                    || _extension.Equals(".png", StringComparison.OrdinalIgnoreCase)
        //                    || _extension.Equals(".jpeg", StringComparison.OrdinalIgnoreCase)
        //                });
        //            _fileIndex++;
        //        }
        //    }
        //}
        var today = DateTime.UtcNow;
        var inspectionItems = _Inspection.Items;
        if (sort != null)
        {
            if (sort.ToLower() == "duedate")
                inspectionItems = inspectionItems.OrderBy(x => x.DueDate).ToList();
            else if (sort.ToLower() == "actionby")
                inspectionItems = inspectionItems.OrderBy(x => x.ActionBy).ToList();
            else if (sort.ToLower() == "inspectionItem")
                inspectionItems = inspectionItems.OrderBy(x => x.Title).ThenBy(x => x.Subtitle).ToList();
        }
        var _minutesItems = inspectionItems.Select(x => new
        {
            x.ID,
            Title = x.Title,
            Subtitle = x.Subtitle,
            Reminder = x.ReminderCount,
            Comment = x.Comment,
            DueDate = x.DueDate != null ? ClockTools.GetIST(x.DueDate.Value).ToString("dd MMM yyyy") : "",
            ActionBy = x.ActionBy,
            History = x.PreviousHistory,
            //Status = x.StatusFlag == McvConstant.INSPECTION_AGENDA_STATUSFLAG_SENT ? "RESOLVED" : "PENDING",
            Breadcrumb = x.DesignScriptEntityID != null ? getDSBreadcrumb(_dsEntities, x.DesignScriptEntityID.Value) : string.Empty,
            //IsDelayed = x.StatusFlag == McvConstant.INSPECTION_AGENDA_STATUSFLAG_PENDING && x.DueDate != null && x.DueDate.Value < today
        });



        var _images = _Inspection.Items
            
            .Where(x => x.Attachments.Where(a => !a.IsDeleted)
                                    .Where(a => a.ThumbUrl != null).Any())
            .SelectMany(join => join.Attachments, (x, y) => new
            {
                Url = y.ThumbUrl,
                ParentID = y.InspectionItemID
            });


        //if (_consolidatedAttachments.Any())
        //{
        //    foreach (var item in _minutesItems)
        //    {
        //        var _attachments = _Inspection.Items.SingleOrDefault(x => x.ID == item.ID).Attachments;
        //        item.References = string.Join(",", _consolidatedAttachments.Where(p => _attachments.Any(a => a.Filename == p.Filename))
        //                     .Select(p => p.Reference));
        //    }
        //}
        var sharedService = new SharedService(db); ;
        var _reportContainerUrl = await sharedService.GetPresetValue(McvConstant.RDLC_REPORT_CONTAINER_URL);
        var DEVMODE = Convert.ToBoolean((await sharedService.GetPresetValue(McvConstant.DEVMODE)));

        var _reportPath = $"InspectionMinutes-{reportSize}.rdlc";

        var _reportDef = new ReportDefinition()
        {
            ReportName = "InspectionMinutes",
            ReportPath = DEVMODE ? $"{_reportContainerUrl}DEV/{_reportPath}" : $"{_reportContainerUrl}{_reportPath}",
            ReportDataSet = DataTools.ToDataTable(_minutesItems),
            ReportProperties = _reportProperties,
            Filename = $"InspectionMinutes-{_Inspection.Code}-{ClockTools.GetISTNow().ToString("yyMMddHHmm")}",
            RenderType = "PDF"
        };

        _reportDef.SubReports.Add(new ReportDefinition()
        {
            ReportName = "InspectionMinutesRecipient",
            ReportPath = DEVMODE ? $"{_reportContainerUrl}DEV/InspectionMinutesRecipient.rdlc" : $"{_reportContainerUrl}InspectionMinutesRecipient.rdlc",
            ReportDataSet = DataTools.ToDataTable(_Inspection.Recipients.Select(x => new
            {
                Name = x.Name,
                Email = x.Email,
                Company = x.Company,
                TypeFlag = x.TypeFlag,
                ItemCount = _Inspection.Items.Where(m => m.ActionByContactID != null && m.ActionByContactID == x.ContactID).Count(),
                x.OrderFlag
            })),
        });

        _reportDef.SubReports.Add(new ReportDefinition()
        {
            ReportName = "InspectionMinutesImages",
            ReportPath = DEVMODE ? $"{_reportContainerUrl}DEV/InspectionMinutesImages.rdlc" : $"{_reportContainerUrl}InspectionMinutesImages.rdlc",
            ReportDataSet = DataTools.ToDataTable(_images),
        });

        var reportServiceApi = await sharedService.GetPresetValue(McvConstant.RDLC_PROCESSOR_API);
        return await ReportClient.GenerateReport(_reportDef, reportServiceApi);

    }

    private string getDSBreadcrumb(IEnumerable<DesignScriptEntity> _dsEntities, int ID)
    {

        var breadcrumb = string.Empty;

        var entity = _dsEntities.FirstOrDefault(x => x.ID == ID);
        if (entity != null)
        {
            breadcrumb = $"{entity.Code}-{entity.Title}";
            while (entity.ParentID != null)
            {
                entity = _dsEntities.FirstOrDefault(x => x.ID == entity.ParentID);
                breadcrumb = $"{entity.Code}-{entity.Title} > {breadcrumb}";
            }

        }
        return breadcrumb;
    }


    #endregion RDLC




    public async Task SendMinutes(int ID)
    {

        var _Inspection = await Get()
            .Include(x => x.Contact)
            .Include(x => x.Recipients)
                .Include(x => x.Items).ThenInclude(c => c.Attachments)
            .SingleOrDefaultAsync(x => x.ID == ID);

        if (_Inspection == null) throw new EntityServiceException("Inspection not found!");

        var _tos = _Inspection.Recipients
            .Where(x => x.TypeFlag == 0)
            .Where(x => x.Email != null)
            .Select(x => new InspectionEmailContact
            {
                Name = x.Name,
                Email = x.Email,
                Company = x.Company,
                ID = x.ContactID.Value,
                OrderFlag = x.OrderFlag
            })
            .ToList();
        var _ccs = _Inspection.Recipients
            .Where(x => x.TypeFlag == 1)
            .Where(x => x.Email != null)
            .Select(x => new InspectionEmailContact
            {
                Name = x.Name,
                Email = x.Email,
                Company = x.Company,
                ID = x.ContactID.Value,
                OrderFlag = x.OrderFlag
            })
            .Where(x => !_tos.Any(c => c.Email == x.Email))
            .ToList();

        if (_Inspection.ProjectID != null)
        {
            var projectService = new ProjectService(db);
            var _project = await projectService.Get()
                    .Include(x => x.Associations).ThenInclude(c => c.Contact)
                .SingleOrDefaultAsync(x => x.ID == _Inspection.ProjectID);
            if (_project != null)
            {
                Regex myRegex = new Regex(McvConstant.EMAIL_REGEX, RegexOptions.None);
                foreach (var _leader in _project.Associations.Where(x => x.TypeFlag == 0))
                {
                    if (_leader != null && _leader.Contact.Email1 != null && myRegex.IsMatch(_leader.Contact.Email1))
                    {
                        var _recipient = new InspectionEmailContact
                        {
                            Email = _leader.Contact.Email1,
                            Name = _leader.Contact.FullName,
                            OrderFlag = _ccs.Any() ? _ccs.Max(x => x.OrderFlag) : 0
                        };


                        //      var _leaves = await db.Leaves.AsNoTracking()
                        //.Where(x => x.ContactID == _leader.ID)
                        //.Where(x => x.StatusFlag == McvConstant.LEAVE_STATUSFLAG_APPROVED)
                        //.Where(x => x.TypeFlag == McvConstant.LEAVE_TYPEFLAG_APPROVED)
                        //.Where(x => x.End > DateTime.UtcNow)
                        //.ToListAsync();

                        //      if (_leaves.Any())
                        //      {
                        //          _recipient.Leaves += $" On leave(";



                        //          foreach (var leave in _leaves)
                        //          {
                        //              _recipient.Leaves += $"{(leave.Start > DateTime.UtcNow ? ClockTools.GetIST(leave.Start).ToString("dd MMM yyyy") : ClockTools.GetISTNow().ToString("dd MMM yyyy"))}-{ClockTools.GetIST(leave.End).ToString("dd MMM yyyy")}";
                        //          }

                        //          _recipient.Leaves += $")";
                        //      }

                        if (!_tos.Any(x => x.Email.ToLower() == _recipient.Email.ToLower()) && !_ccs.Any(x => x.Email.ToLower() == _recipient.Email.ToLower()))
                            _ccs.Add(_recipient);
                    }
                }

                foreach (var _leader in _project.Associations.Where(x => x.TypeFlag == 1))
                {
                    if (_leader != null && _leader.Contact.Email1 != null && myRegex.IsMatch(_leader.Contact.Email1))
                    {
                        var _recipient = new InspectionEmailContact
                        {
                            Email = _leader.Contact.Email1,
                            Name = _leader.Contact.FullName,
                            OrderFlag = _ccs.Any() ? _ccs.Max(x => x.OrderFlag) : 0
                        };

                        if (!_tos.Any(x => x.Email.ToLower() == _recipient.Email.ToLower()) && !_ccs.Any(x => x.Email.ToLower() == _recipient.Email.ToLower()))
                            _ccs.Add(_recipient);
                    }
                }

            }


        }


        var leaveService = new LeaveService(db);
        var _leaves = await leaveService.Get()
                        .Where(x => x.ContactID == _Inspection.Contact.ID)
                        .Where(x => x.StatusFlag == McvConstant.LEAVE_STATUSFLAG_APPROVED)
                        .Where(x => x.TypeFlag == McvConstant.LEAVE_TYPEFLAG_APPROVED)
                        .Where(x => x.End > DateTime.UtcNow)
                        .OrderBy(x => x.Start)
                        .ToListAsync();

        var leaves = "";

        if (_leaves.Any())
        {
            leaves += $"On leave(";



            foreach (var leave in _leaves)
            {
                leaves += $"{(leave.Start > DateTime.UtcNow ? ClockTools.GetIST(leave.Start).ToString("dd MMM yyyy") : ClockTools.GetISTNow().ToString("dd MMM yyyy"))}-{ClockTools.GetIST(leave.End).ToString("dd MMM yyyy")} | ";
            }

            leaves += $")";
        }

        var sharedService = new SharedService(db); ;
        //CC TO STUDIO
        var _defaultCCList = await sharedService.GetPresetValue(McvConstant.INSPECTION_EMAIL_CC);
        if (_defaultCCList != null)
        {
            Regex myRegex = new Regex(McvConstant.EMAIL_REGEX, RegexOptions.None);
            foreach (Match myMatch in myRegex.Matches(_defaultCCList))
            {
                var _address = new InspectionEmailContact
                {
                    Email = myMatch.Value.Trim(),
                    Name = "",
                };
                if (myMatch.Success && !_ccs.Any(x => x.Email.ToLower() == myMatch.Value.Trim().ToLower()) && !_tos.Any(x => x.Email.ToLower() == myMatch.Value.Trim().ToLower()))
                {
                    _ccs.Add(_address);
                }
            }
        }

        //foreach (var item in _tos)
        //{
        //    item.PendingItemCount = _Inspection.Items.Where(x => x.StatusFlag == McvConstant.INSPECTION_AGENDA_STATUSFLAG_PENDING)
        //        .Where(x => x.ActionByContactID != null && x.ActionByContactID == item.ID).Count();
        //}

        //foreach (var item in _ccs)
        //{
        //    item.PendingItemCount = _Inspection.Items.Where(x => x.StatusFlag == McvConstant.INSPECTION_AGENDA_STATUSFLAG_PENDING)
        //        .Where(x => x.ActionByContactID != null && x.ActionByContactID == item.ID).Count();
        //}


        var title = $"{_Inspection.Title}";

        var subtitle = $"#{_Inspection.OrderFlag.ToString("000")} | {ClockTools.GetIST(_Inspection.StartDate).ToString("dd MMM yyyy HH:mm")}" + (_Inspection.Version > 0 ? " | R" + _Inspection.Version.ToString() : "");



        var _emailSubject = $"INSPECTION REPORT | {title} | {subtitle}";

        var _version = $"#{_Inspection.OrderFlag.ToString("000")}";


        var _replyParameter = $"mailto:{_Inspection.Contact.Email1}?cc={_defaultCCList}&subject=RE:{_emailSubject.ToUpper()}";

        //Create a new StringBuilder object
        StringBuilder sb = new StringBuilder();

        sb.AppendLine("<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Transitional//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd\">");
        sb.AppendLine("<html xmlns=\"http://www.w3.org/1999/xhtml\">");
        sb.AppendLine("<head>");
        sb.AppendLine("    <meta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\" />");
        sb.AppendLine("    <title>Email Design</title>");
        sb.AppendLine("    <meta name=\"viewport\" content=\"width=device-width; initial-scale=1.0;\" />");
        sb.AppendLine("    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=9; IE=8; IE=7; IE=EDGE\" />");
        sb.AppendLine("    <meta name=\"format-detection\" content=\"telephone=no\" />");
        sb.AppendLine("    <!--[if gte mso 9]><xml>");
        sb.AppendLine("    <o:OfficeDocumentSettings>");
        sb.AppendLine("    <o:AllowPNG />");
        sb.AppendLine("    <o:PixelsPerInch>96</o:PixelsPerInch>");
        sb.AppendLine("    </o:OfficeDocumentSettings>");
        sb.AppendLine("    </xml><![endif]-->");
        sb.AppendLine("    <style type=\"text/css\">");
        sb.AppendLine("        /* Some resets and issue fixes */");
        sb.AppendLine("        #outlook a {");
        sb.AppendLine("            padding: 0;");
        sb.AppendLine("        }");
        sb.AppendLine("");
        sb.AppendLine("        body {");
        sb.AppendLine("            width: 100% !important;margin:0;");
        sb.AppendLine("            -webkit-text-size-adjust: 100%;");
        sb.AppendLine("            -ms-text-size-adjust: 100%;");
        sb.AppendLine("        }");

        sb.AppendLine("        table{");
        sb.AppendLine("            mso-table-lspace: 0px;");
        sb.AppendLine("            mso-table-rspace: 0px;");
        sb.AppendLine("        }");
        sb.AppendLine("");
        sb.AppendLine("        table td {");
        sb.AppendLine("            border-collapse: collapse;");
        sb.AppendLine("        }");
        sb.AppendLine("        .download-button {");
        sb.AppendLine("            background-color: #3498db;");
        sb.AppendLine("            color: #fff;");
        sb.AppendLine("            text-decoration: none;");
        sb.AppendLine("            padding: 10px 20px;");
        sb.AppendLine("            border-radius: 5px;");
        sb.AppendLine("        }");
        sb.AppendLine("");
        sb.AppendLine("            .download-button:hover {");
        sb.AppendLine("                background-color: #1e6bb8;");
        sb.AppendLine("            }");
        sb.AppendLine("");
        sb.AppendLine("        .ExternalClass * {");
        sb.AppendLine("            line-height: 115%;");
        sb.AppendLine("        }");
        sb.AppendLine("        /* End reset */");


        sb.AppendLine("    </style>");
        sb.AppendLine("</head>");
        sb.AppendLine("");
        sb.AppendLine("<body>");
        sb.AppendLine("");

        sb.AppendLine("");
        sb.AppendLine("    <div style=\"margin: 0 auto;font-family:Calibri;font-size:14px;line-height:1.8;padding-left:5px;padding-right:5px;padding-top:10px;pading-bottom:10px; max-width:500px;\">");
        sb.AppendLine("");
        sb.AppendLine("        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; border-collapse: collapse;padding: 6px; background-color: #e9e9e9;\">");
        sb.AppendLine("            <tr>");
        sb.AppendLine("                <td style=\"font-size:18px; font-weight:bold; text-align:center;\">");
        sb.Append(title);
        sb.AppendLine("                </td>");
        sb.AppendLine("            </tr>");
        sb.AppendLine("            <tr>");
        sb.AppendLine("                <td style=\"font-size:14px; font-weight:bold; text-align:center; color: #494949;\">");
        sb.Append(subtitle);
        sb.AppendLine("                </td>");
        sb.AppendLine("            </tr>");
        sb.AppendLine("        </table>");
        sb.AppendLine("");


        sb.AppendLine("<div style=\"border-bottom: 1px solid #cccccc; margin-top: 15px; margin-bottom: 15px; \"></div>");

        sb.AppendLine("        <table align=\"center\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; border-collapse: collapse;\">");

        sb.AppendLine("            <tr>");
        sb.AppendLine("                <td style=\"text-align: center; font-size: 18px; font-weight: bold;\">");

        var rootApi = await sharedService.GetPresetValue(McvConstant.INSPECTION_REPORT_URL_ROOT);

        sb.AppendLine($"                    <a href=\"{rootApi}{(await RecordVersion(ID)).ToString()}\">INSPECTION REPORT</a>");



        sb.AppendLine("                </td>");
        sb.AppendLine("            </tr>");
        sb.AppendLine("            <tr>");
        sb.AppendLine("                <td style=\"text-align: center; font-size:11px;color:#808080;\">");
        sb.AppendLine(" To view, kindly click the above link. ");
        sb.AppendLine("                </td>");
        sb.AppendLine("            </tr>");
        sb.AppendLine("        </table>");
        sb.AppendLine("");



        sb.AppendLine("<div style=\"border-bottom: 1px solid #cccccc; margin-top: 15px; margin-bottom: 15px; \"></div>");
        sb.AppendLine("        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; border-collapse: collapse;\">");

        sb.AppendLine("            <tr>");
        sb.AppendLine("                <td rowspan=\"2\" valign=\"top\" width=\"80\" style=\"font-weight:bold;\">");
        sb.AppendLine("                    From:");
        sb.AppendLine("                </td>");
        sb.AppendLine("                <td valign=\"top\" style=\"padding: 2px;\">");
        sb.Append(_Inspection.Contact.FullName + " <i>(" + _Inspection.Contact.Email1 + ")</i>");
        sb.AppendLine("                </td>");
        sb.AppendLine("            </tr>");
        sb.AppendLine("            <tr>");
        sb.AppendLine("                <td valign=\"top\" style=\"padding: 2px;\">");
        sb.Append($"<small>{leaves}</small>");
        sb.AppendLine("                </td>");
        sb.AppendLine("            </tr>");
        sb.AppendLine("        </table>");
        sb.AppendLine("");
        sb.AppendLine("        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; border-collapse: collapse;\">");


        sb.AppendLine("            <tr>");
        sb.AppendLine("                <td valign=\"top\"  style=\"font-weight:bold;\">");
        sb.AppendLine(" To:");
        sb.AppendLine("                </td>");
        sb.AppendLine("            </tr>");

        sb.AppendLine("            <tr>");
        sb.AppendLine("                <td valign=\"top\" style=\"padding: 2px;\">");
        sb.AppendLine("        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; border-collapse: collapse;\">");
        foreach (var item in _tos.OrderBy(x => x.OrderFlag))
        {
            sb.AppendLine("            <tr>");
            sb.AppendLine("                <td >");
            sb.Append(item.Name + "<i> (" + item.Email + ")</i>");
            sb.AppendLine("                </td>");
            sb.AppendLine("                <td valign=\"top\" width=\"70px\">");
            //sb.Append(item.PendingItemCount > 0 ? "Pending " + item.PendingItemCount.ToString("00") : "");
            sb.AppendLine("                </td>");
            sb.AppendLine("            </tr>");

        }
        sb.AppendLine("        </table>");
        sb.AppendLine("                </td>");
        sb.AppendLine("            </tr>");

        sb.AppendLine("            <tr>");
        sb.AppendLine("                <td valign=\"top\"  style=\"font-weight:bold;\">");
        sb.AppendLine("                    CC:");
        sb.AppendLine("                </td>");
        sb.AppendLine("            </tr>");

        sb.AppendLine("            <tr>");
        sb.AppendLine("                <td valign=\"top\" style=\"padding: 2px;\">");
        sb.AppendLine("        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; border-collapse: collapse;\">");
        foreach (var item in _ccs.OrderBy(x => x.OrderFlag))
        {
            sb.AppendLine("            <tr>");
            sb.AppendLine("                <td >");
            sb.Append(item.Name + "<i> (" + item.Email + ")</i>");
            sb.AppendLine("                </td>");
            sb.AppendLine("                <td valign=\"top\" width=\"70px\">");
            //sb.Append(item.PendingItemCount > 0 ? "Pending " + item.PendingItemCount.ToString("00") : "");
            sb.AppendLine("                </td>");
            sb.AppendLine("            </tr>");

        }
        sb.AppendLine("        </table>");

        sb.AppendLine("                </td>");
        sb.AppendLine("            </tr>");

        sb.AppendLine("        </table>");



        //FOOTER
        sb.AppendLine("<div style=\"border-bottom: 1px solid #cccccc; margin-top: 15px; margin-bottom: 15px; \"></div>");
        sb.AppendLine("        <table align=\"center\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; border-collapse: collapse;font-size:11px;\">");

        sb.AppendLine("");
        sb.AppendLine("            <tr>");
        sb.AppendLine("");
        sb.AppendLine("                <td align=\"center\" >");
        sb.AppendLine("This is a <b>MyCockpitView<sup>&copy;</sup></b> & <b>DesignScript<sup>&copy;</sup></b> generated e-mail for your information and necessary action.");
        sb.AppendLine("</td>");
        sb.AppendLine("            </tr>");
        sb.AppendLine("");
        sb.AppendLine("            <tr>");
        sb.AppendLine("");
        sb.AppendLine("                <td align=\"center\" >");
        sb.AppendLine("");
        sb.AppendLine("                    Powered by <b>Newarch<sup>&reg;</sup> Infotech LLP</b>");
        sb.AppendLine("");
        sb.AppendLine("                </td>");
        sb.AppendLine("            </tr>");
        sb.AppendLine("        </table>");
        sb.AppendLine("    </div>");

        sb.AppendLine("</body>");
        sb.AppendLine("");
        sb.AppendLine("</html>");

        var _emailBody = sb.ToString();

        var _senderName = await sharedService.GetPresetValue(McvConstant.INSPECTION_EMAIL_SENDER_NAME);
        var _senderEmail = await sharedService.GetPresetValue(McvConstant.INSPECTION_EMAIL_SENDER_ID);

        var _creator = _Inspection.Recipients.FirstOrDefault(x => x.ContactID == _Inspection.ContactID);
        if (_creator == null)
        {
            //_creator = new InspectionRecipient { Name = _Inspection.Contact.FullName, Email = _Inspection.Contact.Emails.FirstOrDefault(c => c.IsPrimary).Email };

            _creator = new InspectionRecipient { Name = _Inspection.Contact.FullName, Email = _Inspection.Contact.Email1 };
        }

        var emailTo = new List<(string name, string email)>();
        foreach (var obj in _tos)
            emailTo.Add((obj.Name, obj.Email));

        var emailCC = new List<(string name, string email)>();
        foreach (var obj in _ccs)
            emailCC.Add((obj.Name, obj.Email));

        if (_defaultCCList != null)
        {
            Regex myRegex = new Regex(McvConstant.EMAIL_REGEX, RegexOptions.None);
            foreach (Match myMatch in myRegex.Matches(_defaultCCList))
            {
                if (!emailTo.Any(a => a.email.Equals(myMatch.Value.Trim())) && !emailCC.Any(a => a.email.Equals(myMatch.Value.Trim())))
                    emailCC.Add(("Company", myMatch.Value.Trim()));

            }
        }

        await sharedService.SendMail(_emailSubject.ToUpper().Trim(),
        _senderName,
          _senderEmail,
          _emailBody, emailTo, emailCC, replyAddress: _creator.Email, replyName: _creator.Name);

        if (_Inspection.ProjectID != null)
        {
            var _project = await db.Projects.SingleOrDefaultAsync(x => x.ID == _Inspection.ProjectID);
            if (_project != null && _project.StatusFlag == McvConstant.PROJECT_STATUSFLAG_LOCKED)
            {
                _project.StatusFlag = McvConstant.PROJECT_STATUSFLAG_INPROGRESS;
                await db.SaveChangesAsync();
            }
        }

        if (_Inspection.ProjectID != null)
        {
            var projectService = new ProjectService(db);
            await projectService.RecordHistory(_Inspection.ProjectID.Value);
        }

    }
    public async Task Delete(int Id)
    {

        var _entity = await Get()

            .SingleOrDefaultAsync(x => x.ID == Id);

        if (_entity == null) throw new EntityServiceException($"{nameof(Inspection)} not found.");

        var InspectionRecipientService = new InspectionRecipientService(db);
        var recipients = await InspectionRecipientService.Get().Where(x => x.InspectionID == Id)
            .Select(x => x.ID).ToListAsync();
        foreach (var i in recipients)
        {
            await InspectionRecipientService.Delete(i);
        }


        var InspectionItemService = new InspectionItemService(db);
        var inspectionItems = await InspectionItemService.Get().Where(x => x.InspectionID == Id)
      .Select(x => x.ID).ToListAsync();
        foreach (var i in inspectionItems)
        {
            await InspectionItemService.Delete(i);
        }


        var taskService = new WFTaskService(db);
        var tasks = await taskService.Get()
            .Where(x => x.Entity == nameof(Inspection) && x.EntityID == Id)
            .Select(x => x.ID)
            .ToListAsync();
        foreach (var i in tasks)
        {
            await taskService.Delete(i);
        }


        var expenseService = new ExpenseService(db);
        var _vouchers = await expenseService.Get()
               .Where(x => x.Entity == nameof(Inspection)
               && x.EntityID == Id)
               .Select(x => x.ID)
               .ToListAsync();

        foreach (var i in _vouchers)
        {
            await expenseService.Delete(i);
        }


        await base.Delete(Id);

    }

    public async Task<Inspection> GetLastPending(Inspection Inspection)
    {
        //pending Inspections or C-note
        var _query = db.Inspections.AsNoTracking()
            .Where(x => x.ParentID == null)
                          .Where(x => x.Title==Inspection.Title
                                   && x.StatusFlag != McvConstant.INSPECTION_STATUSFLAG_SENT)
                          //.Where(x => x.StartDate <= Inspection.StartDate)
                          .OrderByDescending(x => x.StartDate);

        var _lastMeet = await _query
                .FirstOrDefaultAsync();


        return _lastMeet;

    }


    public async Task<Guid> RecordVersion(int ID)
    {

        //RECORD ENTITY HISTORY
        var inspection = await db.Inspections.AsNoTracking()
            .Where(x => !x.IsVersion)
            
                    .Include(x => x.Contact)
                    .Include(x => x.Recipients)
                     .Include(x => x.Items).ThenInclude(a => a.Attachments)
                    .SingleOrDefaultAsync(x => x.ID == ID);

        if (inspection == null) throw new EntityServiceException("Inspection not found!");

        var newEntity = new Inspection();
        // Set values from the original entity to the new entity
        db.Entry(newEntity).CurrentValues.SetValues(inspection);
        // Detach the newRecipient if it's already being tracked
        if (db.Entry(newEntity).State != EntityState.Detached)
        {
            db.Entry(newEntity).State = EntityState.Detached;
        }
        newEntity.ID = default(int);
        newEntity.UID = default(Guid);
        // Create a new entity with a deep copy of the original entity's properties

        db.Entry(newEntity).State = EntityState.Added;

        // Add the new entity to the context
        db.Inspections.Add(newEntity);


        // Reset the ID property
        newEntity.ID = 0;
        newEntity.ParentID = inspection.ID;
        newEntity.IsVersion = true;


        foreach (var item in inspection.Recipients)
        {
            // Create a new instance and copy the values
            var newRecipient = new InspectionRecipient();
            db.Entry(newRecipient).CurrentValues.SetValues(item);

            // Detach the newRecipient if it's already being tracked
            if (db.Entry(newRecipient).State != EntityState.Detached)
            {
                db.Entry(newRecipient).State = EntityState.Detached;
            }

            // Set ID to default (assuming ID is an int)
            newRecipient.ID = default(int);
            newRecipient.UID = default(Guid);
            // Attach the new entity to the context
            db.Entry(newRecipient).State = EntityState.Added;

            // Add the new entity to the context
            db.InspectionRecipients.Add(newRecipient);

            // Add the new entity to the collection
            newEntity.Recipients.Add(newRecipient);

        }

        foreach (var item in inspection.Items)
        {
            // Create a new instance and copy the values
            var newItem = new InspectionItem();
            db.Entry(newItem).CurrentValues.SetValues(item);

            // Detach the newItem if it's already being tracked
            if (db.Entry(newItem).State != EntityState.Detached)
            {
                db.Entry(newItem).State = EntityState.Detached;
            }

            // Set ID to default (assuming ID is an int)
            newItem.ID = default(int);
            newItem.UID = default(Guid);
            newItem.IsVersion = true;
            // Attach the new entity to the context
            db.Entry(newItem).State = EntityState.Added;


            foreach (var attachment in item.Attachments)
            {

                // Create a new instance and copy the values
                var newAttachment = new InspectionItemAttachment();
                db.Entry(newAttachment).CurrentValues.SetValues(attachment);

                // Detach the newAttachment if it's already being tracked
                if (db.Entry(newAttachment).State != EntityState.Detached)
                {
                    db.Entry(newAttachment).State = EntityState.Detached;
                }

                // Set ID to default (assuming ID is an int)
                newAttachment.ID = default(int);
                newAttachment.UID = default(Guid);
                // Attach the new entity to the context
                db.Entry(newAttachment).State = EntityState.Added;
                // Add the new entity to the context
                db.InspectionItemAttachments.Add(newAttachment);

                newItem.Attachments.Add(newAttachment);
            }
            // Add the new entity to the context
            db.InspectionItems.Add(newItem);

            // Add the new entity to the collection
            newEntity.Items.Add(newItem);
        }

        await db.SaveChangesAsync();

        return newEntity.UID;

    }


    public async Task<string> GetInspectionItemHistoryString(int ItemID)
    {
        try
        {
            var _inspectionItem = await db.InspectionItems.AsNoTracking()
                .Include(x => x.Inspection.Contact)
                .Where(x => x.ID == ItemID)
                .SingleOrDefaultAsync();
            if (_inspectionItem == null) throw new EntityServiceException("Item not found for getting Comment History!");
            var _commentHistory = new StringBuilder();

            //Line 1
            if (_inspectionItem.UpdateFrom != null)
            {
                if (_inspectionItem.UpdateFrom.ToUpper() == "SUBMISSION" && _inspectionItem.PackageID != null)
                {
                    var _package = await db.Packages.AsNoTracking().SingleOrDefaultAsync(x => x.ID == _inspectionItem.PackageID);
                    if (_package.SubmissionDate != null)
                        _commentHistory.AppendLine($"[{ClockTools.GetIST(_package != null ? _package.SubmissionDate.Value : _inspectionItem.Modified).ToString("dd MMM yyyy HH:mm")} {(_package != null ? _package.CreatedBy : _inspectionItem.ModifiedBy)} {_inspectionItem.UpdateFrom.ToUpper()}] ");
                }
                else
                {
                    _commentHistory.AppendLine("[" + ClockTools.GetIST(_inspectionItem.Modified).ToString("dd MMM yyyy HH:mm")
                      + " "
                      + _inspectionItem.ModifiedBy
                      + " "
                      + _inspectionItem.UpdateFrom.ToUpper() + "] ");
                }
            }
            else
            {
                _commentHistory.AppendLine("[" + ClockTools.GetIST(_inspectionItem.Inspection.StartDate).ToString("dd MMM yyyy HH:mm")
                  + " "
                  + _inspectionItem.Inspection.Contact.FullName
                  + " "
                  + (_inspectionItem.Inspection.TypeFlag == 1 ? " C-NOTE " :
                  (_inspectionItem.Inspection.TypeFlag == 2 ? " INSPECTION " : " INSPECTION")) + "] ");
            }


            //line 2                   
            _commentHistory.AppendLine((_inspectionItem.Comment != null ? _inspectionItem.Comment.Trim() : (_inspectionItem.PreviousComment != null ? _inspectionItem.PreviousComment.Trim() : "NOT DISCUSSED")));


            //line 3
            if (_inspectionItem.ActionBy != null)
                _commentHistory.Append(" Action By: " + _inspectionItem.ActionBy);

            if (_inspectionItem.DueDate != null)
                _commentHistory.AppendLine(" Due: " + ClockTools.GetIST(_inspectionItem.DueDate.Value).ToString("dd MMM yyyy"));

            //if (_inspectionItem.Inspection.TypeFlag == 1)
            //{
            //    if (_inspectionItem.Comment == null || _inspectionItem.Comment == String.Empty)
            //    {
            //        _commentHistory = new StringBuilder();

            //    }
            //}

            //line 4
            if (_inspectionItem.PreviousHistory != null)
            {
                _commentHistory.AppendLine("  ");//blank line
                _commentHistory.AppendLine(_inspectionItem.PreviousHistory);
            }

            return _commentHistory.ToString().Trim();
        }
        catch (Exception)
        {

            throw;
        }

    }

    public async Task<IEnumerable<int>> GetAssigneeContactIDs(int entityID, string stageCode, string propertyName)
    {
        var Entity = await Get()
           .Include(x => x.Contact)
           .Where(x => x.ID == entityID).SingleOrDefaultAsync();

        if (Entity == null) throw new EntityServiceException($"{nameof(Inspection)} not found!");

        var _list = new List<int>();

        if (stageCode.Equals("INSPECTION_CLOSE", StringComparison.OrdinalIgnoreCase) //Close Inspection
        || stageCode.Equals("INSPECTION_PREPARE_REPORT", StringComparison.OrdinalIgnoreCase)//Prepare Minutes
        )
        {
            var _type = DataTools.GetPropertyType(Entity, propertyName);
            if (_type == typeof(Contact))
            {
                _list.Add(((Contact)DataTools.GetPropertyValue(Entity, propertyName)).ID);
            }
        }
        else if (stageCode.Equals("INSPECTION_TRAVEL_TIME", StringComparison.OrdinalIgnoreCase))//Log Travel time
        {
            var _type = DataTools.GetPropertyType(Entity, propertyName);
            if (_type == typeof(Contact))
            {
                _list.Add(((Contact)DataTools.GetPropertyValue(Entity, propertyName)).ID);
            }


            var _recipients = await db.InspectionRecipients.AsNoTracking()
              .Where(x => x.InspectionID == entityID)
               .Where(x => x.TypeFlag == 0)
               .Where(x => db.Contacts.Where(c => c.Username != null)
                .Select(c => c.ID).Any(c => c == x.ContactID))
               .Select(x => x.ContactID)
               .ToListAsync();

            var _contacts = new List<int>();

            foreach (var i in _recipients)
                _contacts.Add(i.Value);

            if (!_contacts.Any(x => x == Entity.ContactID))
                _contacts.Add(Entity.ContactID);

            return _contacts;

        }
        else
        {
            throw new EntityServiceException($"{nameof(Inspection)} Task assignee not found for stage {stageCode}!");
        }
        return _list;
    }

    public async Task<dynamic> GetTaskByStage(string Entity, int EntityID, string StageCode, string StageTitle, decimal StageDuration, DateTime? FollowUpDate = null)
    {


        var sharedService = new SharedService(db); ;

        var _entity = await Get().SingleOrDefaultAsync(x => x.ID == EntityID);

        if (_entity == null) throw new EntityServiceException($"{nameof(Inspection)} not found!");

        var _startTimeSpan = TimeSpan.Parse(await sharedService.GetPresetValue(McvConstant.OFFICE_OPEN_TIME));
        var _endTimeSpan = TimeSpan.Parse(await sharedService.GetPresetValue(McvConstant.OFFICE_CLOSE_TIME));

        var _nextDue = DateTime.UtcNow.AddDays(Decimal.ToDouble(StageDuration)).Date;

        if (FollowUpDate != null)
            _nextDue = FollowUpDate.Value.Date;

        if (StageCode == "INSPECTION_CLOSE" || StageCode == "INSPECTION_PREPARE_REPORT")
        {
            _nextDue = _entity.EndDate.AddDays(1);

        }
        else if (StageCode == "INSPECTION_TRAVEL_TIME")
        {
            _nextDue = _entity.EndDate.AddDays(1);
        }

        var start = DateTime.UtcNow;
        if (_nextDue < start)
            start = _nextDue;

        return new
        {
            Title = StageTitle,
            Entity = Entity,
            EntityID = EntityID,
            Subtitle = $"{_entity.Code}",
            WFStageCode = StageCode,
            StartDate = ClockTools.GetUTC(ClockTools.GetIST(_nextDue).Date
              .AddMinutes(_startTimeSpan.TotalMinutes)),
            DueDate = ClockTools.GetUTC(ClockTools.GetIST(_nextDue).Date
              .AddMinutes(_endTimeSpan.TotalMinutes)),
            MHrAssigned = 0,
            IsPreAssignedTimeTask = false
        };
    }

    public async Task TaskAction(int EntityID, string StageCode, string taskComment = null)
    {
        var Entity = await Get()
                      .SingleOrDefaultAsync(x => x.ID == EntityID);

        if (Entity == null) throw new EntityServiceException($"{nameof(Inspection)} not found!");

        if (StageCode == "SYS_INSPECTION_CLOSE") //Close Inspection
        {

            if (Entity.StatusFlag != McvConstant.INSPECTION_STATUSFLAG_ATTENDED)
            {
                Entity.StatusFlag = McvConstant.INSPECTION_STATUSFLAG_ATTENDED;

                Entity.ClosedOn = DateTime.UtcNow;
                db.Entry(Entity).State = EntityState.Modified;
                await db.SaveChangesAsync();
                db.Entry(Entity).State = EntityState.Detached;
            }
            var InspectionRecipientService = new InspectionRecipientService(db);
            var _recipients = await InspectionRecipientService.Get()
                .Where(x => x.InspectionID == Entity.ID)
                 .Where(x => x.TypeFlag == McvConstant.INSPECTION_RECIPIENT_TYPEFLAG_TO)
                 .ToListAsync();


            foreach (var _obj in _recipients)
            {
                await InspectionRecipientService.LogRecipientTime(_obj.ContactID.Value, Entity.ID, Entity.StartDate, Entity.EndDate, "INSPECTION_CLOSE");
            }

            await InspectionRecipientService.LogRecipientTime(Entity.ContactID, Entity.ID, Entity.StartDate, Entity.EndDate, "INSPECTION_CLOSE");

        }
        else if (StageCode == "SYS_INSPECTION_DELETE")
        {

            var InspectionRecipientService = new InspectionRecipientService(db);
            var recipients = await InspectionRecipientService.Get().Where(x => x.InspectionID == EntityID)
                .Select(x => x.ID).ToListAsync();
            foreach (var i in recipients)
            {
                await InspectionRecipientService.Delete(i);
            }


            var InspectionItemService = new InspectionItemService(db);
            var inspectionItems = await InspectionItemService.Get().Where(x => x.InspectionID == EntityID)
          .Select(x => x.ID).ToListAsync();
            foreach (var i in inspectionItems)
            {
                await InspectionItemService.Delete(i);
            }


            var taskService = new WFTaskService(db);
            var tasks = await taskService.Get()
                .Where(x => x.Entity == nameof(Inspection) && x.EntityID == EntityID)

                .ToListAsync();
            foreach (var i in tasks)
            {
                i.IsDeleted = true;
            }


            var expenseService = new ExpenseService(db);
            var _vouchers = await expenseService.Get()
                   .Where(x => x.Entity == nameof(Inspection)
                   && x.EntityID == EntityID)
                   .Select(x => x.ID)
                   .ToListAsync();

            foreach (var i in _vouchers)
            {
                await expenseService.Delete(i);
            }

            await base.Delete(EntityID);

        }
        else if (StageCode == "SYS_INSPECTION_SEND") //Send Minutes
        {

            await SendMinutes(Entity.ID);
            Entity.FinalizedOn = DateTime.UtcNow;
            Entity.StatusFlag = McvConstant.INSPECTION_STATUSFLAG_SENT;
            db.Entry(Entity).State = EntityState.Modified;
            await db.SaveChangesAsync();

        }

    }


}

public class InspectionEmailContact
{
    //public int PendingItemCount { get; set; }

    //public string? Leaves { get; set; }

    public int OrderFlag { get; set; }

    public int ID { get; set; }
    public Guid UID { get; set; }
    public string? Name { get; set; }
    public string? Email { get; set; }
    public string? Company { get; set; }
    public string? TypeValue { get; set; }
    public int TypeFlag { get; set; }
    public string? PhotoUrl { get; set; }
}