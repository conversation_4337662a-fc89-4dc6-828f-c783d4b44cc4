﻿using MyCockpitView.WebApi.ContactModule.Dtos;
using MyCockpitView.CoreModule;
using AutoMapper;
using MyCockpitView.WebApi.TodoModule.Entities;
namespace MyCockpitView.WebApi.TodoModule.Dtos;

public class TodoListDto : BaseEntityDto
{


    public string? Title { get; set; }
    public string? Subtitle { get; set; }



    public DateTime DueDate { get; set; }

    public ContactListDto? AssignerContact { get; set; }

    public ContactListDto? AssigneeContact { get; set; }


    public bool IsDelayed { get; set; }

}
public class TodoDto : TodoListDto
{

    public DateTime StartDate { get; set; }

    public string? Comment { get; set; }


    public int AssigneeContactID { get; set; }


    public int AssignerContactID { get; set; }


    public virtual ICollection<TodoAttachmentDto> Attachments { get; set; }=new List<TodoAttachmentDto>();

    public virtual ICollection<TodoAgendaDto> Agendas { get; set; }= new List<TodoAgendaDto>();
    public int? ProjectID { get; set; }
    public int? FunctionID { get; set; }
    public decimal MHrAssigned { get; set; }
    public decimal MHrConsumed { get; set; }

}

public class TodoDtoMapperProfile : Profile
{
    public TodoDtoMapperProfile()
    {
        CreateMap<Todo, TodoDto>().ForMember(dest => dest.IsDelayed, opt => opt.MapFrom(src => src.StatusFlag != 1
                                                                  && (src.DueDate < DateTime.UtcNow) ? true : false))
               .ReverseMap()
               .ForMember(dest => dest.Attachments, opt => opt.Ignore())
               .ForMember(dest => dest.Agendas, opt => opt.Ignore())
               .ForMember(dest => dest.AssignerContact, opt => opt.Ignore())
               .ForMember(dest => dest.AssigneeContact, opt => opt.Ignore());

        CreateMap<Todo, TodoListDto>()
            .ForMember(dest => dest.IsDelayed, opt => opt.MapFrom(src => src.StatusFlag != 1
                                                               && (src.DueDate < DateTime.UtcNow) ? true : false));

    }
}