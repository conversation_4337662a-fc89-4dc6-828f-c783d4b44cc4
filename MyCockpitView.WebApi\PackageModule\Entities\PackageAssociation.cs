﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.ContactModule.Entities;
using System.ComponentModel.DataAnnotations;

namespace MyCockpitView.WebApi.PackageModule.Entities
{
    public class PackageAssociation : BaseEntity
    {
        [StringLength(255)]
        public string?  Title { get; set; }

        [Required]
        public int PackageID { get; set; }

        public virtual Package? Package { get; set; }

        [Required]
        public int ContactID { get; set; }
        public virtual Contact? Contact { get; set; }
        [Precision(14, 2)]
        public decimal ValueHours { get; set; } = 0;
        [Precision(14, 2)]
        public decimal ValueHourRate { get; set; } = 0;
        [Precision(14, 2)]
        public decimal ShareValue { get; set; } = 0;
    }

    public class PackageAssociationConfiguration : BaseEntityConfiguration<PackageAssociation>, IEntityTypeConfiguration<PackageAssociation>
    {
        public void Configure(EntityTypeBuilder<PackageAssociation> builder)
        {
            base.Configure(builder);
            builder.HasIndex(x => x.Title);
        }
    }
}
