﻿using MyCockpitView.WebApi.ContactModule.Services;
using Newtonsoft.Json;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using AutoMapper;
using MyCockpitView.WebApi.ProjectModule.Entities;

namespace MyCockpitView.WebApi.ProjectModule.Dtos;

public class ProjectClientReportLogDto
{
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public Guid ID { get; set; }
    public Guid? MessageID { get; set; }

    [Required]
    public DateTime SentDate { get; set; }
    public int ProjectID { get; set; }

    [Required]
    public string? Title { get; set; }

    public List<EmailContact> EmailTo { get; set; }

    public List<EmailContact> EmailCC { get; set; }
    public string? HTMLContent { get; set; }



}

public class ProjectClientReportLogDtoMapperProfile : Profile
{
    public ProjectClientReportLogDtoMapperProfile()
    {
        CreateMap<ProjectClientReportLog, ProjectClientReportLogDto>()
      .ReverseMap();

    }
}