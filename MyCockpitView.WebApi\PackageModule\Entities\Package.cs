﻿using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using MyCockpitView.CoreModule;
using System.Text.RegularExpressions;
using MyCockpitView.WebApi.ProjectModule.Entities;
using Newtonsoft.Json;

namespace MyCockpitView.WebApi.PackageModule.Entities
{
    public class Package :BaseEntity
    {
        [Required]
        public int ProjectID { get; set; }

        public virtual Project? Project { get; set; }

        [StringLength(255)]
        public string? ProjectCode { get; set; }


        [StringLength(255)]
        public string? ProjectTitle { get; set; }


        [Required]
        [StringLength(255)]
        public string? Title { get; set; }


        [StringLength(255)]
        public string? Code { get; set; }


        [Required]
        [StringLength(255)]
        public string? PhaseCode { get; set; }


        [StringLength(255)]
        public string? PhaseTitle { get; set; }

        public int? DesignScriptEntityID { get; set; }


        [Required]
        [StringLength(255)]
        public string? Stage { get; set; }


        [StringLength(255)]
        public string? DeliveryMode { get; set; }


        [StringLength(255)]
        public string? Purpose { get; set; }
        public int Revision { get; set; }


        public bool IsAnnexure { get; set; }


        public int AnnexureIndex { get; set; }


        [StringLength(5)]
        public string? Annexure { get; set; }


        
        public DateTime FinalDate { get; set; }


        
        public DateTime StartDate { get; set; }


        
        public DateTime? SubmissionDate { get; set; }


        public virtual ICollection<PackageAttachment> Attachments { get; set; } = new List<PackageAttachment>();

        public string? CloudFile { get; set; }

        
        public bool IsSubmissionSetProcessed { get; set; }
        [Precision(14, 2)]
        public decimal VHrAssigned { get; set; } = 0;
        [Precision(14, 2)]
        public decimal VHrAssignedCost { get; set; } = 0;

        [Precision(14, 2)]
        public decimal VHrConsumed { get; set; } = 0;
        [Precision(14, 2)]
        public decimal VHrConsumedInclusive { get; set; } = 0;
        [Precision(14, 2)]
        public decimal VHrConsumedCost { get; set; } = 0;
        [Precision(14, 2)]
        public decimal VHrConsumedInclusiveCost { get; set; } = 0;
        [Precision(14, 2)]
        public decimal VHrRate { get; set; } = 0;

        public virtual ICollection<PackageAssociation> Associations { get; set; } = new List<PackageAssociation>();

        public string? ActiveStage { get; set; }


        public int? CompanyID { get; set; }

        public string? SubmissionMessage { get; set; }


        public decimal DValue { get; set; }

     
        public decimal RValue { get; set; }

   
        
        public DateTime? ProposedFinalDate { get; set; }

        
        public DateTime? ProposedStartDate { get; set; }
        public decimal ProposedVHrAssigned { get; set; }
        public decimal ProposedVHrAssignedCost { get; set; }


        [StringLength(255)]
        public string? ProposedPriority { get; set; }


        public decimal ProposedProbablity { get; set; }

        public virtual ICollection<PackageDesignIntent> DesignIntents { get; set; } = new List<PackageDesignIntent>();
        public virtual ICollection<PackageDeliverable> Deliverables { get; set; } = new List<PackageDeliverable>();

        public string? EmailID { get; set; }

        [NotMapped]
        public List<string> ToRecipients
        {
            get
            {
                if (!string.IsNullOrEmpty(EmailID))
                {
                    Regex myRegex = new Regex(McvConstant.EMAIL_REGEX, RegexOptions.None);

                    // Split EmailID by ';' and validate each email address
                    return EmailID.Split(new[] { ';', ',', ' ' }, StringSplitOptions.RemoveEmptyEntries)
                                  .Where(email => myRegex.IsMatch(email.Trim()))
                                  .Select(email => email.Trim())
                                  .ToList();
                }
                return new List<string>();
            }

            //set
            //{
            //    if (value != null)
            //    {
            //        // Join the valid email addresses with ';' and set EmailID
            //        EmailID = string.Join(";", value);
            //    }
            //}
        }

        public string? CC { get; set; }

        [NotMapped]
        public List<string> CCRecipients
        {
            get
            {
                if (!string.IsNullOrEmpty(CC))
                {
                    Regex myRegex = new Regex(McvConstant.EMAIL_REGEX, RegexOptions.None);

                    // Split CC by ';' and validate each email address
                    return CC.Split(new[] { ';', ',', ' ' }, StringSplitOptions.RemoveEmptyEntries)
                                  .Where(email => myRegex.IsMatch(email.Trim()))
                                  .Select(email => email.Trim())
                                  .ToList();
                }
                return new List<string>();
            }

            //set
            //{
            //    if (value != null)
            //    {
            //        // Join the valid email addresses with ';' and set CC
            //        CC = string.Join(";", value);
            //    }
            //}
        }

        public string? SpaceDiveJson { get; set; }

        [NotMapped]
        public List<SpaceDiveUrl> SpaceDiveUrls
        {
            get => !string.IsNullOrEmpty(SpaceDiveJson) ? JsonConvert.DeserializeObject<List<SpaceDiveUrl>>(SpaceDiveJson) : new List<SpaceDiveUrl>();
            set => SpaceDiveJson = JsonConvert.SerializeObject(value);
        }

        public virtual ICollection<PackageFeedback> Feedbacks { get; set; } = new List<PackageFeedback>();
    }

    public class SpaceDiveUrl
    {
        public string? Title { get; set; }
        public string? Url { get; set; }
    }

    public class PackageConfiguration : BaseEntityConfiguration<Package>, IEntityTypeConfiguration<Package>
    {
        public void Configure(EntityTypeBuilder<Package> builder)
        {
            base.Configure(builder);


            builder.HasIndex(x => x.ProjectCode);
            builder.HasIndex(x => x.ProjectTitle);
            builder.HasIndex(x => x.Title);
            builder.HasIndex(x => x.Code);
            builder.HasIndex(x => x.PhaseCode);
            builder.HasIndex(x => x.PhaseCode);
            builder.HasIndex(x => x.Stage);
            builder.HasIndex(x => x.DeliveryMode);
            builder.HasIndex(x => x.Purpose);
            builder.HasIndex(x => x.Revision);
            builder.HasIndex(x => x.IsAnnexure);
            builder.HasIndex(x => x.AnnexureIndex);
            builder.HasIndex(x => x.Annexure);
            builder.HasIndex(x => x.FinalDate);
            builder.HasIndex(x => x.StartDate);
            builder.HasIndex(x => x.SubmissionDate);
            builder.HasIndex(x => x.IsSubmissionSetProcessed);
            builder.HasIndex(x => x.CompanyID);
            builder.HasIndex(x => x.DValue);
            builder.HasIndex(x => x.RValue);
            builder.HasIndex(x => x.ProposedFinalDate);
            builder.HasIndex(x => x.ProposedStartDate);
            builder.HasIndex(x => x.ProposedPriority);
            builder.HasIndex(x => x.ProposedProbablity);


        }
    }
}
