﻿
using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.LibraryModule.Entities;
using MyCockpitView.WebApi.Services;




namespace MyCockpitView.WebApi.LibraryModule.Services;
public class LibraryAttributeMasterService : BaseEntityService<LibraryAttributeMaster>, ILibraryAttributeMasterService
{
    public LibraryAttributeMasterService(EntitiesContext db) : base(db) { }

    public IQueryable<LibraryAttributeMaster> Get(IEnumerable<QueryFilter>? Filters = null, string? Search = null, string? Sort = null)
    {
     
            IQueryable<LibraryAttributeMaster> _query = base.Get(Filters);

            //Apply filters
            if (Filters != null)
            {

                if (Filters.Where(x => x.Key.Equals("Category", StringComparison.OrdinalIgnoreCase)).Any())
                {
                    var predicate = PredicateBuilder.False<LibraryAttributeMaster>();
                    foreach (var _item in Filters.Where(x => x.Key.Equals("Category", StringComparison.OrdinalIgnoreCase)))
                    {
                        predicate = predicate.Or(x => x.Category==_item.Value);
                    }
                    _query = _query.Where(predicate);
                }
            }

            if (Search != null && Search != String.Empty)
            {
                _query = _query
                    .Where(x => x.Category.ToLower().Contains(Search.ToLower())
                    || x.Attribute.ToLower().Contains(Search.ToLower()));

            }

            return _query
              .OrderBy(x => x.Category).ThenBy(x=>x.Attribute);
    
    }

}