﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using MyCockpitView.CoreModule;
using System.ComponentModel.DataAnnotations;

namespace MyCockpitView.WebApi.ProjectModule.Entities;

public class ProjectScope : BaseEntity
{
    [Required]
    public int ProjectID { get; set; }

    public virtual Project? Project { get; set; }

    [StringLength(255)]
    [Required]
    public string? Title { get; set; }
    [Precision(14, 2)]
    public decimal SharePercentage { get; set; } = 0;
    [Precision(14, 2)]
    public decimal Amount { get; set; } = 0;
    [Precision(14, 2)]
    public decimal VHr { get; set; } = 0;
    [Precision(14, 2)]
    public decimal VHrCost { get; set; } = 0;
    [Precision(14, 2)]
    public decimal XVHrCost { get; set; } = 0;
    [Precision(14, 2)]
    public decimal CompoundCost { get; set; } = 0;
    [Precision(14, 2)]
    public decimal VHrRate { get; set; } = 0;
    [Precision(14, 2)]
    public decimal CompoundTenure { get; set; } = 0;
    [Precision(14, 2)]
    public decimal CompoundRate { get; set; } = 0;


    public virtual ICollection<ProjectScopeService> Services { get; set; }= new List<ProjectScopeService>();
}
public class ProjectScopeConfiguration : BaseEntityConfiguration<ProjectScope>, IEntityTypeConfiguration<ProjectScope>
{
    public void Configure(EntityTypeBuilder<ProjectScope> builder)
    {
        base.Configure(builder);
        // Properties

        builder.HasIndex(e => e.Title);
        builder.HasIndex(e => e.SharePercentage);
        builder.HasIndex(e => e.Amount);
        builder.HasIndex(e => e.VHr);
        builder.HasIndex(e => e.VHrCost);
        builder.HasIndex(e => e.XVHrCost);
        builder.HasIndex(e => e.CompoundCost);
        builder.HasIndex(e => e.VHrRate);
        builder.HasIndex(e => e.CompoundTenure);
        builder.HasIndex(e => e.CompoundRate);

    }
}
