



using MyCockpitView.WebApi.Services;
using MyCockpitView.WebApi.PackageModule.Entities;
using MyCockpitView.CoreModule;

namespace MyCockpitView.WebApi.PackageModule.Services;
public class PackageDeliverableTaskMapService : BaseEntityService<PackageDeliverableTaskMap>, IPackageDeliverableTaskMapService
{
    public PackageDeliverableTaskMapService(EntitiesContext db) : base(db)
    {
    }

    public IQueryable<PackageDeliverableTaskMap> Get(IEnumerable<QueryFilter>? Filters = null, string? Search = null, string? Sort = null)
    {

        IQueryable<PackageDeliverableTaskMap> _query = base.Get(Filters);

        //Apply filters
        if (Filters != null)
        {


            if (Filters.Where(x => x.Key.Equals("PackageID", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<PackageDeliverableTaskMap>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("PackageID", StringComparison.OrdinalIgnoreCase)))
                {
                    var isNumeric = Convert.ToInt32(_item.Value);

                    predicate = predicate.Or(x => x.PackageID == isNumeric);
                }
                _query = _query.Where(predicate);
            }

            if (Filters.Where(x => x.Key.Equals("PackageDeliverableID", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<PackageDeliverableTaskMap>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("PackageDeliverableID", StringComparison.OrdinalIgnoreCase)))
                {
                    var isNumeric = Convert.ToInt32(_item.Value);

                    predicate = predicate.Or(x => x.PackageDeliverableID == isNumeric);
                }
                _query = _query.Where(predicate);
            }

            if (Filters.Where(x => x.Key.Equals("WFTaskID", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<PackageDeliverableTaskMap>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("WFTaskID", StringComparison.OrdinalIgnoreCase)))
                {
                    var isNumeric = Convert.ToInt32(_item.Value);

                    predicate = predicate.Or(x => x.WFTaskID == isNumeric);
                }
                _query = _query.Where(predicate);
            }

        }


        return _query;

    }
}