using MediatR;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.AuthModule.Services;
using MyCockpitView.WebApi.ContactModule.Services;
using MyCockpitView.WebApi.Services;

namespace MyCockpitView.WebApi.ContactModule.Events;

public class OnContactAppointmentUpdateHandler : INotificationHandler<OnContactAppointmentUpdate>
{
    private readonly EntitiesContext _context;
    private readonly IContactService _contactService;
    private readonly ILoginSessionService _loginSessionService;
    private readonly ISharedService _sharedService;
    private readonly ILogger<OnContactAppointmentUpdateHandler> _logger;

    public OnContactAppointmentUpdateHandler(
        EntitiesContext context,
        IContactService contactService,
        ILoginSessionService loginSessionService,
        ISharedService sharedService,
        ILogger<OnContactAppointmentUpdateHandler> logger)
    {
        _context = context;
        _contactService = contactService;
        _loginSessionService = loginSessionService;
        _sharedService = sharedService;
        _logger = logger;
    }

    public async Task Handle(OnContactAppointmentUpdate notification, CancellationToken cancellationToken)
    {
        try
        {
            // Get the contact associated with this appointment
            var contact = await _contactService.Get()
                .FirstOrDefaultAsync(x => x.ID == notification.ContactID, cancellationToken);

            if (contact == null || string.IsNullOrEmpty(contact.Username))
            {
                // No contact found or contact has no username, nothing to do
                return;
            }

            // If the appointment was resigned, also log out the user
            if (notification.StatusFlag == McvConstant.APPOINTMENT_STATUSFLAG_RESIGNED)
            {
                var appUser = await _userManager.FindByNameAsync(contact.Username);

                IdentityResult result = await _userManager.DeleteAsync(appUser);

                // if (!result.Succeeded)

                contact.Username = null;
                await _contactService.Update(contact);


                // Log out the user since their appointment ended
                await _loginSessionService.LogoutUser(contact.Username, null);

                _logger.LogInformation("User {Username} logged out due to appointment resignation. " +
                                     "Appointment ID: {AppointmentId}, Status: {StatusFlag}",
                                     contact.Username, notification.ID, notification.StatusFlag);

            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling OnContactAppointmentUpdate for appointment ID {AppointmentId}: {ErrorMessage}",
                           notification.ID, ex.Message);
            // Don't rethrow - we don't want to break the main appointment update process
        }
    }
}


