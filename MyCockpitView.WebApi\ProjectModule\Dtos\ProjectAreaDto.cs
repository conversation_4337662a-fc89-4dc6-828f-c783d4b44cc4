﻿using AutoMapper;
using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.ProcessLibraryModule.Dtos;
using MyCockpitView.WebApi.ProcessLibraryModule.Entities;
using MyCockpitView.WebApi.ProjectModule.Entities;
using System.ComponentModel.DataAnnotations;

namespace MyCockpitView.WebApi.ProjectModule.Dtos;
public class ProjectAreaDto : BaseEntityDto
{
    [StringLength(255)]
    [Required]
    public string? Title { get; set; }

    public decimal Quantity { get; set; }

    public string? Units { get; set; }
    public int? ProjectID { get; set; }

}

public class ProjectAreaDtoMapperProfile : Profile
{
    public ProjectAreaDtoMapperProfile()
    {
        CreateMap<ProjectArea, ProjectAreaDto>()
                   .ReverseMap();

    }
}