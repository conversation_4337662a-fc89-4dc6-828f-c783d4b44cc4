﻿
using MyCockpitView.WebApi.ExpenseModule.Services;

namespace MyCockpitView.WebApi.ExpenseModule.Extensions;

public static class ServiceExtensions
{

    public static IServiceCollection RegisterExpenseServices(
     this IServiceCollection services)
    {
        services.AddScoped<IExpenseService,ExpenseService>();
        services.AddScoped<IExpenseAttachmentService, ExpenseAttachmentService>();
        return services;
    }
}
