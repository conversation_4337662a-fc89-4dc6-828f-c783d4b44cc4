﻿
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.WebApi.ContactModule.Entities;
using MyCockpitView.CoreModule;

namespace MyCockpitView.WebApi.ProjectModule.Entities;

public class ProjectInward : BaseEntity
{
    public string? Title { get; set; }
    public string? Message { get; set; }
    public DateTime? ReceivedDate { get; set; }
    public int ProjectID { get; set; }
    public virtual Project? Project { get; set; }
    public int ContactID { get; set; }
    public virtual Contact? Contact { get; set; }
    public virtual ICollection<ProjectInwardAttachment> Attachments { get; set; }=new HashSet<ProjectInwardAttachment>();

}


public class ProjectInwardConfiguration : BaseEntityConfiguration<ProjectInward>, IEntityTypeConfiguration<ProjectInward>
{
    public void Configure(EntityTypeBuilder<ProjectInward> builder)
    {
        base.Configure(builder);
        
        // Properties
        builder.Property(pi => pi.Title)
            .HasMaxLength(255);

        builder.Property(pi => pi.Message);

        builder.Property(pi => pi.ReceivedDate);

        builder.Property(pi => pi.ProjectID)
            .IsRequired();

        builder.Property(pi => pi.ContactID)
            .IsRequired();


        builder.HasOne(pi => pi.Contact)
            .WithMany()
            .HasForeignKey(pi => pi.ContactID)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasIndex(e => e.ReceivedDate);
    }
}

