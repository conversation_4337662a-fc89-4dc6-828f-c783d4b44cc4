﻿using AutoMapper;
using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.ProcessLibraryModule.Entities;

namespace MyCockpitView.WebApi.ProcessLibraryModule.Dtos;

public class ProcessLibraryEntityDto : BaseEntityDto
{

    public string? Title { get; set; }

    public string? Code { get; set; }

    public int CodeFlag { get; set; }

    public virtual ICollection<ProcessLibraryAttribute> Attributes { get; set; }= new List<ProcessLibraryAttribute>();

    public virtual ICollection<ProcessLibraryEntityDto> Children { get; set; } = new List<ProcessLibraryEntityDto>();

    public virtual ICollection<ProcessLibraryEntityAttachmentDto> Attachments { get; set; } = new HashSet<ProcessLibraryEntityAttachmentDto>();

    public bool IsReadOnly { get; set; }
}

public class ProcessLibraryEntityDtoMapperProfile : Profile
{
    public ProcessLibraryEntityDtoMapperProfile()
    {
        CreateMap<ProcessLibraryEntity, ProcessLibraryEntityDto>()
                   .ReverseMap()
                   .ForMember(dest => dest.Children, opt => opt.Ignore());

    }
}