﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using MyCockpitView.CoreModule;
using System.ComponentModel.DataAnnotations;

namespace MyCockpitView.WebApi.ActivityModule;

public class Activity : BaseEntity
{
    [StringLength(255)]
    public string? Action { get; set; }

    public int? WFTaskID { get; set; }

    public string? Comments { get; set; }

    [StringLength(50)]
    public string? Status { get; set; }

    public int ContactID { get; set; }

    public Guid ContactUID { get; set; }

    [StringLength(255)]
    public string? ContactName { get; set; }

    public string? ContactPhotoUrl { get; set; }

    [StringLength(255)]
    public string? Entity { get; set; }

    public int? EntityID { get; set; }

    [StringLength(255)]
    public string? EntityTitle { get; set; }
}

public class ActivityConfiguration : BaseEntityConfiguration<Activity>, IEntityTypeConfiguration<Activity>
{
    public void Configure(EntityTypeBuilder<Activity> builder)
    {
        base.Configure(builder);

        builder.HasIndex(e => e.ContactID);
        builder.HasIndex(e => e.Entity);
        builder.HasIndex(e => e.EntityID);
        builder.HasIndex(e => e.IsVersion);
    }
}