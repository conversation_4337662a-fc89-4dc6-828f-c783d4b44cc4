using MyCockpitView.WebApi.AuthModule.Services;

namespace MyCockpitView.WebApi.AuthModule.Extensions;

public static class AuthExtensions
{
    public static IServiceCollection RegisterAuthServices(
       this IServiceCollection services)
    {

        services.AddScoped<ITokenGenerator, TokenGenerator>();
        services.AddScoped<IAccessTokenService, AccessTokenService>();
        services.AddScoped<IRefreshTokenService, RefreshTokenService>();
        services.AddScoped<ILoginSessionService, LoginSessionService>();

        return services;
    }
}
