﻿
using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.WebApi.Responses;
using MyCockpitView.Utility.Common;
using MyCockpitView.WebApi.ActivityModule;
using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.ContactModule.Services;
using MyCockpitView.WebApi.MeetingModule.Services;
using MyCockpitView.WebApi.MeetingModule.Dtos;
using MyCockpitView.WebApi.MeetingModule.Entities;
using MyCockpitView.WebApi.WFTaskModule.Services;
using MyCockpitView.Utility.RDLCClient;
using MyCockpitView.WebApi.Exceptions;
using MyCockpitView.WebApi.ProjectModule.Services;
using MyCockpitView.WebApi.Services;

namespace MyCockpitView.WebApi.MeetingModule.Controllers;

[Authorize]
[ApiController]
[Route("[controller]")]
public class MeetingController : ControllerBase
{
    private readonly IMeetingService service;
    private readonly EntitiesContext db;
    private readonly IActivityService activityService;
    private readonly IMapper mapper;
    private readonly IContactService contactService;
    private readonly IWFTaskService taskService;
    private readonly IProjectService projectService;
    private readonly IMeetingAgendaService meetingAgendaService;
    private readonly ISharedService sharedService;

    public MeetingController(EntitiesContext db, IMeetingService service, IMapper mapper, IActivityService activityService, IContactService contactService, IWFTaskService taskService, IProjectService projectService, IMeetingAgendaService meetingAgendaService, ISharedService sharedService)
    {
        this.db = db;
        this.service = service;
        this.activityService = activityService;
        this.mapper = mapper;
        this.contactService = contactService;
        this.taskService = taskService;
        this.projectService = projectService;
        this.meetingAgendaService = meetingAgendaService;
        this.sharedService = sharedService;
    }

    [HttpGet]
    public async Task<ActionResult<IEnumerable<MeetingDto>>> Get(
        [FromQuery] string? filters = null,
        [FromQuery] string? search = null,
        [FromQuery] string? sort = null)
    {
        var query = service.Get(filters != null ? DataTools.GetObjectFromJsonString<APIFilter>(filters).Filters : null, search, sort).Include(x => x.Contact);
        var results = mapper.Map<IEnumerable<MeetingDto>>(await query.ToListAsync());

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(Meeting))
            .ToListAsync();

        foreach (var obj in results)
        {
            obj.Type = typeMasters.FirstOrDefault(x => x.Value == obj.TypeFlag)?.Title ?? "";
        }

        var statusMasters = await db.StatusMasters
          .AsNoTracking()
          .Where(x => x.Entity == nameof(Meeting))
          .ToListAsync();

        foreach (var obj in results)
        {
            obj.Status = statusMasters.FirstOrDefault(x => x.Value == obj.StatusFlag)?.Title ?? "";
        }

        var contact = await contactService.Get()
               .SingleOrDefaultAsync(x => x.Username == User.Identity.Name);
        foreach (var obj in results)
        {
            if (contact != null)
                obj.IsEditable = await service.IsMeetingEditable(obj.ID, contact.ID);

            var _taskDueDate = await taskService.Get()
           .Where(x => x.Entity != null && x.Entity == nameof(Meeting)
           && x.EntityID == obj.ID && x.StatusFlag == McvConstant.WFTASK_STATUSFLAG_PENDING)
           .OrderBy(x => x.DueDate)
           .Select(x => x.DueDate)
           .FirstOrDefaultAsync();

            if (_taskDueDate != null)
            {
                obj.IsDelayed = _taskDueDate < DateTime.UtcNow;
            }
        }

        return Ok(results);
    }

    [HttpGet("Pages")]
    public async Task<ActionResult<PagedResponse<MeetingDto>>> GetPages(
        [FromQuery] int page = 0,
        [FromQuery] int pageSize = 50,
        [FromQuery] string? filters = null,
        [FromQuery] string? search = null,
        [FromQuery] string? sort = null)
    {
        var query = service.Get(filters != null ? DataTools.GetObjectFromJsonString<APIFilter>(filters).Filters : null, search, sort).Include(x => x.Contact);

        var totalCount = await query.CountAsync();
        var totalPages = (int)Math.Ceiling((decimal)totalCount / pageSize);
        var results = mapper.Map<IEnumerable<MeetingDto>>(await query
            .Skip(pageSize * page)
            .Take(pageSize)
            .ToListAsync());

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(Meeting))
            .ToListAsync();

        foreach (var obj in results)
        {
            obj.Type = typeMasters.FirstOrDefault(x => x.Value == obj.TypeFlag)?.Title ?? "";
        }

        var statusMasters = await db.StatusMasters
           .AsNoTracking()
           .Where(x => x.Entity == nameof(Meeting))
           .ToListAsync();

        foreach (var obj in results)
        {
            obj.Status = statusMasters.FirstOrDefault(x => x.Value == obj.StatusFlag)?.Title ?? "";
        }
        var contact = await contactService.Get()
              .SingleOrDefaultAsync(x => x.Username == User.Identity.Name);
        foreach (var obj in results)
        {
            if (contact != null)
                obj.IsEditable = await service.IsMeetingEditable(obj.ID, contact.ID);

            var _taskDueDate = await taskService.Get()
          .Where(x => x.Entity != null && x.Entity == nameof(Meeting)
          && x.EntityID == obj.ID && x.StatusFlag == McvConstant.WFTASK_STATUSFLAG_PENDING)
          .OrderBy(x => x.DueDate)
          .Select(x => x.DueDate)
          .FirstOrDefaultAsync();

            if (_taskDueDate != null)
            {
                obj.IsDelayed = _taskDueDate < DateTime.UtcNow;
            }
        }
        return Ok(new PagedResponse<MeetingDto>(results, totalCount, totalPages));
    }

    [HttpGet("{id:int}")]
    public async Task<ActionResult<MeetingDto>> GetByID(int id)
    {
        var responseDto = mapper.Map<MeetingDto>(await service.GetById(id));
        if (responseDto == null)
        {
            return NotFound();
        }

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(Meeting))
            .ToListAsync();

        responseDto.Type = typeMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        var statusMasters = await db.StatusMasters
          .AsNoTracking()
          .Where(x => x.Entity == nameof(Meeting))
          .ToListAsync();

        responseDto.Status = statusMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        var contact = await contactService.Get()
              .SingleOrDefaultAsync(x => x.Username == User.Identity.Name);

        if (contact != null)
            responseDto.IsEditable = await service.IsMeetingEditable(responseDto.ID, contact.ID);

        return Ok(responseDto);
    }

    [HttpPost]
    public async Task<ActionResult<MeetingDto>> Post(MeetingDto dto)
    {
        var id = await service.Create(mapper.Map<Meeting>(dto));
        var responseDto = mapper.Map<MeetingDto>(await service.GetById(id));

        if (responseDto == null)
            return BadRequest("Not Created");

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(Meeting))
            .ToListAsync();

        responseDto.Type = typeMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        var statusMasters = await db.StatusMasters
  .AsNoTracking()
  .Where(x => x.Entity == nameof(Meeting))
  .ToListAsync();

        responseDto.Status = statusMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";
        var contact = await contactService.Get()
      .SingleOrDefaultAsync(x => x.Username == User.Identity.Name);

        if (contact != null)
            responseDto.IsEditable = await service.IsMeetingEditable(responseDto.ID, contact.ID);
        //if (!string.IsNullOrEmpty(User.Identity?.Name))
        //{
        //    var currentContact = await contactService.Get().FirstOrDefaultAsync(x => x.Username == User.Identity.Name);
        //    if (currentContact != null)
        //    {
        //        var parent = await contactService.Get().SingleOrDefaultAsync(x => x.ID == responseDto.ContactID);
        //        if (parent != null)
        //        {
        //            await activityService.LogUserActivity(currentContact, nameof(parent), parent.ID, $"{parent.Name}", $"{nameof(Meeting).Replace(nameof(parent),"")} | {responseDto.Company.Title} | {responseDto.Designation} | {responseDto.ManValue}mV", "Created");
        //        }
        //    }
        //}

        return CreatedAtAction(nameof(GetByID), new { id = responseDto.ID }, responseDto);
    }

    [HttpPut("{id:int}")]
    public async Task<ActionResult<MeetingDto>> Put(int id, MeetingDto dto)
    {
        if (id != dto.ID)
        {
            return BadRequest();
        }

        await service.Update(mapper.Map<Meeting>(dto));
        var responseDto = mapper.Map<MeetingDto>(await service.GetById(id));

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(Meeting))
            .ToListAsync();

        responseDto.Type = typeMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        var statusMasters = await db.StatusMasters
  .AsNoTracking()
  .Where(x => x.Entity == nameof(Meeting))
  .ToListAsync();

        responseDto.Status = statusMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";
        var contact = await contactService.Get()
      .SingleOrDefaultAsync(x => x.Username == User.Identity.Name);

        if (contact != null)
            responseDto.IsEditable = await service.IsMeetingEditable(responseDto.ID, contact.ID);
        //if (!string.IsNullOrEmpty(User.Identity?.Name))
        //{
        //    var currentContact = await contactService.Get().FirstOrDefaultAsync(x => x.Username == User.Identity.Name);
        //    if (currentContact != null)
        //    {
        //        var parent = await contactService.Get().SingleOrDefaultAsync(x => x.ID == responseDto.ContactID);
        //        if (parent != null)
        //        {
        //            await activityService.LogUserActivity(currentContact, nameof(parent), parent.ID, $"{parent.Name}", $"{nameof(Meeting).Replace(nameof(parent), "")} | {responseDto.Company.Title} | {responseDto.Designation} | {responseDto.ManValue}mV", "Updated");
        //        }
        //    }
        //}

        return Ok(responseDto);
    }

    [HttpDelete("{id:int}")]
    public async Task<IActionResult> Delete(int id)
    {
        var responseDto = mapper.Map<MeetingDto>(await service.GetById(id));
        if (responseDto == null) return BadRequest($"{nameof(Meeting)} not found!");

        await service.Delete(id);

        //if (!string.IsNullOrEmpty(User.Identity?.Name))
        //{
        //    var currentContact = await contactService.Get().FirstOrDefaultAsync(x => x.Username == User.Identity.Name);
        //    if (currentContact != null)
        //    {
        //        var parent = await contactService.Get().SingleOrDefaultAsync(x => x.ID == responseDto.ContactID);
        //        if (parent != null)
        //        {
        //            await activityService.LogUserActivity(currentContact, nameof(parent), parent.ID, $"{parent.Name}", $"{nameof(Meeting).Replace(nameof(parent), "")} | {responseDto.Company.Title} | {responseDto.Designation} | {responseDto.ManValue}mV", "Deleted");
        //        }
        //    }
        //}

        return NoContent();
    }


    [HttpGet("uid/{id:guid}")]
    [AllowAnonymous]
    public async Task<ActionResult<MeetingDto>> GetByGUID(Guid id)
    {
        var responseDto = mapper.Map<MeetingDto>(await service.GetById(id));
        if (responseDto == null)
        {
            return NotFound();
        }

        var typeMasters = await db.TypeMasters
            .AsNoTracking()
            .Where(x => x.Entity == nameof(Meeting))
            .ToListAsync();

        responseDto.Type = typeMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        var statusMasters = await db.StatusMasters
                        .AsNoTracking()
                        .Where(x => x.Entity == nameof(Meeting))
                        .ToListAsync();

        responseDto.Status = statusMasters.FirstOrDefault(x => x.Value == responseDto.TypeFlag)?.Title ?? "";

        var originalHostName = await sharedService.GetPresetValue(McvConstant.AZURE_STORAGE_BLOB_ORIGINAL_HOSTNAME);
        var cdnHostName = await sharedService.GetPresetValue(McvConstant.AZURE_STORAGE_BLOB_CDN_HOSTNAME);

        foreach (var agenda in responseDto.Agendas)
        {
            foreach (var attachment in agenda.Attachments)
            {
                attachment.Url = attachment.Url.Replace(originalHostName, cdnHostName);
                if (attachment.ThumbUrl != null)
                    attachment.ThumbUrl = attachment.ThumbUrl.Replace(originalHostName, cdnHostName);
            }
        }

        return Ok(responseDto);
    }
    [HttpGet("Count")]
    public async Task<IActionResult> GetCount(
        [FromQuery] string? filters = null,
        [FromQuery] string? search = null,
        [FromQuery] string? sort = null)
    {

        var query = service.Get(filters != null ? DataTools.GetObjectFromJsonString<APIFilter>(filters).Filters : null, search, sort);

        return Ok(await query.CountAsync());

    }

    [HttpGet("SearchTagOptions")]
    public async Task<IActionResult> GetSearchTagOptions()
    {
        var results = await service.GetSearchTagOptions();
        return Ok(results);
    }


    [HttpGet("FieldOptions")]
    public async Task<IActionResult> GetFieldOptions(string field)
    {
        return Ok(await service.GetFieldOptions(field));
    }


    [HttpGet("IsEditable/{id}")]
    public async Task<IActionResult> CheckIfEditable(int id)
    {

        var contact = await contactService.Get()
                  .SingleOrDefaultAsync(x => x.Username == User.Identity.Name);
        if (contact == null)
            return Ok(false);

        return Ok(await service.IsMeetingEditable(id, contact.ID));

    }

    [HttpGet("IsDelayed/{id}")]
    public async Task<IActionResult> CheckIfDelayed(int id)
    {

        var _taskDueDate = await taskService.Get()
         .Where(x => x.Entity != null && x.Entity == nameof(Meeting)
         && x.EntityID == id && x.StatusFlag == McvConstant.WFTASK_STATUSFLAG_PENDING)
         .OrderBy(x => x.DueDate)
         .Select(x => x.DueDate)
         .FirstOrDefaultAsync();

        if (_taskDueDate != null)
        {
            return Ok(_taskDueDate < DateTime.UtcNow);
        }

        return Ok(false);

    }


    [HttpPut("Send/{id}")]
    public async Task<IActionResult> Send(int id)
    {

        await service.SendMinutes(id);
        return Ok();

    }

    [Authorize]
    [HttpGet]
    [Route("LastAttendees")]
    public async Task<IActionResult> GetLastAttendees(string subject)
    {

        var query = await service.GetLastAttendees(subject);

        return Ok(query);

    }

    [AllowAnonymous]
    [HttpGet("Report/{reportName}/{size}/{id:guid}")]
    public async Task<IActionResult> Report(
        string reportName,
        string size,
        Guid id,
        [FromQuery] string? filters = null,
        [FromQuery] string? sort = null,
        [FromQuery] string output = "PDF",
        [FromQuery] bool inline = false)
    {
        var deserializedFilters = filters != null ? DataTools.GetObjectFromJsonString<APIFilter>(filters).Filters : null;

        var _meeting = await service.GetById(id);

        if (_meeting == null) throw new EntityServiceException("Meeting not Found!");

        ReportDefinition? reportDef = reportName.ToLower() switch
        {
            "minutes" => await service.GetMinutesReport(_meeting, sort),
            "agenda" => await service.GetAgendaReport(_meeting, sort),
            _ => null
        };

        if (reportDef?.FileContent == null)
        {
            return BadRequest("Report not generated!");
        }

        var contentDisposition = inline ? "inline" : "attachment";

        return File(
            fileContents: reportDef.FileContent,
            contentType: reportDef.FileContentType,
            fileDownloadName: $"{reportDef.Filename}{reportDef.FileExtension}",
            enableRangeProcessing: true
        );
    }

    [AllowAnonymous]
    [HttpGet("AgendaReportByProject/{id:guid}")]
    public async Task<IResult> GetAgendaReportByProject(
        Guid id,
        [FromQuery] string? filters = null,
        [FromQuery] string? sort = null,
        [FromQuery] string output = "PDF",
        [FromQuery] bool inline = false)
    {
        var deserializedFilters = filters != null ? DataTools.GetObjectFromJsonString<APIFilter>(filters).Filters : null;


        var project = await projectService.Get()
            .Select(x => new
            {
                x.ID,
                x.Code,
                x.Title,
                x.UID
            })
            .SingleOrDefaultAsync(x => x.UID == id);

        if (project is null)
        {
            return Results.BadRequest("Project not found!");
        }

        var meeting = new Meeting
        {
            ProjectID = project.ID,
            Title = $"{project.Code}-{project.Title}",
            TypeFlag = McvConstant.MEETING_TYPEFLAG_MEETING,
            CreatedBy = "Newarch",
            StartDate = DateTime.UtcNow,
            EndDate = DateTime.UtcNow,
            Agendas = new List<MeetingAgenda>()
        };

        var pendingList = await meetingAgendaService.Get()
            .Include(x => x.Meeting.Contact)
            .Where(x => !x.Meeting.IsVersion &&
                       !x.IsForwarded &&
                       x.StatusFlag == McvConstant.MEETING_AGENDA_STATUSFLAG_PENDING &&
                       x.Meeting.ProjectID == project.ID)
            .Where(x => x.Meeting.StatusFlag == McvConstant.MEETING_STATUSFLAG_ATTENDED ||
                       x.Meeting.StatusFlag == McvConstant.MEETING_STATUSFLAG_SENT)
            .Where(x => x.TypeFlag == McvConstant.MEETING_TYPEFLAG_MEETING ||
                       x.TypeFlag == McvConstant.MEETING_TYPEFLAG_CNOTE)
            .ToListAsync();

        foreach (var previousPending in pendingList)
        {
            var previousAgendaHistory = await service.GetMeetingAgendaHistoryString(previousPending);

            var currentAgenda = new MeetingAgenda
            {
                Title = previousPending.Title,
                Subtitle = previousPending.Subtitle,
                IsCustomSubtitle = previousPending.IsCustomSubtitle,
                PreviousComment = previousPending.Comment,
                PreviousHistory = previousAgendaHistory,
                PreviousDueDate = previousPending.DueDate ?? previousPending.PreviousDueDate,
                PreviousActionBy = previousPending.ActionBy ?? previousPending.PreviousActionBy,
                PreviousProgress = previousPending.Progress,
                PreviousAgendaID = previousPending.ID,
                Progress = previousPending.Progress,
                DesignScriptEntityID = previousPending.DesignScriptEntityID,
                ActionBy = previousPending.ActionBy,
                DueDate = previousPending.DueDate,
                ActionByContactID = previousPending.ActionByContactID,
                ReminderCount = previousPending.ReminderCount,
                TypeFlag = previousPending.TypeFlag,
            };

            meeting.Agendas.Add(currentAgenda);
        }

        var reportDef = await service.GetAgendaReport(meeting, sort);

        if (reportDef?.FileContent is null)
        {
            return Results.BadRequest("Report not generated!");
        }

        var fileName = $"Agenda List-{project.Code}-{project.Title}-{TimeProvider.System.GetUtcNow():yyMMddHHmm}{reportDef.FileExtension}";

        return Results.File(
            fileContents: reportDef.FileContent,
            contentType: reportDef.FileContentType,
            fileDownloadName: fileName,
            enableRangeProcessing: true);

        //var contentDisposition = inline
        //   ? $"inline; filename=\"{fileName}\""
        //   : $"attachment; filename=\"{fileName}\"";

        //return Results.File(
        //    fileContents: reportDef.FileContent,
        //    contentType: reportDef.FileContentType,
        //    fileDownloadName: null)
        //    .ExecuteAsync(new DefaultHttpContext())
        //    .ContinueWith(t =>
        //    {
        //        var response = t.Result;
        //        response.Headers.ContentDisposition = contentDisposition;
        //        return Results.Empty;
        //    }).Result;
    }

    [AllowAnonymous]
    [HttpGet("RegenerateSequence")]
    public async Task<IActionResult> RegenerateSequence()
    {
        
        var meetings = await db.Meetings
            .Where(x=> x.ProjectID!=null)
                        .ToListAsync();

        var updateCount = 0;
        //Group by PRojectID
        var groupedMeeting = meetings
            .Where(x=>x.ProjectID!=null && !x.IsVersion)
            .GroupBy(x => x.ProjectID)
            .ToList();
        foreach (var group in groupedMeeting)
        {
            var major = 0.0m;
            var minor = 0.0m;
            foreach (var Entity in group.OrderBy(x=>x.StartDate))
            {
                var sequence = major + minor;
                if (Entity.TypeFlag == McvConstant.MEETING_TYPEFLAG_MEETING)
                {
                    minor = 0.0m;
                    major =major+1.0m;
                    sequence = major + minor;
                }
                else
                {
                    minor = minor + 0.1m;
                    sequence = major + minor;
                }

                Console.WriteLine($"Meeting ID: {Entity.ID} with Old Sequence:{Entity.Sequence} >> new sequence: {sequence}");

                if (Entity.Sequence != sequence)
                {
                    Entity.Sequence = sequence;
                    db.Entry(Entity).State = EntityState.Modified;
                    Console.WriteLine($"Updated Meeting ID: {Entity.ID} with Sequence: {sequence}");
                    updateCount++;
                }

                var versions=meetings.Where(x=> x.IsVersion && x.ParentID==Entity.ID).ToList();
                foreach (var version in versions)
                {
                    Console.WriteLine($"Version {version.ID} with Old Sequence:{Entity.Sequence} >> new sequence: {sequence}");
                    if (version.Sequence != sequence)
                    {
                        version.Sequence = sequence;
                        db.Entry(version).State = EntityState.Modified;
                        Console.WriteLine($"Updated Version ID: {version.ID} with Sequence: {sequence}");
                        updateCount++;
                    }
                }

            }
        }

        await db.SaveChangesAsync();


        return Ok($"{updateCount} meetings updated");
    }

}