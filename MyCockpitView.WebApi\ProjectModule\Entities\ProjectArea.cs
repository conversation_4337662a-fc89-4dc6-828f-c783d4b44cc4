﻿using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.CoreModule;
using System.ComponentModel.DataAnnotations;
namespace MyCockpitView.WebApi.ProjectModule.Entities;

public class ProjectArea : BaseEntity
{
    
    [StringLength(255)]
    [Required]
    public string? Title { get; set; }

    public decimal Quantity { get; set; }

    public string? Units { get; set; }

    public int ProjectID { get; set; }

    public virtual Project? Project { get; set; }
}

public class ProjectAreaConfiguration : BaseEntityConfiguration<ProjectArea>, IEntityTypeConfiguration<ProjectArea>
{
    public void Configure(EntityTypeBuilder<ProjectArea> builder)
    {
        base.Configure(builder);
        builder.HasIndex(x=>x.Title);
    }
}