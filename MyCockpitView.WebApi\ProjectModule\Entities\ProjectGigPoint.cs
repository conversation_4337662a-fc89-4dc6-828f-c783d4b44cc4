﻿using MyCockpitView.CoreModule;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;

namespace MyCockpitView.WebApi.ProjectModule.Entities;

public class ProjectGigPoint : BaseEntity
{
    [Required]
    public int ProjectID { get; set; }

    public string? Comment { get; set; }

    public decimal RecordValue { get; set; }

    [Column(TypeName = "datetime2")]
    public DateTime RecordDate { get; set; }

    public virtual Project? Project { get; set; }
}

public class ProjectGigPointConfiguration : BaseEntityConfiguration<ProjectGigPoint>, IEntityTypeConfiguration<ProjectGigPoint>
{
    public void Configure(EntityTypeBuilder<ProjectGigPoint> builder)
    {
        base.Configure(builder);
    }
}
