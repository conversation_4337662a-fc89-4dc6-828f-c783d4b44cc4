﻿using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.CoreModule;

namespace MyCockpitView.WebApi.ProjectModule.Entities;

public class ProjectInwardAttachment : BaseBlobEntity
{
    public int ProjectInwardID { get; set; }

    public virtual ProjectInward? ProjectInward { get; set; }
}

public class ProjectInwardAttachmentConfiguration : BaseBlobEntityConfiguration<ProjectInwardAttachment>, IEntityTypeConfiguration<ProjectInwardAttachment>
{
    public void Configure(EntityTypeBuilder<ProjectInwardAttachment> builder)
    {
        base.Configure(builder);
    
    }
}