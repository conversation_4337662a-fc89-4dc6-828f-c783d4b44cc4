﻿
using System.Data;
using MyCockpitView.WebApi.Exceptions;
using MyCockpitView.WebApi.Services;
using MyCockpitView.WebApi.ExpenseModule.Entities;
using MyCockpitView.WebApi.WFTaskModule.Services;
using MyCockpitView.CoreModule;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.Utility.Common;
using MyCockpitView.Utility.Excel;

namespace MyCockpitView.WebApi.ExpenseModule.Services;

public class ExpenseService : BaseEntityService<Expense>, IExpenseService
{
    public ExpenseService(EntitiesContext db) : base(db) { }

    public IQueryable<Expense> Get(IEnumerable<QueryFilter>? Filters = null, string? Search = null, string? Sort = null)
    {

        IQueryable<Expense> _query = base.Get(Filters);

        //Apply filters
        if (Filters != null)
        {

            if (Filters.Where(x => x.Key.Equals("payToContactID", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<Expense>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("payToContactID", StringComparison.OrdinalIgnoreCase)))
                {
                    var isNumeric = Convert.ToInt32(_item.Value);

                    predicate = predicate.Or(x => x.PayToContactID == isNumeric);
                }
                _query = _query.Where(predicate);
            }
            if (Filters.Where(x => x.Key.Equals("ApprovedByContactID", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<Expense>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("ApprovedByContactID", StringComparison.OrdinalIgnoreCase)))
                {
                    var isNumeric = Convert.ToInt32(_item.Value);

                    predicate = predicate.Or(x => x.ApprovedByContactID == isNumeric);
                }
                _query = _query.Where(predicate);
            }
            if (Filters.Where(x => x.Key.Equals("ExpenseHead", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<Expense>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("ExpenseHead", StringComparison.OrdinalIgnoreCase)))
                {
                    predicate = predicate.Or(x => x.ExpenseHead == _item.Value);
                }
                _query = _query.Where(predicate);
            }

            if (Filters.Where(x => x.Key.Equals("rangestart", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var _item = Filters.First(x => x.Key.Equals("rangestart", StringComparison.OrdinalIgnoreCase));

                DateTime result = Convert.ToDateTime(_item.Value);
                _query = _query.Where(x => x.ExpenseDate >= result);
            }

            if (Filters.Where(x => x.Key.Equals("rangeend", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var _item = Filters.First(x => x.Key.Equals("rangeend", StringComparison.OrdinalIgnoreCase));

                DateTime result = Convert.ToDateTime(_item.Value);
                var end = result.AddDays(1);
                _query = _query.Where(x => x.ExpenseDate < end);

            }

            if (Filters.Where(x => x.Key.Equals("entity", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<Expense>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("entity", StringComparison.OrdinalIgnoreCase)))
                {
                    predicate = predicate.Or(x => x.Entity != null && x.Entity == _item.Value);
                }
                _query = _query.Where(predicate);
            }

            if (Filters.Where(x => x.Key.Equals("entityID", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<Expense>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("entityID", StringComparison.OrdinalIgnoreCase)))
                {
                    predicate = predicate.Or(x => x.EntityID != null && x.EntityID.ToString() == _item.Value);
                }
                _query = _query.Where(predicate);
            }

            if (Filters.Where(x => x.Key.Equals("ProjectID", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<Expense>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("ProjectID", StringComparison.OrdinalIgnoreCase)))
                {
                    predicate = predicate.Or(x => x.ProjectID != null && x.ProjectID.ToString() == _item.Value);
                }
                _query = _query.Where(predicate);
            }
            if (Filters.Where(x => x.Key.Equals("isCredit", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var _item = Convert.ToBoolean(Filters.First(x => x.Key.Equals("isCredit", StringComparison.OrdinalIgnoreCase)).Value);

                var predicate = PredicateBuilder.False<Expense>();

                if (_item)
                {
                    predicate = predicate.Or(x => x.AmountCr != 0);
                }
                else
                {
                    predicate = predicate.Or(x => x.AmountDr != 0);
                }

                _query = _query.Where(predicate);
            }

            if (Filters.Where(x => x.Key.Equals("companyID", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<Expense>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("companyID", StringComparison.OrdinalIgnoreCase)))
                {
                    var isNumeric = Convert.ToInt32(_item.Value);

                    predicate = predicate.Or(x => x.CompanyID == isNumeric);
                }
                _query = _query.Where(predicate);
            }
        }

        if (Search != null && Search != String.Empty)
        {
            Search = Search.ToLower();
            _query = _query
                 .Where(x => x.ExpenseHead.ToLower().Contains(Search.ToLower())
                 || x.Particulars.ToLower().Contains(Search.ToLower())
                 || x.TransactionRefNo.ToLower().Contains(Search.ToLower())
                                            || x.PayTo.ToLower().Contains(Search.ToLower())
                                            || x.ApprovedBy.ToLower().Contains(Search.ToLower())
                                            || x._searchTags.ToLower().Contains(Search.ToLower())
                                           );

        }

        if (Sort != null && Sort != String.Empty)
        {
            switch (Sort.ToLower())
            {
                case "createddate":
                    return _query
                            .OrderBy(x => x.Created);

                case "modifieddate":
                    return _query
                            .OrderBy(x => x.Modified);

                case "createddate desc":
                    return _query
                            .OrderByDescending(x => x.Created);

                case "modifieddate desc":
                    return _query
                            .OrderByDescending(x => x.Modified);

                case "expensehead":
                    return _query
                            .OrderBy(x => x.ExpenseHead);

                case "expensehead desc":
                    return _query
                            .OrderByDescending(x => x.ExpenseHead);

                case "expensedate":
                    return _query
                            .OrderBy(x => x.ExpenseDate);

                case "expensedate desc":
                    return _query
                            .OrderByDescending(x => x.ExpenseDate);

                case "transactiondate":
                    return _query
                            .OrderBy(x => x.TransactionDate);

                case "transactiondate desc":
                    return _query
                            .OrderByDescending(x => x.TransactionDate);
            }
        }

        return _query.OrderBy(x => x.ExpenseDate).ThenBy(x => x.ExpenseHead).ThenBy(x => x.Particulars);

    }

    public async Task<Expense?> GetById(int Id)
    {

        return await db.Expenses.AsNoTracking()
          .Include(x => x.ApprovedByContact)
                    //.Include(x => x.Taxes)
                    .Include(x => x.Attachments)
                    .Include(x => x.Company)
             .SingleOrDefaultAsync(i => i.ID == Id);

    }

    public async Task<int> Create(Expense Entity)
    {

        var _currentYear = DateTime.UtcNow.Year;
        var _lastOrderByVoucherDate = await Get()
            .Where(x => x.TypeFlag == Entity.TypeFlag)
            .Where(x => x.CompanyID == Entity.CompanyID)
            .Where(x => x.ExpenseDate.Year == DateTime.UtcNow.Year).AnyAsync() ? await Get()
            .Where(x => x.ExpenseDate.Year == _currentYear)
            .MaxAsync(x => x.OrderFlag) : 0;

        var _company = await db.Companies
                
                        .AsNoTracking()
            .SingleOrDefaultAsync(x => x.ID == Entity.CompanyID);

        Entity.OrderFlag = _lastOrderByVoucherDate + 1;
        Entity.Code = Entity.ExpenseDate.ToString("yy") + "."
            + (Entity.TypeFlag == 1 ? 'P' : 'X') + "."
            + (Entity.OrderFlag).ToString("0000");

        if (Entity.PayTo == null || Entity.PayTo == string.Empty)
        {
            var _contact = await db.Contacts.AsNoTracking().SingleOrDefaultAsync(x => x.ID == Entity.PayToContactID);
            if (_contact != null)
                Entity.PayTo = _contact.Name;
        }
        if (Entity.ApprovedByContactID != null && (Entity.ApprovedBy == null || Entity.ApprovedBy == string.Empty))
        {
            var _contact = await db.Contacts.AsNoTracking().SingleOrDefaultAsync(x => x.ID == Entity.ApprovedByContactID);
            if (_contact != null)
                Entity.ApprovedBy = _contact.Name;
        }

        var _lastBalance = 0.0m;
        var _lastRecords = await Get()
             .Where(x => x.TypeFlag == Entity.TypeFlag)
            .Where(x => x.CompanyID == Entity.CompanyID).ToListAsync();

        if (_lastRecords.Any())
        {
            _lastBalance = _lastRecords
                .Sum(x => x.AmountCr)
                - _lastRecords
                .Sum(x => x.AmountDr);
        }

        Entity.AmountBalance = _lastBalance + Entity.AmountCr - Entity.AmountDr;

        return await base.Create(Entity);

    }

    public async Task Update(Expense Entity)
    {


        var _lastBalance = 0.0m;
        var _lastRecords = await Get()
      .Where(x => x.TypeFlag == Entity.TypeFlag)
            .Where(x => x.CompanyID == Entity.CompanyID)
            .Where(x => x.ID != Entity.ID)
            .Where(x => x.Created < Entity.Created).ToListAsync();
        if (_lastRecords.Any())
        {
            _lastBalance = _lastRecords
                .Sum(x => x.AmountCr)
                - _lastRecords
                .Sum(x => x.AmountDr);
        }

        Entity.AmountBalance = _lastBalance + Entity.AmountCr - Entity.AmountDr;
        if (Entity.PayTo == null || Entity.PayTo == string.Empty)
        {
            var _contact = await db.Contacts.AsNoTracking().SingleOrDefaultAsync(x => x.ID == Entity.PayToContactID);
            if (_contact != null)
                Entity.PayTo = _contact.Name;
        }
        if (Entity.ApprovedByContactID != null && (Entity.ApprovedBy == null || Entity.ApprovedBy == string.Empty))
        {
            var _contact = await db.Contacts.AsNoTracking().SingleOrDefaultAsync(x => x.ID == Entity.ApprovedByContactID);
            if (_contact != null)
                Entity.ApprovedBy = _contact.Name;
        }

        await base.Update(Entity);
    }

    public async Task Delete(int Id)
    {

        Expense Entity = await Get().SingleOrDefaultAsync(i => i.ID == Id);

        if (Entity == null) throw new EntityServiceException($"{nameof(Expense)} does not exist");

       

        await base.Delete(Id);

        await RecalculateBalance(Entity.CompanyID, Entity.TypeFlag, Entity.Created);

    }

    public async Task RecalculateBalance(int CompanyID, int TypeFlag, DateTime? LastExpenseDate = null)
    {
        var _query = Get()
              .Where(x => x.TypeFlag == TypeFlag)
                    .Where(x => x.CompanyID == CompanyID);

        if (LastExpenseDate != null)
        {
            _query = _query.Where(x => x.Created > LastExpenseDate);
        }

        var _dataCards = await _query
            .OrderBy(x => x.Created)
            .ToListAsync();

        var _container = "attachments";
        var count = 0;
        var _success = 0;
        var _failed = 0;
        //var _balance = 0m;
        foreach (var card in _dataCards)
        {
            Console.WriteLine($"{card.PayTo} | {card.ExpenseAmount} | {card.ExpenseDate}");
            Console.WriteLine($"CR {card.AmountCr} | DR {card.AmountDr} | Balance {card.AmountBalance}");
            try
            {
                var _lastBalance = 0.0m;
                var _lastRecords = _dataCards
                  .Where(x => x.TypeFlag == card.TypeFlag)
                        .Where(x => x.CompanyID == card.CompanyID)
                        .Where(x => x.ID != card.ID)
                        .Where(x => x.Created < card.Created).ToList();

                if (_lastRecords.Any())
                {
                    foreach (var record in _lastRecords.OrderBy(x => x.Created))
                    {
                        _lastBalance = _lastBalance + record.AmountCr - record.AmountDr;
                    }
                    //_lastBalance = _lastRecords
                    //    .Sum(x => x.AmountCr)
                    //    - _lastRecords
                    //    .Sum(x => x.AmountDr);
                }

                var _newBalance = _lastBalance + card.AmountCr - card.AmountDr;
                //_balance = _balance  + card.AmountCr - card.AmountDr;
                //Console.WriteLine($"TotalBalance {_balance}");

                if (_newBalance != card.AmountBalance)
                {
                    card.AmountBalance = _newBalance;
                    await db.SaveChangesAsync();
                    Console.WriteLine($"CR {card.AmountCr} | DR {card.AmountDr} | Balance {card.AmountBalance}");
                    _success++;
                }

            }
            catch (Exception)
            {
                _failed++;
            }


            count++;
            Console.WriteLine(count + " of " + _dataCards.Count() + " processed");
            Console.WriteLine(_success + " of " + _dataCards.Count() + " updated");
            Console.WriteLine(_failed + " of " + _dataCards.Count() + " Failed");
            Console.WriteLine("========================");
        }
    }



    public async Task<byte[]> GetAnalysisExcel(IEnumerable<QueryFilter>? Filters = null, string? Search = null, string? Sort = null)
    {

        var _dataSet = new DataSet();

        var _data = await Get(Filters, Search, Sort)

            .ToListAsync();

        //var _IstData = new List<Expense>();
        //foreach (var item in _data)
        //{
        //    item.ExpenseDate = (item.ExpenseDate.AddMinutes(330)).Date;
        //    item.TransactionDate = item.TransactionDate != null ? (item.TransactionDate.Value.AddMinutes(330)).Date : (DateTime?)null;
        //  _IstData.Add(item);
        //}

        _dataSet.Tables.Add(DataTools.ToDataTable(_data.Select(item =>
            new
            {
                item.Code,
                ExpenseDate = (item.ExpenseDate.AddMinutes(330)).Date,
                item.Company,
                item.ExpenseHead,
                item.Particulars,
                item.Narration,
                item.PayTo,
                item.ExpenseAmount,
                item._taxes,
                item.AmountAfterTax,
                //item.AmountBalance,
                item.AmountCr,
                item.AmountDr,
                item.TDSPercentage,
                item.TDSAmount,
                item.GST,
                item.ApprovedBy,
                item.Description,
                item.InvoiceNumber,
                item.PAN,
                item.TAN,

                TransactionDate = item.TransactionDate != null ? (item.TransactionDate.Value.AddMinutes(330)).Date : (DateTime?)null,
                item.TransactionDetails,
                item.TransactionRefNo,
                item._searchTags,

            })));

        return ExcelUtility.ExportExcel(_dataSet);

    }


    public async Task<IEnumerable<int>> GetAssigneeContactIDs(int entityID, string stageCode, string propertyName)
    {
        var Entity = await Get()
            .Where(x => x.ID == entityID).SingleOrDefaultAsync();

        if (Entity == null) throw new EntityServiceException($"{nameof(Expense)} not found!");

        var _list = new List<int>();

        if (stageCode.Equals("EXPENSE_VOUCHER_UPDATE", StringComparison.OrdinalIgnoreCase))
        {
            var _type = DataTools.GetPropertyType(Entity, propertyName);
            if (_type == typeof(Int32))
            {
                var contactID = ((Int32)DataTools.GetPropertyValue(Entity, propertyName));
                if (contactID != null)
                {
                    _list.Add(contactID);
                }
            }
        }
        else
        {
            throw new EntityServiceException($"{nameof(Expense)} Task assignee not found for stage {stageCode}!");
        }

        return _list;
    }

    public async Task<dynamic> GetTaskByStage(string Entity, int EntityID, string StageCode, string StageTitle, decimal StageDuration, DateTime? FollowUpDate = null)
    {


        var sharedService = new SharedService(db); ;

        var _entity = await Get().SingleOrDefaultAsync(x => x.ID == EntityID);

        if (_entity == null) throw new EntityServiceException($"{nameof(Expense)} not found!");

        var _startTimeSpan = TimeSpan.Parse(await sharedService.GetPresetValue(McvConstant.OFFICE_OPEN_TIME));
        var _endTimeSpan = TimeSpan.Parse(await sharedService.GetPresetValue(McvConstant.OFFICE_CLOSE_TIME));

        var _nextDue = DateTime.UtcNow.AddDays(Decimal.ToDouble(StageDuration)).Date;

        if (FollowUpDate != null)
            _nextDue = FollowUpDate.Value.Date;

        return new
        {
            Title = StageTitle,
            Entity = Entity,
            EntityID = EntityID,
            Subtitle = _entity.Code + "-" + _entity.ExpenseHead,
            WFStageCode = StageCode,
            StartDate = ClockTools.GetUTC(ClockTools.GetISTNow().Date
              .AddMinutes(_startTimeSpan.TotalMinutes)),
            DueDate = ClockTools.GetUTC(ClockTools.GetIST(_nextDue).Date
              .AddMinutes(_endTimeSpan.TotalMinutes)),
            MHrAssigned = 0,
            IsPreAssignedTimeTask = false
        };
    }

    public async Task TaskAction(int EntityID, string StageCode, int WFTaskID, string taskComment = null)
    {
        var Entity = await Get()
                      .SingleOrDefaultAsync(x => x.ID == EntityID);

        if (Entity == null) throw new EntityServiceException($"{nameof(Expense)} not found!");

        if (StageCode == "SYS_EXPENSE_VOUCHER_APPROVE")
        {
            Entity.StatusFlag = 1;

            //if (Username != null)
            //{
            //    var contactSer
            //    if (currentContact != null)
            //    {
            //        Entity.ApprovedByContactID = currentContact.ID;
            //        Entity.ApprovedBy = currentContact.FullName;
            //    }
            //}
            //Entity.Description = taskComment;
            db.Entry(Entity).State = EntityState.Modified;
            await db.SaveChangesAsync();


        }
        else if (StageCode == "SYS_EXPENSE_VOUCHER_REJECT")
        {


            Entity.StatusFlag = -1;


            //if (currentContact != null)
            //{
            //    Entity.ApprovedByContactID = currentContact.ID;
            //    Entity.ApprovedBy = currentContact.FullName;
            //}
            //Entity.Description = taskComment;
            db.Entry(Entity).State = EntityState.Modified;
            await db.SaveChangesAsync();

        }

    }
}