﻿

using System.Data;




using MyCockpitView.WebApi.Exceptions;

using System.Text.RegularExpressions;
using System.Text;
using MyCockpitView.WebApi.ContactModule.Entities;
using MyCockpitView.WebApi.LeaveModule.Services;
using MyCockpitView.WebApi.Services;
using MyCockpitView.WebApi.PayrollModule.Entities;
using MyCockpitView.WebApi.ProjectModule.Services;
using MyCockpitView.WebApi.WFTaskModule.Services;
using MyCockpitView.CoreModule;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.Utility.Common;
using MyCockpitView.Utility.RDLCClient;
using MyCockpitView.Utility.Excel;
using MyCockpitView.WebApi.ContactModule.Services;

namespace MyCockpitView.WebApi.PayrollModule.Services;

public class PayrollService : BaseEntityService<Payroll>, IPayrollService
{
    public PayrollService(EntitiesContext db) : base(db) { }

    public IQueryable<Payroll> Get(IEnumerable<QueryFilter>? Filters = null, string? Search = null, string? Sort = null)
    {

        IQueryable<Payroll> _query = base.Get(Filters);

        //Apply filters
        if (Filters != null)
        {

            if (Filters.Where(x => x.Key.Equals("contactID", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<Payroll>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("contactID", StringComparison.OrdinalIgnoreCase)))
                {
                    var isNumeric = Convert.ToInt32(_item.Value);

                    predicate = predicate.Or(x => x.PersonContactID == isNumeric);
                }
                _query = _query.Where(predicate);
            }

            if (Filters.Where(x => x.Key.Equals("rangestart", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var _item = Filters.First(x => x.Key.Equals("rangestart", StringComparison.OrdinalIgnoreCase));

                DateTime result = Convert.ToDateTime(_item.Value);
                _query = _query.Where(x => x.StartDate >= result);
            }

            if (Filters.Where(x => x.Key.Equals("rangeend", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var _item = Filters.First(x => x.Key.Equals("rangeend", StringComparison.OrdinalIgnoreCase));

                DateTime result = Convert.ToDateTime(_item.Value);
                var end = result.AddDays(1);
                _query = _query.Where(x => x.StartDate < end);

            }

            if (Filters.Where(x => x.Key.Equals("companyID", StringComparison.OrdinalIgnoreCase)).Any())
            {
                var predicate = PredicateBuilder.False<Payroll>();
                foreach (var _item in Filters.Where(x => x.Key.Equals("companyID", StringComparison.OrdinalIgnoreCase)))
                {
                    var isNumeric = Convert.ToInt32(_item.Value);

                    predicate = predicate.Or(x => x.CompanyID == isNumeric);
                }
                _query = _query.Where(predicate);
            }
        }

        if (Search != null && Search != String.Empty)
        {
            Search = Search.ToLower();
            _query = _query
                 .Where(x => x.PersonName.ToLower().Contains(Search.ToLower())
                                           );

        }

        if (Sort != null && Sort != String.Empty)
        {
            switch (Sort.ToLower())
            {
                case "createddate":
                    return _query
                            .OrderBy(x => x.Created);

                case "modifieddate":
                    return _query
                            .OrderBy(x => x.Modified);

                case "createddate desc":
                    return _query
                            .OrderByDescending(x => x.Created);

                case "modifieddate desc":
                    return _query
                            .OrderByDescending(x => x.Modified);

                case "startdate":
                    return _query
                            .OrderBy(x => x.StartDate);

                case "startdate desc":
                    return _query
                            .OrderByDescending(x => x.StartDate);
            }
        }

        return _query.OrderByDescending(x => x.StartDate).ThenBy(x => x.PersonName);

    }

    public async Task<int> Create(Payroll Entity)
    {

        var _exist = await Get().AnyAsync(x => x.PersonContactID == Entity.PersonContactID && x.StartDate == Entity.StartDate
        && x.AppointmentID == Entity.AppointmentID && x.CompanyID == Entity.CompanyID);

        if (_exist) throw new EntityServiceException("Payroll record already exists!");

        await base.Create(Entity);


        return Entity.ID;

    }

    public async Task Update(Payroll Entity)
    {

        var _contact = await db.Contacts.AsNoTracking()
        
      .SingleOrDefaultAsync(x => x.ID == Entity.PersonContactID);

        if (_contact == null) throw new EntityServiceException("Contact Not found!");


        var appointment = await db.ContactAppointments.AsNoTracking()
            .FirstOrDefaultAsync(x => x.ID == Entity.AppointmentID);

        if (appointment == null) throw new EntityServiceException("Appointment Not found!");

        Entity = await CalculateRemuneration(Entity, _contact, appointment);

        await base.Update(Entity);



    }

   
    public async Task<int> GenerateByContact(int contactID, int year, int month)
    {

        var _contact = await db.Contacts.AsNoTracking()
     
    .SingleOrDefaultAsync(x => x.ID == contactID);

        if (_contact == null) throw new EntityServiceException("Contact Not found!");

        //Console.WriteLine($"Contact {_contact.ID} | {_contact.Name}");

        var _startDateIST = new DateTime(year, month, 1);
        var _endDateIST = _startDateIST.AddMonths(1).AddDays(-1);

        var startUTC = ClockTools.GetUTC(_startDateIST);
        var endUTC = ClockTools.GetUTC(_endDateIST);

        //Console.WriteLine($"Month {_startDate.ToString("MMM yy")}");

        var _appointments = await db.ContactAppointments.AsNoTracking()
             
                           .Where(x => x.StatusFlag == McvConstant.APPOINTMENT_STATUSFLAG_APPOINTED ||
                            (x.StatusFlag == McvConstant.APPOINTMENT_STATUSFLAG_RESIGNED && x.ResignationDate != null && x.ResignationDate > startUTC))

    .Include(x => x.Company)
    .Where(x => x.ContactID == contactID).ToListAsync();

        var _appointmentTypes = await db.TypeMasters.AsNoTracking().Where(x => x.Entity == nameof(ContactAppointment)).ToListAsync();

        var taskService = new WFTaskService(db);

        var _vhrData = await taskService.GetVHrAnalysisData(new List<QueryFilter> {
            new QueryFilter { Key = "rangestart", Value = startUTC.ToString() },
            new QueryFilter { Key = "rangeend", Value = endUTC.ToString() },
            new QueryFilter { Key="contactid" ,Value=contactID.ToString()}
        });

        // Console.WriteLine($"vhrData {JsonConvert.SerializeObject(_vhrData, Formatting.Indented, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore })}");
        var sharedService = new SharedService(db);
        var _leavesPermissible = Convert.ToDecimal(await sharedService.GetPresetValue(McvConstant.LEAVE_PERMISSIBLE_TOTAL));
        var _emergencyPermissible = Convert.ToDecimal(await sharedService.GetPresetValue(McvConstant.LEAVE_PERMISSIBLE_EMERGENCY));
        var _expectedMHr = Convert.ToInt32(await sharedService.GetPresetValue(McvConstant.TEAM_MONTHLY_EXPECTED_MHR));
        var _vhrRate = Convert.ToInt32(await sharedService.GetPresetValue(McvConstant.COMPANY_VHR_COST));

        var _firstAppointment = await db.ContactAppointments.AsNoTracking()
            .Where(x => x.ContactID == contactID)
            .OrderBy(x => x.JoiningDate)
            .FirstOrDefaultAsync();
        var _joiningDate = ClockTools.GetIST(_firstAppointment.JoiningDate);
        var _joiningMonth = new DateTime(_joiningDate.Year, _joiningDate.Month, 1);

        var _start = new DateTime(year, _joiningDate.Month, 1);

        var _end = _start.AddYears(1);
        var _currentMonth = new DateTime(year, month, 1);

        if (_currentMonth < _start) //cycle start from previous year
        {
            _start = _start.AddYears(-1);
            _end = _start.AddYears(1);
        }

        if (_start.Year < 2023)
        { //old cycle
            _start = new DateTime(2023, 1, 1);
            _end = (new DateTime(2023, _joiningDate.Month, 1));//.AddDays(-1);sss
        }
        var _leaveService = new LeaveService(db);
        var _leaveSummary = new List<LeaveSummary>();
        var allowedLeaves = _leavesPermissible;
        var allowedEmergency = _emergencyPermissible;
        if (_start.Year < 2023)
        { //old cycle
            _start = new DateTime(2023, 1, 1);
            _end = (new DateTime(2023, _joiningDate.Month, 1));
            allowedLeaves = (_end.Month - 1) * (_leavesPermissible / 12);
            allowedEmergency = (_end.Month - 1) * (_emergencyPermissible / 12);
        }

        while (_start <= _startDateIST)
        {
            var _summary = await _leaveService.GetMonthSummary(contactID, _start.Year, _start.Month);

            _leaveSummary.Add(_summary);

            _start = _start.AddMonths(1);
        }

        foreach (var appointment in _appointments)
        {
            //Console.WriteLine($"Appointment | {appointment.Code} | {appointment.Designation}");

            //REMOVE EXISTING RECORD
            var _existing = await db.Payrolls.Where(x => x.PersonContactID == _contact.ID
            && x.StartDate == startUTC && x.AppointmentID == appointment.ID).FirstOrDefaultAsync();

            if (_existing != null)
            {
                db.Payrolls.Remove(_existing);
                await db.SaveChangesAsync();
            }

            var Entity = new Payroll
            {
                PersonContactID = _contact.ID,
                PersonName = _contact.Name,
                CompanyBankAccountNo = "",
                CompanyName = appointment.Company.Title,
                CompanyID = appointment.CompanyID,
                AppointmentID = appointment.ID,
                AppointmentType = _appointmentTypes.FirstOrDefault(a => a.Value == appointment.TypeFlag).Title,
                Designation = appointment.Designation,
                StartDate = startUTC,
                EndDate = endUTC,
                VHrRate = _vhrRate,
                ManValue = appointment.ManValue,
                VHrAssessed = _vhrData.Any() ? _vhrData.Sum(x => x.VHr) : 0,
                VHrAssessedAmount = _vhrData.Any() ? _vhrData.Sum(x => x.Remuneration) : 0,
                VHrExpected = _vhrData.Any() ? _vhrData.Sum(x => x.ExpectedVHr) : _expectedMHr * appointment.ManValue,
                VHrExpectedAmount = _vhrData.Any() ? _vhrData.Sum(x => x.ExpectedRemuneration) : _expectedMHr * appointment.ManValue * _vhrRate,
                LeaveCycle = _leaveSummary.FirstOrDefault().LeaveCycle,
                LeaveAllowed = allowedLeaves,
                LeaveAllowedEmergency = allowedEmergency,
                LeaveApprovedBreak = _leaveSummary.Sum(l => l.ApprovedBreak),
                LeaveApprovedHalfDay = _leaveSummary.Sum(l => l.ApprovedHalfDay),
                LeaveApprovedLeave = _leaveSummary.Sum(l => l.ApprovedLeave),
                LeaveApprovedWFH = _leaveSummary.Sum(l => l.ApprovedWFH),
                LeaveBalance = allowedLeaves - _leaveSummary.Sum(l => l.Total),
                LeaveEmergencyBalance = allowedEmergency - _leaveSummary.Sum(l => l.EmergencyLeave + l.EmergencyHalfDay),
                LeaveEmergencyBreak = _leaveSummary.Sum(l => l.EmergencyBreak),
                LeaveEmergencyHalfDay = _leaveSummary.Sum(l => l.EmergencyHalfDay),
                LeaveEmergencyLeave = _leaveSummary.Sum(l => l.EmergencyLeave),
                LeaveEmergencyWFH = _leaveSummary.Sum(l => l.EmergencyWFH),
                LeaveLate = _leaveSummary.Sum(l => l.Late),
                LeavePenalty = _leaveSummary.Sum(l => l.Penalty),
                LeaveTotal = _leaveSummary.Sum(l => l.Total),

            };


            //VOUCHERS
            var _expenses = await db.Expenses.AsNoTracking()
                                    
                .Where(x => x.StatusFlag == McvConstant.EXPENSE_STATUS_FLAG_APPROVED)
                .Where(x => x.TypeFlag == McvConstant.EXPENSE_TYPEFLAG_GENERAL)
                //.Where(x => x.ExpenseHead.Equals("Travel", StringComparison.OrdinalIgnoreCase))
                .Where(x => x.PayToContactID == contactID)
                .Where(x => x.CompanyID == appointment.CompanyID)
                .Where(x => x.Created >= Entity.StartDate && x.Created < Entity.EndDate)

                .ToListAsync();


            Entity.PostTaxAdditions = _expenses.GroupBy(x => x.ExpenseHead)
                .Select(x => new PayrollFragment
                {
                    Title = x.Key,
                    Amount = x.Sum(e => e.ExpenseAmount),
                })
                .ToList();

            Entity.Expenses = _expenses
                .Select(x => new PayrollFragment
                {
                    Title = x.ExpenseHead,
                    Remark = x.Narration,
                    Amount = x.ExpenseAmount,
                    ExpenseID = x.ID,
                })
                .ToList();

            //LEAVES
            if (Entity.LeaveTotal <= Entity.LeaveAllowed)
            {
                //within total allowed

            }
            else
            {
                //exceeded total allowed 27
                if (Entity.Deductions == null)
                    Entity.Deductions = new List<PayrollFragment>();
                else
                    Entity.Deductions = Entity.Deductions.Where(x => x.Title != "Leave Deductions").ToList();

                Entity.Deductions.Add(new PayrollFragment
                {
                    Title = "Leave Deductions",
                    Amount = (Entity.LeaveAllowed - Entity.LeaveTotal) * _expectedMHr / 8 * Entity.ManValue * Entity.VHrRate,
                    Remark = $"Exceed Leaves:{(Entity.LeaveAllowed - Entity.LeaveTotal)}"
                });
            }

            Entity = await CalculateRemuneration(Entity, _contact, appointment);


            return await Create(Entity);

        }

        return -1;
    }

    private async Task<Payroll> CalculateRemuneration(
        Payroll Entity,
        Contact _contact,
        ContactAppointment appointment)
    {
        var _payroleTypes = await db.TypeMasters.AsNoTracking().Where(x => x.Entity == nameof(Payroll)).ToListAsync();

        var _projectService = new ProjectService(db);


        if (appointment.TypeFlag == McvConstant.APPOINTMENT_TYPEFLAG_ASSOCIATE
            || appointment.TypeFlag == McvConstant.APPOINTMENT_TYPEFLAG_DESIGN_ASSOCIATE
            || appointment.TypeFlag == McvConstant.APPOINTMENT_TYPEFLAG_GIG
            || appointment.TypeFlag == McvConstant.APPOINTMENT_TYPEFLAG_PARTNER)
        {
            //Tax applicable as per appointment type
            Entity.TypeFlag = McvConstant.PAYROLL_TYPEFLAG_TDS;
        }
        else
        {
            Entity.TypeFlag = McvConstant.PAYROLL_TYPEFLAG_PTAX;
        }

        //Vhr exceeded beyond expected
        Entity.VHrExceeded = Entity.VHrAssessed > Entity.VHrExpected ? Entity.VHrAssessed - Entity.VHrExpected : 0;
        Entity.VHrExceededAmount = Entity.VHrAssessedAmount > Entity.VHrExpectedAmount ? Entity.VHrAssessedAmount - Entity.VHrExpectedAmount : 0;



        if (appointment.TypeFlag == McvConstant.APPOINTMENT_TYPEFLAG_PARTNER)
        {
            //partner's remuneration from lastbite
            var _lastBite = await _projectService.GetLastBiteData(new List<QueryFilter> {
                            new QueryFilter { Key = "rangestart", Value = Entity.StartDate.ToString() },
                            new QueryFilter { Key = "rangeend", Value = Entity.EndDate.ToString() },
                            new QueryFilter { Key = "contactid", Value = Entity.PersonContactID.ToString() }
                });
            Entity.LastBite = _lastBite.Sum(x => x.LastReceivedPaymentAmount) > 0 ? _lastBite.Sum(x => x.LastReceivedPaymentAmount) : 0;
            Entity.RemunerationAmount = Entity.LastBite + Entity.VHrAssessedAmount;
        }
        else if (appointment.TypeFlag == McvConstant.APPOINTMENT_TYPEFLAG_DESIGN_ASSOCIATE
            || appointment.TypeFlag == McvConstant.APPOINTMENT_TYPEFLAG_GIG
            || appointment.TypeFlag == McvConstant.APPOINTMENT_TYPEFLAG_CANDIDATE)
        {
            //variable renumeration
            Entity.RemunerationAmount = Entity.VHrAssessedAmount - Entity.VHrExceededAmount;
            Entity.BasicPayAmount = appointment.BasicPayPercentage / 100.0m * Entity.RemunerationAmount;
            Entity.HRAAmount = appointment.HRAPercentage / 100.0m * Entity.RemunerationAmount;
            Entity.SpecialAllowanceAmount = appointment.SpecialAllowancePercentage / 100.0m * Entity.RemunerationAmount;
            if (Entity.VHrExceeded > 0)
            {
                Entity.PreTaxAdditions = new List<PayrollFragment> { new PayrollFragment { Title = "Extra VHr", Amount = Entity.VHrExceededAmount, Remark = $"Vhr Exceeded:{Entity.VHrExceeded}" } };
            }

        }
        else if (appointment.TypeFlag == McvConstant.APPOINTMENT_TYPEFLAG_EMPLOYEE || appointment.TypeFlag == McvConstant.APPOINTMENT_TYPEFLAG_ASSOCIATE)
        {
            //Fixed remuneration
            Entity.RemunerationAmount = Entity.VHrExpectedAmount;
            Entity.BasicPayAmount = appointment.BasicPayPercentage / 100.0m * Entity.RemunerationAmount;
            Entity.HRAAmount = appointment.HRAPercentage / 100.0m * Entity.RemunerationAmount;
            Entity.SpecialAllowanceAmount = appointment.SpecialAllowancePercentage / 100.0m * Entity.RemunerationAmount;
            if (Entity.VHrExceeded > 0)
            {
                Entity.PreTaxAdditions = new List<PayrollFragment> { new PayrollFragment { Title = "Extra VHr", Amount = Entity.VHrExceededAmount, Remark = $"Vhr Exceeded:{Entity.VHrExceeded}" } };
            }
        }


        Entity.PreTaxAdditionAmount = Entity.PreTaxAdditions.Any() ? Entity.PreTaxAdditions.Sum(c => c.Amount) : 0;


        Entity.GrossPayAmount = Entity.RemunerationAmount + Entity.PreTaxAdditionAmount;

        var _taxes = new List<PayrollFragment>();
        var sharedService = new SharedService(db); ;
        var PTaxMaleLimit1 = Convert.ToDecimal(await sharedService.GetPresetValue(McvConstant.PROFESSIONAL_TAX_MALE_LIMIT_1));
        var PTaxMaleLimit2 = Convert.ToDecimal(await sharedService.GetPresetValue(McvConstant.PROFESSIONAL_TAX_MALE_LIMIT_2));
        var PTaxMaleLimitAmount1 = Convert.ToDecimal(await sharedService.GetPresetValue(McvConstant.PROFESSIONAL_TAX_MALE_LIMIT_1_AMOUNT));
        var PTaxMaleLimitAmount2 = Convert.ToDecimal(await sharedService.GetPresetValue(McvConstant.PROFESSIONAL_TAX_MALE_LIMIT_2_AMOUNT));
        var PTaxMaleLimitAmount2Feb = Convert.ToDecimal(await sharedService.GetPresetValue(McvConstant.PROFESSIONAL_TAX_MALE_LIMIT_2_AMOUNT_FEB));
        var PTaxFemaleLimit1 = Convert.ToDecimal(await sharedService.GetPresetValue(McvConstant.PROFESSIONAL_TAX_FEMALE_LIMIT_1));
        var PTaxFemaleLimitAmount1 = Convert.ToDecimal(await sharedService.GetPresetValue(McvConstant.PROFESSIONAL_TAX_FEMALE_LIMIT_1_AMOUNT));
        var PTaxFemaleLimitAmount1Feb = Convert.ToDecimal(await sharedService.GetPresetValue(McvConstant.PROFESSIONAL_TAX_FEMALE_LIMIT_1_AMOUNT_FEB));

        if (Entity.TypeFlag == McvConstant.PAYROLL_TYPEFLAG_PTAX)
        {
            //professional tax
            if (_contact.Gender.Equals("male", StringComparison.OrdinalIgnoreCase))
            {
                //if male

                if (Entity.GrossPayAmount <= PTaxMaleLimit1)
                {
                    //below 7500
                    //NO TAX

                }
                else if (Entity.GrossPayAmount > PTaxMaleLimit1 && Entity.GrossPayAmount <= PTaxMaleLimit2)//within limit 7500-10000
                {
                    _taxes = new List<PayrollFragment>() {
                            new PayrollFragment {
                                Title = _payroleTypes.FirstOrDefault(p => p.Value == Entity.TypeFlag).Title,
                                Amount = PTaxMaleLimitAmount1 //175
                            }
                        };
                }
                else //beyond limit 10000
                {
                    _taxes = new List<PayrollFragment>() {
                            new PayrollFragment {
                                Title = _payroleTypes.FirstOrDefault(p => p.Value == Entity.TypeFlag).Title,
                                Amount = Entity.StartDate.Month==2?PTaxMaleLimitAmount2Feb: PTaxMaleLimitAmount2 //200
                            }
                        };
                }
            }
            else
            {
                //if female
                if (Entity.GrossPayAmount <= PTaxFemaleLimit1)
                {// below 10000
                 //NO TAX
                }
                else //beyond limit 10000
                {
                    _taxes = new List<PayrollFragment>() {
                            new PayrollFragment {
                                Title = _payroleTypes.FirstOrDefault(p => p.Value == McvConstant.PAYROLL_TYPEFLAG_PTAX).Title,
                                Amount =  Entity.StartDate.Month==2?PTaxFemaleLimitAmount1Feb:PTaxFemaleLimitAmount1 //200
                            }
                        };
                }
            }
        }
        else
        {
            var _tdsPercentage = Convert.ToDecimal((await db.AppSettingMasters.AsNoTracking().SingleOrDefaultAsync(x => x.PresetKey == McvConstant.TAX_TDS)).PresetValue);
            _taxes = new List<PayrollFragment>() { new PayrollFragment { Title = _payroleTypes.FirstOrDefault(p => p.Value == Entity.TypeFlag).Title,
                    Amount = (Entity.GrossPayAmount*_tdsPercentage/100.0M)  } };
        }

        Entity.Taxes = Entity.Taxes.Where(x => !_taxes.Any(t => t.Title == x.Title)).ToList();
        Entity.Taxes = Entity.Taxes.Concat(_taxes).ToList();

        Entity.TaxAmount = Entity.Taxes.Any() ? Entity.Taxes.Sum(c => c.Amount) : 0;

        Entity.NetRemuneration = Entity.GrossPayAmount - Entity.TaxAmount;

        //ADDITIONS
        Entity.PostTaxAdditionAmount = Entity.PostTaxAdditions.Any() ? Entity.PostTaxAdditions.Sum(c => c.Amount) : 0;

        //LOANS
        Entity.Deductions = Entity.Deductions.Where(x => x.Title != "Loan Installment").ToList();

        var _loans = await db.Loans
           .Where(x => x.StatusFlag == McvConstant.LOAN_STATUS_FLAG_DUE)
           .Where(x => x.PersonContactID == Entity.PersonContactID)
           .Where(x => x.CompanyID == appointment.CompanyID)
           .ToListAsync();

        if (_loans.Any())
        {

            foreach (var loan in _loans)
            {
                var loanPaid = loan.Installments.Any() ? loan.Installments.Sum(s => s.Amount) : 0;

                if (loanPaid < loan.LoanAmount)
                {

                    loan.Installments = loan.Installments.Where(x => x.PayrollDate != Entity.StartDate).ToList();


                    Entity.Deductions = Entity.Deductions.Concat(new List<PayrollFragment>{ new PayrollFragment
                {
                    Title = "Loan Installment",
                    Amount = loan.InstallmentAmount,
                    Remark = $"Amount Due: {(loan.LoanAmount - loanPaid - loan.InstallmentAmount).FormatAsCurrency()}",
                } }).ToList();


                    loan.Installments = loan.Installments.Concat(new List<LoanInstallment>{new LoanInstallment
                {
                    Amount = loan.InstallmentAmount,
                    PaymentDate = DateTime.Now,
                    Remark = "Deducted from payroll",
                    PayrollDate = Entity.StartDate,
                } }).ToList();
                }
                else
                {
                    loan.StatusFlag = McvConstant.LOAN_STATUS_FLAG_PAID;
                }

            }

        }

        //DEDUCTIONS
        Entity.DeductionAmount = Entity.Deductions.Any() ? Entity.Deductions.Sum(c => c.Amount) : 0;

        //NET PAY
        Entity.NetPayAmount = Entity.NetRemuneration + Entity.PostTaxAdditionAmount - Entity.DeductionAmount;


        return Entity;
    }

    public async Task<ReportDefinition> GetMonthSlipPDF(Guid id,
      string reportSize = "a4",
      IEnumerable<QueryFilter> filters = null)
    {

        var payroll = await db.Payrolls.AsNoTracking()
           .Where(x => x.UID == id)
           .SingleOrDefaultAsync();

        if (payroll == null) throw new EntityServiceException($"{nameof(Payroll)} not found!");

        var showLogo = false;
        if (filters != null)
        {
            if (filters.Where(x => x.Key.Equals("showLogo", StringComparison.OrdinalIgnoreCase)).Any())
            {
                showLogo = filters.Where(x => x.Key.Equals("showLogo", StringComparison.OrdinalIgnoreCase)).Select(x => Convert.ToBoolean(x.Value.Trim())).FirstOrDefault();
            }
        }

        var _reportProperties = new List<ReportProperties>
            {
                new ReportProperties() { PropertyName = "Title", PropertyValue = $"{ClockTools.GetIST(payroll.StartDate).ToString("MMM yyyy")} | {payroll.PersonName}" },
                  new ReportProperties() { PropertyName = "ShowLogo", PropertyValue = $"{showLogo}" },

            };
        var sharedService = new SharedService(db); ;
        var _reportContainerUrl = await sharedService.GetPresetValue(McvConstant.RDLC_REPORT_CONTAINER_URL);
        var DEVMODE = Convert.ToBoolean((await sharedService.GetPresetValue(McvConstant.DEVMODE)));

        var _reportPath = $"Payroll-Month-{reportSize}.rdlc";

        var _reportDef = new ReportDefinition()
        {
            ReportName = "Payroll-Month",
            ReportPath = DEVMODE ? $"{_reportContainerUrl}DEV/{_reportPath}" : $"{_reportContainerUrl}{_reportPath}",
            ReportDataSet = DataTools.ToDataTable<Payroll>(new List<Payroll> { payroll }),
            ReportProperties = _reportProperties,
            Filename = $"Payroll-Month {ClockTools.GetIST(payroll.StartDate).ToString("MMM yyyy")} | {payroll.PersonName}",
            RenderType = "PDF"
        };

        var _fragments = payroll.PreTaxAdditions.Select(x => new { Key = "PreTaxAdditions", x.Title, x.Remark, x.Amount }).ToList();

        _fragments = _fragments.Concat(payroll.Taxes.Select(x => new { Key = "Tax", x.Title, x.Remark, x.Amount })).ToList();
        _fragments = _fragments.Concat(payroll.PostTaxAdditions.Select(x => new { Key = "Additions", x.Title, x.Remark, x.Amount })).ToList();
        _fragments = _fragments.Concat(payroll.Deductions.Select(x => new { Key = "Deductions", x.Title, x.Remark, x.Amount })).ToList();

        _reportDef.SubReports.Add(new ReportDefinition()
        {
            ReportName = "PayrollFragments",
            ReportPath = DEVMODE ? $"{_reportContainerUrl}DEV/PayrollFragments.rdlc" : $"{_reportContainerUrl}PayrollFragments.rdlc",
            ReportDataSet = DataTools.ToDataTable(_fragments),
        });
        var reportServiceApi = await sharedService.GetPresetValue(McvConstant.RDLC_PROCESSOR_API);
        return await ReportClient.GenerateReport(_reportDef, reportServiceApi);

    }

    public async Task<byte[]> GetAnalysisExcel(IEnumerable<QueryFilter>? Filters = null, string? Search = null, string? Sort = null)
    {

        var _dataSet = new DataSet();

        var _data = await Get(Filters, Search, Sort).ToListAsync();
        //var _payroleTypes = await _db.TypeMasters.AsNoTracking().Where(x => x.Entity == nameof(Payroll)).ToListAsync();
        //var pTaxTitle= _payroleTypes.FirstOrDefault(p => p.Value == McvConstant.PAYROLL_TYPEFLAG_PTAX).Title;

        //var TDSTitle = _payroleTypes.FirstOrDefault(p => p.Value == McvConstant.PAYROLL_TYPEFLAG_TDS).Title;

        _dataSet.Tables.Add(DataTools.ToDataTable(_data.Select(x => new
        {
            Month = ClockTools.GetIST(x.StartDate).ToString("MMM yyyy"),
            x.PersonName,
            x.PersonBankAccountNo,
            x.AppointmentType,
            x.Designation,
            x.CompanyName,
            x.CompanyBankAccountNo,

            x.ManValue,
            x.VHrRate,
            x.VHrAssessed,
            x.VHrExpected,
            x.VHrAssessedAmount,
            x.VHrExpectedAmount,
            x.VHrExceeded,
            x.VHrExceededAmount,
            x.RemunerationAmount,
            x.BasicPayAmount,
            x.HRAAmount,
            x.SpecialAllowanceAmount,
            x.PreTaxAdditionAmount,
            x.GrossPayAmount,
            TDS = x.TypeFlag == McvConstant.PAYROLL_TYPEFLAG_TDS ? x.TaxAmount : 0,
            PTAX = x.TypeFlag == McvConstant.PAYROLL_TYPEFLAG_PTAX ? x.TaxAmount : 0,
            x.NetRemuneration,
            x.PostTaxAdditionAmount,
            x.DeductionAmount,
            x.NetPayAmount,

        })));

        return ExcelUtility.ExportExcel(_dataSet);
    }


    public async Task SendPaySlip(int ID)
    {

        var payroll = await db.Payrolls
                
                        .AsNoTracking()
            .SingleOrDefaultAsync(x => x.ID == ID);

        if (payroll == null) throw new EntityServiceException($"{nameof(payroll)} not Found!");

        var sharedService = new SharedService(db); ;
        var _emailSenderName = await sharedService.GetPresetValue(McvConstant.PAY_SLIP_EMAIL_SENDER_NAME);
        var _emailSenderAddress = await sharedService.GetPresetValue(McvConstant.PAY_SLIP_EMAIL_SENDER_ID);
        var _defaultCCList = await sharedService.GetPresetValue(McvConstant.PAY_SLIP_EMAIL_CC);
        var _replyTo = await sharedService.GetPresetValue(McvConstant.PAY_SLIP_EMAIL_REPLY);


        var _emailSubject = $"Pay Slip | {ClockTools.GetIST(payroll.StartDate).ToString("MMM yyyy")} | {payroll.PersonName}";


        var contact = await db.Contacts.AsNoTracking()
            .SingleOrDefaultAsync(x => x.ID == payroll.PersonContactID);


        var _toAddresses = new List<EmailContact>();

        _toAddresses.Add(new EmailContact
        {
            Name = contact.Name,
            Email = contact.Email1,
        });


        var _ccAddresses = new List<EmailContact>();

        //default CC
        if (_defaultCCList != null)
        {
            Regex myRegex = new Regex(McvConstant.EMAIL_REGEX, RegexOptions.None);
            foreach (Match myMatch in myRegex.Matches(_defaultCCList))
            {
                if (!_toAddresses.Any(a => a.Equals(myMatch.Value.Trim())) && !_ccAddresses.Any(a => a.Equals(myMatch.Value.Trim())))
                    _ccAddresses.Add(new EmailContact { Name = "", Email = myMatch.Value.Trim() });

            }
        }

        var _replyToEmail = _replyTo != null ? _replyTo : "<EMAIL>";

        var _replyParameter = $"mailto:{_replyToEmail}?cc=";

        foreach (var email in _toAddresses)
        {
            if (!email.Equals(_replyToEmail))
                _replyParameter = _replyParameter + email + ";";
        }
        foreach (var email in _ccAddresses)
        {
            if (!email.Equals(_replyToEmail))
                _replyParameter = _replyParameter + email + ";";
        }

        _replyParameter = _replyParameter + $"&subject=RE:{_emailSubject.ToUpper()}";
        var rootApi = await sharedService.GetPresetValue(McvConstant.ROOT_API);
        var downloadUrl = $"{rootApi}/Payroll/report/month/a4/{payroll.UID}";
        var emailMessage = $"Dear {payroll.PersonName}\r\n\r\nI hope this message finds you well. We are writing to inform you of the recent disbursement of your remuneration for the month of {payroll.StartDate.ToString("MMM yyyy")}. Find the link to a document that serves as an intimation of your payment from {payroll.CompanyName}.\r\n\r\nPlease note that this document is intended for your records and is not to be considered an official pay slip. It is provided as an initial notification of your remuneration disbursement.\r\n\r\nFor any official financial records or any queries related to your remuneration, we kindly request you to HR Manager.\r\n\r\nIf you have any questions or require further assistance regarding your payment, please do not hesitate to reach out to our HR department.\r\n\r\nThank you for your understanding and cooperation. We appreciate your dedication and hard work, and we are committed to ensuring the accuracy and timeliness of all your payroll documents.\r\n\r\nNamaste,";


        var sb = new StringBuilder();

        sb.AppendLine("<!DOCTYPE html>");
        sb.AppendLine("<html>");
        sb.AppendLine("<head>");
        sb.AppendLine("    <meta charset=\"UTF-8\" />");
        sb.AppendLine("    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\" />");
        sb.AppendLine("    <title>Email Design</title>");
        sb.AppendLine("    <style>");
        sb.AppendLine("        /* Reset styles for better compatibility */");
        sb.AppendLine("        body {");
        sb.AppendLine("            margin: 0;");
        sb.AppendLine("            padding: 0;");
        sb.AppendLine("            font-family: Calibri, sans-serif;");
        sb.AppendLine("            font-size: 14px;");
        sb.AppendLine("            line-height: 1.6;");
        sb.AppendLine("            max-width: 500px;");
        sb.AppendLine("            margin: 0 auto;");
        sb.AppendLine("            background-color: #f7f7f7;");
        sb.AppendLine("        }");
        sb.AppendLine("");
        sb.AppendLine("        table {");
        sb.AppendLine("            border-collapse: collapse;");
        sb.AppendLine("            width: 100%;");
        sb.AppendLine("        }");
        sb.AppendLine("");
        sb.AppendLine("            table td {");
        sb.AppendLine("                border-collapse: collapse;");
        sb.AppendLine("                padding: 10px;");
        sb.AppendLine("            }");
        sb.AppendLine("");
        sb.AppendLine("        .header {");
        sb.AppendLine("            background-color: #003558;");
        sb.AppendLine("            color: #fff;");
        sb.AppendLine("            font-weight: bold;");
        sb.AppendLine("            font-size: 18px;");
        sb.AppendLine("            padding: 5px 10px;");
        sb.AppendLine("            text-align: center;");
        sb.AppendLine("            border-radius: 4px;");
        sb.AppendLine("        }");
        sb.AppendLine("");
        sb.AppendLine("        .message {");
        sb.AppendLine("            padding: 15px;");
        sb.AppendLine("            border-radius: 5px;");
        sb.AppendLine("            text-align: justify;");
        sb.AppendLine("        }");
        sb.AppendLine("");
        sb.AppendLine("        .download-button {");
        sb.AppendLine("            background-color: #3498db;");
        sb.AppendLine("            color: #fff;");
        sb.AppendLine("            text-decoration: none;");
        sb.AppendLine("            padding: 10px 20px;");
        sb.AppendLine("            border-radius: 5px;");
        sb.AppendLine("        }");
        sb.AppendLine("");
        sb.AppendLine("            .download-button:hover {");
        sb.AppendLine("                background-color: #1e6bb8;");
        sb.AppendLine("            }");
        sb.AppendLine("");
        sb.AppendLine("        .actions {");
        sb.AppendLine("            padding: 15px;");
        sb.AppendLine("            border-radius: 5px;");
        sb.AppendLine("            text-align: center");
        sb.AppendLine("        }");
        sb.AppendLine("");
        sb.AppendLine("        .signature {");
        sb.AppendLine("            text-align: center;");
        sb.AppendLine("            margin-top: 15px;");
        sb.AppendLine("            font-size: 11px;");
        sb.AppendLine("            color: #888;");
        sb.AppendLine("        }");
        sb.AppendLine("    </style>");
        sb.AppendLine("</head>");

        //BODY
        sb.AppendLine("<body>");
        sb.AppendLine("    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\">");
        sb.AppendLine("        <tr>");
        sb.AppendLine("            <td class=\"header\">");
        sb.AppendLine(_emailSubject);
        sb.AppendLine("            </td>");
        sb.AppendLine("        </tr>");
        sb.AppendLine("    </table>");
        sb.AppendLine("    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\">");
        sb.AppendLine("        <tr>");
        sb.AppendLine("            <td class=\"message\">");
        sb.AppendLine("                    <pre style=\"font-family:Calibri;line-height:1.5; font-size: 14px;margin-top:0;margin-bottom:0; white-space: pre-wrap; white-space: -moz-pre-wrap;");
        sb.AppendLine("                                white-space: -pre-wrap; white-space: -o-pre-wrap; word-wrap: break-word;\">");
        sb.AppendLine(emailMessage);
        sb.AppendLine("                </pre>");
        sb.AppendLine("            </td>");
        sb.AppendLine("        </tr>");
        sb.AppendLine("    </table>");
        sb.AppendLine("    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\">");
        sb.AppendLine("        <tr>");
        sb.AppendLine("            <td class=\"actions\">");
        sb.AppendLine("                <a class=\"download-button\" href=\"" + downloadUrl + "\">Click here to download</a>");
        sb.AppendLine("            </td>");
        sb.AppendLine("        </tr>");
        sb.AppendLine("    </table>");



        //FOOTER
        sb.AppendLine("    <table align=\"center\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\">");
        sb.AppendLine("        <tr>");
        sb.AppendLine("            <td class=\"signature\">");
        sb.AppendLine("                This is a <b>MyCockpitView&copy;</b> & <b>DesignScript&copy;</b> generated email for your information and necessary action.");
        sb.AppendLine("            </td>");
        sb.AppendLine("        </tr>");
        sb.AppendLine("        <tr>");
        sb.AppendLine("            <td class=\"signature\">");
        sb.AppendLine("                Powered by <b>Newarch&reg; Infotech LLP</b>");
        sb.AppendLine("            </td>");
        sb.AppendLine("        </tr>");
        sb.AppendLine("    </table>");

        //end
        sb.AppendLine("</body>");
        sb.AppendLine("</html>");



        var emailTo = new List<(string name, string email)>();
        foreach (var obj in _toAddresses)
            emailTo.Add((obj.Name, obj.Email));

        var emailCC = new List<(string name, string email)>();
        foreach (var obj in _ccAddresses)
            emailCC.Add((obj.Name, obj.Email));


        await sharedService.SendMail(
                _emailSubject.ToUpper(),
                _emailSenderName,
                _emailSenderAddress,
                sb.ToString(),
                emailTo,
                emailCC);


    }
}