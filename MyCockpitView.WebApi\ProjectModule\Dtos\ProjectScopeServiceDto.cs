﻿using AutoMapper;
using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.ProjectModule.Entities;
using System.ComponentModel.DataAnnotations;

namespace MyCockpitView.WebApi.ProjectModule.Dtos;

public class ProjectScopeServiceDto : BaseEntityDto
{
    public int ProjectID { get; set; }
    public int ProjectScopeID { get; set; }

    public string? Title { get; set; }
    public string? Abbreviation { get; set; }
    public decimal SharePercentage { get; set; }

    public decimal Amount { get; set; }
}

public class ProjectScopeServiceDtoMapperProfile : Profile
{
    public ProjectScopeServiceDtoMapperProfile()
    {
        CreateMap<ProjectScopeService, ProjectScopeServiceDto>()
            .ReverseMap()
            .ForMember(dest => dest.ProjectScope, opt => opt.Ignore());

    }
}