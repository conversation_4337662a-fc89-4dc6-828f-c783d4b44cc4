﻿using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.CoreModule;
using System.ComponentModel.DataAnnotations;

namespace MyCockpitView.WebApi.ProjectModule.Entities;

public class ProjectScopeVersion : BaseEntity
{

    [Required]
    public int ProjectID { get; set; }

    public virtual Project? Project { get; set; }

    public string? Scopes { get; set; }
}

public class ProjectScopeVersionConfiguration : BaseEntityConfiguration<ProjectScopeVersion>, IEntityTypeConfiguration<ProjectScopeVersion>
{
    public void Configure(EntityTypeBuilder<ProjectScopeVersion> builder)
    {
        base.Configure(builder);
    }
}