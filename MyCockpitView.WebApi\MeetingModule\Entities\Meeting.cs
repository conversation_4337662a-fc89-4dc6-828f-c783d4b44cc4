﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using MyCockpitView.WebApi.ContactModule.Entities;
using MyCockpitView.CoreModule;

using System.ComponentModel.DataAnnotations;

namespace MyCockpitView.WebApi.MeetingModule.Entities;

public class Meeting : BaseEntity
{

    [Required]
    [StringLength(255)]
    public string? Title { get; set; }


    [StringLength(255)]
    public string? Code { get; set; }

    public DateTime StartDate { get; set; }

    public DateTime EndDate { get; set; }

    [StringLength(255)]
    public string? Location { get; set; }
    public DateTime? ClosedOn { get; set; }
    public DateTime? FinalizedOn { get; set; }

    [Precision(18,2)] public decimal Version { get; set; }
    public int ContactID { get; set; }
    public virtual Contact? Contact { get; set; }
    public virtual ICollection<MeetingAttendee> Attendees { get; set; }= new HashSet<MeetingAttendee>();

    public virtual ICollection<MeetingAgenda> Agendas { get; set; }= new HashSet<MeetingAgenda>();

    public int? ProjectID { get; set; }

    public decimal Sequence { get; set; }
    public int? FunctionID { get; set; }
}

public class MeetingConfiguration : BaseEntityConfiguration<Meeting>, IEntityTypeConfiguration<Meeting>
{
    public void Configure(EntityTypeBuilder<Meeting> builder)
    {
        base.Configure(builder);

        builder.HasIndex(e => e.Title);
        builder.HasIndex(e => e.Code);
        builder.HasIndex(e => e.StartDate);
        builder.HasIndex(e => e.EndDate);
        builder.HasIndex(e => e.ProjectID);

    }
}