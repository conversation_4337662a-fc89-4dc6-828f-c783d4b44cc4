﻿using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.ContactModule.Entities;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;

namespace MyCockpitView.WebApi.ProjectModule.Entities;

public class ProjectOutward : BaseEntity
{
    public string? Title { get; set; }
    public string? Message { get; set; }
    public DateTime? SentDate { get; set; }
    public int ProjectID { get; set; }
    public virtual Project? Project { get; set; }
    public int ContactID { get; set; }
    public virtual Contact? Contact { get; set; }
    public virtual ICollection<ProjectOutwardAttachment> Attachments { get; set; } = new List<ProjectOutwardAttachment>();

}
public class ProjectOutwardConfiguration : BaseEntityConfiguration<ProjectOutward>, IEntityTypeConfiguration<ProjectOutward>
{
    public void Configure(EntityTypeBuilder<ProjectOutward> builder)
    {
        base.Configure(builder);
        // Properties
        builder.Property(pi => pi.Title)
            .HasMaxLength(255);

        builder.Property(pi => pi.Message);

        builder.Property(pi => pi.SentDate);

        builder.Property(pi => pi.ProjectID)
            .IsRequired();

        builder.Property(pi => pi.ContactID)
            .IsRequired();

        // Relationships
        builder.HasOne(pi => pi.Project)
            .WithMany(p => p.Outwards)
            .HasForeignKey(pi => pi.ProjectID)
            .IsRequired()
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(pi => pi.Contact)
            .WithMany()
            .HasForeignKey(pi => pi.ContactID)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasIndex(e => e.SentDate);
    }
}

