﻿using MyCockpitView.CoreModule;
using MyCockpitView.Utility.RDLCClient;
using MyCockpitView.WebApi.DesignScriptModule.Entities;
using MyCockpitView.WebApi.MeetingModule.Entities;
using MyCockpitView.WebApi.PackageModule.Dtos;
using MyCockpitView.WebApi.PackageModule.Entities;
using MyCockpitView.WebApi.Services;
using System.Data;

namespace MyCockpitView.WebApi.PackageModule.Services;

public interface IPackageService : IBaseEntityService<Package>
{
    Task<string> AssignPackageTask(int PackageID, int StageIndex = 1, bool IsAttached = false);
    Task CompletePackageTasks(int WFTaskID, string taskStatus, string Username);
    Task<IEnumerable<PackageAnalysis>> GetAnalysisData(IEnumerable<QueryFilter>? Filters = null, string? Search = null, string? Sort = null);
    Task<byte[]> GetAnalysisExcel(IEnumerable<QueryFilter>? Filters = null, string? Search = null, string? Sort = null);
    Task<ReportDefinition> GetDesignIntentReport(int PackageID, string RenderType = "PDF");
    Task<decimal> GetDuration(int ID);
    Task<string> GetNextRevision(Package Entity);
    Task<decimal> GetPackageStageServicePercentage(int scopeID, int phaseID, string stage);
    Task<DateTime> GetPackageTaskDueDate(DateTime StartDate, DateTime EndDate, int StageIndex = 1);
    Task<PackageDto> GetPackageVHrConsumption(int PackageID);
    Task<List<int>> GetRecursiveChildrenIDs(List<int> _entityIDs);
    Task<ReportDefinition> GetSubmissionDetailIndex(int PackageID, DataTable DataTable);
    Task<IEnumerable<SubmissionDirectory>> GetSubmissionDirectoryData(int ProjectID, int? PackageID = null);
    Task<ReportDefinition> GetSubmissionDirectoryReport(int ProjectID, string RenderType = "PDF");
    Task<string> GetSubmissionEmailPreview(int Id);
    Task<ReportDefinition> GetSubmissionHistoryBriefReport(int ProjectID, string RenderType = "PDF");
    Task<DataTable> GetSubmissionHistoryData(DesignScriptEntity _phase, IEnumerable<Package> Submissions);
    Task<DataTable> GetSubmissionHistoryData(int Id);
    Task<ReportDefinition> GetSubmissionHistoryDetailReport(int ProjectID, string RenderType = "PDF");
    Task<DataTable> GetSubmissionHistorySubData(int ProjectID);
    string GetSubmissionMailBody(string Subject, string ProjectTitle, string ProjectCode, string FirstContact, string FirstContactLeaves, string SecondContact, string SecondContactLeaves, IEnumerable<string> Tos, IEnumerable<string> CCs, string SubmissionDate, string SubmissionTitle, string Message, string DownloadUrl, IEnumerable<PackageAttachment> PdfFiles, IEnumerable<PackageAttachment> SupportFiles, IEnumerable<MeetingAgenda> MeetingAgendas = null);
    Task<string> GetSubmissionSet(int PackageID);
    Task<DataTable> GetTransmittalData(int PackageID);
    Task<ReportDefinition> GetTransmittalReport(int PackageID, string RenderType = "PDF");
    IQueryable<UpcomingPackageDto> GetUpcomingPackages(IEnumerable<QueryFilter>? Filters = null, string? Search = null, string? Sort = null);
    Task<byte[]> GetUpcomingPackagesExcel(IEnumerable<QueryFilter>? Filters = null, string? Search = null, string? Sort = null);
    Task PurgeInvitees(int PackageID);
    Task SendSubmission(int Id, bool skipAgendaUpdate = false, bool skipEmail = false);
    Task StartFlow(int packageID);
    Task UpdateAgendaAfterSubmission(Package package, List<PackageAssociation> packageAssociations);
    Task UpdatePackageConsumption();
    Task UpdatePackageTaskDue(int ID, DateTime StartDate, DateTime FinalDate);
    Task UploadSubmissionsToCloud();
}