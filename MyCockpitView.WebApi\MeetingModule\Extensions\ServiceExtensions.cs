﻿
using MyCockpitView.WebApi.MeetingModule.Services;

namespace MyCockpitView.WebApi.MeetingModule.Extensions;

public static class ServiceExtensions
{

    public static IServiceCollection RegisterMeetingServices(
     this IServiceCollection services)
    {
        services.AddScoped<IMeetingService,MeetingService>();
        services.AddScoped<IMeetingAgendaService, MeetingAgendaService>();
        services.AddScoped<IMeetingAgendaAttachmentService, MeetingAgendaAttachmentService>();
        services.AddScoped<IMeetingAttendeeService, MeetingAttendeeService>();
        return services;
    }
}
