﻿using AutoMapper;
using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.PayrollModule.Entities;

namespace MyCockpitView.WebApi.PayrollModule.Dtos;

public class LoanDto : BaseEntityDto
{


    public int PersonContactID { get; set; }



    public string? PersonName { get; set; }



    public string? PersonBankAccountNo { get; set; }



    public int CompanyID { get; set; }



    public string? CompanyName { get; set; }



    public string? CompanyBankAccountNo { get; set; }

    public decimal LoanAmount { get; set; }

    public decimal InstallmentAmount { get; set; }
    public ICollection<LoanInstallment> Installments { get; set; }

}

public class LoanDtoMapperProfile : Profile
{
    public LoanDtoMapperProfile()
    {
        CreateMap<Loan, LoanDto>()
            .ReverseMap();
    }
}