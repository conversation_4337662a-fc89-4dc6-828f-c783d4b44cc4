﻿using AutoMapper;
using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.ExpenseModule.Entities;

namespace MyCockpitView.WebApi.ExpenseModule.Dtos;

public class ExpenseAttachmentDto : BaseBlobEntityDto
{
    public int ExpenseID { get; set; }
}

public class ExpenseAttachmentDtoMapperProfile : Profile
{
    public ExpenseAttachmentDtoMapperProfile()
    {


        CreateMap<ExpenseAttachment, ExpenseAttachmentDto>()
                    .ReverseMap()
                   .ForMember(dest => dest.Expense, opt => opt.Ignore());
    }
}