﻿

using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using MyCockpitView.CoreModule;


namespace MyCockpitView.WebApi.DesignScriptModule.Entities;

public class DesignScriptItem : BaseEntity
{
    [StringLength(256)]
    public string? DSR { get; set; }

    [StringLength(256)]
    public string? DSRNumber { get; set; }

    [StringLength(256)]
    public string? Title { get; set; }

    [StringLength(256)]
    public string? ItemGroup { get; set; }

    [StringLength(256)]
    public string? Category { get; set; }
    public string? Specification { get; set; }

    [StringLength(256)]
    public string?  Units { get; set; }
    [Precision(14, 2)]
    public decimal Rate { get; set; } = 0;

    public int CodeFlag { get; set; }

    [StringLength(256)]
    public string?  Code { get; set; }

    [StringLength(10)]
    public string?  ProjectCode { get; set; }
    public int? ProjectID { get; set; }
    public Guid? ProjectUID { get; set; }
    public virtual ICollection<DesignScriptEntityItemMap> Entities { get; set; }=new List<DesignScriptEntityItemMap>();

    [StringLength(256)]
    public string? SubCategory { get; set; }
    public int CategoryOrderFlag { get; set; }
    public int SubCategoryOrderFlag { get; set; }
    public string? Reference { get; set; }
    public int? MasterID { get; set; }
    public string? DrawingSpecification { get; set; }


}

public class DesignScriptItemConfiguration : BaseEntityConfiguration<DesignScriptItem>, IEntityTypeConfiguration<DesignScriptItem>
{
    public void Configure(EntityTypeBuilder<DesignScriptItem> builder)
    {
       base.Configure(builder);

        builder.HasIndex(e => e.ProjectID);
        builder.HasIndex(e => e.ProjectUID);
        builder.HasIndex(e => e.ProjectCode);
        builder.HasIndex(e => e.CodeFlag);
        builder.HasIndex(e => e.Code);
        builder.HasIndex(e => e.Category);
        builder.HasIndex(e => e.ItemGroup);
        builder.HasIndex(e => e.Title);
        builder.HasIndex(e => e.DSR);
        builder.HasIndex(e => e.DSRNumber);
    }
}