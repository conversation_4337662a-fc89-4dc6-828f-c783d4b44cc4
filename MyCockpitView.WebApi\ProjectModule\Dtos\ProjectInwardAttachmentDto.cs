﻿using AutoMapper;
using MyCockpitView.CoreModule;
using MyCockpitView.WebApi.ProjectModule.Entities;
using System.ComponentModel.DataAnnotations;

namespace MyCockpitView.WebApi.ProjectModule.Dtos;

public class ProjectInwardAttachmentDto : BaseBlobEntityDto
{

    public int ProjectInwardID { get; set; }
}

public class ProjectInwardAttachmentDtoMapperProfile : Profile
{
    public ProjectInwardAttachmentDtoMapperProfile()
    {
        CreateMap<ProjectInwardAttachment, ProjectInwardAttachmentDto>()
                   .ReverseMap();

    }
}