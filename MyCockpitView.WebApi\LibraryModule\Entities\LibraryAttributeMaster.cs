﻿using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using MyCockpitView.CoreModule;
using System.ComponentModel.DataAnnotations;

namespace MyCockpitView.WebApi.LibraryModule.Entities;

public class LibraryAttributeMaster : BaseEntity
{
    [Required]
    [StringLength(255)]
    public string Category { get; set; }

    [Required]
    [StringLength(255)]
    public string Attribute { get; set; }
}

public class LibraryAttributeMasterConfiguration : BaseEntityConfiguration<LibraryAttributeMaster>, IEntityTypeConfiguration<LibraryAttributeMaster>
{
    public void Configure(EntityTypeBuilder<LibraryAttributeMaster> builder)
    {
      base.Configure(builder);

        builder.HasIndex(e => e.Category);
        builder.HasIndex(e => e.Attribute);
    }
}