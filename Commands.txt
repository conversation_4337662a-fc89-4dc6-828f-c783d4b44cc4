########### DOCKER ######################
docker pull mcr.microsoft.com/mssql/server:2022-latest

docker run -e "ACCEPT_EULA=1" -e "MSSQL_SA_PASSWORD=MyC0ckpitView@2025" -e "MSSQL_PID=Express" -v newarch-sqldb:/var/opt/mssql -p 1433:1433 -d --name=newarch-sql mcr.microsoft.com/mssql/server:2022-latest

sqlcmd -S localhost,1433 -U sa -P MyC0ckpitView@2025

docker build -t newarch-webapi .
docker run -it --rm -p 8080:80 -p 8081:443 -e ASPNETCORE_URLS="https://+;http://+" -e ASPNETCORE_HTTPS_PORT=8081 -e ASPNETCORE_ENVIRONMENT=Development -e SqlServerDbSettings__Server=localhost,1433 -e SqlServerDbSettings__Password=MyC0ckpitView@2025 -v $env:APPDATA\Microsoft\UserSecrets\:/root/.microsoft/usersecrets:ro -v $env:USERPROFILE\.aspnet\https:/root/.aspnet/https:ro --name newarch-api newarch-webapi

dotnet dev-certs https -ep %USERPROFILE%\.aspnet\https\Newarch.WebApi.pfx -p pa55w0rd!
dotnet dev-certs https --trust

dotnet user-secrets set "Kestrel:Certificates:Development:Password" "pa55w0rd!"

########### DOCKER_COMPOSE ######################
docker-compose -f "docker-compose.yml" -f "docker-compose.override.yml"

docker-compose up --build

########### DOCKER_HUB ######################

docker login
docker tag newarch-webapi amey4nit/newarch-webapi
docker push amey4nit/newarch-webapi

########### KUBERNETES ######################

kubectl apply -f newarch-webapi-deploy.yml

kubectl delete deployment newarch-sqldb
kubectl delete deployment newarch-webapi 

########### AZURE CLI ######################

az login

az group create --name NewarchMicroserviceRG --location centralindia

########### AZURE CONTAINER REGISTRY ######################

az acr login --name newarchdockyard.azurecr.io

az acr update -n newarchdockyard.azurecr.io --admin-enabled true

docker tag newarch-webapi newarchdockyard.azurecr.io/newarch-webapi

docker push newarchdockyard.azurecr.io/newarch-webapi

########### AZURE CONTAINER INTANCES SQL SERVER ######################

# Create a storage account
az storage account create `
--resource-group NewarchMicroserviceRG `
--location centralindia `
--name newarchsqlserver `
--sku Standard_LRS

# Create the file share
az storage share create `
--name aci-sql-server `
--account-name newarchsqlserver

az storage account keys list `
--resource-group NewarchMicroserviceRG `
--account-name newarchsqlserver `
--query "[0].value" `
--output tsv

# Create the container
az container create `
--resource-group NewarchMicroserviceRG `
--location centralindia `
--name newarch-sqldb `
--image mcr.microsoft.com/mssql/server:2022-latest `
--environment-variables ACCEPT_EULA=Y MSSQL_PID=Express MSSQL_SA_PASSWORD=MyC0ckpitView@2025 `
--ip-address public `
--dns-name-label newarch-sqldb  `
--cpu 2 `
--memory 2 `
--port 1433 `
--azure-file-volume-account-name newarchsqlserver `
--azure-file-volume-account-key **************************************************************************************** --azure-file-volume-share-name aci-sql-server `
--azure-file-volume-mount-path "/var/opt/mssql"


# Get container details
az container show `
--resource-group NewarchMicroserviceRG `
--name newarch-sqldb `
--query "{Status:instanceView.state}" `
--out table

# Get container details
az container show `
--resource-group NewarchMicroserviceRG `
--name newarch-sqldb  `
--query "{IP_Adress:ipAddress.ip,FQDN:ipAddress.fqdn}" `
--out table

az container show `
--resource-group NewarchMicroserviceRG `
--name newarch-sqldb  `
--query ipAddress.fqdn `
--output tsv

# Test connection with SQLCMD
sqlcmd -S newarch-sqldb.centralindia.azurecontainer.io -U SA -P MyC0ckpitView@2025 -Q "select @@version"

# Attache existing database
sqlcmd -S newarch-sqldb.centralindia.azurecontainer.io -U SA -P MyC0ckpitView@2025 -Q "
CREATE DATABASE [Newarch] ON (FILENAME = '/var/opt/mssql/data/Newarch.mdf'),(FILENAME = '/var/opt/mssql/data/Newarch_log.ldf') FOR ATTACH;"

# Stop Container
az container stop -n newarch-sqldb -g NewarchMicroserviceRG

# Start Container
az container start -n newarch-sqldb -g NewarchMicroserviceRG

# Delete Container
az container delete -n newarch-sqldb -g NewarchMicroserviceRG

########### AZURE CONTAINER INTANCES WebApi ######################

az container create `
--resource-group NewarchMicroserviceRG `
--location centralindia `
--name newarch-webapi `
--image newarchdockyard.azurecr.io/newarch-webapi `
--environment-variables `
ASPNETCORE_ENVIRONMENT=Development `
ASPNETCORE_URLS=https://+:443;http://+:80 `
ASPNETCORE_HTTPS_PORT=8081 `
SqlServerDbSettings__Server=newarch-sqldb.centralindia.azurecontainer.io,1433 `
SqlServerDbSettings__Database=Newarch `
SqlServerDbSettings__Password=MyC0ckpitView@2025 `
--ip-address public `
--cpu 2 `
--memory 2 `
--ports 80 443 `
--azure-file-volume-account-name newarchsqlserver `
--azure-file-volume-account-key **************************************************************************************** `
--azure-file-volume-share-name aci-https `
--azure-file-volume-mount-path "/root/.aspnet/https"

HfnraZ488qRHQzNc4yz8FY3l7Lyt5my0to4LKtzbTz+ACRBef/jV

########### AZURE CONTAINER INTANCES DOCKER-COMPOSE ######################

docker login azure

docker context create aci newarch-aci-context

docker context ls

docker context use newarch-aci-context

docker compose up



---
apiVersion: v1
kind: Service
metadata:
  name: newarch-webapi
spec:
  selector:
    app: newarch-webapi
  ports:
    - name: http
      port: 80
      targetPort: 8080

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: newarch-sqldb
spec:
  replicas: 1
  selector:
    matchLabels:
      app: newarch-sqldb
  template:
    metadata:
      labels:
        app: newarch-sqldb
    spec:
      containers:
        - name: newarch-sqldb
          image: mcr.microsoft.com/mssql/server:2022-latest
          ports:
            - containerPort: 1433
          env:
            - name: ACCEPT_EULA
              value: "Y"
            - name: MSSQL_SA_PASSWORD
              value: "MyC0ckpitView@2025"
            - name: MSSQL_PID
              value: "Express"
          volumeMounts:
            - name: mssql-data
              mountPath: /var/opt/mssql/data
            - name: mssql-log
              mountPath: /var/opt/mssql/log
            - name: mssql-secrets
              mountPath: /var/opt/mssql/secrets
      volumes:
        - name: mssql-data
          hostPath:
            path: C:/Git/mssql/data
        - name: mssql-log
          hostPath:
            path: C:/Git/mssql/log
        - name: mssql-secrets
          hostPath:
            path: C:/Git/mssql/secrets

---
apiVersion: v1
kind: Service
metadata:
  name: newarch-sqldb
spec:
  selector:
    app: newarch-sqldb
  ports:
    - name: sql
      port: 1433
      targetPort: 1433
